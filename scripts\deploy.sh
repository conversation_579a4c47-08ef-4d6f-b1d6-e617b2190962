#!/bin/bash

# 自動部署腳本
# 使用方法: ./scripts/deploy.sh [環境] [訊息]
# 範例: ./scripts/deploy.sh staging "新增支付功能"

set -e  # 遇到錯誤立即退出

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函數定義
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查參數
ENVIRONMENT=${1:-"staging"}
COMMIT_MESSAGE=${2:-"自動部署更新"}

log_info "開始部署到 $ENVIRONMENT 環境..."
log_info "提交訊息: $COMMIT_MESSAGE"

# 檢查是否在 Git 倉庫中
if [ ! -d ".git" ]; then
    log_error "當前目錄不是 Git 倉庫"
    exit 1
fi

# 檢查是否有未提交的變更
if [ -n "$(git status --porcelain)" ]; then
    log_info "發現未提交的變更，正在處理..."
    
    # 顯示變更的文件
    echo "變更的文件:"
    git status --short
    
    # 添加所有變更
    log_info "添加所有變更到暫存區..."
    git add .
    
    # 提交變更
    log_info "提交變更..."
    git commit -m "$COMMIT_MESSAGE"
    
    log_success "變更已提交"
else
    log_info "沒有未提交的變更"
fi

# 檢查遠端倉庫
if ! git remote get-url origin > /dev/null 2>&1; then
    log_warning "未設置遠端倉庫，請先設置 GitHub 倉庫:"
    echo "git remote add origin https://github.com/your-username/member-management-system.git"
    exit 1
fi

# 獲取當前分支
CURRENT_BRANCH=$(git branch --show-current)
log_info "當前分支: $CURRENT_BRANCH"

# 根據環境決定目標分支
case $ENVIRONMENT in
    "production" | "prod")
        TARGET_BRANCH="main"
        ;;
    "staging" | "stage")
        TARGET_BRANCH="develop"
        ;;
    *)
        TARGET_BRANCH=$CURRENT_BRANCH
        ;;
esac

log_info "目標分支: $TARGET_BRANCH"

# 如果當前分支不是目標分支，則切換或創建
if [ "$CURRENT_BRANCH" != "$TARGET_BRANCH" ]; then
    log_info "切換到 $TARGET_BRANCH 分支..."
    
    # 檢查分支是否存在
    if git show-ref --verify --quiet refs/heads/$TARGET_BRANCH; then
        git checkout $TARGET_BRANCH
        git merge $CURRENT_BRANCH --no-ff -m "Merge $CURRENT_BRANCH into $TARGET_BRANCH"
    else
        git checkout -b $TARGET_BRANCH
    fi
fi

# 運行測試
log_info "運行測試..."
cd backend

if npm run test:ci; then
    log_success "所有測試通過"
else
    log_error "測試失敗，停止部署"
    exit 1
fi

cd ..

# 推送到遠端倉庫
log_info "推送到遠端倉庫..."
if git push origin $TARGET_BRANCH; then
    log_success "成功推送到 origin/$TARGET_BRANCH"
else
    log_error "推送失敗"
    exit 1
fi

# 創建標籤 (僅生產環境)
if [ "$ENVIRONMENT" = "production" ] || [ "$ENVIRONMENT" = "prod" ]; then
    log_info "創建發布標籤..."
    
    # 獲取版本號
    VERSION=$(node -p "require('./backend/package.json').version")
    TAG_NAME="v$VERSION-$(date +%Y%m%d-%H%M%S)"
    
    git tag -a $TAG_NAME -m "Release $TAG_NAME"
    git push origin $TAG_NAME
    
    log_success "創建標籤: $TAG_NAME"
fi

# 顯示部署狀態
log_success "部署完成！"
echo ""
echo "📋 部署摘要:"
echo "   環境: $ENVIRONMENT"
echo "   分支: $TARGET_BRANCH"
echo "   提交: $(git rev-parse --short HEAD)"
echo "   時間: $(date)"
echo ""

# 顯示 GitHub Actions 連結
REPO_URL=$(git remote get-url origin | sed 's/\.git$//')
if [[ $REPO_URL == *"github.com"* ]]; then
    ACTIONS_URL="$REPO_URL/actions"
    echo "🔗 查看 CI/CD 狀態: $ACTIONS_URL"
fi

# 顯示下一步
echo ""
echo "🚀 下一步:"
echo "   1. 查看 GitHub Actions 執行狀態"
echo "   2. 確認部署是否成功"
echo "   3. 進行煙霧測試"

log_success "腳本執行完成"
