import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function SubscriptionDebug() {
  const [subscriptions, setSubscriptions] = useState([]);
  const [services, setServices] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [debugInfo, setDebugInfo] = useState('');

  const addDebugInfo = (info) => {
    setDebugInfo(prev => prev + '\n' + new Date().toLocaleTimeString() + ': ' + info);
  };

  const loadSubscriptions = async () => {
    try {
      addDebugInfo('🔄 載入訂閱數據...');
      const token = localStorage.getItem('token');
      
      if (!token) {
        addDebugInfo('❌ 沒有 token');
        return [];
      }

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      addDebugInfo(`📋 訂閱 API 響應: ${JSON.stringify(result)}`);

      if (result.success && result.data) {
        const formatted = result.data.map(sub => ({
          id: sub.id,
          service_id: sub.service_id,
          service_name: sub.service_name,
          status: sub.status
        }));
        
        setSubscriptions(formatted);
        addDebugInfo(`✅ 載入 ${formatted.length} 個訂閱`);
        return formatted;
      }
      
      return [];
    } catch (error) {
      addDebugInfo(`❌ 載入訂閱失敗: ${error.message}`);
      return [];
    }
  };

  const loadServices = async () => {
    try {
      addDebugInfo('🔄 載入服務數據...');
      const response = await fetch('/api/services/database');
      const result = await response.json();
      
      addDebugInfo(`🛠️ 服務 API 響應: ${JSON.stringify(result)}`);

      if (result.success && result.data) {
        const activeServices = result.data.filter(s => s.status === 'active');
        setServices(activeServices);
        addDebugInfo(`✅ 載入 ${activeServices.length} 個活躍服務`);
        return activeServices;
      }
      
      return [];
    } catch (error) {
      addDebugInfo(`❌ 載入服務失敗: ${error.message}`);
      return [];
    }
  };

  const calculateAvailableServices = (allServices, currentSubscriptions) => {
    addDebugInfo('🔄 計算可用服務...');
    
    const activeSubscribedServiceIds = currentSubscriptions
      .filter(sub => sub.status === 'active')
      .map(sub => sub.service_id);
    
    addDebugInfo(`📋 活躍訂閱的服務 ID: [${activeSubscribedServiceIds.join(', ')}]`);
    
    const available = allServices.filter(service => 
      !activeSubscribedServiceIds.includes(service.id)
    );
    
    addDebugInfo(`🎯 可用服務: ${available.length} 個`);
    addDebugInfo(`📊 邏輯驗證: ${currentSubscriptions.filter(s => s.status === 'active').length} + ${available.length} = ${currentSubscriptions.filter(s => s.status === 'active').length + available.length} (總服務: ${allServices.length})`);
    
    setAvailableServices(available);
    return available;
  };

  const testSubscribe = async (serviceId) => {
    try {
      addDebugInfo(`🔄 測試訂閱服務 ${serviceId}...`);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/subscriptions/simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ service_id: serviceId })
      });

      const result = await response.json();
      addDebugInfo(`📡 訂閱響應: ${JSON.stringify(result)}`);
      
      if (result.success) {
        addDebugInfo('✅ 訂閱成功，重新載入數據...');
        await loadData();
      }
    } catch (error) {
      addDebugInfo(`❌ 訂閱失敗: ${error.message}`);
    }
  };

  const testCancel = async (subscriptionId) => {
    try {
      addDebugInfo(`🔄 測試取消訂閱 ${subscriptionId}...`);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/subscriptions/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ subscription_id: subscriptionId })
      });

      const result = await response.json();
      addDebugInfo(`📡 取消響應: ${JSON.stringify(result)}`);
      
      if (result.success) {
        addDebugInfo('✅ 取消成功，重新載入數據...');
        await loadData();
      }
    } catch (error) {
      addDebugInfo(`❌ 取消失敗: ${error.message}`);
    }
  };

  const loadData = async () => {
    const subs = await loadSubscriptions();
    const servs = await loadServices();
    calculateAvailableServices(servs, subs);
  };

  useEffect(() => {
    loadData();
  }, []);

  return (
    <>
      <Head>
        <title>訂閱邏輯調試</title>
      </Head>
      
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">訂閱邏輯調試</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 當前狀態 */}
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">活躍訂閱 ({subscriptions.filter(s => s.status === 'active').length})</h2>
                {subscriptions.filter(s => s.status === 'active').map(sub => (
                  <div key={sub.id} className="flex justify-between items-center p-3 bg-green-50 rounded mb-2">
                    <span>{sub.service_name} (ID: {sub.service_id})</span>
                    <button 
                      onClick={() => testCancel(sub.id)}
                      className="px-3 py-1 bg-red-500 text-white rounded text-sm"
                    >
                      取消
                    </button>
                  </div>
                ))}
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">已取消訂閱 ({subscriptions.filter(s => s.status === 'cancelled').length})</h2>
                {subscriptions.filter(s => s.status === 'cancelled').map(sub => (
                  <div key={sub.id} className="flex justify-between items-center p-3 bg-gray-50 rounded mb-2">
                    <span>{sub.service_name} (ID: {sub.service_id})</span>
                    <button 
                      onClick={() => testSubscribe(sub.service_id)}
                      className="px-3 py-1 bg-green-500 text-white rounded text-sm"
                    >
                      重新訂閱
                    </button>
                  </div>
                ))}
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">可用服務 ({availableServices.length})</h2>
                {availableServices.map(service => (
                  <div key={service.id} className="flex justify-between items-center p-3 bg-blue-50 rounded mb-2">
                    <span>{service.name} (ID: {service.id})</span>
                    <button 
                      onClick={() => testSubscribe(service.id)}
                      className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
                    >
                      訂閱
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* 調試日誌 */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">調試日誌</h2>
                <button 
                  onClick={() => setDebugInfo('')}
                  className="px-3 py-1 bg-gray-500 text-white rounded text-sm"
                >
                  清除
                </button>
              </div>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto h-96 whitespace-pre-wrap">
                {debugInfo}
              </pre>
            </div>
          </div>

          <div className="mt-8 text-center">
            <button 
              onClick={loadData}
              className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
            >
              重新載入數據
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
