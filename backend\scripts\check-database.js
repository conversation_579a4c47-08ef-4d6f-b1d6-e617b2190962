#!/usr/bin/env node

// 資料庫狀態檢查腳本
const { Pool } = require('pg');

console.log('🔍 檢查資料庫狀態...\n');

// 檢查 PostgreSQL 連接
async function checkPostgreSQL() {
  console.log('📊 檢查 PostgreSQL...');
  
  const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'member_system',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });

  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW() as current_time, version() as version');
    
    console.log('✅ PostgreSQL 連接成功');
    console.log(`   時間: ${result.rows[0].current_time}`);
    console.log(`   版本: ${result.rows[0].version.split(' ')[0]} ${result.rows[0].version.split(' ')[1]}`);
    
    // 檢查資料庫是否存在
    const dbResult = await client.query(
      "SELECT datname FROM pg_database WHERE datname IN ('member_system', 'member_system_test')"
    );
    
    console.log(`   資料庫: ${dbResult.rows.map(row => row.datname).join(', ')}`);
    
    client.release();
    await pool.end();
    return true;
  } catch (error) {
    console.log('❌ PostgreSQL 連接失敗');
    console.log(`   錯誤: ${error.message}`);
    return false;
  }
}

// 檢查 Redis 連接
async function checkRedis() {
  console.log('\n🔄 檢查 Redis...');
  
  try {
    const redis = require('redis');
    const client = redis.createClient({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined
    });

    await client.connect();
    const pong = await client.ping();
    const info = await client.info('server');
    
    console.log('✅ Redis 連接成功');
    console.log(`   響應: ${pong}`);
    
    // 解析 Redis 版本
    const versionMatch = info.match(/redis_version:([^\r\n]+)/);
    if (versionMatch) {
      console.log(`   版本: Redis ${versionMatch[1]}`);
    }
    
    await client.quit();
    return true;
  } catch (error) {
    console.log('❌ Redis 連接失敗');
    console.log(`   錯誤: ${error.message}`);
    return false;
  }
}

// 檢查環境變數
function checkEnvironment() {
  console.log('\n⚙️ 檢查環境變數...');
  
  const requiredVars = [
    'DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD',
    'JWT_SECRET', 'ENCRYPTION_KEY'
  ];
  
  const missingVars = [];
  
  requiredVars.forEach(varName => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  });
  
  if (missingVars.length === 0) {
    console.log('✅ 所有必要環境變數已設置');
    return true;
  } else {
    console.log('⚠️ 缺少環境變數:');
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log('\n請檢查 .env 文件');
    return false;
  }
}

// 主函數
async function main() {
  console.log('🗄️ 資料庫狀態檢查\n');
  
  // 載入環境變數
  try {
    require('dotenv').config();
  } catch (error) {
    console.log('⚠️ 無法載入 dotenv，使用預設值');
  }
  
  const results = {
    environment: checkEnvironment(),
    postgresql: await checkPostgreSQL(),
    redis: await checkRedis()
  };
  
  console.log('\n📋 檢查結果摘要:');
  console.log(`   環境變數: ${results.environment ? '✅' : '❌'}`);
  console.log(`   PostgreSQL: ${results.postgresql ? '✅' : '❌'}`);
  console.log(`   Redis: ${results.redis ? '✅' : '❌'}`);
  
  const allGood = Object.values(results).every(result => result);
  
  if (allGood) {
    console.log('\n🎉 所有檢查通過！資料庫已準備就緒');
    console.log('\n下一步:');
    console.log('   1. npm run migrate  # 運行資料庫遷移');
    console.log('   2. npm run seed     # 插入種子資料');
    console.log('   3. npm run dev      # 啟動開發服務器');
  } else {
    console.log('\n⚠️ 部分檢查失敗，請參考上述錯誤訊息');
    console.log('\n建議:');
    console.log('   1. 檢查資料庫服務是否運行');
    console.log('   2. 確認 .env 文件配置');
    console.log('   3. 查看 docs/DATABASE_SETUP.md');
  }
  
  process.exit(allGood ? 0 : 1);
}

// 執行檢查
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 檢查過程發生錯誤:', error.message);
    process.exit(1);
  });
}

module.exports = { checkPostgreSQL, checkRedis, checkEnvironment };
