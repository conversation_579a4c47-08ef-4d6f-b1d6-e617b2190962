#!/usr/bin/env node

// 資料庫種子資料腳本
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

// 載入環境變數
require('dotenv').config();

console.log('🌱 開始插入種子資料...\n');

// 資料庫連接配置
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'member_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

// 種子資料
const seedData = {
  services: [
    {
      name: 'AI 文字生成服務',
      description: '使用先進的 AI 技術生成高品質文字內容',
      category: 'ai',
      status: 'active',
      metadata: {
        features: ['文章生成', '摘要製作', '翻譯服務'],
        api_endpoint: '/api/ai/text-generation'
      }
    },
    {
      name: '圖片處理服務',
      description: '提供圖片壓縮、格式轉換、濾鏡等功能',
      category: 'media',
      status: 'active',
      metadata: {
        features: ['圖片壓縮', '格式轉換', '濾鏡效果'],
        api_endpoint: '/api/media/image-processing'
      }
    },
    {
      name: '資料分析服務',
      description: '強大的資料分析和視覺化工具',
      category: 'analytics',
      status: 'active',
      metadata: {
        features: ['資料視覺化', '統計分析', '報表生成'],
        api_endpoint: '/api/analytics/data-analysis'
      }
    }
  ],

  servicePlans: [
    // AI 文字生成服務方案
    {
      service_name: 'AI 文字生成服務',
      name: '基礎方案',
      description: '適合個人用戶的基礎 AI 文字生成服務',
      price: 299.00,
      currency: 'TWD',
      billing_period: 'monthly',
      features: {
        requests_per_month: 1000,
        max_words_per_request: 500,
        languages: ['zh-TW', 'en'],
        support: 'email'
      },
      limits: {
        daily_requests: 50,
        concurrent_requests: 2
      }
    },
    {
      service_name: 'AI 文字生成服務',
      name: '專業方案',
      description: '適合中小企業的專業 AI 文字生成服務',
      price: 899.00,
      currency: 'TWD',
      billing_period: 'monthly',
      features: {
        requests_per_month: 5000,
        max_words_per_request: 2000,
        languages: ['zh-TW', 'en', 'ja', 'ko'],
        support: 'priority_email'
      },
      limits: {
        daily_requests: 200,
        concurrent_requests: 5
      }
    },
    // 圖片處理服務方案
    {
      service_name: '圖片處理服務',
      name: '個人方案',
      description: '適合個人用戶的圖片處理服務',
      price: 199.00,
      currency: 'TWD',
      billing_period: 'monthly',
      features: {
        images_per_month: 500,
        max_file_size: '10MB',
        formats: ['jpg', 'png', 'webp'],
        support: 'email'
      },
      limits: {
        daily_images: 25,
        concurrent_processing: 2
      }
    },
    {
      service_name: '圖片處理服務',
      name: '商業方案',
      description: '適合企業用戶的圖片處理服務',
      price: 599.00,
      currency: 'TWD',
      billing_period: 'monthly',
      features: {
        images_per_month: 2000,
        max_file_size: '50MB',
        formats: ['jpg', 'png', 'webp', 'svg', 'tiff'],
        support: 'priority_support'
      },
      limits: {
        daily_images: 100,
        concurrent_processing: 5
      }
    },
    // 資料分析服務方案
    {
      service_name: '資料分析服務',
      name: '入門方案',
      description: '適合小型團隊的資料分析服務',
      price: 499.00,
      currency: 'TWD',
      billing_period: 'monthly',
      features: {
        datasets_per_month: 10,
        max_dataset_size: '100MB',
        chart_types: ['bar', 'line', 'pie'],
        export_formats: ['png', 'pdf']
      },
      limits: {
        daily_analysis: 5,
        concurrent_analysis: 1
      }
    },
    {
      service_name: '資料分析服務',
      name: '企業方案',
      description: '適合大型企業的資料分析服務',
      price: 1299.00,
      currency: 'TWD',
      billing_period: 'monthly',
      features: {
        datasets_per_month: 100,
        max_dataset_size: '1GB',
        chart_types: ['bar', 'line', 'pie', 'scatter', 'heatmap', 'treemap'],
        export_formats: ['png', 'pdf', 'svg', 'excel']
      },
      limits: {
        daily_analysis: 50,
        concurrent_analysis: 10
      }
    }
  ],

  testUsers: [
    {
      username: 'admin',
      email: '<EMAIL>',
      password: 'Admin123456',
      first_name: '系統',
      last_name: '管理員',
      phone: '+886912345678',
      status: 'active',
      email_verified_at: new Date()
    },
    {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'Test123456',
      first_name: '測試',
      last_name: '用戶',
      phone: '+886987654321',
      status: 'active',
      email_verified_at: new Date()
    }
  ]
};

// 插入服務資料
async function seedServices(client) {
  console.log('📦 插入服務資料...');
  
  for (const service of seedData.services) {
    const result = await client.query(
      `INSERT INTO services (name, description, category, status, metadata)
       VALUES ($1, $2, $3, $4, $5)
       ON CONFLICT DO NOTHING
       RETURNING id`,
      [service.name, service.description, service.category, service.status, JSON.stringify(service.metadata)]
    );
    
    if (result.rows.length > 0) {
      console.log(`✅ 服務已創建: ${service.name}`);
    } else {
      console.log(`⏭️ 服務已存在: ${service.name}`);
    }
  }
}

// 插入服務方案資料
async function seedServicePlans(client) {
  console.log('\n💰 插入服務方案資料...');
  
  for (const plan of seedData.servicePlans) {
    // 獲取服務 ID
    const serviceResult = await client.query(
      'SELECT id FROM services WHERE name = $1',
      [plan.service_name]
    );
    
    if (serviceResult.rows.length === 0) {
      console.log(`⚠️ 找不到服務: ${plan.service_name}`);
      continue;
    }
    
    const serviceId = serviceResult.rows[0].id;
    
    const result = await client.query(
      `INSERT INTO service_plans (service_id, name, description, price, currency, billing_period, features, limits)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       ON CONFLICT DO NOTHING
       RETURNING id`,
      [
        serviceId,
        plan.name,
        plan.description,
        plan.price,
        plan.currency,
        plan.billing_period,
        JSON.stringify(plan.features),
        JSON.stringify(plan.limits)
      ]
    );
    
    if (result.rows.length > 0) {
      console.log(`✅ 方案已創建: ${plan.service_name} - ${plan.name}`);
    } else {
      console.log(`⏭️ 方案已存在: ${plan.service_name} - ${plan.name}`);
    }
  }
}

// 插入測試用戶資料
async function seedTestUsers(client) {
  console.log('\n👤 插入測試用戶資料...');
  
  for (const user of seedData.testUsers) {
    // 檢查用戶是否已存在
    const existingUser = await client.query(
      'SELECT id FROM users WHERE email = $1 OR username = $2',
      [user.email, user.username]
    );
    
    if (existingUser.rows.length > 0) {
      console.log(`⏭️ 用戶已存在: ${user.username}`);
      continue;
    }
    
    // 加密密碼
    const hashedPassword = await bcrypt.hash(user.password, 10);
    
    const result = await client.query(
      `INSERT INTO users (username, email, password, first_name, last_name, phone, status, email_verified_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       RETURNING id`,
      [
        user.username,
        user.email,
        hashedPassword,
        user.first_name,
        user.last_name,
        user.phone,
        user.status,
        user.email_verified_at
      ]
    );
    
    const userId = result.rows[0].id;
    
    // 為用戶創建預設錢包
    await client.query(
      `INSERT INTO wallets (user_id, currency, balance)
       VALUES ($1, 'TWD', 0.00), ($1, 'USD', 0.00)`,
      [userId]
    );
    
    console.log(`✅ 用戶已創建: ${user.username} (ID: ${userId})`);
  }
}

// 主函數
async function runSeeding() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    await seedServices(client);
    await seedServicePlans(client);
    await seedTestUsers(client);
    
    await client.query('COMMIT');
    console.log('\n🎉 所有種子資料插入完成！');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ 種子資料插入失敗:', error.message);
    throw error;
  } finally {
    client.release();
  }
}

// 執行種子資料插入
async function main() {
  try {
    await runSeeding();
    console.log('\n✅ 種子資料插入成功完成');
    console.log('\n📋 測試帳號:');
    console.log('   管理員: <EMAIL> / Admin123456');
    console.log('   測試用戶: <EMAIL> / Test123456');
  } catch (error) {
    console.error('\n❌ 種子資料插入失敗:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// 執行種子資料插入
if (require.main === module) {
  main();
}

module.exports = { runSeeding };
