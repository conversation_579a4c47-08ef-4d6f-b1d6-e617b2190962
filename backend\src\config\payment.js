const Stripe = require('stripe');
const paypal = require('paypal-rest-sdk');

// Stripe 配置
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

// PayPal 配置
paypal.configure({
  mode: process.env.PAYPAL_MODE || 'sandbox',
  client_id: process.env.PAYPAL_CLIENT_ID,
  client_secret: process.env.PAYPAL_CLIENT_SECRET,
});

// 綠界科技配置
const ecpayConfig = {
  merchantID: process.env.ECPAY_MERCHANT_ID,
  hashKey: process.env.ECPAY_HASH_KEY,
  hashIV: process.env.ECPAY_HASH_IV,
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'test',
  returnURL: `${process.env.FRONTEND_URL}/payment/return`,
  notifyURL: `${process.env.API_URL}/api/v1/payments/ecpay/notify`,
  clientBackURL: `${process.env.FRONTEND_URL}/wallet`,
};

// 支援的幣種和匯率
const supportedCurrencies = {
  TWD: { symbol: 'NT$', decimals: 0 },
  USD: { symbol: '$', decimals: 2 },
  EUR: { symbol: '€', decimals: 2 },
  JPY: { symbol: '¥', decimals: 0 },
  CNY: { symbol: '¥', decimals: 2 },
  HKD: { symbol: 'HK$', decimals: 2 },
};

// 充值獎勵配置
const rewardConfig = {
  // 充值金額階梯獎勵
  tiers: [
    { minAmount: 1000, bonusPercent: 5 },   // 1000以上 5%獎勵
    { minAmount: 5000, bonusPercent: 10 },  // 5000以上 10%獎勵
    { minAmount: 10000, bonusPercent: 15 }, // 10000以上 15%獎勵
    { minAmount: 50000, bonusPercent: 20 }, // 50000以上 20%獎勵
  ],
  // 首次充值獎勵
  firstTimeBonus: 100, // 首次充值額外獎勵100元
  // 每日充值限額
  dailyLimit: 100000,
  // 單筆充值限額
  singleLimit: 50000,
  // 最小充值金額
  minAmount: 100,
};

// 支付方式配置
const paymentMethods = {
  stripe: {
    name: 'Stripe',
    supportedCurrencies: ['USD', 'EUR', 'TWD'],
    fees: {
      percentage: 2.9,
      fixed: 30, // cents
    },
  },
  paypal: {
    name: 'PayPal',
    supportedCurrencies: ['USD', 'EUR', 'TWD'],
    fees: {
      percentage: 3.4,
      fixed: 0,
    },
  },
  ecpay: {
    name: '綠界科技',
    supportedCurrencies: ['TWD'],
    fees: {
      percentage: 1.5,
      fixed: 0,
    },
  },
};

module.exports = {
  stripe,
  paypal,
  ecpayConfig,
  supportedCurrencies,
  rewardConfig,
  paymentMethods,
};