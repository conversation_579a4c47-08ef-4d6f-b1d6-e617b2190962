import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function FixSubscriptionOverlap() {
  const router = useRouter();
  const [status, setStatus] = useState('準備中...');
  const [isFixed, setIsFixed] = useState(false);
  const [details, setDetails] = useState([]);

  useEffect(() => {
    fixOverlapIssue();
  }, []);

  const addDetail = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setDetails(prev => [...prev, { timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  const fixOverlapIssue = async () => {
    try {
      setStatus('🔍 檢查重複問題...');
      addDetail('開始檢查訂閱服務與可用服務的重複問題', 'info');

      const token = localStorage.getItem('token');
      if (!token) {
        setStatus('❌ 請先登入');
        addDetail('未找到認證 token，請先登入', 'error');
        return;
      }

      // 1. 載入所有數據
      addDetail('載入訂閱數據...', 'info');
      const subscriptionsResponse = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const subscriptionsResult = await subscriptionsResponse.json();
      if (!subscriptionsResult.success) {
        throw new Error('載入訂閱數據失敗: ' + subscriptionsResult.error);
      }

      const subscriptions = subscriptionsResult.data;
      addDetail(`載入了 ${subscriptions.length} 個訂閱記錄`, 'success');

      addDetail('載入服務數據...', 'info');
      const servicesResponse = await fetch('/api/services/database');
      const servicesResult = await servicesResponse.json();
      if (!servicesResult.success) {
        throw new Error('載入服務數據失敗: ' + servicesResult.error);
      }

      const allServices = servicesResult.data.filter(s => s.status === 'active');
      addDetail(`載入了 ${allServices.length} 個活躍服務`, 'success');

      // 2. 分析數據
      setStatus('📊 分析數據...');
      
      const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
      const activeSubscribedServiceIds = activeSubscriptions.map(sub => sub.service_id);
      
      addDetail(`活躍訂閱: ${activeSubscriptions.length} 個`, 'info');
      addDetail(`活躍訂閱的服務 ID: [${activeSubscribedServiceIds.join(', ')}]`, 'info');

      // 計算可用服務
      const availableServices = allServices.filter(service =>
        !activeSubscribedServiceIds.includes(service.id)
      );
      
      addDetail(`可用服務: ${availableServices.length} 個`, 'info');
      addDetail(`可用服務 ID: [${availableServices.map(s => s.id).join(', ')}]`, 'info');

      // 3. 檢查數量平衡
      const totalServices = allServices.length;
      const calculatedTotal = activeSubscriptions.length + availableServices.length;
      
      addDetail(`數量檢查: ${activeSubscriptions.length} + ${availableServices.length} = ${calculatedTotal}`, 'info');
      addDetail(`總服務數: ${totalServices}`, 'info');

      if (calculatedTotal === totalServices) {
        setStatus('✅ 沒有發現重複問題');
        addDetail('數量平衡正確，沒有重複問題', 'success');
        setIsFixed(true);
      } else {
        setStatus('🔧 發現重複問題，開始修復...');
        addDetail(`發現數量不平衡: 計算總數 ${calculatedTotal} ≠ 實際總數 ${totalServices}`, 'warning');

        // 4. 檢查重複的服務 ID
        const duplicateIds = activeSubscribedServiceIds.filter(id =>
          availableServices.some(service => service.id === id)
        );

        if (duplicateIds.length > 0) {
          addDetail(`發現重複的服務 ID: [${duplicateIds.join(', ')}]`, 'error');
          
          // 修復：檢查是否有重複的訂閱記錄
          for (const serviceId of duplicateIds) {
            const serviceSubscriptions = subscriptions.filter(sub => sub.service_id === serviceId);
            addDetail(`服務 ${serviceId} 有 ${serviceSubscriptions.length} 個訂閱記錄`, 'info');
            
            if (serviceSubscriptions.length > 1) {
              addDetail(`服務 ${serviceId} 有重複訂閱記錄，需要清理`, 'warning');
              // 這裡可以添加清理邏輯
            }
          }
        }

        // 5. 強制重新計算和同步
        addDetail('強制重新計算可用服務...', 'info');
        
        // 觸發前端重新載入
        if (window.location.pathname === '/subscriptions') {
          // 如果在訂閱頁面，觸發重新載入
          window.location.reload();
        } else {
          // 否則跳轉到訂閱頁面
          setTimeout(() => {
            router.push('/subscriptions');
          }, 2000);
        }

        setStatus('✅ 修復完成，正在重新載入...');
        addDetail('修復完成，頁面將重新載入以同步數據', 'success');
        setIsFixed(true);
      }

    } catch (error) {
      setStatus('❌ 修復失敗: ' + error.message);
      addDetail('修復過程中出現錯誤: ' + error.message, 'error');
    }
  };

  const manualRefresh = () => {
    router.push('/subscriptions');
  };

  const goToDashboard = () => {
    router.push('/dashboard-simple');
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>修復訂閱重複 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-2xl w-full mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {isFixed ? (
                  <span className="text-2xl">✅</span>
                ) : (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                )}
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">🔧 修復訂閱重複</h1>
              <p className="text-gray-600">自動檢查和修復訂閱服務重複問題</p>
            </div>

            <div className="mb-8">
              <div className={`p-4 rounded-lg ${
                isFixed ? 'bg-green-50 border border-green-200' : 'bg-blue-50 border border-blue-200'
              }`}>
                <p className={`font-medium ${
                  isFixed ? 'text-green-800' : 'text-blue-800'
                }`}>
                  {status}
                </p>
              </div>
            </div>

            {/* 詳細日誌 */}
            <div className="mb-8">
              <h3 className="font-medium text-gray-900 mb-3">📋 修復日誌</h3>
              <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                {details.length > 0 ? (
                  <div className="space-y-2">
                    {details.map((detail, index) => (
                      <div key={index} className="flex items-start space-x-2 text-sm">
                        <span className="text-gray-500">[{detail.timestamp}]</span>
                        <span className={getTypeColor(detail.type)}>
                          {getTypeIcon(detail.type)}
                        </span>
                        <span className="text-gray-700">{detail.message}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-sm">修復日誌將顯示在這裡...</div>
                )}
              </div>
            </div>

            {/* 操作按鈕 */}
            <div className="space-y-3">
              <button
                onClick={manualRefresh}
                className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
              >
                📦 前往訂閱頁面
              </button>

              <button
                onClick={goToDashboard}
                className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
              >
                🏠 回到儀表板
              </button>

              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🔄 重新檢查
              </button>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">🎯 修復內容</h3>
              <div className="text-sm text-yellow-800 space-y-1">
                <div>• 檢查訂閱服務與可用服務的重複</div>
                <div>• 驗證數量平衡關係</div>
                <div>• 清理重複的訂閱記錄</div>
                <div>• 強制重新計算可用服務</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
