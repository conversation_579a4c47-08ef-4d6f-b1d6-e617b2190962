// Next.js API 路由 - 服務管理
// 處理 /api/services 的請求

import fs from 'fs';
import path from 'path';

const DATA_FILE = path.join(process.cwd(), 'data', 'services.json');

// 確保數據目錄存在
const ensureDataDirectory = () => {
  const dataDir = path.dirname(DATA_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// 讀取服務數據
const readServicesData = () => {
  try {
    ensureDataDirectory();
    if (fs.existsSync(DATA_FILE)) {
      const data = fs.readFileSync(DATA_FILE, 'utf8');
      return JSON.parse(data);
    } else {
      // 如果文件不存在，創建包含模擬數據的默認數據
      const defaultData = {
        lastUpdated: new Date().toISOString(),
        version: "1.0.0",
        services: [
          {
            id: 1,
            name: 'AI 文字生成服務',
            description: '強大的 AI 文字生成工具，支援多種語言和格式',
            price: 299,
            billingCycle: 'monthly',
            category: 'AI 服務',
            features: ['無限文字生成', '多語言支援', '模板庫', '24/7 客服'],
            status: 'active',
            popular: true,
            subscribers: 1250,
            revenue: 373750,
            maxUsers: 1,
            apiLimit: 50000,
            storageLimit: '10GB',
            supportLevel: 'premium',
            createdAt: '2025-01-15',
            updatedAt: '2025-06-20'
          },
          {
            id: 2,
            name: '圖片處理服務',
            description: '專業的圖片編輯和處理工具',
            price: 199,
            billingCycle: 'monthly',
            category: '媒體處理',
            features: ['批量處理', '格式轉換', '智能壓縮', '雲端存儲'],
            status: 'active',
            popular: false,
            subscribers: 890,
            revenue: 177110,
            maxUsers: 3,
            apiLimit: 1000,
            storageLimit: '50GB',
            supportLevel: 'standard',
            createdAt: '2025-02-01',
            updatedAt: '2025-06-18'
          },
          {
            id: 3,
            name: '數據分析服務',
            description: '深度數據分析和可視化工具',
            price: 499,
            billingCycle: 'monthly',
            category: '數據分析',
            features: ['實時分析', '自定義報表', '數據可視化', 'API 接入'],
            status: 'inactive',
            popular: false,
            subscribers: 320,
            revenue: 159680,
            maxUsers: 5,
            apiLimit: 100,
            storageLimit: '100GB',
            supportLevel: 'premium',
            createdAt: '2025-03-10',
            updatedAt: '2025-06-15'
          },
          {
            id: 4,
            name: '語音轉文字服務',
            description: '高精度語音識別和轉換服務',
            price: 149,
            billingCycle: 'monthly',
            category: 'AI 服務',
            features: ['多語言識別', '實時轉換', '批量處理', '高精度識別'],
            status: 'active',
            popular: false,
            subscribers: 560,
            revenue: 83440,
            maxUsers: 1,
            apiLimit: 10000,
            storageLimit: '5GB',
            supportLevel: 'basic',
            createdAt: '2025-04-05',
            updatedAt: '2025-06-22'
          },
          {
            id: 5,
            name: '雲端存儲服務',
            description: '安全可靠的雲端存儲解決方案',
            price: 99,
            billingCycle: 'monthly',
            category: '存儲服務',
            features: ['無限存儲', '自動備份', '版本控制', '分享功能'],
            status: 'active',
            popular: true,
            subscribers: 2100,
            revenue: 207900,
            maxUsers: 10,
            apiLimit: 0,
            storageLimit: '無限',
            supportLevel: 'standard',
            createdAt: '2025-01-20',
            updatedAt: '2025-06-25'
          },
          {
            id: 6,
            name: '企業級 API 服務',
            description: '為企業提供的高級 API 接入服務',
            price: 999,
            billingCycle: 'monthly',
            category: '企業服務',
            features: ['無限 API 調用', '專屬支援', '自定義功能', 'SLA 保證'],
            status: 'active',
            popular: false,
            subscribers: 85,
            revenue: 84915,
            maxUsers: 50,
            apiLimit: 0,
            storageLimit: '1TB',
            supportLevel: 'enterprise',
            createdAt: '2025-05-01',
            updatedAt: '2025-06-24'
          }
        ],
        metadata: {
          totalServices: 6,
          activeServices: 5,
          inactiveServices: 1,
          totalSubscribers: 5205,
          totalRevenue: 1087795,
          categories: ['AI 服務', '媒體處理', '數據分析', '存儲服務', '企業服務']
        }
      };
      writeServicesData(defaultData);
      return defaultData;
    }
  } catch (error) {
    console.error('讀取服務數據失敗:', error);
    throw new Error('無法讀取服務數據');
  }
};

// 寫入服務數據
const writeServicesData = (data) => {
  try {
    ensureDataDirectory();
    
    // 更新元數據
    const services = data.services || [];
    data.metadata = {
      totalServices: services.length,
      activeServices: services.filter(s => s.status === 'active').length,
      inactiveServices: services.filter(s => s.status === 'inactive').length,
      totalSubscribers: services.reduce((sum, s) => sum + (s.subscribers || 0), 0),
      totalRevenue: services.reduce((sum, s) => sum + (s.revenue || 0), 0),
      categories: [...new Set(services.map(s => s.category).filter(Boolean))]
    };
    
    data.lastUpdated = new Date().toISOString();
    
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2), 'utf8');
  } catch (error) {
    console.error('寫入服務數據失敗:', error);
    throw new Error('無法保存服務數據');
  }
};

// 生成新的服務 ID
const generateNewId = (services) => {
  if (services.length === 0) return 1;
  return Math.max(...services.map(s => s.id || 0)) + 1;
};

export default function handler(req, res) {
  try {
    const { method } = req;

    switch (method) {
      case 'GET':
        // 獲取所有服務
        const data = readServicesData();
        res.status(200).json({
          success: true,
          data: data.services,
          metadata: data.metadata,
          lastUpdated: data.lastUpdated
        });
        break;

      case 'POST':
        // 新增服務
        const { body } = req;
        
        // 驗證必要欄位
        if (!body.name || !body.description || !body.price) {
          return res.status(400).json({
            success: false,
            error: '缺少必要欄位：name, description, price'
          });
        }

        const currentData = readServicesData();
        const newService = {
          id: generateNewId(currentData.services),
          ...body,
          price: parseFloat(body.price),
          maxUsers: parseInt(body.maxUsers) || 1,
          apiLimit: parseInt(body.apiLimit) || 0,
          subscribers: 0,
          revenue: 0,
          createdAt: new Date().toISOString().split('T')[0],
          updatedAt: new Date().toISOString().split('T')[0]
        };

        currentData.services.push(newService);
        writeServicesData(currentData);

        res.status(201).json({
          success: true,
          data: newService,
          message: '服務新增成功'
        });
        break;

      case 'PUT':
        // 批量更新服務
        const { serviceIds, updates } = req.body;
        
        if (!serviceIds || !Array.isArray(serviceIds)) {
          return res.status(400).json({
            success: false,
            error: '無效的服務 ID 列表'
          });
        }

        const batchData = readServicesData();
        const updatedServices = [];

        batchData.services = batchData.services.map(service => {
          if (serviceIds.includes(service.id)) {
            const updated = {
              ...service,
              ...updates,
              updatedAt: new Date().toISOString().split('T')[0]
            };
            updatedServices.push(updated);
            return updated;
          }
          return service;
        });

        writeServicesData(batchData);

        res.status(200).json({
          success: true,
          data: updatedServices,
          message: `${updatedServices.length} 個服務已更新`
        });
        break;

      case 'DELETE':
        // 批量刪除服務
        const { ids } = req.body;
        
        if (!ids || !Array.isArray(ids)) {
          return res.status(400).json({
            success: false,
            error: '無效的服務 ID 列表'
          });
        }

        const deleteData = readServicesData();
        const beforeCount = deleteData.services.length;
        
        deleteData.services = deleteData.services.filter(service => !ids.includes(service.id));
        
        const deletedCount = beforeCount - deleteData.services.length;
        writeServicesData(deleteData);

        res.status(200).json({
          success: true,
          message: `${deletedCount} 個服務已刪除`
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        res.status(405).json({
          success: false,
          error: `方法 ${method} 不被允許`
        });
        break;
    }
  } catch (error) {
    console.error('API 錯誤:', error);
    res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: error.message
    });
  }
}
