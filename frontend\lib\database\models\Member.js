// 會員數據模型
const { getDatabase, dbRun, dbGet, dbAll } = require('../init');
const bcrypt = require('bcrypt');

class Member {
  constructor(data = {}) {
    this.id = data.id;
    this.email = data.email;
    this.name = data.name;
    this.password_hash = data.password_hash;
    this.role = data.role || 'user';
    this.status = data.status || 'active';
    this.avatar_url = data.avatar_url;
    this.phone = data.phone;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // 根據 email 查找會員
  static async findByEmail(email) {
    try {
      const db = await getDatabase();
      const row = await dbGet(db, 'SELECT * FROM members WHERE email = ?', [email]);
      
      if (!row) {
        return null;
      }

      return new Member(row);
    } catch (error) {
      console.error('根據 email 查找會員失敗:', error);
      throw error;
    }
  }

  // 根據 ID 查找會員
  static async findById(id) {
    try {
      const db = await getDatabase();
      const row = await dbGet(db, 'SELECT * FROM members WHERE id = ?', [id]);
      
      if (!row) {
        return null;
      }

      return new Member(row);
    } catch (error) {
      console.error('根據 ID 查找會員失敗:', error);
      throw error;
    }
  }

  // 創建新會員
  static async create(memberData) {
    try {
      const db = await getDatabase();
      
      // 檢查 email 是否已存在
      const existing = await Member.findByEmail(memberData.email);
      if (existing) {
        throw new Error('此 email 已被註冊');
      }

      // 加密密碼
      const saltRounds = 10;
      const password_hash = await bcrypt.hash(memberData.password, saltRounds);

      const sql = `
        INSERT INTO members (email, name, password_hash, role, status, phone)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      const params = [
        memberData.email,
        memberData.name,
        password_hash,
        memberData.role || 'user',
        memberData.status || 'active',
        memberData.phone || null
      ];

      const result = await dbRun(db, sql, params);
      return await Member.findById(result.id);
    } catch (error) {
      console.error('創建會員失敗:', error);
      throw error;
    }
  }

  // 驗證密碼
  async verifyPassword(password) {
    try {
      return await bcrypt.compare(password, this.password_hash);
    } catch (error) {
      console.error('密碼驗證失敗:', error);
      throw error;
    }
  }

  // 更新會員資料
  async update(updateData) {
    try {
      const db = await getDatabase();
      
      const fields = [];
      const params = [];

      // 動態構建更新欄位
      const allowedFields = ['name', 'email', 'phone', 'avatar_url', 'status'];

      allowedFields.forEach(field => {
        if (updateData.hasOwnProperty(field)) {
          fields.push(`${field} = ?`);
          params.push(updateData[field]);
        }
      });

      // 如果要更新密碼
      if (updateData.password) {
        const saltRounds = 10;
        const password_hash = await bcrypt.hash(updateData.password, saltRounds);
        fields.push('password_hash = ?');
        params.push(password_hash);
      }

      if (fields.length === 0) {
        throw new Error('沒有要更新的欄位');
      }

      params.push(this.id);

      const sql = `UPDATE members SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
      await dbRun(db, sql, params);

      // 重新獲取更新後的數據
      const updated = await Member.findById(this.id);
      Object.assign(this, updated);

      return this;
    } catch (error) {
      console.error('更新會員失敗:', error);
      throw error;
    }
  }

  // 獲取會員的訂閱服務
  async getSubscriptions() {
    try {
      const MemberService = require('./MemberService');
      return await MemberService.findByMemberId(this.id);
    } catch (error) {
      console.error('獲取會員訂閱失敗:', error);
      throw error;
    }
  }

  // 獲取會員可用的服務
  async getAvailableServices() {
    try {
      const MemberService = require('./MemberService');
      return await MemberService.getAvailableServices(this.id);
    } catch (error) {
      console.error('獲取可用服務失敗:', error);
      throw error;
    }
  }

  // 訂閱服務
  async subscribeService(serviceId, options = {}) {
    try {
      const MemberService = require('./MemberService');
      return await MemberService.subscribe(this.id, serviceId, options);
    } catch (error) {
      console.error('訂閱服務失敗:', error);
      throw error;
    }
  }

  // 獲取所有會員（管理員功能）
  static async findAll(options = {}) {
    try {
      const db = await getDatabase();
      let sql = 'SELECT * FROM members';
      const params = [];
      const conditions = [];

      // 狀態篩選
      if (options.status) {
        conditions.push('status = ?');
        params.push(options.status);
      }

      // 角色篩選
      if (options.role) {
        conditions.push('role = ?');
        params.push(options.role);
      }

      // 搜尋
      if (options.search) {
        conditions.push('(name LIKE ? OR email LIKE ?)');
        const searchTerm = `%${options.search}%`;
        params.push(searchTerm, searchTerm);
      }

      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }

      // 排序
      sql += ' ORDER BY created_at DESC';

      // 分頁
      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit);
        
        if (options.offset) {
          sql += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const rows = await dbAll(db, sql, params);
      return rows.map(row => new Member(row));
    } catch (error) {
      console.error('獲取會員列表失敗:', error);
      throw error;
    }
  }

  // 獲取會員統計
  static async getStats() {
    try {
      const db = await getDatabase();
      
      const stats = await dbGet(db, `
        SELECT 
          COUNT(*) as total_members,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_members,
          COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_members,
          COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_members,
          COUNT(CASE WHEN role = 'user' THEN 1 END) as user_members
        FROM members
      `);

      const recentMembers = await dbAll(db, `
        SELECT name, email, created_at 
        FROM members 
        ORDER BY created_at DESC 
        LIMIT 5
      `);

      return {
        ...stats,
        recent_members: recentMembers
      };
    } catch (error) {
      console.error('獲取會員統計失敗:', error);
      throw error;
    }
  }

  // 轉換為安全的 JSON 格式（不包含密碼）
  toJSON() {
    const json = { ...this };
    delete json.password_hash; // 移除密碼哈希
    return json;
  }

  // 轉換為公開資料格式
  toPublic() {
    return {
      id: this.id,
      name: this.name,
      email: this.email,
      avatar_url: this.avatar_url,
      role: this.role,
      status: this.status,
      created_at: this.created_at
    };
  }
}

module.exports = Member;
