// 錢包交易記錄 API
const jwt = require('jsonwebtoken');
const Wallet = require('../../../../lib/database/models/Wallet');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export default async function handler(req, res) {
  try {
    console.log('錢包交易記錄 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'GET') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    // 驗證認證
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('錢包交易記錄 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    const userId = decoded.userId;
    console.log('錢包交易記錄 API: 用戶 ID:', userId);

    // 解析查詢參數
    const {
      type,
      status,
      start_date,
      end_date,
      page = 1,
      limit = 20
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;

    // 構建查詢選項
    const options = {
      limit: limitNum,
      offset: offset
    };

    if (type) {
      options.type = type;
    }

    if (status) {
      options.status = status;
    }

    if (start_date) {
      options.startDate = start_date;
    }

    if (end_date) {
      options.endDate = end_date;
    }

    console.log('錢包交易記錄 API: 查詢選項', options);

    // 獲取交易記錄
    const transactions = await Wallet.findByMemberId(userId, options);

    // 獲取錢包統計
    const stats = await Wallet.getStats(userId);

    console.log('錢包交易記錄 API: 成功獲取', {
      transactionCount: transactions.length,
      currentBalance: stats.current_balance
    });

    // 格式化交易記錄
    const formattedTransactions = transactions.map(transaction => {
      const data = transaction.toJSON();
      
      // 添加顯示格式
      data.formatted_amount = `$${data.amount.toFixed(2)}`;
      data.formatted_balance_before = `$${data.balance_before.toFixed(2)}`;
      data.formatted_balance_after = `$${data.balance_after.toFixed(2)}`;
      
      // 添加交易類型顯示名稱
      const typeNames = {
        'recharge': '充值',
        'withdraw': '提現',
        'payment': '支付',
        'refund': '退款',
        'bonus': '獎勵'
      };
      data.type_name = typeNames[data.transaction_type] || data.transaction_type;
      
      // 添加狀態顯示名稱
      const statusNames = {
        'pending': '處理中',
        'completed': '已完成',
        'failed': '失敗',
        'cancelled': '已取消'
      };
      data.status_name = statusNames[data.status] || data.status;
      
      return data;
    });

    return res.status(200).json({
      success: true,
      data: {
        transactions: formattedTransactions,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: stats.total_transactions,
          has_more: transactions.length === limitNum
        },
        stats: {
          current_balance: stats.current_balance,
          formatted_balance: `$${stats.current_balance.toFixed(2)}`,
          total_transactions: stats.total_transactions,
          recharge_count: stats.recharge_count,
          withdraw_count: stats.withdraw_count,
          payment_count: stats.payment_count,
          refund_count: stats.refund_count,
          total_recharge: stats.total_recharge,
          total_withdraw: stats.total_withdraw,
          total_payment: stats.total_payment,
          total_refund: stats.total_refund,
          formatted_total_recharge: `$${stats.total_recharge.toFixed(2)}`,
          formatted_total_withdraw: `$${stats.total_withdraw.toFixed(2)}`,
          formatted_total_payment: `$${stats.total_payment.toFixed(2)}`,
          formatted_total_refund: `$${stats.total_refund.toFixed(2)}`
        }
      },
      message: '成功獲取交易記錄'
    });

  } catch (error) {
    console.error('錢包交易記錄 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
