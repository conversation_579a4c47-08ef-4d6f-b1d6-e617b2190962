const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcrypt');
const path = require('path');
const fs = require('fs');

// 連接資料庫
const dbPath = path.join(__dirname, 'database.sqlite');

if (!fs.existsSync(dbPath)) {
  console.error('資料庫文件不存在:', dbPath);
  process.exit(1);
}

const db = new sqlite3.Database(dbPath);

async function addAdminUser() {
  try {
    console.log('開始添加管理員用戶...');

    // 生成密碼哈希
    const password = 'password123';
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    console.log('密碼哈希生成完成:', passwordHash);

    // 檢查用戶是否已存在
    const checkQuery = 'SELECT id FROM members WHERE email = ?';
    
    db.get(checkQuery, ['<EMAIL>'], (err, row) => {
      if (err) {
        console.error('檢查用戶失敗:', err);
        db.close();
        return;
      }

      if (row) {
        console.log('用戶已存在，更新密碼...');
        
        const updateQuery = `
          UPDATE members 
          SET password_hash = ?, name = ?, role = ?, status = ?, updated_at = CURRENT_TIMESTAMP
          WHERE email = ?
        `;

        db.run(updateQuery, [passwordHash, '系統管理員', 'admin', 'active', '<EMAIL>'], function(updateErr) {
          if (updateErr) {
            console.error('更新用戶失敗:', updateErr);
          } else {
            console.log('✅ 用戶更新成功！');
            console.log('Email: <EMAIL>');
            console.log('Password: password123');
            console.log('Role: admin');
          }
          db.close();
        });
      } else {
        console.log('用戶不存在，創建新用戶...');
        
        const insertQuery = `
          INSERT INTO members (email, name, password_hash, role, status, phone, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `;

        db.run(insertQuery, [
          '<EMAIL>',
          '系統管理員',
          passwordHash,
          'admin',
          'active',
          '0912345678'
        ], function(insertErr) {
          if (insertErr) {
            console.error('創建用戶失敗:', insertErr);
          } else {
            console.log('✅ 用戶創建成功！');
            console.log('ID:', this.lastID);
            console.log('Email: <EMAIL>');
            console.log('Password: password123');
            console.log('Role: admin');
          }
          db.close();
        });
      }
    });

  } catch (error) {
    console.error('操作失敗:', error);
    db.close();
  }
}

// 同時檢查所有現有用戶
function listAllUsers() {
  console.log('\n📋 所有用戶列表:');
  
  const query = 'SELECT id, email, name, role, status FROM members ORDER BY id';
  
  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('查詢用戶失敗:', err);
      return;
    }

    if (rows.length === 0) {
      console.log('沒有找到任何用戶');
    } else {
      console.log('ID | Email | Name | Role | Status');
      console.log('---|-------|------|------|-------');
      rows.forEach(row => {
        console.log(`${row.id} | ${row.email} | ${row.name} | ${row.role} | ${row.status}`);
      });
    }
    
    console.log('\n開始添加/更新管理員用戶...');
    addAdminUser();
  });
}

// 開始執行
listAllUsers();
