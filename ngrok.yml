# ngrok 配置文件
version: "2"

# 認證令牌 (請替換為您的實際令牌)
authtoken: 2y8bGKAqWrV5MeFcAJ8FUIAKSq3_38gAGLA3LMM69wCkzA2JC

# 隧道配置
tunnels:
  # 前端隧道
  frontend:
    addr: 3001
    proto: http
    hostname: topyun.ngrok.app
    bind_tls: true
    inspect: true
    
  # 後端隧道 (如果需要)
  backend:
    addr: 3000
    proto: http
    bind_tls: true
    inspect: true

# 全域配置
web_addr: localhost:4040
log_level: info
log_format: logfmt
log: ngrok.log

# 區域設定 (選擇最近的區域以獲得最佳性能)
region: ap  # 亞太地區

# 控制台配置
console_ui: true
console_ui_color: true

# 更新配置
update_channel: stable
update_check: true
