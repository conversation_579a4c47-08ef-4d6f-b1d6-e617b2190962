import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function DuplicateSubscriptionTest() {
  const [subscriptions, setSubscriptions] = useState([]);
  const [allServices, setAllServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [analysis, setAnalysis] = useState(null);

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setIsLoading(true);
    
    try {
      await Promise.all([
        loadSubscriptions(),
        loadAllServices()
      ]);
    } catch (error) {
      console.error('載入數據失敗:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubscriptions = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bear<PERSON> ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        const formattedSubscriptions = result.data.map(sub => ({
          id: sub.id,
          service_id: sub.service_id,
          service_name: sub.service_name,
          status: sub.status,
          subscribed_at: sub.subscribed_at,
          member_id: sub.member_id || 'unknown'
        }));
        
        setSubscriptions(formattedSubscriptions);
        analyzeSubscriptions(formattedSubscriptions);
      }
    } catch (error) {
      console.error('載入訂閱失敗:', error);
    }
  };

  const loadAllServices = async () => {
    try {
      const response = await fetch('/api/services/database');
      const result = await response.json();

      if (result.success) {
        setAllServices(result.data);
      }
    } catch (error) {
      console.error('載入服務失敗:', error);
    }
  };

  const analyzeSubscriptions = (subs) => {
    // 按狀態分組
    const activeSubscriptions = subs.filter(sub => sub.status === 'active');
    const cancelledSubscriptions = subs.filter(sub => sub.status === 'cancelled');
    const expiredSubscriptions = subs.filter(sub => sub.status === 'expired');

    // 檢查服務 ID 重複
    const activeServiceIds = activeSubscriptions.map(sub => sub.service_id);
    const cancelledServiceIds = cancelledSubscriptions.map(sub => sub.service_id);
    const expiredServiceIds = expiredSubscriptions.map(sub => sub.service_id);

    // 找出重複的服務 ID
    const activeVsCancelled = activeServiceIds.filter(id => cancelledServiceIds.includes(id));
    const activeVsExpired = activeServiceIds.filter(id => expiredServiceIds.includes(id));
    const cancelledVsExpired = cancelledServiceIds.filter(id => expiredServiceIds.includes(id));

    // 檢查同一服務的多個訂閱記錄
    const serviceSubscriptionMap = {};
    subs.forEach(sub => {
      if (!serviceSubscriptionMap[sub.service_id]) {
        serviceSubscriptionMap[sub.service_id] = [];
      }
      serviceSubscriptionMap[sub.service_id].push(sub);
    });

    const duplicateServices = Object.entries(serviceSubscriptionMap)
      .filter(([serviceId, subscriptions]) => subscriptions.length > 1)
      .map(([serviceId, subscriptions]) => ({
        serviceId: parseInt(serviceId),
        serviceName: subscriptions[0].service_name,
        subscriptions: subscriptions.sort((a, b) => new Date(a.subscribed_at) - new Date(b.subscribed_at))
      }));

    // 檢查同一服務的活躍訂閱
    const multipleActiveServices = Object.entries(serviceSubscriptionMap)
      .filter(([serviceId, subscriptions]) => 
        subscriptions.filter(sub => sub.status === 'active').length > 1
      )
      .map(([serviceId, subscriptions]) => ({
        serviceId: parseInt(serviceId),
        serviceName: subscriptions[0].service_name,
        activeSubscriptions: subscriptions.filter(sub => sub.status === 'active')
      }));

    setAnalysis({
      totalSubscriptions: subs.length,
      activeCount: activeSubscriptions.length,
      cancelledCount: cancelledSubscriptions.length,
      expiredCount: expiredSubscriptions.length,

      activeServiceIds,
      cancelledServiceIds,
      expiredServiceIds,

      overlaps: {
        activeVsCancelled,
        activeVsExpired,
        cancelledVsExpired
      },

      duplicateServices,
      multipleActiveServices,

      // 詳細的重複分析
      duplicateAnalysis: duplicateServices.map(item => ({
        ...item,
        hasActive: item.subscriptions.some(sub => sub.status === 'active'),
        hasCancelled: item.subscriptions.some(sub => sub.status === 'cancelled'),
        hasExpired: item.subscriptions.some(sub => sub.status === 'expired'),
        statusCombination: [...new Set(item.subscriptions.map(sub => sub.status))].join(' + ')
      }))
    });
  };

  const cleanupDuplicates = async () => {
    if (!analysis || analysis.multipleActiveServices.length === 0) {
      alert('沒有發現需要清理的重複活躍訂閱');
      return;
    }

    const confirmed = confirm(`發現 ${analysis.multipleActiveServices.length} 個服務有多個活躍訂閱，是否要清理？\n\n將保留最新的訂閱，取消較舊的訂閱。`);
    
    if (!confirmed) return;

    try {
      const token = localStorage.getItem('token');
      
      for (const service of analysis.multipleActiveServices) {
        // 保留最新的，取消其他的
        const sortedSubscriptions = service.activeSubscriptions.sort(
          (a, b) => new Date(b.subscribed_at) - new Date(a.subscribed_at)
        );
        
        const toKeep = sortedSubscriptions[0];
        const toCancel = sortedSubscriptions.slice(1);
        
        console.log(`服務 ${service.serviceName}: 保留訂閱 ${toKeep.id}, 取消訂閱 [${toCancel.map(s => s.id).join(', ')}]`);
        
        for (const subscription of toCancel) {
          const response = await fetch('/api/subscriptions/cancel', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subscription_id: subscription.id
            })
          });

          const result = await response.json();
          if (!result.success) {
            console.error(`取消訂閱 ${subscription.id} 失敗:`, result.error);
          }
        }
      }
      
      alert('重複訂閱清理完成！');
      loadAllData(); // 重新載入數據
      
    } catch (error) {
      console.error('清理失敗:', error);
      alert('清理失敗: ' + error.message);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>重複訂閱檢測 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔍 重複訂閱檢測</h1>
              <p className="text-gray-600 mt-2">檢測和分析重複的訂閱記錄</p>
            </div>

            {analysis && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 基本統計 */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 基本統計</h2>
                  
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h3 className="font-medium text-blue-900 mb-2">📦 訂閱統計</h3>
                      <div className="text-sm text-blue-800 space-y-1">
                        <div>總訂閱記錄: {analysis.totalSubscriptions}</div>
                        <div>使用中: {analysis.activeCount}</div>
                        <div>已取消: {analysis.cancelledCount}</div>
                        <div>已過期: {analysis.expiredCount}</div>
                      </div>
                    </div>

                    {/* 重複檢測結果 */}
                    <div className={`p-4 border rounded-lg ${
                      analysis.overlaps.activeVsCancelled.length > 0 || 
                      analysis.overlaps.activeVsExpired.length > 0
                        ? 'bg-red-50 border-red-200' 
                        : 'bg-green-50 border-green-200'
                    }`}>
                      <h3 className={`font-medium mb-2 ${
                        analysis.overlaps.activeVsCancelled.length > 0 || 
                        analysis.overlaps.activeVsExpired.length > 0
                          ? 'text-red-900' 
                          : 'text-green-900'
                      }`}>
                        {analysis.overlaps.activeVsCancelled.length > 0 || 
                         analysis.overlaps.activeVsExpired.length > 0 ? '❌' : '✅'} 重複檢測
                      </h3>
                      <div className={`text-sm space-y-1 ${
                        analysis.overlaps.activeVsCancelled.length > 0 || 
                        analysis.overlaps.activeVsExpired.length > 0
                          ? 'text-red-800' 
                          : 'text-green-800'
                      }`}>
                        <div>使用中 vs 已取消: {analysis.overlaps.activeVsCancelled.length > 0 ? `❌ 重複 [${analysis.overlaps.activeVsCancelled.join(', ')}]` : '✅ 無重複'}</div>
                        <div>使用中 vs 已過期: {analysis.overlaps.activeVsExpired.length > 0 ? `❌ 重複 [${analysis.overlaps.activeVsExpired.join(', ')}]` : '✅ 無重複'}</div>
                        <div>已取消 vs 已過期: {analysis.overlaps.cancelledVsExpired.length > 0 ? `⚠️ 重複 [${analysis.overlaps.cancelledVsExpired.join(', ')}]` : '✅ 無重複'}</div>
                      </div>
                    </div>

                    {/* 多個活躍訂閱警告 */}
                    {analysis.multipleActiveServices.length > 0 && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <h3 className="font-medium text-red-900 mb-2">🚨 嚴重問題：多個活躍訂閱</h3>
                        <div className="text-sm text-red-800 space-y-1">
                          <div>發現 {analysis.multipleActiveServices.length} 個服務有多個活躍訂閱</div>
                          {analysis.multipleActiveServices.map(service => (
                            <div key={service.serviceId} className="ml-2">
                              • 服務 {service.serviceId} ({service.serviceName}): {service.activeSubscriptions.length} 個活躍訂閱
                            </div>
                          ))}
                        </div>
                        <button
                          onClick={cleanupDuplicates}
                          className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                        >
                          🧹 清理重複訂閱
                        </button>
                      </div>
                    )}

                    <button
                      onClick={loadAllData}
                      disabled={isLoading}
                      className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                    >
                      🔄 重新檢測
                    </button>
                  </div>
                </div>

                {/* 詳細分析 */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">🔍 詳細分析</h2>
                  
                  <div className="space-y-4">
                    {/* 重複服務列表 */}
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg max-h-96 overflow-y-auto">
                      <h3 className="font-medium text-yellow-900 mb-2">📋 重複服務詳情</h3>
                      {analysis.duplicateServices.length > 0 ? (
                        <div className="text-sm text-yellow-800 space-y-3">
                          {analysis.duplicateAnalysis.map(item => (
                            <div key={item.serviceId} className="border-l-2 border-yellow-400 pl-3">
                              <div className="font-medium">服務 {item.serviceId}: {item.serviceName}</div>
                              <div className="text-xs mt-1">狀態組合: {item.statusCombination}</div>
                              <div className="mt-2 space-y-1">
                                {item.subscriptions.map(sub => (
                                  <div key={sub.id} className={`text-xs p-1 rounded ${
                                    sub.status === 'active' ? 'bg-green-100' :
                                    sub.status === 'cancelled' ? 'bg-gray-100' :
                                    'bg-red-100'
                                  }`}>
                                    訂閱 {sub.id}: {sub.status} ({new Date(sub.subscribed_at).toLocaleDateString()})
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-sm text-yellow-800">✅ 沒有發現重複的服務訂閱</div>
                      )}
                    </div>

                    {/* 服務 ID 分布 */}
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                      <h3 className="font-medium text-gray-900 mb-2">📊 服務 ID 分布</h3>
                      <div className="text-xs text-gray-700 space-y-2">
                        <div>
                          <strong>使用中訂閱的服務 ID:</strong>
                          <div className="font-mono bg-white p-2 rounded border mt-1">
                            [{analysis.activeServiceIds.join(', ')}]
                          </div>
                        </div>
                        
                        <div>
                          <strong>已取消訂閱的服務 ID:</strong>
                          <div className="font-mono bg-white p-2 rounded border mt-1">
                            [{analysis.cancelledServiceIds.join(', ')}]
                          </div>
                        </div>
                        
                        <div>
                          <strong>已過期訂閱的服務 ID:</strong>
                          <div className="font-mono bg-white p-2 rounded border mt-1">
                            [{analysis.expiredServiceIds.join(', ')}]
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 所有訂閱記錄 */}
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg max-h-64 overflow-y-auto">
                      <h3 className="font-medium text-gray-900 mb-2">📦 所有訂閱記錄</h3>
                      <div className="text-xs text-gray-600 space-y-1">
                        {subscriptions.map(sub => (
                          <div key={sub.id} className="flex justify-between items-center p-1 bg-white rounded border">
                            <span>訂閱 {sub.id}: 服務 {sub.service_id} ({sub.service_name})</span>
                            <span className={`px-2 py-0.5 rounded text-xs ${
                              sub.status === 'active' ? 'bg-green-100 text-green-800' :
                              sub.status === 'cancelled' ? 'bg-gray-100 text-gray-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {sub.status}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📚 問題說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>發現的問題：</strong> 同一個服務可能有多個訂閱記錄</p>
                <p><strong>可能原因：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>用戶重複訂閱同一服務</li>
                  <li>取消訂閱後又重新訂閱，產生新記錄</li>
                  <li>系統邏輯允許同一服務多次訂閱</li>
                </ul>
                <p><strong>解決方案：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>清理重複的活躍訂閱（保留最新的）</li>
                  <li>修改訂閱邏輯，防止重複訂閱</li>
                  <li>重新訂閱時重用已取消的記錄</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
