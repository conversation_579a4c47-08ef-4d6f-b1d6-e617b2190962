// 測試環境變數配置
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';

// 資料庫配置
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_NAME = 'member_system_test';
process.env.DB_USER = 'postgres';
process.env.DB_PASSWORD = 'password';

// JWT 配置
process.env.JWT_SECRET = 'test-jwt-secret-key';
process.env.JWT_REFRESH_SECRET = 'test-jwt-refresh-secret';
process.env.JWT_EXPIRES_IN = '15m';
process.env.JWT_REFRESH_EXPIRES_IN = '7d';

// 加密配置
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters';
process.env.ENCRYPTION_SALT = 'test-encryption-salt';
process.env.HMAC_SECRET = 'test-hmac-secret';

// 支付配置 (測試模式)
process.env.STRIPE_SECRET_KEY = 'sk_test_test_key';
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test_secret';
process.env.PAYPAL_CLIENT_ID = 'test_paypal_client_id';
process.env.PAYPAL_CLIENT_SECRET = 'test_paypal_client_secret';
process.env.PAYPAL_MODE = 'sandbox';

// ECPay 測試配置
process.env.ECPAY_MERCHANT_ID = '2000132';
process.env.ECPAY_HASH_KEY = '5294y06JbISpM5x9';
process.env.ECPAY_HASH_IV = 'v77hoKGq4kWxNNIS';
process.env.ECPAY_MODE = 'test';

// 安全配置
process.env.MAX_LOGIN_ATTEMPTS = '5';
process.env.ACCOUNT_LOCKOUT_DURATION = '15';
process.env.RATE_LIMIT_WINDOW_MS = '900000';
process.env.RATE_LIMIT_MAX_REQUESTS = '1000';

// 郵件配置 (測試模式)
process.env.SMTP_HOST = 'localhost';
process.env.SMTP_PORT = '1025';
process.env.SMTP_USER = 'test';
process.env.SMTP_PASS = 'test';
process.env.FROM_EMAIL = '<EMAIL>';

// Redis 配置
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.REDIS_PASSWORD = '';

// 日誌配置
process.env.LOG_LEVEL = 'error';
process.env.LOG_FILE = 'false';
