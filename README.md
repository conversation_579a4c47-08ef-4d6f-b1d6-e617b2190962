# 會員管理系統 (Member Management System)

一個功能完整的會員管理系統，包含註冊、充值、訂閱服務等功能，並符合 PCI DSS 和 GDPR 合規要求。

## 🚀 功能特色

### 核心功能
- 👤 **會員註冊與管理** - 完整的用戶註冊、登入、個人資料管理
- 💰 **多元支付系統** - 支援 Stripe、PayPal、ECPay 等支付方式
- 🏦 **錢包系統** - 多幣別錢包管理和交易記錄
- 📱 **服務訂閱** - 靈活的訂閱方案和使用量追蹤
- 🔐 **安全認證** - JWT Token、雙因素認證、社群登入

### 安全與合規
- 🛡️ **PCI DSS 合規** - 信用卡資料安全處理
- 🔒 **GDPR 合規** - 完整的資料保護和用戶權利實現
- 🔐 **資料加密** - AES-256-GCM 端到端加密
- 📊 **安全審計** - 完整的活動日誌和威脅檢測

### 技術架構
- 🏗️ **微服務架構** - 模組化設計，易於擴展
- 🐳 **Docker 容器化** - 完整的容器化部署方案
- 🧪 **完整測試** - 單元測試、整合測試、E2E 測試
- 🔄 **CI/CD 流程** - 自動化測試和部署

## 📋 技術棧

### 後端
- **Node.js** + **Express.js** - 服務器框架
- **SQLite** (開發) / **PostgreSQL** (生產) - 資料庫
- **Redis** - 快取和會話管理
- **JWT** - 身份認證
- **bcrypt** - 密碼加密

### 前端
- **React** + **Next.js** - 前端框架
- **Tailwind CSS** - 樣式框架
- **TypeScript** - 類型安全

### 支付整合
- **Stripe** - 國際信用卡支付
- **PayPal** - 國際電子錢包
- **ECPay** - 台灣本地支付

### 開發工具
- **Jest** - 測試框架
- **Docker** - 容器化
- **GitHub Actions** - CI/CD
- **ESLint** + **Prettier** - 代碼規範

## ⚙️ 環境變數設置

### 快速設置

```bash
# 1. 自動生成環境變數檔案（包含安全密鑰）
npm run setup:env

# 2. 檢查環境變數配置
npm run check:env
```

### 手動設置

```bash
# 複製範例檔案
cp .env.example backend/.env
cp .env.example frontend/.env.local

# 編輯配置檔案
# 修改 backend/.env 和 frontend/.env.local
```

### 必要配置項目

#### 後端 (backend/.env)
```env
NODE_ENV=development
PORT=3000
JWT_SECRET=your-secure-jwt-secret
DB_PATH=../frontend/lib/database/database.sqlite
```

#### 前端 (frontend/.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_APP_NAME=會員管理系統
```

詳細設置說明請參考：[環境變數設置指南](docs/ENVIRONMENT_SETUP.md)

## 🛠️ 安裝與運行

### 前置要求
- Node.js 18+ 
- PostgreSQL 15+
- Redis 7+
- Docker (可選)

### 本地開發

1. **克隆專案**
```bash
git clone <repository-url>
cd member-management-system
```

2. **安裝依賴**
```bash
# 後端
cd backend
npm install

# 前端
cd ../frontend
npm install
```

3. **環境配置**
```bash
# 複製環境變數範例
cp backend/.env.example backend/.env
cp backend/.env.security.example backend/.env.security

# 編輯環境變數
nano backend/.env
```

4. **資料庫設置**
```bash
cd backend
npm run migrate
npm run seed
```

5. **啟動服務**

#### 本地開發
```bash
# 後端 (Port 3000)
cd backend
npm run dev

# 前端 (Port 3001)
cd frontend
npm run dev
```

#### 使用 ngrok 域名 (https://topyun.ngrok.app)
```bash
# 後端 (Port 3000)
cd backend
npm run dev

# 前端 (允許外部訪問)
cd frontend
npm run dev:ngrok

# 啟動 ngrok 隧道
npm run ngrok
```

**訪問地址**:
- 本地開發: http://localhost:3001
- ngrok 域名: https://topyun.ngrok.app
- 後端 API: http://localhost:3000
- ngrok 控制台: http://localhost:4040

詳細 ngrok 配置請參考：[ngrok 配置指南](docs/NGROK_SETUP.md)

### Docker 部署

```bash
# 啟動所有服務
docker-compose up -d

# 查看日誌
docker-compose logs -f

# 停止服務
docker-compose down
```

## 🧪 測試

### 運行測試
```bash
cd backend

# 單元測試
npm run test:unit

# 整合測試
npm run test:integration

# E2E 測試
npm run test:e2e

# 覆蓋率測試
npm run test:coverage

# 所有測試
npm test
```

### 測試覆蓋率
- 目標覆蓋率: 70%+
- 包含單元測試、整合測試、E2E 測試
- 自動生成覆蓋率報告

## 🚀 部署

### CI/CD 流程
1. 推送代碼到 GitHub
2. 自動觸發 GitHub Actions
3. 運行所有測試
4. 安全審計檢查
5. 自動部署到 Staging/Production

### 生產環境部署
```bash
# 構建生產映像
docker build -t member-system:latest .

# 運行生產容器
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 API 文檔

API 文檔可在以下位置查看：
- 開發環境: http://localhost:3000/api-docs
- Swagger UI 完整文檔

### 主要 API 端點

#### 認證
- `POST /api/auth/register` - 用戶註冊
- `POST /api/auth/login` - 用戶登入
- `POST /api/auth/refresh` - 刷新 Token
- `POST /api/auth/logout` - 用戶登出

#### 用戶管理
- `GET /api/users/profile` - 獲取個人資料
- `PUT /api/users/profile` - 更新個人資料
- `POST /api/users/change-password` - 修改密碼

#### 錢包系統
- `GET /api/wallets` - 獲取錢包列表
- `GET /api/wallets/transactions` - 交易記錄
- `POST /api/wallets/transfer` - 錢包轉帳

#### 支付系統
- `POST /api/payments/stripe/create-intent` - 創建 Stripe 支付
- `POST /api/payments/paypal/create-order` - 創建 PayPal 訂單
- `POST /api/payments/ecpay/create` - 創建 ECPay 支付

#### 訂閱服務
- `GET /api/services` - 獲取服務列表
- `POST /api/subscriptions` - 創建訂閱
- `GET /api/subscriptions` - 獲取訂閱列表

## 🔒 安全性

### 資料保護
- AES-256-GCM 加密
- bcrypt 密碼雜湊
- 敏感資料遮罩
- 安全的 Token 生成

### 合規標準
- **PCI DSS Level 1** - 信用卡資料安全
- **GDPR** - 歐盟資料保護法規
- **ISO 27001** - 資訊安全管理

### 安全功能
- 雙因素認證 (2FA)
- IP 白名單/黑名單
- 速率限制
- 安全審計日誌

## 📊 監控

### 系統監控
- Prometheus 指標收集
- Grafana 視覺化儀表板
- 健康檢查端點
- 錯誤追蹤

### 日誌管理
- 結構化日誌
- 安全事件記錄
- 效能監控
- 錯誤報告

## 📚 完整文檔

### 📖 詳細文檔
- **[功能說明文檔](docs/FEATURES.md)** - 詳細的功能介紹和使用說明
- **[API 文檔](docs/API.md)** - 完整的 API 接口說明
- **[部署指南](docs/DEPLOYMENT.md)** - 詳細的部署和配置說明
- **[更新日誌](CHANGELOG.md)** - 版本更新記錄

### 🎯 快速鏈接
- **[極簡儀表板功能](docs/FEATURES.md#-極簡儀表板)** - 無多餘圖標的純淨設計
- **[錢包管理系統](docs/FEATURES.md#-錢包管理系統)** - 充值、提現、轉帳功能
- **[服務訂閱系統](docs/FEATURES.md#-服務訂閱系統)** - 完整的訂閱管理
- **[認證系統](docs/FEATURES.md#-認證系統)** - 安全的用戶認證

### 🚀 最新功能 (v1.0.0)
- ✅ **極簡設計** - 移除所有多餘圖標，純淨界面
- ✅ **錢包管理** - 完整的充值、提現、轉帳功能
- ✅ **服務訂閱** - 6種服務類型，使用量追蹤
- ✅ **認證系統** - JWT + Cookie 雙重認證
- ✅ **響應式設計** - 完美支援各種設備

## 🤝 貢獻

1. Fork 專案
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交變更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

## 📄 授權

此專案採用 MIT 授權 - 查看 [LICENSE](LICENSE) 文件了解詳情。

## 📞 聯絡

- 專案維護者: Member System Team
- Email: <EMAIL>
- 專案連結: [GitHub Repository](https://github.com/your-username/member-management-system)

## 🙏 致謝

感謝所有貢獻者和開源社群的支持！
