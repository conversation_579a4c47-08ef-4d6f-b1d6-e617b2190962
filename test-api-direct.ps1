# 直接測試 API
Write-Host "🔐 測試認證流程..." -ForegroundColor Green

# 1. 登入
Write-Host "1️⃣ 登入..." -ForegroundColor Yellow
$loginBody = @{
    email = "<EMAIL>"
    password = "1234"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/v1/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "✅ 登入成功" -ForegroundColor Green
    Write-Host "Token: $($loginResponse.data.token.Substring(0, 20))..." -ForegroundColor Cyan
    
    $token = $loginResponse.data.token
    
    # 2. 測試訂閱 API
    Write-Host "`n2️⃣ 測試訂閱 API..." -ForegroundColor Yellow
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    $subscriptionResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/subscriptions/simple" -Method GET -Headers $headers
    Write-Host "✅ 訂閱 API 成功" -ForegroundColor Green
    Write-Host "響應: $($subscriptionResponse | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ 錯誤: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "響應內容: $responseBody" -ForegroundColor Red
    }
}
