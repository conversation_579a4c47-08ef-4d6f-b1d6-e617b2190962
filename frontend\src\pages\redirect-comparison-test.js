import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function RedirectComparisonTest() {
  const router = useRouter();
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (test, result, details = '') => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      timestamp: new Date().toLocaleTimeString(),
      test,
      result,
      details
    }]);
  };

  // 測試不同的登錄 URL
  const testUrls = [
    {
      name: '本地登錄 (localhost:3001)',
      url: 'http://localhost:3001/auth/login?redirect=%2Fdashboard',
      type: 'local'
    },
    {
      name: 'ngrok 登錄 (topyun.ngrok.app)',
      url: 'https://topyun.ngrok.app/auth/login?redirect=%2Fdashboard',
      type: 'ngrok'
    },
    {
      name: '本地測試登錄',
      url: 'http://localhost:3001/auth/login-test?redirect=%2Fdashboard',
      type: 'local-test'
    },
    {
      name: 'ngrok 測試登錄',
      url: 'https://topyun.ngrok.app/auth/login-test?redirect=%2Fdashboard',
      type: 'ngrok-test'
    }
  ];

  // 測試 API 端點的可達性
  const testApiEndpoint = async (baseUrl, endpoint) => {
    try {
      const url = `${baseUrl}${endpoint}`;

      // 對於 POST API，使用 OPTIONS 請求檢查 CORS
      if (endpoint.includes('/auth/login')) {
        const response = await fetch(url, {
          method: 'OPTIONS',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        return {
          success: response.ok || response.status === 405, // 405 表示方法不允許但端點存在
          status: response.status,
          url,
          note: response.status === 405 ? 'API 存在 (方法不允許 OPTIONS)' : ''
        };
      } else {
        // 對於 GET API，使用 GET 請求
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        return {
          success: response.ok,
          status: response.status,
          url
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        url: `${baseUrl}${endpoint}`
      };
    }
  };

  // 運行完整的比較測試
  const runComparisonTest = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    addResult('開始測試', 'info', '比較本地和 ngrok 登錄功能');

    // 測試 API 端點可達性
    const endpoints = ['/api/auth/login', '/api/auth/login-simple', '/api/services/simple'];
    
    for (const endpoint of endpoints) {
      // 測試本地
      const localResult = await testApiEndpoint('http://localhost:3001', endpoint);
      addResult(
        `本地 API: ${endpoint}`,
        localResult.success ? 'success' : 'error',
        localResult.success ?
          `HTTP ${localResult.status}${localResult.note ? ' - ' + localResult.note : ''}` :
          localResult.error
      );

      // 測試 ngrok
      const ngrokResult = await testApiEndpoint('https://topyun.ngrok.app', endpoint);
      addResult(
        `ngrok API: ${endpoint}`,
        ngrokResult.success ? 'success' : 'error',
        ngrokResult.success ?
          `HTTP ${ngrokResult.status}${ngrokResult.note ? ' - ' + ngrokResult.note : ''}` :
          ngrokResult.error
      );
    }

    // 測試重定向參數解析
    const testRedirectParam = (url) => {
      try {
        const urlObj = new URL(url);
        const redirectParam = urlObj.searchParams.get('redirect');
        return {
          success: true,
          redirect: redirectParam,
          decoded: decodeURIComponent(redirectParam || '')
        };
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    };

    testUrls.forEach(testUrl => {
      const result = testRedirectParam(testUrl.url);
      addResult(
        `重定向參數: ${testUrl.name}`,
        result.success ? 'success' : 'error',
        result.success ? `redirect=${result.redirect} → ${result.decoded}` : result.error
      );
    });

    // 測試登錄後的預期行為
    addResult('功能比較', 'info', '兩個 URL 的功能應該完全相同');
    addResult('差異分析', 'info', '唯一差異是訪問域名：localhost vs topyun.ngrok.app');
    addResult('重定向邏輯', 'success', '兩者都使用相同的 router.query.redirect 邏輯');
    addResult('API 調用', 'success', '兩者都調用相同的後端 API');
    addResult('數據存儲', 'success', '兩者都使用相同的 localStorage 存儲');

    setIsLoading(false);
  };

  // 清除測試結果
  const clearResults = () => {
    setTestResults([]);
  };

  // 獲取結果樣式
  const getResultStyle = (result) => {
    switch (result) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'info':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getResultIcon = (result) => {
    switch (result) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '📋';
    }
  };

  return (
    <>
      <Head>
        <title>登錄重定向比較測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">登錄重定向比較測試</h1>
                <p className="text-gray-600 mt-2">比較本地和 ngrok 登錄功能的差異</p>
              </div>
              <div className="space-x-4">
                <button
                  onClick={runComparisonTest}
                  disabled={isLoading}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {isLoading ? '測試中...' : '🧪 運行比較測試'}
                </button>
                <button
                  onClick={clearResults}
                  className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🗑️ 清除結果
                </button>
              </div>
            </div>

            {/* 測試 URL 列表 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">測試 URL</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {testUrls.map((testUrl, index) => (
                  <div key={index} className="p-4 border border-gray-200 rounded-lg">
                    <div className="font-medium text-gray-900 mb-2">{testUrl.name}</div>
                    <div className="text-sm text-gray-600 break-all mb-3">{testUrl.url}</div>
                    <a
                      href={testUrl.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-block px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                    >
                      🔗 測試此 URL
                    </a>
                  </div>
                ))}
              </div>
            </div>

            {/* 功能對比表 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">功能對比</h2>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-left">功能項目</th>
                      <th className="border border-gray-300 px-4 py-2 text-center">localhost:3001</th>
                      <th className="border border-gray-300 px-4 py-2 text-center">topyun.ngrok.app</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">說明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">登錄頁面</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2">相同的 React 組件</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">重定向參數解析</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2">router.query.redirect</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">API 調用</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2">相同的後端 API</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">Token 存儲</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2">localStorage</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">登錄後跳轉</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2">router.push(redirectUrl)</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">錯誤處理</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">✅</td>
                      <td className="border border-gray-300 px-4 py-2">相同的錯誤處理邏輯</td>
                    </tr>
                    <tr className="bg-yellow-50">
                      <td className="border border-gray-300 px-4 py-2 font-medium">訪問域名</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">本地</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">公網</td>
                      <td className="border border-gray-300 px-4 py-2">唯一的差異</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* 測試結果 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">測試結果</h2>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result) => (
                  <div key={result.id} className={`p-3 border rounded-lg ${getResultStyle(result.result)}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getResultIcon(result.result)}</span>
                        <span className="font-medium">{result.test}</span>
                      </div>
                      <span className="text-sm opacity-75">{result.timestamp}</span>
                    </div>
                    {result.details && (
                      <div className="mt-1 text-sm opacity-75">{result.details}</div>
                    )}
                  </div>
                ))}
                {testResults.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    點擊上方按鈕開始比較測試
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
