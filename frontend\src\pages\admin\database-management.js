import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function DatabaseManagement() {
  const router = useRouter();
  const [selectedTable, setSelectedTable] = useState('members');
  const [tableData, setTableData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const tables = [
    { name: 'members', label: '👥 會員表', description: '所有註冊會員的基本信息' },
    { name: 'services', label: '🛍️ 服務表', description: '系統提供的所有服務項目' },
    { name: 'member_services', label: '📦 訂閱關聯表', description: '會員與服務的訂閱關係' }
  ];

  useEffect(() => {
    // 檢查管理員權限
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (!user.id || user.role !== 'admin') {
      router.push('/auth/login?redirect=/admin/database-management');
      return;
    }

    // 載入預設表格
    loadTableData(selectedTable);
  }, [router]);

  const loadTableData = async (tableName) => {
    setIsLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('請先登入');
        return;
      }

      console.log('載入表格數據:', tableName);

      const response = await fetch(`/api/admin/database-tables?table=${tableName}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      console.log('表格數據 API 響應:', result);

      if (!response.ok || !result.success) {
        throw new Error(result.error || `載入失敗 (HTTP ${response.status})`);
      }

      setTableData(result.data);
      console.log('表格數據載入成功:', result.data.count, '筆記錄');

    } catch (err) {
      console.error('載入表格數據失敗:', err);
      setError(err.message || '載入表格數據失敗');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTableChange = (tableName) => {
    setSelectedTable(tableName);
    loadTableData(tableName);
  };

  const formatValue = (value, columnName) => {
    if (value === null || value === undefined) return '-';
    
    // 布林值格式化
    if (typeof value === 'boolean' || (typeof value === 'number' && (value === 0 || value === 1) && columnName.includes('verified'))) {
      return value ? '✅' : '❌';
    }
    
    // 日期格式化
    if (columnName.includes('_at') || columnName.includes('date')) {
      if (value) {
        try {
          return new Date(value).toLocaleString('zh-TW');
        } catch {
          return value;
        }
      }
      return '從未';
    }
    
    // 價格格式化
    if (columnName === 'price') {
      return `$${parseFloat(value).toFixed(2)}`;
    }
    
    return value;
  };

  const getColumnDisplayName = (columnName) => {
    const columnMap = {
      // 通用欄位
      'id': 'ID',
      'created_at': '創建時間',
      'updated_at': '更新時間',
      
      // 會員表
      'name': '姓名',
      'email': 'Email',
      'password_hash': '密碼哈希',
      'role': '角色',
      'status': '狀態',
      'phone': '電話',
      'email_verified': 'Email驗證',
      'last_login_at': '最後登入',
      
      // 服務表
      'description': '描述',
      'price': '價格',
      'billing_cycle': '計費週期',
      'category': '分類',
      'features': '功能特色',
      
      // 訂閱關聯表
      'member_id': '會員ID',
      'service_id': '服務ID',
      'subscribed_at': '訂閱時間',
      'next_billing_date': '下次計費日期',
      'auto_renew': '自動續費',
      'member_name': '會員姓名',
      'member_email': '會員Email',
      'service_name': '服務名稱',
      'service_price': '服務價格',
      'service_billing_cycle': '服務計費週期'
    };
    
    return columnMap[columnName] || columnName;
  };

  if (isLoading && !tableData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入資料庫管理界面中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>資料庫管理 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🗄️ 資料庫管理</h1>
                <p className="text-gray-600 mt-2">查看和管理資料庫表格內容</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
                >
                  <span>🏠</span>
                  <span>回儀表板</span>
                </button>
                <button
                  onClick={() => loadTableData(selectedTable)}
                  disabled={isLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {isLoading ? '載入中...' : '🔄 重新載入'}
                </button>
              </div>
            </div>

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600">❌ {error}</p>
              </div>
            )}

            {/* 表格選擇 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">選擇資料表</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {tables.map((table) => (
                  <button
                    key={table.name}
                    onClick={() => handleTableChange(table.name)}
                    className={`p-4 rounded-lg border-2 transition-colors text-left ${
                      selectedTable === table.name
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900 mb-1">{table.label}</div>
                    <div className="text-sm text-gray-600">{table.description}</div>
                    {tableData && selectedTable === table.name && (
                      <div className="text-sm text-blue-600 mt-2">
                        📊 {tableData.count} 筆記錄
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* 表格數據 */}
            {tableData && (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">
                    {tables.find(t => t.name === selectedTable)?.label} 數據
                  </h2>
                  <div className="text-sm text-gray-500">
                    共 {tableData.count} 筆記錄
                    {tableData.enriched && <span className="ml-2 text-green-600">✨ 已關聯</span>}
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300 bg-white rounded-lg overflow-hidden shadow">
                    <thead>
                      <tr className="bg-gray-100">
                        {tableData.columns.map((column) => (
                          <th key={column.name} className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                            {getColumnDisplayName(column.name)}
                          </th>
                        ))}
                        {/* 如果是關聯表，顯示額外的關聯欄位 */}
                        {tableData.enriched && selectedTable === 'member_services' && (
                          <>
                            <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">會員姓名</th>
                            <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">會員Email</th>
                            <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">服務名稱</th>
                            <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">服務價格</th>
                          </>
                        )}
                      </tr>
                    </thead>
                    <tbody>
                      {tableData.rows.map((row, index) => (
                        <tr key={row.id || index} className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                          {tableData.columns.map((column) => (
                            <td key={column.name} className="border border-gray-300 px-4 py-3">
                              {column.name === 'password_hash' ? (
                                <span className="text-gray-400 font-mono text-xs">
                                  {row[column.name] ? '••••••••••••' : '-'}
                                </span>
                              ) : column.name === 'features' ? (
                                <div className="max-w-xs">
                                  {row[column.name] ? (
                                    <details className="cursor-pointer">
                                      <summary className="text-blue-600 hover:text-blue-800">查看功能</summary>
                                      <div className="mt-1 text-xs text-gray-600">
                                        {typeof row[column.name] === 'string' ? row[column.name] : JSON.stringify(row[column.name])}
                                      </div>
                                    </details>
                                  ) : '-'}
                                </div>
                              ) : (
                                <span className={
                                  column.name === 'email' ? 'font-mono text-sm' :
                                  column.name === 'role' && row[column.name] === 'admin' ? 'text-purple-600 font-medium' :
                                  column.name === 'status' && row[column.name] === 'active' ? 'text-green-600' :
                                  ''
                                }>
                                  {formatValue(row[column.name], column.name)}
                                </span>
                              )}
                            </td>
                          ))}
                          {/* 關聯數據 */}
                          {tableData.enriched && selectedTable === 'member_services' && (
                            <>
                              <td className="border border-gray-300 px-4 py-3 font-medium">{row.member_name}</td>
                              <td className="border border-gray-300 px-4 py-3 font-mono text-sm">{row.member_email}</td>
                              <td className="border border-gray-300 px-4 py-3 font-medium">{row.service_name}</td>
                              <td className="border border-gray-300 px-4 py-3">${row.service_price}/{row.service_billing_cycle}</td>
                            </>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>

                  {tableData.rows.length === 0 && (
                    <div className="text-center py-12 bg-gray-50">
                      <div className="text-gray-500 text-lg">📭 此表格暫無數據</div>
                      <p className="text-gray-400 mt-2">表格中沒有找到任何記錄</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 說明信息 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📋 資料庫管理說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>👥 會員表 (members):</strong> 包含所有註冊用戶的基本信息，如姓名、Email、角色等</p>
                <p><strong>🛍️ 服務表 (services):</strong> 系統提供的所有服務項目，包含價格、描述、功能等</p>
                <p><strong>📦 訂閱關聯表 (member_services):</strong> 記錄會員與服務的訂閱關係，包含訂閱時間、計費信息等</p>
                <p><strong>🔒 安全性:</strong> 密碼哈希等敏感信息已隱藏顯示</p>
                <p><strong>🔄 實時性:</strong> 點擊重新載入可獲取最新的資料庫數據</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
