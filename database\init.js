// 資料庫初始化和連接管理
const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

// 根據當前執行環境確定正確的路徑
const getProjectRoot = () => {
  const cwd = process.cwd();
  console.log('Current working directory:', cwd);

  // 如果在 frontend 目錄中執行，需要向上一級
  if (cwd.includes('frontend')) {
    return path.resolve(cwd, '..');
  }
  return cwd;
};

const PROJECT_ROOT = getProjectRoot();
const DB_PATH = path.join(PROJECT_ROOT, 'database', 'memberservice.db');
const SCHEMA_PATH = path.join(PROJECT_ROOT, 'database', 'schema.sql');

console.log('Project root:', PROJECT_ROOT);
console.log('Database path:', DB_PATH);
console.log('Schema path:', SCHEMA_PATH);

// 確保資料庫目錄存在
const ensureDatabaseDirectory = () => {
  const dbDir = path.dirname(DB_PATH);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
    console.log('📁 創建資料庫目錄:', dbDir);
  }
};

// 創建資料庫連接
const createConnection = () => {
  ensureDatabaseDirectory();
  
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('❌ 資料庫連接失敗:', err.message);
        reject(err);
      } else {
        console.log('✅ 資料庫連接成功:', DB_PATH);
        resolve(db);
      }
    });
  });
};

// 執行 SQL 文件
const executeSqlFile = (db, filePath) => {
  return new Promise((resolve, reject) => {
    if (!fs.existsSync(filePath)) {
      reject(new Error(`SQL 文件不存在: ${filePath}`));
      return;
    }

    const sql = fs.readFileSync(filePath, 'utf8');
    
    // 分割 SQL 語句（以分號分隔）
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    let completed = 0;
    const total = statements.length;

    if (total === 0) {
      resolve();
      return;
    }

    statements.forEach((statement, index) => {
      db.run(statement, (err) => {
        if (err) {
          console.error(`❌ SQL 執行失敗 (${index + 1}/${total}):`, err.message);
          console.error('SQL:', statement.substring(0, 100) + '...');
        } else {
          console.log(`✅ SQL 執行成功 (${index + 1}/${total})`);
        }
        
        completed++;
        if (completed === total) {
          resolve();
        }
      });
    });
  });
};

// 初始化資料庫
const initializeDatabase = async () => {
  try {
    console.log('🔄 開始初始化資料庫...');
    
    const db = await createConnection();
    
    // 執行 schema.sql
    await executeSqlFile(db, SCHEMA_PATH);
    
    console.log('✅ 資料庫初始化完成');
    
    return db;
  } catch (error) {
    console.error('❌ 資料庫初始化失敗:', error);
    throw error;
  }
};

// 獲取資料庫連接（單例模式）
let dbInstance = null;

const getDatabase = async () => {
  if (!dbInstance) {
    dbInstance = await initializeDatabase();
  }
  return dbInstance;
};

// 關閉資料庫連接
const closeDatabase = () => {
  return new Promise((resolve) => {
    if (dbInstance) {
      dbInstance.close((err) => {
        if (err) {
          console.error('❌ 關閉資料庫失敗:', err.message);
        } else {
          console.log('✅ 資料庫連接已關閉');
        }
        dbInstance = null;
        resolve();
      });
    } else {
      resolve();
    }
  });
};

// Promise 化的資料庫操作
const dbRun = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
};

const dbGet = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

const dbAll = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// 測試資料庫連接
const testConnection = async () => {
  try {
    const db = await getDatabase();
    const result = await dbGet(db, 'SELECT COUNT(*) as count FROM services');
    console.log('🧪 資料庫測試成功，服務數量:', result.count);
    return true;
  } catch (error) {
    console.error('❌ 資料庫測試失敗:', error);
    return false;
  }
};

// 重置資料庫（開發用）
const resetDatabase = async () => {
  try {
    console.log('🔄 重置資料庫...');
    
    // 關閉現有連接
    await closeDatabase();
    
    // 刪除資料庫文件
    if (fs.existsSync(DB_PATH)) {
      fs.unlinkSync(DB_PATH);
      console.log('🗑️ 刪除舊資料庫文件');
    }
    
    // 重新初始化
    const db = await initializeDatabase();
    console.log('✅ 資料庫重置完成');
    
    return db;
  } catch (error) {
    console.error('❌ 資料庫重置失敗:', error);
    throw error;
  }
};

module.exports = {
  getDatabase,
  closeDatabase,
  initializeDatabase,
  testConnection,
  resetDatabase,
  dbRun,
  dbGet,
  dbAll,
  DB_PATH
};
