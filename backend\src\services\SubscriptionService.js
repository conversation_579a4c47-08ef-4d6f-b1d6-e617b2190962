const { query, transaction } = require('../config/database');
const Decimal = require('decimal.js');

class SubscriptionService {
  // 獲取所有可用服務
  static async getAvailableServices() {
    try {
      const result = await query(
        `SELECT s.*,
                COUNT(sp.id) as plan_count,
                MIN(sp.price) as min_price,
                MAX(sp.price) as max_price
         FROM services s
         LEFT JOIN service_plans sp ON s.id = sp.service_id AND sp.is_active = true
         WHERE s.is_active = true
         GROUP BY s.id
         ORDER BY s.sort_order ASC, s.created_at DESC`
      );

      return result.rows;
    } catch (error) {
      console.error('Get available services error:', error);
      throw error;
    }
  }

  // 獲取服務詳情和方案
  static async getServiceWithPlans(serviceId) {
    try {
      // 獲取服務基本資訊
      const serviceResult = await query(
        'SELECT * FROM services WHERE id = $1 AND is_active = true',
        [serviceId]
      );

      if (serviceResult.rows.length === 0) {
        throw new Error('服務不存在或已停用');
      }

      const service = serviceResult.rows[0];

      // 獲取服務方案
      const plansResult = await query(
        `SELECT * FROM service_plans
         WHERE service_id = $1 AND is_active = true
         ORDER BY sort_order ASC, price ASC`,
        [serviceId]
      );

      return {
        ...service,
        plans: plansResult.rows,
      };
    } catch (error) {
      console.error('Get service with plans error:', error);
      throw error;
    }
  }

  // 獲取用戶訂閱列表
  static async getUserSubscriptions(userId) {
    try {
      const result = await query(
        `SELECT sub.*,
                s.name as service_name,
                s.icon_url as service_icon,
                s.category as service_category,
                sp.name as plan_name,
                sp.features as plan_features,
                sp.limits as plan_limits
         FROM subscriptions sub
         JOIN services s ON sub.service_id = s.id
         JOIN service_plans sp ON sub.plan_id = sp.id
         WHERE sub.user_id = $1
         ORDER BY sub.created_at DESC`,
        [userId]
      );

      return result.rows;
    } catch (error) {
      console.error('Get user subscriptions error:', error);
      throw error;
    }
  }

  // 檢查用戶是否已訂閱服務
  static async checkUserSubscription(userId, serviceId) {
    try {
      const result = await query(
        `SELECT * FROM subscriptions
         WHERE user_id = $1 AND service_id = $2
         AND status IN ('trial', 'active')`,
        [userId, serviceId]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      console.error('Check user subscription error:', error);
      throw error;
    }
  }

  // 創建訂閱
  static async createSubscription(userId, planId, paymentMethodId = null) {
    return await transaction(async (client) => {
      try {
        // 獲取方案資訊
        const planResult = await client.query(
          `SELECT sp.*, s.name as service_name, s.trial_days
           FROM service_plans sp
           JOIN services s ON sp.service_id = s.id
           WHERE sp.id = $1 AND sp.is_active = true`,
          [planId]
        );

        if (planResult.rows.length === 0) {
          throw new Error('方案不存在或已停用');
        }

        const plan = planResult.rows[0];

        // 檢查用戶是否已有此服務的訂閱
        const existingSubscription = await this.checkUserSubscription(userId, plan.service_id);
        if (existingSubscription) {
          throw new Error('您已經訂閱了此服務');
        }

        // 計算訂閱期間
        const now = new Date();
        const trialDays = plan.trial_days || 0;
        const isTrialAvailable = trialDays > 0;

        let currentPeriodStart = now;
        let currentPeriodEnd = new Date();
        let trialEndsAt = null;
        let status = 'active';

        if (isTrialAvailable) {
          // 有試用期
          status = 'trial';
          trialEndsAt = new Date(now.getTime() + trialDays * 24 * 60 * 60 * 1000);
          currentPeriodEnd = new Date(trialEndsAt);
        } else {
          // 無試用期，直接開始計費
          switch (plan.billing_period) {
            case 'monthly':
              currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + plan.billing_interval);
              break;
            case 'quarterly':
              currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + (3 * plan.billing_interval));
              break;
            case 'yearly':
              currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + plan.billing_interval);
              break;
            default:
              throw new Error('不支援的計費週期');
          }
        }

        // 計算下次計費日期
        let nextBillingDate = null;
        if (!isTrialAvailable || status === 'active') {
          nextBillingDate = new Date(currentPeriodEnd);
        } else {
          nextBillingDate = new Date(trialEndsAt);
        }

        // 創建訂閱記錄
        const subscriptionResult = await client.query(
          `INSERT INTO subscriptions (
            user_id, service_id, plan_id, status, trial_ends_at,
            current_period_start, current_period_end, price, currency,
            billing_period, next_billing_date, auto_renew
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          RETURNING *`,
          [
            userId,
            plan.service_id,
            planId,
            status,
            trialEndsAt,
            currentPeriodStart,
            currentPeriodEnd,
            plan.price,
            plan.currency,
            plan.billing_period,
            nextBillingDate,
            true
          ]
        );

        const subscription = subscriptionResult.rows[0];

        // 創建使用記錄（如果方案有限制）
        if (plan.limits) {
          const limits = typeof plan.limits === 'string' ? JSON.parse(plan.limits) : plan.limits;

          for (const [metricName, limit] of Object.entries(limits)) {
            await client.query(
              `INSERT INTO subscription_usage (
                subscription_id, metric_name, usage_count, usage_limit,
                period_start, period_end
              ) VALUES ($1, $2, $3, $4, $5, $6)`,
              [
                subscription.id,
                metricName,
                0,
                limit,
                currentPeriodStart,
                currentPeriodEnd
              ]
            );
          }
        }

        return {
          ...subscription,
          service_name: plan.service_name,
          plan_name: plan.name,
          is_trial: isTrialAvailable,
        };
      } catch (error) {
        console.error('Create subscription error:', error);
        throw error;
      }
    });
  }

  // 取消訂閱
  static async cancelSubscription(userId, subscriptionId, cancelAtPeriodEnd = true) {
    try {
      const subscription = await query(
        'SELECT * FROM subscriptions WHERE id = $1 AND user_id = $2',
        [subscriptionId, userId]
      );

      if (subscription.rows.length === 0) {
        throw new Error('訂閱不存在');
      }

      const sub = subscription.rows[0];

      if (sub.status === 'cancelled' || sub.status === 'expired') {
        throw new Error('訂閱已經取消或過期');
      }

      if (cancelAtPeriodEnd) {
        // 在當前週期結束時取消
        await query(
          'UPDATE subscriptions SET cancel_at_period_end = true, auto_renew = false WHERE id = $1',
          [subscriptionId]
        );
      } else {
        // 立即取消
        await query(
          'UPDATE subscriptions SET status = $1, cancelled_at = CURRENT_TIMESTAMP, auto_renew = false WHERE id = $2',
          ['cancelled', subscriptionId]
        );
      }

      return { success: true, cancelAtPeriodEnd };
    } catch (error) {
      console.error('Cancel subscription error:', error);
      throw error;
    }
  }

  // 恢復訂閱
  static async resumeSubscription(userId, subscriptionId) {
    try {
      const subscription = await query(
        'SELECT * FROM subscriptions WHERE id = $1 AND user_id = $2',
        [subscriptionId, userId]
      );

      if (subscription.rows.length === 0) {
        throw new Error('訂閱不存在');
      }

      const sub = subscription.rows[0];

      if (sub.status !== 'cancelled' && !sub.cancel_at_period_end) {
        throw new Error('訂閱狀態不允許恢復');
      }

      await query(
        'UPDATE subscriptions SET cancel_at_period_end = false, auto_renew = true WHERE id = $1',
        [subscriptionId]
      );

      return { success: true };
    } catch (error) {
      console.error('Resume subscription error:', error);
      throw error;
    }
  }
}

module.exports = SubscriptionService;