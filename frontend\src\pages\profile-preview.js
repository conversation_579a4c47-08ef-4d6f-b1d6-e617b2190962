import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { AvatarDisplay } from '../components/AvatarUpload';

export default function ProfilePreview() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 檢查認證
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=' + encodeURIComponent('/profile-preview'));
      return;
    }

    // 載入用戶數據
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setIsLoading(false);
  }, [router]);

  const formatDate = (dateString) => {
    if (!dateString) return '未設定';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-TW');
  };

  const getGenderText = (gender) => {
    switch (gender) {
      case 'male': return '男性';
      case 'female': return '女性';
      case 'other': return '其他';
      case 'prefer-not-to-say': return '不願透露';
      default: return '未設定';
    }
  };

  const getMembershipDuration = (memberSince) => {
    if (!memberSince) return '新會員';
    
    const joinDate = new Date(memberSince);
    const now = new Date();
    const diffTime = Math.abs(now - joinDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) {
      return `${diffDays} 天`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months} 個月`;
    } else {
      const years = Math.floor(diffDays / 365);
      const remainingMonths = Math.floor((diffDays % 365) / 30);
      return remainingMonths > 0 ? `${years} 年 ${remainingMonths} 個月` : `${years} 年`;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入個人資料中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>個人資料 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/minimal-dashboard')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回儀表板
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">個人資料</h1>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/account-settings')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  編輯資料
                </button>
                <span className="text-sm text-gray-600">歡迎，{user?.name}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 個人資料卡片 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-xl shadow-lg overflow-hidden"
            >
              {/* 封面背景 */}
              <div className="h-32 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-600"></div>
              
              {/* 個人資料內容 */}
              <div className="relative px-8 pb-8">
                {/* 頭像 */}
                <div className="absolute -top-16 left-8">
                  <div className="border-4 border-white rounded-full">
                    <AvatarDisplay 
                      avatar={user?.avatar} 
                      name={user?.name} 
                      size="xl" 
                    />
                  </div>
                </div>

                {/* 基本信息 */}
                <div className="pt-20">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        {user?.name || '未設定姓名'}
                      </h1>
                      <p className="text-gray-600 mb-4">
                        {user?.email}
                      </p>
                      {user?.bio && (
                        <p className="text-gray-700 max-w-2xl">
                          {user.bio}
                        </p>
                      )}
                    </div>
                    
                    <div className="mt-4 md:mt-0">
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <span>會員時長:</span>
                        <span className="font-medium text-blue-600">
                          {getMembershipDuration(user?.memberSince)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* 詳細信息 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="grid grid-cols-1 md:grid-cols-2 gap-8"
            >
              {/* 個人信息 */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">個人信息</h2>
                
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-gray-600">手機號碼</span>
                    <span className="font-medium text-gray-900">
                      {user?.phone || '未設定'}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-gray-600">生日</span>
                    <span className="font-medium text-gray-900">
                      {formatDate(user?.birthday)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-gray-600">性別</span>
                    <span className="font-medium text-gray-900">
                      {getGenderText(user?.gender)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-start py-3">
                    <span className="text-gray-600">地址</span>
                    <span className="font-medium text-gray-900 text-right max-w-xs">
                      {user?.address || '未設定'}
                    </span>
                  </div>
                </div>
              </div>

              {/* 帳戶信息 */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">帳戶信息</h2>
                
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-gray-600">註冊時間</span>
                    <span className="font-medium text-gray-900">
                      {formatDate(user?.memberSince)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-gray-600">會員狀態</span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      已認證
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-gray-600">最後登入</span>
                    <span className="font-medium text-gray-900">
                      {formatDate(new Date().toISOString())}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center py-3">
                    <span className="text-gray-600">帳戶安全</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-green-600">安全</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* 統計信息 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <h2 className="text-xl font-semibold text-gray-900 mb-6">使用統計</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-2">3</div>
                  <div className="text-sm text-gray-600">活躍訂閱</div>
                </div>
                
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-2">24</div>
                  <div className="text-sm text-gray-600">總交易數</div>
                </div>
                
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 mb-2">NT$ 1,250</div>
                  <div className="text-sm text-gray-600">錢包餘額</div>
                </div>
                
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600 mb-2">{getMembershipDuration(user?.memberSince)}</div>
                  <div className="text-sm text-gray-600">會員時長</div>
                </div>
              </div>
            </motion.div>
          </div>
        </main>
      </div>
    </>
  );
}
