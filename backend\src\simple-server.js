const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();

// 基本中間件
app.use(cors({
  origin: ['http://localhost:3001', 'https://topyun.ngrok.app'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

// 健康檢查端點
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: '後端服務運行正常'
  });
});

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: '會員管理系統後端 API',
    version: '1.0.0',
    status: 'running'
  });
});

// 簡單的登入 API
app.post('/api/v1/auth/login', (req, res) => {
  console.log('收到登入請求:', req.body);
  const { email, password } = req.body;
  
  // 簡單的測試用戶
  if (email === '<EMAIL>' && password === 'password') {
    res.json({
      success: true,
      data: {
        token: 'test-token-123',
        user: {
          id: 1,
          email: '<EMAIL>',
          name: '測試用戶',
          role: 'member',
          createdAt: new Date().toISOString()
        }
      }
    });
  } else {
    res.status(401).json({
      success: false,
      error: '電子郵件或密碼錯誤'
    });
  }
});

// 簡單的訂閱 API
app.get('/api/v1/subscriptions', (req, res) => {
  res.json({
    success: true,
    data: []
  });
});

// 簡單的錢包 API
app.get('/api/v1/wallets', (req, res) => {
  res.json({
    success: true,
    data: {
      balance: 0,
      currency: 'TWD'
    }
  });
});

// 404 處理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: '找不到請求的資源',
    path: req.originalUrl
  });
});

// 錯誤處理
app.use((err, req, res, next) => {
  console.error('錯誤:', err);
  res.status(500).json({
    success: false,
    error: '服務器內部錯誤'
  });
});

// 啟動服務器
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 簡單後端服務器運行在端口 ${PORT}`);
  console.log(`🏥 健康檢查: http://localhost:${PORT}/health`);
  console.log(`🌍 CORS 已啟用，允許來自前端的請求`);
});

module.exports = app;
