// 智能推薦聯絡人 API（基於轉帳歷史）
import jwt from 'jsonwebtoken';
import sqlite3 from 'sqlite3';
import path from 'path';
import fs from 'fs';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];
    
    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('聯絡人推薦 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }
    
    console.log('聯絡人推薦 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('聯絡人推薦 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    // 驗證 JWT token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('聯絡人推薦 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    const userId = decoded.userId;
    console.log('聯絡人推薦 API: 用戶 ID:', userId);

    const db = connectDatabase();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    return new Promise((resolve) => {
      // 1. 基於轉帳歷史的推薦（直接從 wallet_transactions 查詢）
      const transferBasedQuery = `
        SELECT
          CASE
            WHEN wt.description LIKE '%轉帳給%' THEN
              SUBSTR(wt.description, 4, INSTR(wt.description, '(') - 4)
            ELSE '未知聯絡人'
          END as contact_name,
          CASE
            WHEN wt.description LIKE '%轉帳給%' THEN
              SUBSTR(wt.description, INSTR(wt.description, '(') + 1, INSTR(wt.description, ')') - INSTR(wt.description, '(') - 1)
            ELSE NULL
          END as contact_email,
          COUNT(*) as transfer_count,
          SUM(ABS(wt.amount)) as total_amount,
          MAX(wt.created_at) as last_transfer_at,
          AVG(ABS(wt.amount)) as avg_amount,
          'transfer_history' as suggestion_type,
          CASE
            WHEN COUNT(*) >= 5 THEN 'high'
            WHEN COUNT(*) >= 2 THEN 'medium'
            ELSE 'low'
          END as priority
        FROM wallet_transactions wt
        LEFT JOIN frequent_contacts fc ON fc.user_id = ? AND fc.contact_email = SUBSTR(wt.description, INSTR(wt.description, '(') + 1, INSTR(wt.description, ')') - INSTR(wt.description, '(') - 1)
        WHERE wt.member_id = ? AND wt.transaction_type = 'transfer_out'
          AND wt.description LIKE '%轉帳給%' AND fc.id IS NULL
        GROUP BY contact_email, contact_name
        HAVING contact_email IS NOT NULL AND contact_email != ''
        ORDER BY transfer_count DESC, last_transfer_at DESC
        LIMIT 5
      `;

      db.all(transferBasedQuery, [userId, userId], (err, transferSuggestions) => {
        if (err) {
          console.error('聯絡人推薦 API: 查詢轉帳歷史失敗:', err);
          db.close();
          resolve(res.status(500).json({
            success: false,
            error: '查詢轉帳歷史失敗: ' + err.message
          }));
          return;
        }

        // 2. 基於系統會員的推薦（排除已添加的聯絡人）
        const memberBasedQuery = `
          SELECT 
            m.name as contact_name,
            m.email as contact_email,
            m.created_at,
            'system_member' as suggestion_type,
            'medium' as priority
          FROM members m
          LEFT JOIN frequent_contacts fc ON fc.user_id = ? AND fc.contact_email = m.email
          WHERE m.id != ? AND fc.id IS NULL
          ORDER BY m.created_at DESC
          LIMIT 5
        `;

        db.all(memberBasedQuery, [userId, userId], (err2, memberSuggestions) => {
          db.close();
          
          if (err2) {
            console.error('聯絡人推薦 API: 查詢系統會員失敗:', err2);
            resolve(res.status(500).json({
              success: false,
              error: '查詢系統會員失敗: ' + err2.message
            }));
            return;
          }

          // 合併和排序推薦結果
          const allSuggestions = [
            ...transferSuggestions.map(s => ({
              ...s,
              score: s.transfer_count * 10 + (s.total_amount / 100),
              reason: `曾轉帳 ${s.transfer_count} 次，總金額 $${s.total_amount}`
            })),
            ...memberSuggestions.map(s => ({
              ...s,
              score: 5,
              reason: '系統會員，可能需要轉帳'
            }))
          ];

          // 按分數排序
          allSuggestions.sort((a, b) => b.score - a.score);

          // 為每個推薦生成合適的頭像
          const avatarOptions = ['👨‍💼', '👩‍💻', '👨‍🔧', '👩‍🎨', '👨‍🏫', '👩‍⚕️', '👨‍🍳', '👩‍🔬'];
          allSuggestions.forEach((suggestion, index) => {
            suggestion.suggested_avatar = avatarOptions[index % avatarOptions.length];
          });

          console.log(`聯絡人推薦 API: 找到 ${allSuggestions.length} 個推薦聯絡人`);

          resolve(res.status(200).json({
            success: true,
            data: {
              suggestions: allSuggestions.slice(0, 8), // 最多返回8個推薦
              categories: {
                transfer_based: transferSuggestions.length,
                member_based: memberSuggestions.length
              }
            },
            message: '成功獲取聯絡人推薦'
          }));
        });
      });
    });

  } catch (error) {
    console.error('聯絡人推薦 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
