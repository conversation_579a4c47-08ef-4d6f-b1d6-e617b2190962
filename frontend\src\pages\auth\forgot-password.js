import React, { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { EnvelopeIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError('請輸入電子郵件');
      return;
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      setError('請輸入有效的電子郵件');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // 模擬 API 調用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 這裡應該調用實際的忘記密碼 API
      console.log('忘記密碼請求:', email);
      
      setIsSubmitted(true);
    } catch (err) {
      setError('發送失敗，請稍後再試');
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <>
        <Head>
          <title>重設密碼郵件已發送 - 會員管理系統</title>
        </Head>

        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-md w-full space-y-8"
          >
            <div className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center"
              >
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
              </motion.div>
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                郵件已發送
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                我們已將重設密碼的連結發送到您的電子郵件
              </p>
            </div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="bg-white p-8 rounded-xl shadow-lg space-y-6"
            >
              <div className="text-center space-y-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <EnvelopeIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <p className="text-sm text-gray-700">
                    請檢查您的電子郵件信箱：
                  </p>
                  <p className="font-medium text-gray-900 mt-1">
                    {email}
                  </p>
                </div>

                <div className="text-sm text-gray-600 space-y-2">
                  <p>• 請點擊郵件中的連結來重設您的密碼</p>
                  <p>• 連結將在 24 小時後失效</p>
                  <p>• 如果沒有收到郵件，請檢查垃圾郵件資料夾</p>
                </div>

                <div className="pt-4">
                  <button
                    onClick={() => setIsSubmitted(false)}
                    className="text-blue-600 hover:text-blue-500 text-sm font-medium transition-colors"
                  >
                    重新發送郵件
                  </button>
                </div>
              </div>
            </motion.div>

            <div className="text-center space-y-2">
              <Link href="/auth/login" className="text-sm text-blue-600 hover:text-blue-500 transition-colors">
                返回登入頁面
              </Link>
              <br />
              <Link href="/" className="text-sm text-gray-500 hover:text-gray-700 transition-colors">
                ← 返回首頁
              </Link>
            </div>
          </motion.div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>忘記密碼 - 會員管理系統</title>
        <meta name="description" content="重設您的密碼" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md w-full space-y-8"
        >
          {/* Logo 和標題 */}
          <div className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center"
            >
              <span className="text-white text-2xl font-bold">M</span>
            </motion.div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              忘記密碼
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              輸入您的電子郵件，我們將發送重設密碼的連結
            </p>
          </div>

          {/* 忘記密碼表單 */}
          <motion.form
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="mt-8 space-y-6 bg-white p-8 rounded-xl shadow-lg"
            onSubmit={handleSubmit}
          >
            {error && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg"
              >
                {error}
              </motion.div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                電子郵件
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm transition-colors"
                  placeholder="請輸入您的電子郵件"
                />
              </div>
            </div>

            <div>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    發送中...
                  </div>
                ) : (
                  '發送重設連結'
                )}
              </motion.button>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600">
                記起密碼了？{' '}
                <Link href="/auth/login" className="font-medium text-blue-600 hover:text-blue-500 transition-colors">
                  返回登入
                </Link>
              </p>
            </div>
          </motion.form>

          {/* 返回首頁 */}
          <div className="text-center">
            <Link href="/" className="text-sm text-gray-500 hover:text-gray-700 transition-colors">
              ← 返回首頁
            </Link>
          </div>
        </motion.div>
      </div>
    </>
  );
}
