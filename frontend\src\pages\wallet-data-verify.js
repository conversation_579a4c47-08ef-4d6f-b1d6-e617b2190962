import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletDataVerify() {
  const router = useRouter();
  const [verificationResults, setVerificationResults] = useState([]);
  const [isVerifying, setIsVerifying] = useState(false);
  const [summary, setSummary] = useState(null);

  const addResult = (category, test, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setVerificationResults(prev => [...prev, {
      timestamp,
      category,
      test,
      status, // 'pass', 'fail', 'warning', 'info'
      message,
      data
    }]);
  };

  const verifyWalletData = async () => {
    setIsVerifying(true);
    setVerificationResults([]);
    setSummary(null);

    try {
      addResult('開始', '驗證', 'info', '開始驗證錢包數據來源');

      // 檢查認證
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (!token || !userStr) {
        addResult('認證', '檢查', 'fail', '未找到認證數據');
        return;
      }

      let user;
      try {
        user = JSON.parse(userStr);
        addResult('認證', '解析', 'pass', `用戶: ${user.name} (ID: ${user.userId})`);
      } catch (error) {
        addResult('認證', '解析', 'fail', '用戶數據解析失敗');
        return;
      }

      // 測試 1: 直接查詢資料庫
      addResult('資料庫', '直接查詢', 'info', '測試直接資料庫查詢...');
      
      const dbResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: 'SELECT id, name, email, wallet_balance FROM members WHERE id = ?',
          params: [user.userId]
        })
      });

      let dbBalance = null;
      if (dbResponse.ok) {
        const dbResult = await dbResponse.json();
        if (dbResult.success && dbResult.data.length > 0) {
          dbBalance = dbResult.data[0].wallet_balance;
          addResult('資料庫', '餘額查詢', 'pass', `資料庫餘額: $${dbBalance || 0}`, dbResult.data[0]);
        } else {
          addResult('資料庫', '餘額查詢', 'fail', '資料庫查詢失敗或無數據');
        }
      } else {
        addResult('資料庫', '餘額查詢', 'fail', '無法連接資料庫');
      }

      // 測試 2: 錢包 API 查詢
      addResult('API', '錢包餘額', 'info', '測試錢包餘額 API...');
      
      const apiResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      let apiBalance = null;
      if (apiResponse.ok) {
        const apiResult = await apiResponse.json();
        if (apiResult.success) {
          apiBalance = apiResult.data.balance;
          addResult('API', '錢包餘額', 'pass', `API 餘額: $${apiBalance}`, apiResult.data);
        } else {
          addResult('API', '錢包餘額', 'fail', `API 失敗: ${apiResult.error}`);
        }
      } else {
        addResult('API', '錢包餘額', 'fail', 'API 請求失敗');
      }

      // 測試 3: 數據一致性檢查
      if (dbBalance !== null && apiBalance !== null) {
        if (Math.abs(dbBalance - apiBalance) < 0.01) {
          addResult('一致性', '餘額對比', 'pass', '資料庫和 API 餘額一致');
        } else {
          addResult('一致性', '餘額對比', 'fail', `餘額不一致！資料庫: $${dbBalance}, API: $${apiBalance}`);
        }
      } else {
        addResult('一致性', '餘額對比', 'warning', '無法比較餘額（缺少數據）');
      }

      // 測試 4: 交易記錄查詢
      addResult('交易', '記錄查詢', 'info', '測試交易記錄 API...');
      
      const transactionResponse = await fetch('/api/wallet/transactions?limit=5', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (transactionResponse.ok) {
        const transactionResult = await transactionResponse.json();
        if (transactionResult.success) {
          addResult('交易', '記錄查詢', 'pass', `找到 ${transactionResult.data.transactions.length} 筆交易記錄`);
          addResult('交易', '統計數據', 'pass', `總交易: ${transactionResult.data.stats.total_transactions} 筆`);
        } else {
          addResult('交易', '記錄查詢', 'fail', `交易記錄 API 失敗: ${transactionResult.error}`);
        }
      } else {
        addResult('交易', '記錄查詢', 'fail', '交易記錄 API 請求失敗');
      }

      // 測試 5: 支付方式查詢
      addResult('支付', '方式查詢', 'info', '測試支付方式 API...');
      
      const paymentResponse = await fetch('/api/wallet/payment-methods');
      
      if (paymentResponse.ok) {
        const paymentResult = await paymentResponse.json();
        if (paymentResult.success) {
          addResult('支付', '方式查詢', 'pass', `找到 ${paymentResult.data.payment_methods.length} 種支付方式`);
        } else {
          addResult('支付', '方式查詢', 'fail', `支付方式 API 失敗: ${paymentResult.error}`);
        }
      } else {
        addResult('支付', '方式查詢', 'fail', '支付方式 API 請求失敗');
      }

      // 生成總結
      const results = verificationResults;
      const passCount = results.filter(r => r.status === 'pass').length;
      const failCount = results.filter(r => r.status === 'fail').length;
      const warningCount = results.filter(r => r.status === 'warning').length;

      setSummary({
        total: results.length,
        pass: passCount,
        fail: failCount,
        warning: warningCount,
        dbBalance: dbBalance,
        apiBalance: apiBalance,
        dataSource: dbBalance !== null && apiBalance !== null ? '真實資料庫' : '可能使用模擬數據'
      });

      addResult('完成', '驗證', 'info', `驗證完成 - 通過: ${passCount}, 失敗: ${failCount}, 警告: ${warningCount}`);

    } catch (error) {
      addResult('錯誤', '系統', 'fail', '驗證過程中出現錯誤: ' + error.message);
    } finally {
      setIsVerifying(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pass': return 'text-green-600';
      case 'fail': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pass': return '✅';
      case 'fail': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  const getBgColor = (status) => {
    switch (status) {
      case 'pass': return 'bg-green-50 border-green-200';
      case 'fail': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      default: return 'bg-blue-50 border-blue-200';
    }
  };

  return (
    <>
      <Head>
        <title>錢包數據驗證 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔍 錢包數據驗證</h1>
                <p className="text-gray-600 mt-2">驗證錢包功能是否使用真實資料庫數據</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/wallet-db-check')}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  🗄️ 資料庫檢查
                </button>
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={verifyWalletData}
                  disabled={isVerifying}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-medium"
                >
                  {isVerifying ? '驗證中...' : '🔍 開始驗證'}
                </button>
              </div>
            </div>

            {/* 驗證總結 */}
            {summary && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 驗證總結</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{summary.pass}</div>
                    <div className="text-sm text-green-800">通過測試</div>
                  </div>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-red-600">{summary.fail}</div>
                    <div className="text-sm text-red-800">失敗測試</div>
                  </div>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-yellow-600">{summary.warning}</div>
                    <div className="text-sm text-yellow-800">警告</div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-lg font-bold text-blue-600">{summary.dataSource}</div>
                    <div className="text-sm text-blue-800">數據來源</div>
                  </div>
                </div>
              </div>
            )}

            {/* 驗證結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 詳細結果</h2>
              
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {verificationResults.length > 0 ? (
                  verificationResults.map((result, index) => (
                    <div key={index} className={`rounded-lg p-4 border ${getBgColor(result.status)}`}>
                      <div className="flex items-start space-x-3">
                        <span className={getStatusColor(result.status)}>
                          {getStatusIcon(result.status)}
                        </span>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 text-sm">
                            <span className="text-gray-500">[{result.timestamp}]</span>
                            <span className="font-medium text-gray-700">{result.category}</span>
                            <span className="text-gray-500">-</span>
                            <span className="font-medium text-gray-700">{result.test}:</span>
                            <span className={getStatusColor(result.status)}>{result.message}</span>
                          </div>
                          {result.data && (
                            <div className="mt-2 p-2 bg-white rounded text-xs font-mono max-h-32 overflow-y-auto">
                              <pre className="whitespace-pre-wrap">
                                {JSON.stringify(result.data, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「開始驗證」來驗證錢包數據來源
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 驗證項目</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>資料庫直接查詢:</strong> 直接查詢 members 表的 wallet_balance</div>
                <div><strong>錢包 API 查詢:</strong> 通過錢包 API 獲取餘額</div>
                <div><strong>數據一致性:</strong> 比較資料庫和 API 返回的餘額是否一致</div>
                <div><strong>交易記錄:</strong> 測試交易記錄 API 是否正常</div>
                <div><strong>支付方式:</strong> 測試支付方式 API 是否正常</div>
                <div><strong>目的:</strong> 確認錢包功能完全使用真實資料庫數據</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
