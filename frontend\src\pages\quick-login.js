import { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function QuickLogin() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const router = useRouter();

  const quickLogin = async () => {
    setIsLoading(true);
    setMessage('正在登入...');

    try {
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        // 保存認證數據
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        
        setMessage('登入成功！正在跳轉...');
        
        // 跳轉到儀表板
        setTimeout(() => {
          router.push('/dashboard-simple');
        }, 1000);
      } else {
        setMessage('登入失敗: ' + result.error);
      }
    } catch (error) {
      setMessage('登入請求失敗: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const clearAndRetry = () => {
    localStorage.clear();
    document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    setMessage('已清除認證數據');
    setTimeout(() => {
      quickLogin();
    }, 500);
  };

  const checkAuth = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    if (token && user) {
      setMessage('認證數據存在，嘗試跳轉...');
      router.push('/dashboard-simple');
    } else {
      setMessage('沒有認證數據，請先登入');
    }
  };

  return (
    <>
      <Head>
        <title>快速登入 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🚀 快速登入</h1>
              <p className="text-gray-600 mt-2">解決儀表板載入問題</p>
            </div>

            <div className="space-y-4">
              <button
                onClick={quickLogin}
                disabled={isLoading}
                className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
              >
                {isLoading ? '登入中...' : '🔑 管理員登入'}
              </button>

              <button
                onClick={checkAuth}
                className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
              >
                🔍 檢查認證狀態
              </button>

              <button
                onClick={clearAndRetry}
                disabled={isLoading}
                className="w-full px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 font-medium"
              >
                🗑️ 清除並重新登入
              </button>

              <button
                onClick={() => router.push('/debug-auth')}
                className="w-full px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 font-medium"
              >
                🔧 認證調試
              </button>
            </div>

            {message && (
              <div className={`mt-6 p-4 rounded-lg text-center ${
                message.includes('成功') ? 'bg-green-50 text-green-800' :
                message.includes('失敗') || message.includes('錯誤') ? 'bg-red-50 text-red-800' :
                'bg-blue-50 text-blue-800'
              }`}>
                {message}
              </div>
            )}

            <div className="mt-8 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">📋 說明</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <div>• 管理員帳號: <EMAIL></div>
                <div>• 密碼: password123</div>
                <div>• 如果儀表板一直載入，請使用此頁面重新登入</div>
              </div>
            </div>

            <div className="mt-4 text-center">
              <button
                onClick={() => router.push('/dashboard-simple')}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                🏠 直接前往儀表板
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
