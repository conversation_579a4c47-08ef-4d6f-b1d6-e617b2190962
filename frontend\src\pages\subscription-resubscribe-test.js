import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function SubscriptionResubscribeTest() {
  const [subscriptions, setSubscriptions] = useState([]);
  const [allServices, setAllServices] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setIsLoading(true);
    addLog('🔄 載入所有數據...', 'info');

    try {
      await Promise.all([
        loadSubscriptions(),
        loadAllServices(),
        loadAvailableServices()
      ]);
    } catch (error) {
      addLog(`❌ 載入失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubscriptions = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        addLog('❌ 未登入', 'error');
        return;
      }

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        setSubscriptions(result.data);
        addLog(`✅ 載入 ${result.data.length} 個訂閱`, 'success');
        
        // 分析訂閱狀態
        const statusCount = result.data.reduce((acc, sub) => {
          acc[sub.status] = (acc[sub.status] || 0) + 1;
          return acc;
        }, {});
        
        Object.entries(statusCount).forEach(([status, count]) => {
          addLog(`  📊 ${status}: ${count} 個`, 'info');
        });

        // 顯示每個訂閱的詳細信息
        result.data.forEach(sub => {
          addLog(`  📦 訂閱 ${sub.id}: 服務 ${sub.service_id} (${sub.service_name}) - ${sub.status}`, 'info');
        });
      } else {
        addLog(`❌ 載入訂閱失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 訂閱請求失敗: ${error.message}`, 'error');
    }
  };

  const loadAllServices = async () => {
    try {
      const response = await fetch('/api/services/database');
      const result = await response.json();

      if (result.success) {
        setAllServices(result.data);
        addLog(`✅ 載入 ${result.data.length} 個服務`, 'success');
        
        result.data.forEach(service => {
          addLog(`  🛍️ 服務 ${service.id}: ${service.name} - ${service.status}`, 'info');
        });
      } else {
        addLog(`❌ 載入服務失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 服務請求失敗: ${error.message}`, 'error');
    }
  };

  const loadAvailableServices = async () => {
    try {
      addLog('🔍 計算可用服務...', 'info');
      
      // 模擬訂閱頁面的邏輯
      const activeServices = allServices.filter(service => service.status === 'active');
      addLog(`📋 活躍服務數量: ${activeServices.length}`, 'info');

      // 獲取活躍訂閱的服務 ID
      const activeSubscribedServiceIds = subscriptions
        .filter(sub => sub.status === 'active')
        .map(sub => sub.service_id);
      
      addLog(`🎯 活躍訂閱的服務 ID: [${activeSubscribedServiceIds.join(', ')}]`, 'info');

      // 計算可用服務
      const available = activeServices.filter(service =>
        !activeSubscribedServiceIds.includes(service.id)
      );

      setAvailableServices(available);
      addLog(`✅ 可用服務數量: ${available.length}`, 'success');
      
      available.forEach(service => {
        addLog(`  🎁 可訂閱: ${service.id} - ${service.name}`, 'success');
      });

      // 檢查被取消的服務是否可重新訂閱
      const cancelledSubscriptions = subscriptions.filter(sub => sub.status === 'cancelled');
      if (cancelledSubscriptions.length > 0) {
        addLog(`🔄 檢查已取消的訂閱是否可重新訂閱...`, 'info');
        cancelledSubscriptions.forEach(sub => {
          const isAvailable = available.some(service => service.id === sub.service_id);
          addLog(`  ${isAvailable ? '✅' : '❌'} 服務 ${sub.service_id} (${sub.service_name}) ${isAvailable ? '可重新訂閱' : '不可訂閱'}`, isAvailable ? 'success' : 'warning');
        });
      }

    } catch (error) {
      addLog(`❌ 計算可用服務失敗: ${error.message}`, 'error');
    }
  };

  const testCancelAndResubscribe = async (subscriptionId) => {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    addLog(`🧪 測試取消並重新訂閱: ${subscription.service_name}`, 'info');

    try {
      // 1. 取消訂閱
      addLog(`1️⃣ 取消訂閱 ${subscription.service_name}...`, 'info');
      
      const token = localStorage.getItem('token');
      const cancelResponse = await fetch('/api/subscriptions/cancel', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription_id: subscriptionId
        })
      });

      const cancelResult = await cancelResponse.json();

      if (cancelResult.success) {
        addLog(`✅ 取消成功: ${cancelResult.message}`, 'success');
        
        // 2. 重新載入數據
        addLog(`2️⃣ 重新載入數據...`, 'info');
        await loadAllData();
        
        // 3. 檢查服務是否出現在可用列表
        const serviceInAvailable = availableServices.some(s => s.id === subscription.service_id);
        addLog(`3️⃣ 服務 ${subscription.service_name} ${serviceInAvailable ? '✅ 已出現在可用列表' : '❌ 未出現在可用列表'}`, serviceInAvailable ? 'success' : 'error');
        
      } else {
        addLog(`❌ 取消失敗: ${cancelResult.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 測試失敗: ${error.message}`, 'error');
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      <Head>
        <title>重新訂閱測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔄 重新訂閱測試</h1>
              <p className="text-gray-600 mt-2">測試取消訂閱後服務是否重新出現在可用列表</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* 控制面板 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試控制</h2>
                
                <div className="space-y-4">
                  <button
                    onClick={loadAllData}
                    disabled={isLoading}
                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isLoading ? '載入中...' : '🔄 重新載入所有數據'}
                  </button>

                  <button
                    onClick={clearLogs}
                    className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    🗑️ 清除日誌
                  </button>
                </div>

                {/* 數據摘要 */}
                <div className="mt-6 space-y-4">
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">📊 數據摘要</h3>
                    <div className="text-sm text-blue-800 space-y-1">
                      <div>總服務數: {allServices.length}</div>
                      <div>總訂閱數: {subscriptions.length}</div>
                      <div>可用服務數: {availableServices.length}</div>
                      <div>活躍訂閱: {subscriptions.filter(s => s.status === 'active').length}</div>
                      <div>已取消訂閱: {subscriptions.filter(s => s.status === 'cancelled').length}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 訂閱列表 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">我的訂閱</h2>
                
                {subscriptions.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    暫無訂閱數據
                  </div>
                ) : (
                  <div className="space-y-3">
                    {subscriptions.map((subscription) => (
                      <div key={subscription.id} className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {subscription.service_name}
                            </h4>
                            <p className="text-sm text-gray-600">
                              訂閱ID: {subscription.id} | 服務ID: {subscription.service_id}
                            </p>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                            {subscription.status}
                          </span>
                        </div>

                        {subscription.status === 'active' && (
                          <button
                            onClick={() => testCancelAndResubscribe(subscription.id)}
                            className="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700"
                          >
                            🧪 測試取消→重新訂閱
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 可用服務 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">可用服務</h2>
                
                {availableServices.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    暫無可用服務
                  </div>
                ) : (
                  <div className="space-y-3">
                    {availableServices.map((service) => (
                      <div key={service.id} className="p-4 border border-green-200 bg-green-50 rounded-lg">
                        <h4 className="font-medium text-green-900">
                          {service.name}
                        </h4>
                        <p className="text-sm text-green-700">
                          服務ID: {service.id} | 價格: ${service.price}
                        </p>
                        <div className="mt-2">
                          <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                            可訂閱
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* 測試日誌 */}
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">測試日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                {logs.map((log) => (
                  <div key={log.id} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'success' ? 'text-green-400' : 
                    log.type === 'warning' ? 'text-yellow-400' :
                    'text-blue-400'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">🎯 測試目標</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>問題:</strong> 取消訂閱後，該服務是否重新出現在可用服務列表？</p>
                <p><strong>預期行為:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>取消訂閱後，狀態變為 'cancelled'</li>
                  <li>該服務重新出現在「可用服務」列表</li>
                  <li>用戶可以重新訂閱該服務</li>
                  <li>只有 'active' 狀態的訂閱會排除服務</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
