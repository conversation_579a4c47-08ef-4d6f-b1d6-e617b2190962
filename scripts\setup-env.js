#!/usr/bin/env node

/**
 * 環境變數快速設置腳本
 * 自動生成安全的密鑰並設置環境變數
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 顏色輸出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 生成安全密鑰
function generateSecureKey(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

function generateJWTSecret() {
  return generateSecureKey(32);
}

function generateEncryptionKey() {
  return generateSecureKey(32);
}

// 後端環境變數模板
function getBackendEnvTemplate() {
  const jwtSecret = generateJWTSecret();
  const jwtRefreshSecret = generateJWTSecret();
  const encryptionKey = generateEncryptionKey();
  const hmacSecret = generateSecureKey(32);
  
  return `# ===========================================
# 會員管理系統 - 後端環境變數配置
# ===========================================
# 自動生成於 ${new Date().toISOString()}

# 應用程式基本配置
NODE_ENV=development
PORT=3000
API_VERSION=v1

# 資料庫配置 (開發環境使用 SQLite)
DB_TYPE=sqlite
DB_PATH=../frontend/lib/database/database.sqlite
# PostgreSQL 配置 (生產環境)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=member_system
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT 配置 (自動生成的安全密鑰)
JWT_SECRET=${jwtSecret}
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=${jwtRefreshSecret}
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置 (自動生成的安全密鑰)
ENCRYPTION_KEY=${encryptionKey}
ENCRYPTION_SALT=your-encryption-salt-change-this
HMAC_SECRET=${hmacSecret}

# 郵件服務配置 (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=Member System

# 簡訊服務配置 (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# OAuth 配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3001/api/auth/google/callback

FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_CALLBACK_URL=http://localhost:3001/api/auth/facebook/callback

LINE_CHANNEL_ID=your-line-channel-id
LINE_CHANNEL_SECRET=your-line-channel-secret
LINE_CALLBACK_URL=http://localhost:3001/api/auth/line/callback

# 支付服務配置
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox

ECPAY_MERCHANT_ID=your-ecpay-merchant-id
ECPAY_HASH_KEY=your-ecpay-hash-key
ECPAY_HASH_IV=your-ecpay-hash-iv

# 檔案上傳配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 前端配置
FRONTEND_URL=http://localhost:3001
CORS_ORIGINS=http://localhost:3001,http://localhost:3000

# 監控配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# reCAPTCHA 配置
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key
`;
}

// 前端環境變數模板
function getFrontendEnvTemplate() {
  return `# ===========================================
# 會員管理系統 - 前端環境變數配置
# ===========================================
# 自動生成於 ${new Date().toISOString()}

# API 配置
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000
NEXT_PUBLIC_BACKEND_URL=http://localhost:3000

# 應用配置
NEXT_PUBLIC_APP_NAME=會員管理系統
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_DESCRIPTION=專業的會員管理與訂閱服務系統

# 認證配置
NEXT_PUBLIC_JWT_STORAGE_KEY=memberservice_token
NEXT_PUBLIC_REFRESH_TOKEN_KEY=memberservice_refresh_token
NEXT_PUBLIC_USER_STORAGE_KEY=memberservice_user

# OAuth 配置
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
NEXT_PUBLIC_FACEBOOK_APP_ID=your-facebook-app-id
NEXT_PUBLIC_LINE_CHANNEL_ID=your-line-channel-id

# 支付服務配置
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your-paypal-client-id

# reCAPTCHA 配置
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your-recaptcha-site-key

# 功能開關
NEXT_PUBLIC_ENABLE_REGISTRATION=true
NEXT_PUBLIC_ENABLE_SOCIAL_LOGIN=true
NEXT_PUBLIC_ENABLE_EMAIL_VERIFICATION=true
NEXT_PUBLIC_ENABLE_SUBSCRIPTION=true
NEXT_PUBLIC_ENABLE_PAYMENT=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_SENTRY=false

# UI 配置
NEXT_PUBLIC_DEFAULT_LANGUAGE=zh-TW
NEXT_PUBLIC_DEFAULT_THEME=light
NEXT_PUBLIC_ENABLE_DARK_MODE=true

# 開發模式配置
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_SHOW_DEV_TOOLS=true
NEXT_PUBLIC_ENABLE_MOCK_DATA=false

# 檔案上傳配置
NEXT_PUBLIC_MAX_FILE_SIZE=5242880
NEXT_PUBLIC_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf

# 分頁配置
NEXT_PUBLIC_DEFAULT_PAGE_SIZE=10
NEXT_PUBLIC_MAX_PAGE_SIZE=100
`;
}

function createEnvFile(filePath, content, description) {
  try {
    // 確保目錄存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 檢查檔案是否已存在
    if (fs.existsSync(filePath)) {
      colorLog('yellow', `⚠️  ${description} 已存在: ${filePath}`);
      colorLog('yellow', '   如需重新生成，請先刪除現有檔案');
      return false;
    }

    // 寫入檔案
    fs.writeFileSync(filePath, content, 'utf8');
    colorLog('green', `✅ ${description} 已創建: ${filePath}`);
    return true;
  } catch (error) {
    colorLog('red', `❌ 創建 ${description} 失敗: ${error.message}`);
    return false;
  }
}

function main() {
  colorLog('cyan', '🚀 環境變數快速設置...\n');

  const backendEnvPath = path.join(__dirname, '../backend/.env');
  const frontendEnvPath = path.join(__dirname, '../frontend/.env.local');

  let success = true;

  // 創建後端環境變數檔案
  colorLog('blue', '📋 設置後端環境變數...');
  const backendContent = getBackendEnvTemplate();
  if (!createEnvFile(backendEnvPath, backendContent, '後端環境變數檔案')) {
    success = false;
  }

  console.log();

  // 創建前端環境變數檔案
  colorLog('blue', '📋 設置前端環境變數...');
  const frontendContent = getFrontendEnvTemplate();
  if (!createEnvFile(frontendEnvPath, frontendContent, '前端環境變數檔案')) {
    success = false;
  }

  console.log();

  if (success) {
    colorLog('green', '✅ 環境變數設置完成！');
    colorLog('cyan', '🔑 已自動生成安全的 JWT 和加密密鑰');
    colorLog('yellow', '⚠️  請根據需要修改第三方服務的配置');
    colorLog('blue', '📖 詳細說明請參考: docs/ENVIRONMENT_SETUP.md');
  } else {
    colorLog('red', '❌ 部分環境變數設置失敗');
    colorLog('yellow', '💡 請檢查檔案權限或手動創建');
  }

  console.log();
  colorLog('cyan', '🔍 運行檢查腳本: node scripts/check-env.js');
}

if (require.main === module) {
  main();
}

module.exports = { generateSecureKey, generateJWTSecret, generateEncryptionKey };
