// 充值方式數據模型
const { getDatabase, dbRun, dbGet, dbAll } = require('../init');

class PaymentMethod {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.type = data.type;
    this.description = data.description;
    this.icon_url = data.icon_url;
    this.min_amount = data.min_amount;
    this.max_amount = data.max_amount;
    this.fee_type = data.fee_type;
    this.fee_amount = data.fee_amount;
    this.is_active = data.is_active;
    this.sort_order = data.sort_order;
    this.config_json = data.config_json;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // 獲取所有可用的充值方式
  static async findAll(options = {}) {
    try {
      const db = await getDatabase();
      let sql = 'SELECT * FROM payment_methods';
      const params = [];
      const conditions = [];

      // 只獲取啟用的充值方式
      if (options.activeOnly !== false) {
        conditions.push('is_active = 1');
      }

      // 類型篩選
      if (options.type) {
        conditions.push('type = ?');
        params.push(options.type);
      }

      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }

      // 排序
      sql += ' ORDER BY sort_order ASC, name ASC';

      const rows = await dbAll(db, sql, params);
      return rows.map(row => new PaymentMethod(row));
    } catch (error) {
      console.error('獲取充值方式失敗:', error);
      throw error;
    }
  }

  // 根據 ID 查找充值方式
  static async findById(id) {
    try {
      const db = await getDatabase();
      const row = await dbGet(db, 'SELECT * FROM payment_methods WHERE id = ?', [id]);
      
      if (!row) {
        return null;
      }

      return new PaymentMethod(row);
    } catch (error) {
      console.error('根據 ID 查找充值方式失敗:', error);
      throw error;
    }
  }

  // 根據類型查找充值方式
  static async findByType(type) {
    try {
      const db = await getDatabase();
      const rows = await dbAll(db, 'SELECT * FROM payment_methods WHERE type = ? AND is_active = 1 ORDER BY sort_order ASC', [type]);
      return rows.map(row => new PaymentMethod(row));
    } catch (error) {
      console.error('根據類型查找充值方式失敗:', error);
      throw error;
    }
  }

  // 創建新的充值方式
  static async create(data) {
    try {
      const db = await getDatabase();
      
      const sql = `
        INSERT INTO payment_methods 
        (name, type, description, icon_url, min_amount, max_amount, fee_type, fee_amount, is_active, sort_order, config_json)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        data.name,
        data.type,
        data.description || null,
        data.icon_url || null,
        data.min_amount || 1.00,
        data.max_amount || 10000.00,
        data.fee_type || 'none',
        data.fee_amount || 0.00,
        data.is_active !== undefined ? data.is_active : 1,
        data.sort_order || 0,
        data.config_json ? JSON.stringify(data.config_json) : null
      ];

      const result = await dbRun(db, sql, params);
      return await PaymentMethod.findById(result.id);
    } catch (error) {
      console.error('創建充值方式失敗:', error);
      throw error;
    }
  }

  // 更新充值方式
  async update(updateData) {
    try {
      const db = await getDatabase();
      
      const fields = [];
      const params = [];

      // 動態構建更新欄位
      const allowedFields = ['name', 'type', 'description', 'icon_url', 'min_amount', 'max_amount', 'fee_type', 'fee_amount', 'is_active', 'sort_order'];

      allowedFields.forEach(field => {
        if (updateData.hasOwnProperty(field)) {
          fields.push(`${field} = ?`);
          params.push(updateData[field]);
        }
      });

      // 處理 config_json
      if (updateData.hasOwnProperty('config_json')) {
        fields.push('config_json = ?');
        params.push(updateData.config_json ? JSON.stringify(updateData.config_json) : null);
      }

      if (fields.length === 0) {
        throw new Error('沒有要更新的欄位');
      }

      params.push(this.id);

      const sql = `UPDATE payment_methods SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
      await dbRun(db, sql, params);

      // 重新獲取更新後的數據
      const updated = await PaymentMethod.findById(this.id);
      Object.assign(this, updated);

      return this;
    } catch (error) {
      console.error('更新充值方式失敗:', error);
      throw error;
    }
  }

  // 刪除充值方式（軟刪除，設為不啟用）
  async delete() {
    try {
      return await this.update({ is_active: 0 });
    } catch (error) {
      console.error('刪除充值方式失敗:', error);
      throw error;
    }
  }

  // 計算手續費
  calculateFee(amount) {
    const amountNum = parseFloat(amount);
    
    switch (this.fee_type) {
      case 'fixed':
        return parseFloat(this.fee_amount);
      case 'percentage':
        return amountNum * (parseFloat(this.fee_amount) / 100);
      case 'none':
      default:
        return 0;
    }
  }

  // 計算總金額（包含手續費）
  calculateTotal(amount) {
    const amountNum = parseFloat(amount);
    const fee = this.calculateFee(amount);
    return amountNum + fee;
  }

  // 驗證充值金額
  validateAmount(amount) {
    const amountNum = parseFloat(amount);
    
    if (isNaN(amountNum) || amountNum <= 0) {
      return { valid: false, error: '充值金額必須大於 0' };
    }

    if (amountNum < this.min_amount) {
      return { valid: false, error: `最小充值金額為 $${this.min_amount}` };
    }

    if (amountNum > this.max_amount) {
      return { valid: false, error: `最大充值金額為 $${this.max_amount}` };
    }

    return { valid: true };
  }

  // 獲取配置
  getConfig() {
    try {
      return this.config_json ? JSON.parse(this.config_json) : {};
    } catch (error) {
      console.error('解析配置 JSON 失敗:', error);
      return {};
    }
  }

  // 轉換為 JSON 格式
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      description: this.description,
      icon_url: this.icon_url,
      min_amount: parseFloat(this.min_amount),
      max_amount: parseFloat(this.max_amount),
      fee_type: this.fee_type,
      fee_amount: parseFloat(this.fee_amount),
      is_active: Boolean(this.is_active),
      sort_order: this.sort_order,
      config: this.getConfig(),
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }

  // 轉換為公開格式（用於前端顯示）
  toPublic() {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      description: this.description,
      icon_url: this.icon_url,
      min_amount: parseFloat(this.min_amount),
      max_amount: parseFloat(this.max_amount),
      fee_type: this.fee_type,
      fee_amount: parseFloat(this.fee_amount)
    };
  }
}

module.exports = PaymentMethod;
