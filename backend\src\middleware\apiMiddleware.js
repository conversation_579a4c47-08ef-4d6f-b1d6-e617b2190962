const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');

// CORS 配置
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:3001',
      'http://localhost:3000',
      process.env.FRONTEND_URL,
      process.env.ADMIN_URL
    ].filter(Boolean);

    // 允許沒有 origin 的請求（如移動應用）
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('不被 CORS 政策允許'), false);
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
};

// 速率限制配置
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message,
      error: 'Rate limit exceeded'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // 跳過健康檢查和內部請求
      return req.path === '/api/v1/health' || req.ip === '127.0.0.1';
    }
  });
};

// 一般 API 速率限制
const generalRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 分鐘
  1000, // 每個 IP 最多 1000 次請求
  '請求過於頻繁，請稍後再試'
);

// 認證相關速率限制
const authRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 分鐘
  10, // 每個 IP 最多 10 次認證請求
  '認證請求過於頻繁，請稍後再試'
);

// 支付相關速率限制
const paymentRateLimit = createRateLimit(
  60 * 60 * 1000, // 1 小時
  50, // 每個 IP 最多 50 次支付請求
  '支付請求過於頻繁，請稍後再試'
);

// 安全標頭配置
const helmetOptions = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
};

// 請求日誌中間件
const requestLogger = (req, res, next) => {
  const start = Date.now();

  // 記錄請求開始
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl} - ${req.ip}`);

  // 監聽響應結束
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`);
  });

  next();
};

// 錯誤處理中間件
const errorHandler = (err, req, res, next) => {
  console.error('API Error:', err);

  // 驗證錯誤
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: '輸入資料驗證失敗',
      errors: Object.values(err.errors).map(e => e.message)
    });
  }

  // JWT 錯誤
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: '無效的認證令牌',
      error: 'Invalid token'
    });
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: '認證令牌已過期',
      error: 'Token expired'
    });
  }

  // 資料庫錯誤
  if (err.code === '23505') { // PostgreSQL unique violation
    return res.status(409).json({
      success: false,
      message: '資料已存在',
      error: 'Duplicate entry'
    });
  }

  if (err.code === '23503') { // PostgreSQL foreign key violation
    return res.status(400).json({
      success: false,
      message: '關聯資料不存在',
      error: 'Foreign key violation'
    });
  }

  // CORS 錯誤
  if (err.message.includes('CORS')) {
    return res.status(403).json({
      success: false,
      message: '跨域請求被拒絕',
      error: 'CORS policy violation'
    });
  }

  // 預設錯誤
  const statusCode = err.statusCode || err.status || 500;
  const message = err.message || '伺服器內部錯誤';

  res.status(statusCode).json({
    success: false,
    message,
    error: process.env.NODE_ENV === 'development' ? err.stack : 'Internal server error'
  });
};

module.exports = {
  corsOptions,
  generalRateLimit,
  authRateLimit,
  paymentRateLimit,
  helmetOptions,
  requestLogger,
  errorHandler,
  compression: compression(),
  helmet: helmet(helmetOptions),
  cors: cors(corsOptions)
};