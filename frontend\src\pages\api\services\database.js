// 服務管理 API (資料庫版本)
const path = require('path');
const fs = require('fs');

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();
    
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];
    
    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('服務 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }
    
    console.log('服務 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('服務 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    console.log('服務 API: 收到請求');

    // 連接資料庫
    const db = connectDatabase();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    // 使用 Promise 包裝資料庫操作
    return new Promise((resolve) => {
      // 查詢所有啟用的服務
      const query = `
        SELECT id, name, description, price, billing_cycle, status, category, features, created_at, updated_at
        FROM services 
        WHERE status = 'active'
        ORDER BY created_at ASC
      `;

      db.all(query, [], (err, rows) => {
        db.close();

        if (err) {
          console.error('服務 API: 查詢服務失敗:', err);
          resolve(res.status(500).json({
            success: false,
            error: '查詢服務失敗'
          }));
          return;
        }

        console.log('服務 API: 查詢到服務數量:', rows.length);

        // 處理服務數據
        const services = rows.map(service => {
          let features = [];
          try {
            // 嘗試解析 JSON 格式的 features
            if (service.features) {
              features = JSON.parse(service.features);
            }
          } catch (parseError) {
            console.warn('服務 API: 解析 features 失敗:', parseError);
            features = [];
          }

          return {
            id: service.id,
            name: service.name,
            description: service.description,
            price: service.price,
            billing_cycle: service.billing_cycle,
            status: service.status,
            category: service.category,
            features: features,
            created_at: service.created_at,
            updated_at: service.updated_at
          };
        });

        console.log('服務 API: 返回服務列表');

        resolve(res.status(200).json({
          success: true,
          data: services,
          source: 'database',
          message: '從資料庫查詢服務成功',
          total: services.length
        }));
      });
    });

  } catch (error) {
    console.error('服務 API: 未預期的錯誤:', error);
    
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
