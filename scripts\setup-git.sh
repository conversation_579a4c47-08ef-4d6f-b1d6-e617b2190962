#!/bin/bash

# Git 設置腳本
# 設置 Git 倉庫、遠端連接和 hooks

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔧 設置 Git 倉庫和自動部署..."

# 1. 檢查 Git 是否已初始化
if [ ! -d ".git" ]; then
    log_info "初始化 Git 倉庫..."
    git init
    log_success "Git 倉庫初始化完成"
else
    log_info "Git 倉庫已存在"
fi

# 2. 設置 Git 配置
log_info "設置 Git 配置..."

# 檢查用戶名和郵箱
if [ -z "$(git config user.name)" ]; then
    read -p "請輸入您的 Git 用戶名: " git_username
    git config user.name "$git_username"
fi

if [ -z "$(git config user.email)" ]; then
    read -p "請輸入您的 Git 郵箱: " git_email
    git config user.email "$git_email"
fi

log_success "Git 配置完成"
echo "   用戶名: $(git config user.name)"
echo "   郵箱: $(git config user.email)"

# 3. 設置 Git Hooks
log_info "設置 Git Hooks..."

# 設置 hooks 目錄
git config core.hooksPath .githooks

# 給 hooks 文件執行權限 (在 Windows 上可能不需要)
if [ -f ".githooks/pre-commit" ]; then
    chmod +x .githooks/pre-commit
fi

if [ -f ".githooks/pre-push" ]; then
    chmod +x .githooks/pre-push
fi

log_success "Git Hooks 設置完成"

# 4. 設置遠端倉庫
if ! git remote get-url origin > /dev/null 2>&1; then
    log_warning "未設置遠端倉庫"
    echo ""
    echo "請選擇設置方式:"
    echo "1. 手動輸入 GitHub 倉庫 URL"
    echo "2. 稍後手動設置"
    echo ""
    read -p "請選擇 (1/2): " choice
    
    case $choice in
        1)
            read -p "請輸入 GitHub 倉庫 URL: " repo_url
            git remote add origin "$repo_url"
            log_success "遠端倉庫設置完成: $repo_url"
            ;;
        2)
            log_info "請稍後使用以下命令設置遠端倉庫:"
            echo "git remote add origin https://github.com/your-username/member-management-system.git"
            ;;
        *)
            log_warning "無效選擇，請稍後手動設置遠端倉庫"
            ;;
    esac
else
    log_info "遠端倉庫已設置: $(git remote get-url origin)"
fi

# 5. 創建初始提交
if [ -z "$(git log --oneline 2>/dev/null)" ]; then
    log_info "創建初始提交..."
    
    git add .
    git commit -m "🎉 初始提交: 會員管理系統

✨ 功能特色:
- 👤 會員註冊與管理
- 💰 多元支付系統 (Stripe, PayPal, ECPay)
- 🏦 錢包系統
- 📱 服務訂閱
- 🔐 安全認證與合規 (PCI DSS, GDPR)

🏗️ 技術架構:
- Node.js + Express.js 後端
- React + Next.js 前端
- PostgreSQL + Redis
- Docker 容器化
- 完整測試覆蓋

🚀 CI/CD:
- GitHub Actions 自動化
- 多環境部署
- 安全檢查和測試"
    
    log_success "初始提交完成"
else
    log_info "已存在提交記錄"
fi

# 6. 創建開發分支
if ! git show-ref --verify --quiet refs/heads/develop; then
    log_info "創建 develop 分支..."
    git checkout -b develop
    git checkout main
    log_success "develop 分支創建完成"
fi

# 7. 顯示設置摘要
echo ""
log_success "🎉 Git 設置完成！"
echo ""
echo "📋 設置摘要:"
echo "   Git 用戶: $(git config user.name) <$(git config user.email)>"
echo "   當前分支: $(git branch --show-current)"
echo "   遠端倉庫: $(git remote get-url origin 2>/dev/null || echo '未設置')"
echo "   Git Hooks: 已啟用"
echo ""

# 8. 顯示下一步
echo "🚀 下一步:"
echo ""
echo "1. 如果還未設置 GitHub 倉庫，請:"
echo "   - 在 GitHub 創建新倉庫"
echo "   - 運行: git remote add origin <倉庫URL>"
echo ""
echo "2. 推送代碼到 GitHub:"
echo "   - 推送到 main: ./scripts/deploy.sh production"
echo "   - 推送到 develop: ./scripts/deploy.sh staging"
echo ""
echo "3. 設置 GitHub Actions:"
echo "   - GitHub Actions 配置已包含在 .github/workflows/"
echo "   - 推送後會自動觸發 CI/CD 流程"
echo ""
echo "4. 配置環境變數:"
echo "   - 在 GitHub 倉庫設置中添加 Secrets"
echo "   - 包含資料庫、支付等敏感配置"
echo ""

log_success "設置腳本執行完成"
