import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function RedirectTest() {
  const router = useRouter();
  const [logs, setLogs] = useState([]);
  const [isRedirecting, setIsRedirecting] = useState(false);

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    addLog('頁面載入完成');
  }, []);

  const testRedirect = async (path) => {
    setIsRedirecting(true);
    addLog(`開始測試重定向到: ${path}`);
    
    try {
      await router.push(path);
      addLog(`重定向成功: ${path}`);
    } catch (error) {
      addLog(`重定向失敗: ${error.message}`);
    } finally {
      setIsRedirecting(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const checkAuth = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    addLog(`Token 存在: ${!!token}`);
    addLog(`User 存在: ${!!user}`);
    if (user) {
      try {
        const userData = JSON.parse(user);
        addLog(`用戶名: ${userData.name}`);
      } catch (error) {
        addLog(`用戶數據解析錯誤: ${error.message}`);
      }
    }
  };

  const simulateLogin = () => {
    const mockUser = {
      name: '測試用戶',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      created_at: new Date().toISOString()
    };
    
    localStorage.setItem('token', 'test-token-' + Date.now());
    localStorage.setItem('user', JSON.stringify(mockUser));
    document.cookie = 'access_token=test-token; path=/; max-age=3600';
    
    addLog('模擬登入完成');
    addLog(`保存用戶: ${mockUser.name}`);
  };

  const clearAuth = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('subscriptions');
    document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    addLog('認證數據已清除');
  };

  return (
    <>
      <Head>
        <title>重定向測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">重定向循環測試</h1>
            
            {/* 認證控制 */}
            <div className="mb-8 p-6 bg-blue-50 rounded-lg">
              <h2 className="text-lg font-semibold text-blue-900 mb-4">認證控制</h2>
              <div className="flex flex-wrap gap-4">
                <button
                  onClick={checkAuth}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  🔍 檢查認證狀態
                </button>
                
                <button
                  onClick={simulateLogin}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  🔑 模擬登入
                </button>
                
                <button
                  onClick={clearAuth}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  🗑️ 清除認證
                </button>
              </div>
            </div>

            {/* 重定向測試 */}
            <div className="mb-8 p-6 bg-green-50 rounded-lg">
              <h2 className="text-lg font-semibold text-green-900 mb-4">重定向測試</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <button
                  onClick={() => testRedirect('/minimal-dashboard')}
                  disabled={isRedirecting}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                >
                  測試 /minimal-dashboard
                </button>
                
                <button
                  onClick={() => testRedirect('/dashboard-simple')}
                  disabled={isRedirecting}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  測試 /dashboard-simple
                </button>
                
                <button
                  onClick={() => testRedirect('/subscriptions')}
                  disabled={isRedirecting}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  測試 /subscriptions
                </button>
                
                <button
                  onClick={() => testRedirect('/auth/login')}
                  disabled={isRedirecting}
                  className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50"
                >
                  測試 /auth/login
                </button>
                
                <button
                  onClick={() => testRedirect('/backup-dashboard')}
                  disabled={isRedirecting}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
                >
                  測試 /backup-dashboard
                </button>
                
                <button
                  onClick={() => testRedirect('/analytics')}
                  disabled={isRedirecting}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50"
                >
                  測試 /analytics
                </button>
              </div>
            </div>

            {/* 日誌控制 */}
            <div className="mb-8 flex gap-4">
              <button
                onClick={clearLogs}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🧹 清除日誌
              </button>
              
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
              >
                🔄 重新載入頁面
              </button>
            </div>

            {/* 測試日誌 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">測試日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
                {logs.map((log, index) => (
                  <div key={index} className="mb-1">{log}</div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">尚無日誌記錄</div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-yellow-50 rounded-lg">
              <h3 className="font-semibold text-yellow-900 mb-3">測試說明</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>測試目標：</strong>確認重定向循環問題已解決</p>
                <p><strong>測試步驟：</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>先模擬登入以獲得認證狀態</li>
                  <li>測試各個路由的重定向行為</li>
                  <li>觀察日誌中是否有循環重定向</li>
                  <li>確認最終能正確到達目標頁面</li>
                </ol>
                <p><strong>預期結果：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>/minimal-dashboard → /dashboard-simple (一次重定向)</li>
                  <li>/dashboard-simple → 直接載入</li>
                  <li>/subscriptions → 需要認證，有認證則直接載入</li>
                  <li>/auth/login → 有認證則重定向到 /dashboard-simple</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
