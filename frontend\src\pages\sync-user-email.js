import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function SyncUserEmail() {
  const router = useRouter();
  const [syncResults, setSyncResults] = useState([]);
  const [isSyncing, setIsSyncing] = useState(false);
  const [emailData, setEmailData] = useState(null);

  useEffect(() => {
    checkEmailData();
  }, []);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setSyncResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const checkEmailData = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let tokenData = null;
    let userData = null;

    if (token) {
      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          tokenData = JSON.parse(atob(tokenParts[1]));
        }
      } catch (error) {
        console.error('Token 解析失敗:', error);
      }
    }

    if (userStr) {
      try {
        userData = JSON.parse(userStr);
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setEmailData({
      hasToken: !!token,
      hasUser: !!userData,
      tokenEmail: tokenData?.email,
      userEmail: userData?.email,
      isEmailConsistent: tokenData?.email === userData?.email,
      tokenData: tokenData,
      userData: userData
    });
  };

  const syncUserEmail = async () => {
    setIsSyncing(true);
    setSyncResults([]);

    try {
      addResult('開始', 'info', '開始同步用戶 Email');

      // 步驟 1: 檢查當前數據
      addResult('步驟1', 'info', '檢查當前 Email 數據...');
      
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');

      if (!token || !userStr) {
        addResult('步驟1', 'error', '缺少認證數據');
        return;
      }

      // 解析數據
      let tokenData, userData;
      try {
        const tokenParts = token.split('.');
        tokenData = JSON.parse(atob(tokenParts[1]));
        userData = JSON.parse(userStr);
        
        addResult('步驟1', 'success', '數據解析成功');
        addResult('步驟1', 'info', `Token Email: ${tokenData.email}`);
        addResult('步驟1', 'info', `localStorage Email: ${userData.email}`);
        
        if (tokenData.email === userData.email) {
          addResult('步驟1', 'success', 'Email 已經一致，無需修復');
          return;
        } else {
          addResult('步驟1', 'warning', 'Email 不一致，需要同步');
        }
      } catch (error) {
        addResult('步驟1', 'error', `數據解析失敗: ${error.message}`);
        return;
      }

      // 步驟 2: 同步 Email
      addResult('步驟2', 'info', '同步 Email 到 localStorage...');
      
      const syncedUserData = {
        ...userData,
        email: tokenData.email  // 使用 Token 中的正確 Email
      };

      localStorage.setItem('user', JSON.stringify(syncedUserData));
      
      addResult('步驟2', 'success', 'Email 同步完成');
      addResult('步驟2', 'info', `新 Email: ${syncedUserData.email}`);

      // 步驟 3: 驗證同步結果
      addResult('步驟3', 'info', '驗證同步結果...');
      
      const verifyUserStr = localStorage.getItem('user');
      const verifyUserData = JSON.parse(verifyUserStr);
      
      if (verifyUserData.email === tokenData.email) {
        addResult('步驟3', 'success', 'Email 同步驗證成功');
      } else {
        addResult('步驟3', 'error', 'Email 同步驗證失敗');
        return;
      }

      // 步驟 4: 測試錢包 API
      addResult('步驟4', 'info', '測試錢包 API...');
      
      const walletResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (walletResponse.ok) {
        const walletResult = await walletResponse.json();
        if (walletResult.success) {
          addResult('步驟4', 'success', `錢包 API 正常！餘額: $${walletResult.data.balance}`);
        } else {
          addResult('步驟4', 'error', `錢包 API 業務失敗: ${walletResult.error}`);
        }
      } else {
        const errorResult = await walletResponse.json();
        addResult('步驟4', 'error', `錢包 API HTTP 失敗: ${walletResponse.status} - ${errorResult.error}`);
      }

      addResult('完成', 'success', 'Email 同步完成！');
      
      // 更新顯示
      checkEmailData();

    } catch (error) {
      addResult('錯誤', 'error', '同步過程中出現錯誤: ' + error.message);
    } finally {
      setIsSyncing(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>同步用戶 Email - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">📧 同步用戶 Email</h1>
                <p className="text-gray-600 mt-2">修復 JWT Token 和 localStorage Email 不一致問題</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/fix-user-data')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  🔧 修復用戶數據
                </button>
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={syncUserEmail}
                  disabled={isSyncing}
                  className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 font-medium"
                >
                  {isSyncing ? '同步中...' : '📧 同步 Email'}
                </button>
              </div>
            </div>

            {/* Email 數據狀態 */}
            {emailData && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 Email 數據狀態</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="font-medium text-blue-900 mb-2">🔑 JWT Token Email</h3>
                    <div className="text-sm text-blue-800 space-y-1">
                      <div>存在: {emailData.hasToken ? '✅' : '❌'}</div>
                      <div>Email: {emailData.tokenEmail || 'undefined'}</div>
                      <div>用戶 ID: {emailData.tokenData?.userId || 'undefined'}</div>
                      <div>角色: {emailData.tokenData?.role || 'undefined'}</div>
                    </div>
                  </div>
                  
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="font-medium text-green-900 mb-2">👤 localStorage Email</h3>
                    <div className="text-sm text-green-800 space-y-1">
                      <div>存在: {emailData.hasUser ? '✅' : '❌'}</div>
                      <div>Email: {emailData.userEmail || 'undefined'}</div>
                      <div>姓名: {emailData.userData?.name || 'undefined'}</div>
                      <div>用戶 ID: {emailData.userData?.userId || 'undefined'}</div>
                    </div>
                  </div>
                </div>
                
                <div className={`mt-4 p-4 rounded-lg border ${
                  emailData.isEmailConsistent ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}>
                  <div className={`font-medium ${
                    emailData.isEmailConsistent ? 'text-green-900' : 'text-red-900'
                  }`}>
                    Email 一致性: {emailData.isEmailConsistent ? '✅ 一致' : '❌ 不一致'}
                  </div>
                  {!emailData.isEmailConsistent && (
                    <div className="text-sm text-red-800 mt-1">
                      Token Email ({emailData.tokenEmail}) 與 localStorage Email ({emailData.userEmail}) 不一致
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 同步結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 同步結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {syncResults.length > 0 ? (
                  <div className="space-y-3">
                    {syncResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「同步 Email」來修復 Email 不一致問題
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-orange-50 border border-orange-200 rounded-lg">
              <h3 className="font-medium text-orange-900 mb-3">📧 Email 不一致問題</h3>
              <div className="text-sm text-orange-800 space-y-2">
                <div><strong>發現問題:</strong> JWT Token 中的 Email 與 localStorage 中的 Email 不一致</div>
                <div><strong>Token Email:</strong> <EMAIL> (正確)</div>
                <div><strong>localStorage Email:</strong> <EMAIL> (錯誤)</div>
                <div><strong>影響:</strong> 可能導致 API 認證失敗或數據不一致</div>
                <div><strong>解決方案:</strong> 將 localStorage 中的 Email 同步為 Token 中的正確 Email</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
