import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function ServiceManagement() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [services, setServices] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedService, setSelectedService] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isSaving, setIsSaving] = useState(false);
  const [selectedServices, setSelectedServices] = useState([]);
  const [showBatchActions, setShowBatchActions] = useState(false);

  // 服務表單數據
  const [serviceForm, setServiceForm] = useState({
    name: '',
    description: '',
    price: '',
    billingCycle: 'monthly',
    category: '',
    features: [''],
    status: 'active',
    popular: false,
    maxUsers: '',
    apiLimit: '',
    storageLimit: '',
    supportLevel: 'basic'
  });

  // 模擬服務數據
  const mockServices = [
    {
      id: 1,
      name: 'AI 文字生成服務',
      description: '強大的 AI 文字生成工具，支援多種語言和格式',
      price: 299,
      billingCycle: 'monthly',
      category: 'AI 服務',
      features: ['無限文字生成', '多語言支援', '模板庫', '24/7 客服'],
      status: 'active',
      popular: true,
      subscribers: 1250,
      revenue: 373750,
      maxUsers: 1,
      apiLimit: 50000,
      storageLimit: '10GB',
      supportLevel: 'premium',
      createdAt: '2025-01-15',
      updatedAt: '2025-06-20'
    },
    {
      id: 2,
      name: '圖片處理服務',
      description: '專業的圖片編輯和處理工具',
      price: 199,
      billingCycle: 'monthly',
      category: '媒體處理',
      features: ['批量處理', '格式轉換', '智能壓縮', '雲端存儲'],
      status: 'active',
      popular: false,
      subscribers: 890,
      revenue: 177110,
      maxUsers: 3,
      apiLimit: 1000,
      storageLimit: '50GB',
      supportLevel: 'standard',
      createdAt: '2025-02-01',
      updatedAt: '2025-06-18'
    },
    {
      id: 3,
      name: '數據分析服務',
      description: '深度數據分析和可視化工具',
      price: 499,
      billingCycle: 'monthly',
      category: '數據分析',
      features: ['實時分析', '自定義報表', '數據可視化', 'API 接入'],
      status: 'inactive',
      popular: false,
      subscribers: 320,
      revenue: 159680,
      maxUsers: 5,
      apiLimit: 100,
      storageLimit: '100GB',
      supportLevel: 'premium',
      createdAt: '2025-03-10',
      updatedAt: '2025-06-15'
    },
    {
      id: 4,
      name: '語音轉文字服務',
      description: '高精度語音識別和轉換服務',
      price: 149,
      billingCycle: 'monthly',
      category: 'AI 服務',
      features: ['多語言識別', '實時轉換', '批量處理', '高精度識別'],
      status: 'active',
      popular: false,
      subscribers: 560,
      revenue: 83440,
      maxUsers: 1,
      apiLimit: 10000,
      storageLimit: '5GB',
      supportLevel: 'basic',
      createdAt: '2025-04-05',
      updatedAt: '2025-06-22'
    },
    {
      id: 5,
      name: '雲端存儲服務',
      description: '安全可靠的雲端存儲解決方案',
      price: 99,
      billingCycle: 'monthly',
      category: '存儲服務',
      features: ['無限存儲', '自動備份', '版本控制', '分享功能'],
      status: 'active',
      popular: true,
      subscribers: 2100,
      revenue: 207900,
      maxUsers: 10,
      apiLimit: 0,
      storageLimit: '無限',
      supportLevel: 'standard',
      createdAt: '2025-01-20',
      updatedAt: '2025-06-25'
    },
    {
      id: 6,
      name: '企業級 API 服務',
      description: '為企業提供的高級 API 接入服務',
      price: 999,
      billingCycle: 'monthly',
      category: '企業服務',
      features: ['無限 API 調用', '專屬支援', '自定義功能', 'SLA 保證'],
      status: 'active',
      popular: false,
      subscribers: 85,
      revenue: 84915,
      maxUsers: 50,
      apiLimit: 0,
      storageLimit: '1TB',
      supportLevel: 'enterprise',
      createdAt: '2025-05-01',
      updatedAt: '2025-06-24'
    }
  ];

  useEffect(() => {
    // 檢查認證和管理員權限
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=' + encodeURIComponent('/admin/services'));
      return;
    }

    // 載入用戶數據
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        
        // 檢查管理員權限 (這裡簡化處理，實際應該從後端驗證)
        if (userData.email !== '<EMAIL>') {
          alert('您沒有管理員權限');
          router.push('/minimal-dashboard');
          return;
        }
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    // 載入服務數據
    loadServices();
  }, [router]);

  // API 調用函數
  const loadServices = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/services');
      const result = await response.json();

      if (result.success) {
        // 合併 API 數據和內存模擬數據
        const apiServices = result.data || [];
        const mergedServices = mergeServices(apiServices, mockServices);
        setServices(mergedServices);
      } else {
        console.error('載入服務失敗:', result.error);
        // 如果 API 失敗，使用模擬數據
        setServices(mockServices);
      }
    } catch (error) {
      console.error('載入服務失敗:', error);
      // 如果 API 失敗，使用模擬數據
      setServices(mockServices);
    } finally {
      setIsLoading(false);
    }
  };

  // 合併服務數據的函數
  const mergeServices = (apiServices, mockServices) => {
    // 創建一個 Map 來追蹤已存在的服務 ID
    const serviceMap = new Map();

    // 首先添加 API 中的服務
    apiServices.forEach(service => {
      serviceMap.set(service.id, service);
    });

    // 然後添加模擬數據中不存在的服務
    mockServices.forEach(mockService => {
      if (!serviceMap.has(mockService.id)) {
        serviceMap.set(mockService.id, {
          ...mockService,
          isFromMock: true // 標記來源
        });
      }
    });

    // 轉換為數組並按 ID 排序
    return Array.from(serviceMap.values()).sort((a, b) => a.id - b.id);
  };

  // 初始化服務數據
  const initializeServices = async () => {
    try {
      const confirmInit = window.confirm(
        '確定要初始化服務數據嗎？\n\n這將會：\n- 確保所有預設服務都存在\n- 合併現有數據和模擬數據\n- 不會覆蓋已有的修改'
      );

      if (!confirmInit) return;

      setIsLoading(true);
      const response = await fetch('/api/services/init', {
        method: 'POST',
      });

      const result = await response.json();

      if (result.success) {
        alert(`✅ 初始化成功！\n\n總服務數：${result.data.totalServices}\n預設服務：${result.data.addedServices}\n現有服務：${result.data.existingServices}`);
        // 重新載入服務列表
        await loadServices();
      } else {
        throw new Error(result.error || '初始化失敗');
      }
    } catch (error) {
      console.error('初始化失敗:', error);
      alert('❌ 初始化失敗，請稍後再試');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setServiceForm({
      name: '',
      description: '',
      price: '',
      billingCycle: 'monthly',
      category: '',
      features: [''],
      status: 'active',
      popular: false,
      maxUsers: '',
      apiLimit: '',
      storageLimit: '',
      supportLevel: 'basic'
    });
  };

  const handleAddService = () => {
    resetForm();
    setShowAddModal(true);
  };

  const handleEditService = (service) => {
    setSelectedService(service);
    setServiceForm({
      name: service.name,
      description: service.description,
      price: service.price.toString(),
      billingCycle: service.billingCycle,
      category: service.category,
      features: service.features,
      status: service.status,
      popular: service.popular,
      maxUsers: service.maxUsers.toString(),
      apiLimit: service.apiLimit.toString(),
      storageLimit: service.storageLimit,
      supportLevel: service.supportLevel
    });
    setShowEditModal(true);
  };

  const handleDeleteService = async (service) => {
    // 檢查是否有訂閱用戶
    if (service.subscribers > 0) {
      const confirmDelete = window.confirm(
        `⚠️ 警告：服務「${service.name}」目前有 ${service.subscribers} 個訂閱用戶。\n\n刪除此服務將會：\n- 取消所有用戶的訂閱\n- 無法恢復服務數據\n\n確定要繼續刪除嗎？`
      );
      if (!confirmDelete) return;
    }

    const confirmDelete = window.confirm(
      `確定要刪除服務「${service.name}」嗎？\n\n此操作無法撤回！`
    );

    if (confirmDelete) {
      try {
        const response = await fetch(`/api/services/${service.id}`, {
          method: 'DELETE',
        });

        const result = await response.json();

        if (result.success) {
          // 重新載入服務列表
          await loadServices();
          alert('✅ 服務已成功刪除');
        } else {
          throw new Error(result.error || '刪除失敗');
        }
      } catch (error) {
        console.error('刪除失敗:', error);
        alert('❌ 刪除失敗，請稍後再試');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // 表單驗證
      if (!serviceForm.name || !serviceForm.description || !serviceForm.price) {
        alert('請填寫必要欄位：服務名稱、描述和價格');
        setIsSaving(false);
        return;
      }

      if (!serviceForm.category) {
        alert('請選擇服務類別');
        setIsSaving(false);
        return;
      }

      if (parseFloat(serviceForm.price) <= 0) {
        alert('價格必須大於 0');
        setIsSaving(false);
        return;
      }

      const serviceData = {
        ...serviceForm,
        price: parseFloat(serviceForm.price),
        maxUsers: parseInt(serviceForm.maxUsers) || 1,
        apiLimit: parseInt(serviceForm.apiLimit) || 0,
        features: serviceForm.features.filter(f => f.trim() !== '')
      };

      if (showEditModal && selectedService) {
        // 更新服務
        const response = await fetch(`/api/services/${selectedService.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(serviceData),
        });

        const result = await response.json();

        if (result.success) {
          // 重新載入服務列表
          await loadServices();
          alert('✅ 服務更新成功！');
        } else {
          throw new Error(result.error || '更新失敗');
        }
      } else {
        // 新增服務
        const response = await fetch('/api/services', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(serviceData),
        });

        const result = await response.json();

        if (result.success) {
          // 重新載入服務列表
          await loadServices();
          alert('✅ 服務新增成功！');
        } else {
          throw new Error(result.error || '新增失敗');
        }
      }

      setShowAddModal(false);
      setShowEditModal(false);
      resetForm();
    } catch (error) {
      console.error('提交失敗:', error);
      alert('❌ 操作失敗，請稍後再試');
    } finally {
      setIsSaving(false);
    }
  };

  const addFeature = () => {
    setServiceForm({
      ...serviceForm,
      features: [...serviceForm.features, '']
    });
  };

  const removeFeature = (index) => {
    const newFeatures = serviceForm.features.filter((_, i) => i !== index);
    setServiceForm({
      ...serviceForm,
      features: newFeatures.length > 0 ? newFeatures : ['']
    });
  };

  const updateFeature = (index, value) => {
    const newFeatures = [...serviceForm.features];
    newFeatures[index] = value;
    setServiceForm({
      ...serviceForm,
      features: newFeatures
    });
  };

  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || service.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return '啟用';
      case 'inactive': return '停用';
      case 'draft': return '草稿';
      default: return '未知';
    }
  };

  const getSupportLevelText = (level) => {
    switch (level) {
      case 'basic': return '基礎';
      case 'standard': return '標準';
      case 'premium': return '高級';
      case 'enterprise': return '企業';
      default: return '基礎';
    }
  };

  // 批量操作處理
  const handleSelectService = (serviceId) => {
    setSelectedServices(prev => {
      if (prev.includes(serviceId)) {
        return prev.filter(id => id !== serviceId);
      } else {
        return [...prev, serviceId];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedServices.length === filteredServices.length) {
      setSelectedServices([]);
    } else {
      setSelectedServices(filteredServices.map(s => s.id));
    }
  };

  const handleBatchStatusUpdate = async (status) => {
    if (selectedServices.length === 0) {
      alert('請先選擇要操作的服務');
      return;
    }

    const confirmUpdate = window.confirm(
      `確定要將 ${selectedServices.length} 個服務的狀態更改為「${getStatusText(status)}」嗎？`
    );

    if (confirmUpdate) {
      try {
        // 模擬 API 延遲
        await new Promise(resolve => setTimeout(resolve, 800));

        const updatedServices = services.map(service => {
          if (selectedServices.includes(service.id)) {
            return {
              ...service,
              status: status,
              updatedAt: new Date().toISOString().split('T')[0]
            };
          }
          return service;
        });

        setServices(updatedServices);
        setSelectedServices([]);
        setShowBatchActions(false);
        alert(`✅ 已成功更新 ${selectedServices.length} 個服務的狀態`);
      } catch (error) {
        console.error('批量更新失敗:', error);
        alert('❌ 批量更新失敗，請稍後再試');
      }
    }
  };

  const handleBatchDelete = async () => {
    if (selectedServices.length === 0) {
      alert('請先選擇要刪除的服務');
      return;
    }

    const selectedServiceNames = services
      .filter(s => selectedServices.includes(s.id))
      .map(s => s.name)
      .join('、');

    const confirmDelete = window.confirm(
      `⚠️ 確定要刪除以下 ${selectedServices.length} 個服務嗎？\n\n${selectedServiceNames}\n\n此操作無法撤回！`
    );

    if (confirmDelete) {
      try {
        // 模擬 API 延遲
        await new Promise(resolve => setTimeout(resolve, 1000));

        const updatedServices = services.filter(s => !selectedServices.includes(s.id));
        setServices(updatedServices);
        setSelectedServices([]);
        setShowBatchActions(false);
        alert(`✅ 已成功刪除 ${selectedServices.length} 個服務`);
      } catch (error) {
        console.error('批量刪除失敗:', error);
        alert('❌ 批量刪除失敗，請稍後再試');
      }
    }
  };

  // 數據導出功能
  const handleExportData = async (format = 'json') => {
    try {
      const exportData = {
        exportDate: new Date().toISOString(),
        totalServices: services.length,
        services: services
      };

      let content, mimeType, fileName;

      if (format === 'csv') {
        const csvHeaders = 'ID,名稱,描述,價格,計費週期,類別,狀態,訂閱數,收入,創建時間,更新時間\n';
        const csvRows = services.map(service =>
          `${service.id},"${service.name}","${service.description}",${service.price},${service.billingCycle},"${service.category}",${service.status},${service.subscribers},${service.revenue},${service.createdAt},${service.updatedAt}`
        ).join('\n');
        content = csvHeaders + csvRows;
        mimeType = 'text/csv';
        fileName = `services_${new Date().toISOString().split('T')[0]}.csv`;
      } else {
        content = JSON.stringify(exportData, null, 2);
        mimeType = 'application/json';
        fileName = `services_${new Date().toISOString().split('T')[0]}.json`;
      }

      // 創建並下載文件
      const blob = new Blob([content], { type: mimeType });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      alert(`✅ 數據已導出：${fileName}`);
    } catch (error) {
      console.error('導出失敗:', error);
      alert('❌ 導出失敗，請稍後再試');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入服務管理中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>服務管理 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回儀表板
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">服務管理</h1>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <button
                  onClick={initializeServices}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  title="初始化服務數據，確保所有預設服務都存在"
                >
                  🔄 初始化數據
                </button>
                <div className="relative">
                  <button
                    onClick={() => document.getElementById('exportDropdown').classList.toggle('hidden')}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    📊 導出數據
                  </button>
                  <div id="exportDropdown" className="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                    <button
                      onClick={() => {
                        handleExportData('json');
                        document.getElementById('exportDropdown').classList.add('hidden');
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      導出為 JSON
                    </button>
                    <button
                      onClick={() => {
                        handleExportData('csv');
                        document.getElementById('exportDropdown').classList.add('hidden');
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      導出為 CSV
                    </button>
                  </div>
                </div>
                <button
                  onClick={handleAddService}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  ➕ 新增服務
                </button>
                <span className="text-sm text-gray-600">管理員：{user?.name}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 統計卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <span className="text-2xl">📦</span>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm text-gray-500">總服務數</p>
                    <p className="text-2xl font-bold text-gray-900">{services.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <span className="text-2xl">✅</span>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm text-gray-500">啟用服務</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {services.filter(s => s.status === 'active').length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <span className="text-2xl">👥</span>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm text-gray-500">總訂閱數</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {services.reduce((sum, s) => sum + s.subscribers, 0).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-yellow-100 rounded-lg">
                    <span className="text-2xl">💰</span>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm text-gray-500">總收入</p>
                    <p className="text-2xl font-bold text-gray-900">
                      NT$ {services.reduce((sum, s) => sum + s.revenue, 0).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 搜尋和篩選 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex flex-col space-y-4">
                {/* 搜尋和篩選行 */}
                <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                  <div className="flex-1 max-w-md">
                    <input
                      type="text"
                      placeholder="搜尋服務名稱或類別..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div className="flex space-x-4">
                    <select
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value)}
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">所有狀態</option>
                      <option value="active">啟用</option>
                      <option value="inactive">停用</option>
                      <option value="draft">草稿</option>
                    </select>

                    {selectedServices.length > 0 && (
                      <button
                        onClick={() => setShowBatchActions(!showBatchActions)}
                        className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                      >
                        批量操作 ({selectedServices.length})
                      </button>
                    )}
                  </div>
                </div>

                {/* 批量操作面板 */}
                {showBatchActions && selectedServices.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="border-t border-gray-200 pt-4"
                  >
                    <div className="flex flex-wrap gap-2">
                      <button
                        onClick={() => handleBatchStatusUpdate('active')}
                        className="px-3 py-1 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors text-sm"
                      >
                        批量啟用
                      </button>
                      <button
                        onClick={() => handleBatchStatusUpdate('inactive')}
                        className="px-3 py-1 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors text-sm"
                      >
                        批量停用
                      </button>
                      <button
                        onClick={() => handleBatchStatusUpdate('draft')}
                        className="px-3 py-1 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                      >
                        設為草稿
                      </button>
                      <button
                        onClick={handleBatchDelete}
                        className="px-3 py-1 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
                      >
                        批量刪除
                      </button>
                      <button
                        onClick={() => {
                          setSelectedServices([]);
                          setShowBatchActions(false);
                        }}
                        className="px-3 py-1 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
                      >
                        取消選擇
                      </button>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>

            {/* 服務列表 */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">
                  服務列表 ({filteredServices.length})
                </h2>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input
                          type="checkbox"
                          checked={selectedServices.length === filteredServices.length && filteredServices.length > 0}
                          onChange={handleSelectAll}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        服務資訊
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        價格
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        訂閱數
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        收入
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        狀態
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredServices.map((service, index) => (
                      <motion.tr
                        key={service.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className="hover:bg-gray-50"
                      >
                        <td className="px-6 py-4">
                          <input
                            type="checkbox"
                            checked={selectedServices.includes(service.id)}
                            onChange={() => handleSelectService(service.id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              {service.popular && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 mr-2">
                                  🔥 熱門
                                </span>
                              )}
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {service.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {service.category}
                              </div>
                              <div className="text-xs text-gray-400 mt-1">
                                {service.description.length > 50
                                  ? service.description.substring(0, 50) + '...'
                                  : service.description
                                }
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            NT$ {service.price.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-500">
                            {service.billingCycle === 'monthly' ? '每月' : '每年'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {service.subscribers.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-500">
                            用戶
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            NT$ {service.revenue.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-500">
                            總收入
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(service.status)}`}>
                            {getStatusText(service.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEditService(service)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              編輯
                            </button>
                            <button
                              onClick={() => handleDeleteService(service)}
                              className="text-red-600 hover:text-red-900"
                            >
                              刪除
                            </button>
                          </div>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </main>

        {/* 新增服務模態框 */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">新增服務</h2>
              </div>

              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 基本資訊 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">基本資訊</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服務名稱 *
                    </label>
                    <input
                      type="text"
                      value={serviceForm.name}
                      onChange={(e) => setServiceForm({...serviceForm, name: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服務類別 *
                    </label>
                    <select
                      value={serviceForm.category}
                      onChange={(e) => setServiceForm({...serviceForm, category: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">請選擇類別</option>
                      <option value="AI 服務">AI 服務</option>
                      <option value="媒體處理">媒體處理</option>
                      <option value="數據分析">數據分析</option>
                      <option value="存儲服務">存儲服務</option>
                      <option value="企業服務">企業服務</option>
                      <option value="開發工具">開發工具</option>
                    </select>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服務描述 *
                    </label>
                    <textarea
                      value={serviceForm.description}
                      onChange={(e) => setServiceForm({...serviceForm, description: e.target.value})}
                      rows={3}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  {/* 價格設定 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">價格設定</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      價格 (NT$) *
                    </label>
                    <input
                      type="number"
                      value={serviceForm.price}
                      onChange={(e) => setServiceForm({...serviceForm, price: e.target.value})}
                      min="0"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      計費週期
                    </label>
                    <select
                      value={serviceForm.billingCycle}
                      onChange={(e) => setServiceForm({...serviceForm, billingCycle: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="monthly">每月</option>
                      <option value="yearly">每年</option>
                    </select>
                  </div>

                  {/* 服務限制 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">服務限制</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      最大用戶數
                    </label>
                    <input
                      type="number"
                      value={serviceForm.maxUsers}
                      onChange={(e) => setServiceForm({...serviceForm, maxUsers: e.target.value})}
                      min="1"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API 調用限制
                    </label>
                    <input
                      type="number"
                      value={serviceForm.apiLimit}
                      onChange={(e) => setServiceForm({...serviceForm, apiLimit: e.target.value})}
                      min="0"
                      placeholder="0 表示無限制"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      存儲限制
                    </label>
                    <input
                      type="text"
                      value={serviceForm.storageLimit}
                      onChange={(e) => setServiceForm({...serviceForm, storageLimit: e.target.value})}
                      placeholder="例如: 10GB, 無限"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      支援等級
                    </label>
                    <select
                      value={serviceForm.supportLevel}
                      onChange={(e) => setServiceForm({...serviceForm, supportLevel: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="basic">基礎</option>
                      <option value="standard">標準</option>
                      <option value="premium">高級</option>
                      <option value="enterprise">企業</option>
                    </select>
                  </div>

                  {/* 服務功能 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">服務功能</h3>
                    <div className="space-y-3">
                      {serviceForm.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={feature}
                            onChange={(e) => updateFeature(index, e.target.value)}
                            placeholder="輸入功能描述"
                            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <button
                            type="button"
                            onClick={() => removeFeature(index)}
                            className="px-3 py-2 text-red-600 hover:text-red-800"
                          >
                            移除
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={addFeature}
                        className="px-4 py-2 text-blue-600 hover:text-blue-800"
                      >
                        + 新增功能
                      </button>
                    </div>
                  </div>

                  {/* 其他設定 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">其他設定</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服務狀態
                    </label>
                    <select
                      value={serviceForm.status}
                      onChange={(e) => setServiceForm({...serviceForm, status: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="active">啟用</option>
                      <option value="inactive">停用</option>
                      <option value="draft">草稿</option>
                    </select>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="popular"
                      checked={serviceForm.popular}
                      onChange={(e) => setServiceForm({...serviceForm, popular: e.target.checked})}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="popular" className="ml-2 block text-sm text-gray-900">
                      標記為熱門服務
                    </label>
                  </div>
                </div>

                {/* 按鈕 */}
                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isSaving}
                    className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                      isSaving
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                  >
                    {isSaving ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        新增中...
                      </div>
                    ) : (
                      '新增服務'
                    )}
                  </button>
                </div>
              </form>
            </motion.div>
          </div>
        )}

        {/* 編輯服務模態框 */}
        {showEditModal && selectedService && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">編輯服務 - {selectedService.name}</h2>
              </div>

              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 基本資訊 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">基本資訊</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服務名稱 *
                    </label>
                    <input
                      type="text"
                      value={serviceForm.name}
                      onChange={(e) => setServiceForm({...serviceForm, name: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服務類別 *
                    </label>
                    <select
                      value={serviceForm.category}
                      onChange={(e) => setServiceForm({...serviceForm, category: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">請選擇類別</option>
                      <option value="AI 服務">AI 服務</option>
                      <option value="媒體處理">媒體處理</option>
                      <option value="數據分析">數據分析</option>
                      <option value="存儲服務">存儲服務</option>
                      <option value="企業服務">企業服務</option>
                      <option value="開發工具">開發工具</option>
                    </select>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服務描述 *
                    </label>
                    <textarea
                      value={serviceForm.description}
                      onChange={(e) => setServiceForm({...serviceForm, description: e.target.value})}
                      rows={3}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  {/* 價格設定 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">價格設定</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      價格 (NT$) *
                    </label>
                    <input
                      type="number"
                      value={serviceForm.price}
                      onChange={(e) => setServiceForm({...serviceForm, price: e.target.value})}
                      min="0"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      計費週期
                    </label>
                    <select
                      value={serviceForm.billingCycle}
                      onChange={(e) => setServiceForm({...serviceForm, billingCycle: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="monthly">每月</option>
                      <option value="yearly">每年</option>
                    </select>
                  </div>

                  {/* 服務限制 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">服務限制</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      最大用戶數
                    </label>
                    <input
                      type="number"
                      value={serviceForm.maxUsers}
                      onChange={(e) => setServiceForm({...serviceForm, maxUsers: e.target.value})}
                      min="1"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API 調用限制
                    </label>
                    <input
                      type="number"
                      value={serviceForm.apiLimit}
                      onChange={(e) => setServiceForm({...serviceForm, apiLimit: e.target.value})}
                      min="0"
                      placeholder="0 表示無限制"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      存儲限制
                    </label>
                    <input
                      type="text"
                      value={serviceForm.storageLimit}
                      onChange={(e) => setServiceForm({...serviceForm, storageLimit: e.target.value})}
                      placeholder="例如: 10GB, 無限"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      支援等級
                    </label>
                    <select
                      value={serviceForm.supportLevel}
                      onChange={(e) => setServiceForm({...serviceForm, supportLevel: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="basic">基礎</option>
                      <option value="standard">標準</option>
                      <option value="premium">高級</option>
                      <option value="enterprise">企業</option>
                    </select>
                  </div>

                  {/* 服務功能 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">服務功能</h3>
                    <div className="space-y-3">
                      {serviceForm.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={feature}
                            onChange={(e) => updateFeature(index, e.target.value)}
                            placeholder="輸入功能描述"
                            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <button
                            type="button"
                            onClick={() => removeFeature(index)}
                            className="px-3 py-2 text-red-600 hover:text-red-800"
                          >
                            移除
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={addFeature}
                        className="px-4 py-2 text-blue-600 hover:text-blue-800"
                      >
                        + 新增功能
                      </button>
                    </div>
                  </div>

                  {/* 其他設定 */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">其他設定</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服務狀態
                    </label>
                    <select
                      value={serviceForm.status}
                      onChange={(e) => setServiceForm({...serviceForm, status: e.target.value})}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="active">啟用</option>
                      <option value="inactive">停用</option>
                      <option value="draft">草稿</option>
                    </select>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="editPopular"
                      checked={serviceForm.popular}
                      onChange={(e) => setServiceForm({...serviceForm, popular: e.target.checked})}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="editPopular" className="ml-2 block text-sm text-gray-900">
                      標記為熱門服務
                    </label>
                  </div>

                  {/* 統計信息 (只讀) */}
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">統計信息</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      當前訂閱數
                    </label>
                    <input
                      type="text"
                      value={selectedService.subscribers.toLocaleString()}
                      readOnly
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      總收入
                    </label>
                    <input
                      type="text"
                      value={`NT$ ${selectedService.revenue.toLocaleString()}`}
                      readOnly
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      創建時間
                    </label>
                    <input
                      type="text"
                      value={selectedService.createdAt}
                      readOnly
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      最後更新
                    </label>
                    <input
                      type="text"
                      value={selectedService.updatedAt}
                      readOnly
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                  </div>
                </div>

                {/* 按鈕 */}
                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowEditModal(false)}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isSaving}
                    className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                      isSaving
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                  >
                    {isSaving ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        更新中...
                      </div>
                    ) : (
                      '更新服務'
                    )}
                  </button>
                </div>
              </form>
            </motion.div>
          </div>
        )}
      </div>
    </>
  );
}
