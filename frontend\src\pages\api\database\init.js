// 資料庫初始化 API
const jwt = require('jsonwebtoken');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export default async function handler(req, res) {
  try {
    console.log('資料庫初始化 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    // 驗證認證
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('資料庫初始化 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    // 檢查管理員權限
    if (decoded.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: '需要管理員權限'
      });
    }

    const { force_recreate = false } = req.body;

    console.log('資料庫初始化 API: 開始初始化', { force_recreate });

    // 使用資料庫初始化函數
    const { initializeDatabase } = require('../../../../lib/database/init');
    
    try {
      await initializeDatabase();
      
      console.log('資料庫初始化 API: 初始化成功');

      return res.status(200).json({
        success: true,
        message: '資料庫初始化成功',
        timestamp: new Date().toISOString()
      });

    } catch (initError) {
      console.error('資料庫初始化 API: 初始化失敗:', initError);
      
      return res.status(500).json({
        success: false,
        error: '資料庫初始化失敗: ' + initError.message,
        details: initError.message
      });
    }

  } catch (error) {
    console.error('資料庫初始化 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
