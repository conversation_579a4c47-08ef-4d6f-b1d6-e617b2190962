// 會員服務關聯模型 - Frontend 版本
const { getDatabase, dbRun, dbGet, dbAll } = require('../init');
const Service = require('./Service');

class MemberService {
  constructor(data = {}) {
    this.id = data.id || data.subscription_id;
    this.member_id = data.member_id;
    this.service_id = data.service_id;
    this.status = data.status || data.subscription_status || 'active';
    this.subscribed_at = data.subscribed_at;
    this.next_billing_date = data.next_billing_date;
    this.cancelled_at = data.cancelled_at;
    this.usage_data = data.usage_data;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    
    // 關聯數據
    this.member_name = data.member_name;
    this.member_email = data.member_email;
    this.service_name = data.service_name;
    this.service_description = data.service_description;
    this.service_price = data.service_price;
    this.billing_cycle = data.billing_cycle;
    this.category = data.category;
  }

  // 獲取會員的所有訂閱
  static async findByMemberId(memberId, options = {}) {
    try {
      const db = await getDatabase();
      
      // 檢查是否有 member_service_details 視圖
      let sql = `
        SELECT 
          ms.id as subscription_id,
          ms.member_id,
          ms.service_id,
          ms.status as subscription_status,
          ms.subscribed_at,
          ms.next_billing_date,
          ms.cancelled_at,
          ms.usage_data,
          ms.created_at,
          ms.updated_at,
          s.name as service_name,
          s.description as service_description,
          s.price as service_price,
          s.billing_cycle,
          s.category
        FROM member_services ms
        JOIN services s ON ms.service_id = s.id
        WHERE ms.member_id = ?
      `;
      
      const params = [memberId];

      // 狀態篩選
      if (options.status) {
        sql += ' AND ms.status = ?';
        params.push(options.status);
      }

      // 排序
      sql += ' ORDER BY ms.subscribed_at DESC';

      const rows = await dbAll(db, sql, params);
      return rows.map(row => {
        // 解析 usage_data
        if (row.usage_data && typeof row.usage_data === 'string') {
          try {
            row.usage_data = JSON.parse(row.usage_data);
          } catch (e) {
            row.usage_data = {};
          }
        }
        return new MemberService(row);
      });
    } catch (error) {
      console.error('獲取會員訂閱失敗:', error);
      throw error;
    }
  }

  // 檢查會員是否已訂閱某服務
  static async isSubscribed(memberId, serviceId) {
    try {
      const db = await getDatabase();
      const result = await dbGet(db, 
        'SELECT id FROM member_services WHERE member_id = ? AND service_id = ? AND status = "active"',
        [memberId, serviceId]
      );
      return !!result;
    } catch (error) {
      console.error('檢查訂閱狀態失敗:', error);
      throw error;
    }
  }

  // 獲取會員可用的服務（未訂閱的啟用服務）
  static async getAvailableServices(memberId) {
    try {
      const db = await getDatabase();
      const sql = `
        SELECT s.* FROM services s
        WHERE s.status = 'active'
        AND s.id NOT IN (
          SELECT service_id FROM member_services 
          WHERE member_id = ? AND status = 'active'
        )
        ORDER BY s.popular DESC, s.name ASC
      `;
      
      const rows = await dbAll(db, sql, [memberId]);
      return rows.map(row => {
        // 解析 features
        if (row.features && typeof row.features === 'string') {
          try {
            row.features = JSON.parse(row.features);
          } catch (e) {
            row.features = [];
          }
        }
        return row;
      });
    } catch (error) {
      console.error('獲取可用服務失敗:', error);
      throw error;
    }
  }

  // 創建新訂閱
  static async subscribe(memberId, serviceId, options = {}) {
    try {
      const db = await getDatabase();
      
      // 檢查是否已經訂閱
      const existing = await MemberService.isSubscribed(memberId, serviceId);
      if (existing) {
        throw new Error('已經訂閱此服務');
      }

      // 檢查服務是否存在且啟用
      const service = await Service.findById(serviceId);
      if (!service) {
        throw new Error('服務不存在');
      }
      if (service.status !== 'active') {
        throw new Error('服務未啟用');
      }

      // 計算下次計費日期
      const nextBillingDate = new Date();
      if (service.billing_cycle === 'yearly') {
        nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
      } else {
        nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
      }

      // 初始化使用數據
      const usageData = {
        used: 0,
        limit: service.api_limit || 10000,
        unit: '次調用',
        last_reset: new Date().toISOString(),
        ...options.usage_data
      };

      const sql = `
        INSERT INTO member_services (
          member_id, service_id, status, next_billing_date, usage_data
        ) VALUES (?, ?, ?, ?, ?)
      `;

      const result = await dbRun(db, sql, [
        memberId,
        serviceId,
        'active',
        nextBillingDate.toISOString().split('T')[0],
        JSON.stringify(usageData)
      ]);

      return await MemberService.findById(result.id);
    } catch (error) {
      console.error('訂閱服務失敗:', error);
      throw error;
    }
  }

  // 根據 ID 獲取訂閱
  static async findById(id) {
    try {
      const db = await getDatabase();
      const sql = `
        SELECT 
          ms.id as subscription_id,
          ms.member_id,
          ms.service_id,
          ms.status as subscription_status,
          ms.subscribed_at,
          ms.next_billing_date,
          ms.cancelled_at,
          ms.usage_data,
          ms.created_at,
          ms.updated_at,
          s.name as service_name,
          s.description as service_description,
          s.price as service_price,
          s.billing_cycle,
          s.category
        FROM member_services ms
        JOIN services s ON ms.service_id = s.id
        WHERE ms.id = ?
      `;
      
      const row = await dbGet(db, sql, [id]);
      
      if (!row) {
        return null;
      }

      // 解析 usage_data
      if (row.usage_data && typeof row.usage_data === 'string') {
        try {
          row.usage_data = JSON.parse(row.usage_data);
        } catch (e) {
          row.usage_data = {};
        }
      }

      return new MemberService(row);
    } catch (error) {
      console.error('獲取訂閱失敗:', error);
      throw error;
    }
  }

  // 取消訂閱
  async cancel() {
    try {
      const db = await getDatabase();
      
      const sql = `
        UPDATE member_services 
        SET status = 'cancelled', cancelled_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `;

      await dbRun(db, sql, [this.id]);

      // 更新本地狀態
      this.status = 'cancelled';
      this.cancelled_at = new Date().toISOString();

      return this;
    } catch (error) {
      console.error('取消訂閱失敗:', error);
      throw error;
    }
  }

  // 重新啟用訂閱
  async reactivate() {
    try {
      const db = await getDatabase();
      
      // 計算新的計費日期
      const nextBillingDate = new Date();
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);

      const sql = `
        UPDATE member_services 
        SET status = 'active', cancelled_at = NULL, next_billing_date = ?
        WHERE id = ?
      `;

      await dbRun(db, sql, [
        nextBillingDate.toISOString().split('T')[0],
        this.id
      ]);

      // 更新本地狀態
      this.status = 'active';
      this.cancelled_at = null;
      this.next_billing_date = nextBillingDate.toISOString().split('T')[0];

      return this;
    } catch (error) {
      console.error('重新啟用訂閱失敗:', error);
      throw error;
    }
  }

  // 更新使用數據
  async updateUsage(usageData) {
    try {
      const db = await getDatabase();
      
      // 合併現有使用數據
      const currentUsage = this.usage_data || {};
      const newUsage = { ...currentUsage, ...usageData };

      const sql = 'UPDATE member_services SET usage_data = ? WHERE id = ?';
      await dbRun(db, sql, [JSON.stringify(newUsage), this.id]);

      // 更新本地狀態
      this.usage_data = newUsage;

      return this;
    } catch (error) {
      console.error('更新使用數據失敗:', error);
      throw error;
    }
  }

  // 轉換為 JSON 格式
  toJSON() {
    const json = { ...this };
    
    // 確保 usage_data 是物件
    if (typeof json.usage_data === 'string') {
      try {
        json.usage_data = JSON.parse(json.usage_data);
      } catch (e) {
        json.usage_data = {};
      }
    }

    return json;
  }
}

module.exports = MemberService;
