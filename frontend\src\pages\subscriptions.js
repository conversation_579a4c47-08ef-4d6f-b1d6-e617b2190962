import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function Subscriptions() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [activeTab, setActiveTab] = useState('active');
  const [subscriptions, setSubscriptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // 模擬訂閱數據
  const mockSubscriptions = [
    {
      id: 1,
      name: 'AI 文字生成服務',
      description: '強大的 AI 文字生成工具，支援多種語言和格式',
      price: 299,
      billingCycle: 'monthly',
      status: 'active',
      nextBilling: '2025-07-26',
      features: ['無限文字生成', '多語言支援', '模板庫', '24/7 客服'],
      usage: { used: 15000, limit: 50000, unit: '字符' }
    },
    {
      id: 2,
      name: '圖片處理服務',
      description: '專業的圖片編輯和處理工具',
      price: 199,
      billingCycle: 'monthly',
      status: 'active',
      nextBilling: '2025-07-15',
      features: ['批量處理', '格式轉換', '智能壓縮', '雲端存儲'],
      usage: { used: 250, limit: 1000, unit: '張圖片' }
    },
    {
      id: 3,
      name: '數據分析服務',
      description: '深度數據分析和可視化工具',
      price: 499,
      billingCycle: 'monthly',
      status: 'expired',
      expiredDate: '2025-06-15',
      features: ['實時分析', '自定義報表', '數據可視化', 'API 接入'],
      usage: { used: 0, limit: 0, unit: '報表' }
    }
  ];

  // 移除虛擬服務 - 現在所有服務都來自資料庫

  useEffect(() => {
    // 檢查認證
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=' + encodeURIComponent('/subscriptions'));
      return;
    }

    // 載入用戶數據
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    // 載入真實的訂閱數據
    const initializeData = async () => {
      const loadedSubscriptions = await loadSubscriptions();
      await loadAvailableServicesWithSubscriptions(loadedSubscriptions);
      setIsLoading(false);
    };

    initializeData();
  }, [router]);

  // 當訂閱變化時重新載入可用服務 (但避免初始載入時的重複調用)
  useEffect(() => {
    // 只有在 subscriptions 不是初始狀態且不在載入中時才重新載入
    if (subscriptions.length > 0 && !isLoading) {
      console.log('🔄 訂閱數量變化，重新計算可用服務...');
      loadAvailableServicesWithSubscriptions(subscriptions);
    }
  }, [subscriptions.length]); // 只監聽長度變化，避免無限循環

  // 載入可用服務數據（帶參數版本）
  const loadAvailableServicesWithSubscriptions = async (currentSubscriptions = null) => {
    const subsToUse = currentSubscriptions || subscriptions;
    return await loadAvailableServicesInternal(subsToUse);
  };

  // 載入用戶訂閱數據
  const loadSubscriptions = async () => {
    try {
      console.log('🔄 開始載入用戶訂閱...');

      const token = localStorage.getItem('token');
      if (!token) {
        console.log('⚠️ 沒有 token，使用模擬數據');
        setSubscriptions(mockSubscriptions);
        return mockSubscriptions;
      }

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          console.log('📋 載入用戶訂閱成功:', result.data.length, '個訂閱');

          // 轉換 API 數據格式以匹配前端期望的格式
          const formattedSubscriptions = result.data.map(sub => ({
            id: sub.id, // 使用訂閱記錄的真實 ID
            service_id: sub.service_id, // 保留服務 ID 用於過濾
            service_name: sub.service_name, // 保留服務名稱
            name: sub.service_name, // 向後兼容
            price: sub.service_price,
            billing_cycle: sub.service_billing_cycle,
            status: sub.status,
            nextBilling: sub.next_billing_date,
            subscribed_at: sub.subscribed_at,
            features: sub.features || [], // 添加功能列表
            usage: { used: 0, limit: 10000, unit: '次調用' } // 默認使用量
          }));

          setSubscriptions(formattedSubscriptions);
          return formattedSubscriptions;
        }
      }

      console.log('⚠️ API 調用失敗，使用模擬數據');
      setSubscriptions(mockSubscriptions);
      return mockSubscriptions;

    } catch (error) {
      console.error('載入訂閱失敗:', error);
      setSubscriptions(mockSubscriptions);
      return mockSubscriptions;
    }
  };



  // 內部載入函數
  const loadAvailableServicesInternal = async (currentSubscriptions) => {
    try {
      console.log('🔄 開始載入可用服務...');
      setIsLoading(true);

      const response = await fetch('/api/services/database');
      const result = await response.json();

      console.log('📊 API 響應:', result);

      if (result.success && result.data) {
        console.log('📦 所有服務數量:', result.data.length);

        // 過濾出啟用的服務
        const activeServices = result.data.filter(service =>
          service.status === 'active'
        );
        console.log('✅ 啟用服務數量:', activeServices.length);
        console.log('✅ 啟用服務列表:', activeServices.map(s => `${s.id}: ${s.name}`));

        // 獲取當前活躍訂閱的服務 ID (只排除 active 狀態的訂閱)
        const subsToUse = currentSubscriptions || [];
        const activeSubscribedServiceIds = subsToUse
          .filter(sub => sub.status === 'active')  // 只考慮活躍的訂閱
          .map(sub => sub.service_id);  // 使用 service_id 而不是 subscription id
        console.log('📋 活躍訂閱的服務 ID:', activeSubscribedServiceIds);

        // 排除已有活躍訂閱的服務（cancelled 和 expired 的服務可以重新訂閱）
        const availableServices = activeServices.filter(service =>
          !activeSubscribedServiceIds.includes(service.id)
        );
        console.log('🎯 最終可用服務數量:', availableServices.length);
        console.log('🎯 最終可用服務列表:', availableServices.map(s => `${s.id}: ${s.name}`));

        setAvailableServices(availableServices);

        // 顯示載入結果和數量驗證
        console.log(`✅ 成功載入 ${availableServices.length} 個可用服務`);

        // 數量驗證
        const totalServices = activeServices.length;
        const activeSubscriptions = subsToUse.filter(sub => sub.status === 'active').length;
        const availableCount = availableServices.length;

        console.log('📊 數量驗證:');
        console.log(`  總活躍服務: ${totalServices}`);
        console.log(`  活躍訂閱數: ${activeSubscriptions}`);
        console.log(`  可用服務數: ${availableCount}`);
        console.log(`  計算結果: ${activeSubscriptions} + ${availableCount} = ${activeSubscriptions + availableCount}`);
        console.log(`  是否正確: ${activeSubscriptions + availableCount === totalServices ? '✅' : '❌'}`);

        if (activeSubscriptions + availableCount !== totalServices) {
          console.warn('⚠️ 數量不匹配，可能存在重複計算問題');
        }
      } else {
        console.error('❌ API 響應無效:', result);
        // 設置為空數組，不再使用模擬數據
        setAvailableServices([]);
        console.log('❌ 無法載入服務數據');
      }
    } catch (error) {
      console.error('❌ 載入服務失敗:', error);
      // 設置為空數組，不再使用模擬數據
      setAvailableServices([]);
      console.log('❌ 服務載入失敗');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubscribe = async (serviceId) => {
    const service = availableServices.find(s => s.id === serviceId);
    if (!service) {
      alert('❌ 找不到指定的服務');
      return;
    }

    try {
      console.log('🔄 開始訂閱服務:', service.name);

      // 獲取認證 token
      const token = localStorage.getItem('token');
      if (!token) {
        alert('❌ 請先登入');
        return;
      }

      // 調用真實的訂閱 API
      const response = await fetch('/api/subscriptions/simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          service_id: serviceId
        })
      });

      const result = await response.json();
      console.log('📡 API 響應:', result);

      if (!response.ok || !result.success) {
        throw new Error(result.error || `訂閱失敗 (HTTP ${response.status})`);
      }

      console.log('✅ 訂閱 API 調用成功');

      // 立即更新本地狀態，讓服務從可用列表中消失
      console.log('🔄 立即更新本地狀態...');

      // 1. 添加新訂閱到訂閱列表
      const newSubscription = {
        id: result.data.id,
        service_id: result.data.service_id,
        service_name: result.data.service_name,
        name: result.data.service_name,
        price: result.data.service_price,
        billing_cycle: result.data.service_billing_cycle,
        status: result.data.status,
        nextBilling: result.data.next_billing_date,
        subscribed_at: result.data.subscribed_at,
        features: result.data.features || [],
        usage: { used: 0, limit: 10000, unit: '次調用' }
      };

      // 2. 更新訂閱列表
      setSubscriptions(prev => {
        // 如果是重新激活，更新現有記錄；如果是新訂閱，添加新記錄
        if (result.action === 'reactivated') {
          return prev.map(sub =>
            sub.id === newSubscription.id
              ? newSubscription
              : sub
          );
        } else {
          return [...prev, newSubscription];
        }
      });

      // 3. 立即從可用服務列表中移除該服務
      setAvailableServices(prev => prev.filter(s => s.id !== serviceId));

      console.log('✅ 本地狀態更新完成，服務已從可用列表移除');

      // 顯示成功訊息
      alert(`✅ 成功訂閱 ${service.name}！`);

      // 4. 後台重新載入數據以確保同步（不阻塞 UI）
      setTimeout(async () => {
        try {
          // 先載入訂閱數據，然後基於新的訂閱數據重新計算可用服務
          const updatedSubscriptions = await loadSubscriptions();
          await loadAvailableServicesWithSubscriptions(updatedSubscriptions);
          console.log('🔄 後台數據同步完成');
        } catch (error) {
          console.warn('後台數據同步失敗:', error);
        }
      }, 500);

    } catch (error) {
      console.error('訂閱失敗:', error);
      alert(`❌ 訂閱失敗: ${error.message}`);
    }
  };

  const handleCancelSubscription = async (subscriptionId) => {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    if (confirm(`確定要取消「${subscription.service_name || subscription.name}」的訂閱嗎？\n\n注意：取消後的訂閱記錄會保留在系統中，狀態會變為「已取消」。`)) {
      try {
        console.log('🔄 取消訂閱:', subscription.service_name || subscription.name);

        const token = localStorage.getItem('token');
        if (!token) {
          alert('❌ 請先登入');
          return;
        }

        // 調用取消訂閱 API
        const response = await fetch('/api/subscriptions/cancel', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            subscription_id: subscriptionId
          })
        });

        const result = await response.json();

        if (response.ok && result.success) {
          console.log('✅ 取消訂閱成功:', result.message);

          // 更新本地狀態 - 將訂閱狀態改為 cancelled
          const updatedSubscriptions = subscriptions.map(sub =>
            sub.id === subscriptionId
              ? { ...sub, status: 'cancelled' }
              : sub
          );
          setSubscriptions(updatedSubscriptions);

          // 重新載入可用服務（被取消的服務應該重新出現）
          setTimeout(() => {
            loadAvailableServicesWithSubscriptions(updatedSubscriptions);
          }, 100);

          alert(`✅ ${result.message}`);
        } else {
          console.error('❌ 取消訂閱失敗:', result.error);
          alert(`❌ 取消訂閱失敗: ${result.error}`);
        }
      } catch (error) {
        console.error('取消訂閱請求失敗:', error);
        alert('❌ 取消訂閱失敗，請稍後再試');
      }
    }
  };

  const handleRenewSubscription = (subscriptionId) => {
    setSubscriptions(prev =>
      prev.map(sub =>
        sub.id === subscriptionId
          ? {
              ...sub,
              status: 'active',
              nextBilling: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            }
          : sub
      )
    );
    alert('訂閱已續費');
  };

  const handleReactivateSubscription = async (subscriptionId) => {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    if (confirm(`確定要重新訂閱「${subscription.service_name || subscription.name}」嗎？`)) {
      try {
        console.log('🔄 重新激活訂閱:', subscription.service_name || subscription.name);

        const token = localStorage.getItem('token');
        if (!token) {
          alert('❌ 請先登入');
          return;
        }

        // 調用重新激活 API
        const response = await fetch('/api/subscriptions/simple', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            service_id: subscription.service_id
          })
        });

        const result = await response.json();

        if (response.ok && result.success) {
          console.log('✅ 重新激活成功:', result.message);

          // 更新本地狀態 - 將訂閱狀態改為 active
          const updatedSubscriptions = subscriptions.map(sub =>
            sub.id === subscriptionId
              ? { ...sub, status: 'active' }
              : sub
          );
          setSubscriptions(updatedSubscriptions);

          // 從可用服務列表中移除該服務
          setAvailableServices(prev => prev.filter(s => s.id !== subscription.service_id));

          alert(`✅ 成功重新訂閱「${subscription.service_name || subscription.name}」！`);

          // 重新載入數據以確保同步
          setTimeout(async () => {
            const currentSubscriptions = await loadSubscriptions();
            await loadAvailableServicesWithSubscriptions(currentSubscriptions);
          }, 500);
        } else {
          console.error('❌ 重新激活失敗:', result.error);
          alert(`❌ 重新訂閱失敗: ${result.error}`);
        }
      } catch (error) {
        console.error('重新激活請求失敗:', error);
        alert('❌ 重新訂閱失敗，請稍後再試');
      }
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'expired': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return '使用中';
      case 'expired': return '已過期';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  const filteredSubscriptions = subscriptions.filter(sub => {
    if (activeTab === 'active') return sub.status === 'active';
    if (activeTab === 'expired') return sub.status === 'expired' || sub.status === 'cancelled';
    return true;
  });

  // 計算各標籤的數量
  const activeSubscriptionsCount = subscriptions.filter(sub => sub.status === 'active').length;
  const expiredCancelledCount = subscriptions.filter(sub => sub.status === 'expired' || sub.status === 'cancelled').length;
  const availableServicesCount = availableServices.length;

  // 調試日誌：顯示標籤數量和正確的邏輯關係
  console.log('📊 標籤數量統計:');
  console.log(`  使用中訂閱: ${activeSubscriptionsCount}`);
  console.log(`  已過期/已取消: ${expiredCancelledCount}`);
  console.log(`  可用服務: ${availableServicesCount}`);
  console.log(`  總訂閱數: ${subscriptions.length}`);

  // 正確的邏輯關係分析
  console.log('🔍 邏輯關係分析:');
  console.log(`  訂閱記錄總數: ${activeSubscriptionsCount} + ${expiredCancelledCount} = ${activeSubscriptionsCount + expiredCancelledCount}`);
  console.log(`  服務總數驗證: ${activeSubscriptionsCount} (已訂閱) + ${availableServicesCount} (可用) = 總活躍服務`);

  // 檢查已取消服務是否重新可用
  const cancelledSubscriptions = subscriptions.filter(sub => sub.status === 'cancelled');
  const cancelledServiceIds = cancelledSubscriptions.map(sub => sub.service_id);
  const availableServiceIds = availableServices.map(service => service.id);
  const reappearingServices = cancelledServiceIds.filter(id => availableServiceIds.includes(id));

  console.log(`  已取消的服務 ID: [${cancelledServiceIds.join(', ')}]`);
  console.log(`  可用的服務 ID: [${availableServiceIds.join(', ')}]`);
  console.log(`  重新可用的服務: [${reappearingServices.join(', ')}] (${reappearingServices.length}個)`);
  console.log(`  ✅ 已取消服務重新可用: ${reappearingServices.length > 0 ? '正常' : '可能有問題'}`);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>服務訂閱 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回儀表板
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">服務訂閱</h1>
                </div>
              </div>
              
              <div className="flex items-center">
                <span className="text-sm text-gray-600">歡迎，{user?.name}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 標籤切換 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setActiveTab('active')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'active'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <span className="flex items-center justify-center space-x-2">
                    <span>使用中訂閱</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                      activeTab === 'active'
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {activeSubscriptionsCount}
                    </span>
                  </span>
                </button>
                <button
                  onClick={() => setActiveTab('expired')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'expired'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <span className="flex items-center justify-center space-x-2">
                    <span>已過期/已取消</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                      activeTab === 'expired'
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {expiredCancelledCount}
                    </span>
                  </span>
                </button>
                <button
                  onClick={() => setActiveTab('available')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'available'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <span className="flex items-center justify-center space-x-2">
                    <span>可用服務</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                      activeTab === 'available'
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {availableServicesCount}
                    </span>
                  </span>
                </button>
              </div>
            </div>

            {/* 訂閱列表 */}
            {(activeTab === 'active' || activeTab === 'expired') && (
              <div className="space-y-6">
                {filteredSubscriptions.length === 0 ? (
                  <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {activeTab === 'active' ? '暫無使用中的訂閱' : '暫無過期的訂閱'}
                    </h3>
                    <p className="text-gray-500">
                      {activeTab === 'active' ? '點擊「可用服務」標籤來訂閱新服務' : '您的過期訂閱將顯示在這裡'}
                    </p>
                  </div>
                ) : (
                  filteredSubscriptions.map((subscription, index) => (
                    <motion.div
                      key={subscription.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
                    >
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">{subscription.name}</h3>
                          <p className="text-gray-600 mb-3">{subscription.description}</p>
                          <div className="flex items-center space-x-4">
                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                              {getStatusText(subscription.status)}
                            </span>
                            <span className="text-lg font-bold text-gray-900">
                              NT$ {subscription.price}/月
                            </span>
                          </div>
                        </div>

                        <div className="text-right">
                          {subscription.status === 'active' && (
                            <div className="mb-2">
                              <p className="text-sm text-gray-500">下次計費</p>
                              <p className="font-medium text-gray-900">{subscription.nextBilling}</p>
                            </div>
                          )}
                          {subscription.status === 'expired' && (
                            <div className="mb-2">
                              <p className="text-sm text-gray-500">過期時間</p>
                              <p className="font-medium text-red-600">{subscription.expiredDate}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 使用量顯示 */}
                      {subscription.status === 'active' && subscription.usage && (
                        <div className="mb-4">
                          <div className="flex justify-between text-sm text-gray-600 mb-1">
                            <span>本月使用量</span>
                            <span>{subscription.usage.used.toLocaleString()} / {subscription.usage.limit.toLocaleString()} {subscription.usage.unit}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${Math.min((subscription.usage.used / subscription.usage.limit) * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      )}

                      {/* 功能列表 */}
                      <div className="mb-6">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">包含功能</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {(subscription.features || []).map((feature, idx) => (
                            <div key={idx} className="flex items-center text-sm text-gray-600">
                              <div className="h-1.5 w-1.5 bg-green-500 rounded-full mr-2"></div>
                              {feature}
                            </div>
                          ))}
                          {(!subscription.features || subscription.features.length === 0) && (
                            <div className="col-span-2 text-sm text-gray-400 italic">
                              暫無功能說明
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 操作按鈕 */}
                      <div className="flex space-x-3">
                        {subscription.status === 'active' && (
                          <>
                            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                              管理訂閱
                            </button>
                            <button
                              onClick={() => handleCancelSubscription(subscription.id)}
                              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                            >
                              取消訂閱
                            </button>
                          </>
                        )}
                        {(subscription.status === 'expired' || subscription.status === 'cancelled') && (
                          <button
                            onClick={() => handleReactivateSubscription(subscription.id)}
                            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                          >
                            重新訂閱
                          </button>
                        )}
                      </div>
                    </motion.div>
                  ))
                )}
              </div>
            )}

            {/* 可用服務 */}
            {activeTab === 'available' && (
              <div className="space-y-6">
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">可用服務</h2>
                      <p className="text-gray-600 mt-2">選擇適合您需求的服務，立即開始使用</p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={async () => {
                          console.log('🔄 手動刷新可用服務...');
                          const currentSubscriptions = await loadSubscriptions();
                          await loadAvailableServicesWithSubscriptions(currentSubscriptions);
                        }}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        disabled={isLoading}
                      >
                        {isLoading ? '載入中...' : '🔄 刷新'}
                      </button>
                      <span className="px-3 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm">
                        {availableServices.length} 個可用
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                  {availableServices.map((service, index) => (
                    <motion.div
                      key={service.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className={`bg-white rounded-xl shadow-lg p-6 border-2 transition-all duration-300 hover:shadow-xl ${
                        service.popular ? 'border-blue-500 relative' : 'border-gray-100 hover:border-blue-300'
                      }`}
                    >
                      {service.popular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                            熱門推薦
                          </span>
                        </div>
                      )}

                      <div className="text-center mb-6">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">{service.name}</h3>
                        <p className="text-gray-600 mb-4">{service.description}</p>
                        <div className="text-3xl font-bold text-gray-900 mb-1">
                          NT$ {service.price}
                          <span className="text-lg font-normal text-gray-500">/月</span>
                        </div>
                      </div>

                      {/* 功能列表 */}
                      <div className="mb-6">
                        <h4 className="text-sm font-medium text-gray-700 mb-3">包含功能</h4>
                        <ul className="space-y-2">
                          {(service.features || []).map((feature, idx) => (
                            <li key={idx} className="flex items-center text-sm text-gray-600">
                              <div className="h-1.5 w-1.5 bg-green-500 rounded-full mr-3"></div>
                              {feature}
                            </li>
                          ))}
                          {(!service.features || service.features.length === 0) && (
                            <li className="text-sm text-gray-400 italic">
                              暫無功能說明
                            </li>
                          )}
                        </ul>
                      </div>

                      {/* 訂閱按鈕 */}
                      <button
                        onClick={() => handleSubscribe(service.id)}
                        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                          service.popular
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'bg-gray-900 text-white hover:bg-gray-800'
                        }`}
                      >
                        立即訂閱
                      </button>
                    </motion.div>
                  ))}
                </div>

                {availableServices.length === 0 && (
                  <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                    <div className="text-green-600 mb-3">
                      ✅
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">所有服務已訂閱</h3>
                    <p className="text-gray-500">您已經訂閱了所有可用的服務</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
      </div>
    </>
  );
}
