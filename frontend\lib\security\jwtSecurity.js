const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// JWT 安全管理類
class JWTSecurity {
  constructor() {
    // JWT 配置
    this.config = {
      accessTokenExpiry: '15m',      // 訪問令牌15分鐘
      refreshTokenExpiry: '7d',      // 刷新令牌7天
      issuer: 'member-management-system',
      audience: 'member-app',
      algorithm: 'HS256'
    };

    // Token 黑名單（實際應用中應使用 Redis 或數據庫）
    this.tokenBlacklist = new Set();
    
    // 刷新令牌存儲（實際應用中應使用數據庫）
    this.refreshTokens = new Map();

    // 安全密鑰（實際應用中應從環境變量讀取）
    this.accessTokenSecret = process.env.JWT_ACCESS_SECRET || this.generateSecureSecret();
    this.refreshTokenSecret = process.env.JWT_REFRESH_SECRET || this.generateSecureSecret();
  }

  // 生成安全密鑰
  generateSecureSecret() {
    return crypto.randomBytes(64).toString('hex');
  }

  // 生成訪問令牌
  generateAccessToken(payload) {
    const tokenPayload = {
      ...payload,
      type: 'access',
      iat: Math.floor(Date.now() / 1000),
      jti: this.generateTokenId() // JWT ID for tracking
    };

    return jwt.sign(tokenPayload, this.accessTokenSecret, {
      expiresIn: this.config.accessTokenExpiry,
      issuer: this.config.issuer,
      audience: this.config.audience,
      algorithm: this.config.algorithm
    });
  }

  // 生成刷新令牌
  generateRefreshToken(userId) {
    const tokenPayload = {
      userId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      jti: this.generateTokenId()
    };

    const refreshToken = jwt.sign(tokenPayload, this.refreshTokenSecret, {
      expiresIn: this.config.refreshTokenExpiry,
      issuer: this.config.issuer,
      audience: this.config.audience,
      algorithm: this.config.algorithm
    });

    // 存儲刷新令牌
    this.refreshTokens.set(tokenPayload.jti, {
      userId,
      token: refreshToken,
      createdAt: new Date(),
      isActive: true
    });

    return refreshToken;
  }

  // 生成令牌對（訪問令牌 + 刷新令牌）
  generateTokenPair(userPayload) {
    const accessToken = this.generateAccessToken(userPayload);
    const refreshToken = this.generateRefreshToken(userPayload.userId);

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: this.parseExpiry(this.config.accessTokenExpiry)
    };
  }

  // 驗證訪問令牌
  verifyAccessToken(token) {
    try {
      // 檢查黑名單
      if (this.tokenBlacklist.has(token)) {
        throw new Error('Token has been revoked');
      }

      const decoded = jwt.verify(token, this.accessTokenSecret, {
        issuer: this.config.issuer,
        audience: this.config.audience,
        algorithms: [this.config.algorithm]
      });

      // 檢查令牌類型
      if (decoded.type !== 'access') {
        throw new Error('Invalid token type');
      }

      return {
        isValid: true,
        decoded,
        error: null
      };
    } catch (error) {
      return {
        isValid: false,
        decoded: null,
        error: error.message
      };
    }
  }

  // 驗證刷新令牌
  verifyRefreshToken(token) {
    try {
      const decoded = jwt.verify(token, this.refreshTokenSecret, {
        issuer: this.config.issuer,
        audience: this.config.audience,
        algorithms: [this.config.algorithm]
      });

      // 檢查令牌類型
      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      // 檢查令牌是否在存儲中且活躍
      const storedToken = this.refreshTokens.get(decoded.jti);
      if (!storedToken || !storedToken.isActive) {
        throw new Error('Refresh token not found or inactive');
      }

      return {
        isValid: true,
        decoded,
        error: null
      };
    } catch (error) {
      return {
        isValid: false,
        decoded: null,
        error: error.message
      };
    }
  }

  // 刷新訪問令牌
  refreshAccessToken(refreshToken, userPayload) {
    const verification = this.verifyRefreshToken(refreshToken);
    
    if (!verification.isValid) {
      throw new Error(`Invalid refresh token: ${verification.error}`);
    }

    // 生成新的訪問令牌
    const newAccessToken = this.generateAccessToken(userPayload);

    return {
      accessToken: newAccessToken,
      tokenType: 'Bearer',
      expiresIn: this.parseExpiry(this.config.accessTokenExpiry)
    };
  }

  // 撤銷令牌
  revokeToken(token) {
    try {
      const decoded = jwt.decode(token);
      
      if (decoded.type === 'access') {
        // 將訪問令牌加入黑名單
        this.tokenBlacklist.add(token);
      } else if (decoded.type === 'refresh') {
        // 將刷新令牌標記為非活躍
        const storedToken = this.refreshTokens.get(decoded.jti);
        if (storedToken) {
          storedToken.isActive = false;
        }
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // 撤銷用戶的所有令牌
  revokeAllUserTokens(userId) {
    let revokedCount = 0;

    // 撤銷所有刷新令牌
    for (const [jti, tokenData] of this.refreshTokens.entries()) {
      if (tokenData.userId === userId && tokenData.isActive) {
        tokenData.isActive = false;
        revokedCount++;
      }
    }

    return { success: true, revokedCount };
  }

  // 生成令牌 ID
  generateTokenId() {
    return crypto.randomBytes(16).toString('hex');
  }

  // 解析過期時間為秒數
  parseExpiry(expiry) {
    const units = {
      's': 1,
      'm': 60,
      'h': 3600,
      'd': 86400
    };

    const match = expiry.match(/^(\d+)([smhd])$/);
    if (!match) return 900; // 默認15分鐘

    const [, value, unit] = match;
    return parseInt(value) * units[unit];
  }

  // 清理過期的黑名單令牌
  cleanupBlacklist() {
    // 實際應用中，應該定期清理過期的令牌
    // 這裡只是示例實現
    const now = Math.floor(Date.now() / 1000);
    
    for (const token of this.tokenBlacklist) {
      try {
        const decoded = jwt.decode(token);
        if (decoded && decoded.exp < now) {
          this.tokenBlacklist.delete(token);
        }
      } catch (error) {
        // 無效令牌，直接刪除
        this.tokenBlacklist.delete(token);
      }
    }
  }

  // 清理過期的刷新令牌
  cleanupRefreshTokens() {
    const now = new Date();
    const expiredTokens = [];

    for (const [jti, tokenData] of this.refreshTokens.entries()) {
      try {
        const decoded = jwt.decode(tokenData.token);
        if (decoded && decoded.exp * 1000 < now.getTime()) {
          expiredTokens.push(jti);
        }
      } catch (error) {
        expiredTokens.push(jti);
      }
    }

    expiredTokens.forEach(jti => this.refreshTokens.delete(jti));
    return expiredTokens.length;
  }

  // 獲取令牌統計信息
  getTokenStats() {
    const activeRefreshTokens = Array.from(this.refreshTokens.values())
      .filter(token => token.isActive).length;
    
    return {
      blacklistedTokens: this.tokenBlacklist.size,
      activeRefreshTokens,
      totalRefreshTokens: this.refreshTokens.size,
      config: this.config
    };
  }

  // 驗證令牌強度
  validateTokenSecurity(token) {
    const issues = [];
    
    try {
      const decoded = jwt.decode(token, { complete: true });
      
      // 檢查算法
      if (decoded.header.alg !== this.config.algorithm) {
        issues.push('使用了不安全的簽名算法');
      }

      // 檢查過期時間
      const exp = decoded.payload.exp;
      const iat = decoded.payload.iat;
      const tokenLifetime = exp - iat;
      
      if (decoded.payload.type === 'access' && tokenLifetime > 3600) {
        issues.push('訪問令牌有效期過長');
      }
      
      if (decoded.payload.type === 'refresh' && tokenLifetime > 30 * 24 * 3600) {
        issues.push('刷新令牌有效期過長');
      }

      // 檢查必要字段
      const requiredFields = ['iat', 'exp', 'iss', 'aud'];
      for (const field of requiredFields) {
        if (!decoded.payload[field]) {
          issues.push(`缺少必要字段: ${field}`);
        }
      }

      return {
        isSecure: issues.length === 0,
        issues,
        tokenInfo: {
          algorithm: decoded.header.alg,
          lifetime: tokenLifetime,
          type: decoded.payload.type
        }
      };
    } catch (error) {
      return {
        isSecure: false,
        issues: ['令牌格式無效'],
        error: error.message
      };
    }
  }
}

// 創建全局實例
const jwtSecurity = new JWTSecurity();

// 導出
module.exports = { JWTSecurity, jwtSecurity };
