import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function LocalStorageAnalysis() {
  const router = useRouter();
  const [storageData, setStorageData] = useState({});
  const [analysisResults, setAnalysisResults] = useState([]);

  useEffect(() => {
    analyzeLocalStorage();
  }, []);

  const addResult = (category, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setAnalysisResults(prev => [...prev, {
      timestamp,
      category,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const analyzeLocalStorage = () => {
    try {
      addResult('開始', 'info', '開始分析 localStorage 內容');

      const storage = {};
      const keys = Object.keys(localStorage);
      
      addResult('掃描', 'info', `找到 ${keys.length} 個 localStorage 項目`);

      keys.forEach(key => {
        const value = localStorage.getItem(key);
        let parsedValue = value;
        let dataType = 'string';
        let size = value ? value.length : 0;

        // 嘗試解析 JSON
        try {
          parsedValue = JSON.parse(value);
          dataType = 'json';
        } catch (error) {
          // 保持原始字符串值
        }

        storage[key] = {
          rawValue: value,
          parsedValue: parsedValue,
          dataType: dataType,
          size: size,
          sizeKB: (size / 1024).toFixed(2)
        };

        addResult('項目', 'success', `${key}: ${dataType} (${size} 字符)`);
      });

      setStorageData(storage);

      // 分析認證相關數據
      analyzeAuthData(storage);

      // 分析數據完整性
      analyzeDataIntegrity(storage);

      addResult('完成', 'success', 'localStorage 分析完成');

    } catch (error) {
      addResult('錯誤', 'error', `分析過程中出現錯誤: ${error.message}`);
    }
  };

  const analyzeAuthData = (storage) => {
    addResult('認證分析', 'info', '分析認證相關數據...');

    // 檢查 token
    if (storage.token) {
      const token = storage.token.rawValue;
      const parts = token.split('.');
      
      if (parts.length === 3) {
        try {
          const payload = JSON.parse(atob(parts[1]));
          const exp = new Date(payload.exp * 1000);
          const isExpired = exp < new Date();
          
          addResult('Token', 'success', `JWT Token 格式正確`);
          addResult('Token', 'info', `用戶 ID: ${payload.userId}`);
          addResult('Token', 'info', `角色: ${payload.role}`);
          addResult('Token', 'info', `過期時間: ${exp.toLocaleString()}`);
          addResult('Token', isExpired ? 'warning' : 'success', 
            isExpired ? 'Token 已過期' : 'Token 未過期');
        } catch (error) {
          addResult('Token', 'error', 'Token payload 解析失敗');
        }
      } else {
        addResult('Token', 'error', 'Token 格式錯誤');
      }
    } else {
      addResult('Token', 'warning', '未找到 token');
    }

    // 檢查 user
    if (storage.user) {
      const user = storage.user.parsedValue;
      if (typeof user === 'object') {
        addResult('User', 'success', '用戶數據格式正確');
        addResult('User', 'info', `姓名: ${user.name || '未設置'}`);
        addResult('User', 'info', `Email: ${user.email || '未設置'}`);
        addResult('User', 'info', `角色: ${user.role || '未設置'}`);
        addResult('User', 'info', `用戶 ID: ${user.userId || '未設置'}`);
      } else {
        addResult('User', 'error', '用戶數據格式錯誤');
      }
    } else {
      addResult('User', 'warning', '未找到用戶數據');
    }
  };

  const analyzeDataIntegrity = (storage) => {
    addResult('完整性', 'info', '檢查數據完整性...');

    const hasToken = !!storage.token;
    const hasUser = !!storage.user;

    if (hasToken && hasUser) {
      // 檢查 token 和 user 數據是否一致
      try {
        const tokenPayload = JSON.parse(atob(storage.token.rawValue.split('.')[1]));
        const userData = storage.user.parsedValue;

        if (tokenPayload.userId === userData.userId) {
          addResult('完整性', 'success', 'Token 和用戶數據 ID 一致');
        } else {
          addResult('完整性', 'error', 'Token 和用戶數據 ID 不一致');
        }

        if (tokenPayload.role === userData.role) {
          addResult('完整性', 'success', 'Token 和用戶數據角色一致');
        } else {
          addResult('完整性', 'error', 'Token 和用戶數據角色不一致');
        }

        if (tokenPayload.email === userData.email) {
          addResult('完整性', 'success', 'Token 和用戶數據 Email 一致');
        } else {
          addResult('完整性', 'error', 'Token 和用戶數據 Email 不一致');
        }

      } catch (error) {
        addResult('完整性', 'error', '數據一致性檢查失敗');
      }
    } else {
      addResult('完整性', 'warning', '認證數據不完整');
    }

    // 檢查其他數據
    if (storage.subscriptions) {
      const subs = storage.subscriptions.parsedValue;
      if (Array.isArray(subs)) {
        addResult('完整性', 'success', `訂閱數據: ${subs.length} 個訂閱`);
      } else {
        addResult('完整性', 'error', '訂閱數據格式錯誤');
      }
    }
  };

  const clearAllStorage = () => {
    if (confirm('確定要清除所有 localStorage 數據嗎？這將登出當前用戶。')) {
      localStorage.clear();
      addResult('清除', 'warning', '已清除所有 localStorage 數據');
      analyzeLocalStorage();
    }
  };

  const clearAuthData = () => {
    if (confirm('確定要清除認證數據嗎？這將登出當前用戶。')) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('subscriptions');
      addResult('清除', 'warning', '已清除認證相關數據');
      analyzeLocalStorage();
    }
  };

  const simulateLogin = async () => {
    try {
      addResult('模擬', 'info', '模擬登入過程...');
      
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        
        addResult('模擬', 'success', '模擬登入成功，已更新 localStorage');
        analyzeLocalStorage();
      } else {
        addResult('模擬', 'error', `模擬登入失敗: ${result.error}`);
      }
    } catch (error) {
      addResult('模擬', 'error', `模擬登入錯誤: ${error.message}`);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  const getBgColor = (status) => {
    switch (status) {
      case 'success': return 'bg-green-50 border-green-200';
      case 'error': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      default: return 'bg-blue-50 border-blue-200';
    }
  };

  return (
    <>
      <Head>
        <title>localStorage 分析 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">💾 localStorage 分析</h1>
                <p className="text-gray-600 mt-2">分析瀏覽器 localStorage 中存儲的數據</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={simulateLogin}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  🔄 模擬登入
                </button>
                <button
                  onClick={clearAuthData}
                  className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
                >
                  🗑️ 清除認證
                </button>
                <button
                  onClick={clearAllStorage}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  🗑️ 清除全部
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={analyzeLocalStorage}
                  className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium"
                >
                  🔍 重新分析
                </button>
              </div>
            </div>

            {/* localStorage 數據表格 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 localStorage 數據</h2>
              
              {Object.keys(storageData).length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">鍵名</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">數據類型</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">大小</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">值預覽</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {Object.entries(storageData).map(([key, data]) => (
                        <tr key={key} className="hover:bg-gray-50">
                          <td className="px-4 py-3 text-sm font-medium text-gray-900">{key}</td>
                          <td className="px-4 py-3 text-sm text-gray-600">
                            <span className={`px-2 py-1 rounded text-xs ${
                              data.dataType === 'json' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {data.dataType}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-600">
                            {data.size} 字符 ({data.sizeKB} KB)
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-600 max-w-xs truncate">
                            {data.dataType === 'json' ? 
                              JSON.stringify(data.parsedValue).substring(0, 100) + '...' :
                              data.rawValue.substring(0, 100) + (data.rawValue.length > 100 ? '...' : '')
                            }
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-gray-500 text-center py-8 border border-gray-200 rounded-lg">
                  localStorage 中沒有數據
                </div>
              )}
            </div>

            {/* 分析結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 分析結果</h2>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {analysisResults.length > 0 ? (
                  analysisResults.map((result, index) => (
                    <div key={index} className={`rounded-lg p-4 border ${getBgColor(result.status)}`}>
                      <div className="flex items-start space-x-3">
                        <span className={getStatusColor(result.status)}>
                          {getStatusIcon(result.status)}
                        </span>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 text-sm">
                            <span className="text-gray-500">[{result.timestamp}]</span>
                            <span className="font-medium text-gray-700">{result.category}:</span>
                            <span className={getStatusColor(result.status)}>{result.message}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「重新分析」來分析 localStorage 數據
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">💾 localStorage 說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>token:</strong> JWT 認證令牌，包含用戶 ID、角色、過期時間等信息</div>
                <div><strong>user:</strong> 用戶基本信息，包含姓名、Email、角色等</div>
                <div><strong>subscriptions:</strong> 用戶的服務訂閱信息（如果有）</div>
                <div><strong>重新登入:</strong> 通常會清除舊數據並保存新的認證信息</div>
                <div><strong>數據持久性:</strong> localStorage 數據會持續保存直到手動清除或過期</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
