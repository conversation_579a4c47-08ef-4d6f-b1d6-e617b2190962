import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function TestDashboard() {
  const router = useRouter();
  const [authInfo, setAuthInfo] = useState({
    token: null,
    user: null,
    isAuthenticated: false
  });

  useEffect(() => {
    // 檢查認證狀態
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let user = null;
    if (userStr) {
      try {
        user = JSON.parse(userStr);
      } catch (error) {
        console.error('解析用戶數據失敗:', error);
      }
    }

    setAuthInfo({
      token,
      user,
      isAuthenticated: !!token
    });

    console.log('認證檢查結果:', {
      token: !!token,
      user: !!user,
      userStr
    });
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/');
  };

  const goToLogin = () => {
    router.push('/auth/login');
  };

  return (
    <>
      <Head>
        <title>測試儀表板 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 py-4">
            <div className="flex justify-between items-center">
              <h1 className="text-xl font-semibold text-gray-900">測試儀表板</h1>
              <div className="space-x-4">
                <button
                  onClick={goToLogin}
                  className="px-4 py-2 text-blue-600 hover:text-blue-700"
                >
                  登入頁面
                </button>
                <button
                  onClick={handleLogout}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  登出
                </button>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-7xl mx-auto py-6 px-4">
          <div className="space-y-6">
            {/* 認證狀態 */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-lg font-semibold mb-4">認證狀態</h2>
              <div className="space-y-2">
                <p>
                  <span className="font-medium">是否已認證:</span>{' '}
                  <span className={authInfo.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                    {authInfo.isAuthenticated ? '是' : '否'}
                  </span>
                </p>
                <p>
                  <span className="font-medium">Token:</span>{' '}
                  <span className={authInfo.token ? 'text-green-600' : 'text-red-600'}>
                    {authInfo.token ? '存在' : '不存在'}
                  </span>
                </p>
                <p>
                  <span className="font-medium">用戶數據:</span>{' '}
                  <span className={authInfo.user ? 'text-green-600' : 'text-red-600'}>
                    {authInfo.user ? '存在' : '不存在'}
                  </span>
                </p>
              </div>
            </div>

            {/* 用戶資訊 */}
            {authInfo.user && (
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-lg font-semibold mb-4">用戶資訊</h2>
                <div className="space-y-2">
                  <p><span className="font-medium">姓名:</span> {authInfo.user.name}</p>
                  <p><span className="font-medium">電子郵件:</span> {authInfo.user.email}</p>
                  <p><span className="font-medium">註冊時間:</span> {authInfo.user.memberSince}</p>
                </div>
              </div>
            )}

            {/* 調試資訊 */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-lg font-semibold mb-4">調試資訊</h2>
              <div className="space-y-2 text-sm">
                <p><span className="font-medium">當前路徑:</span> {router.asPath}</p>
                <p><span className="font-medium">查詢參數:</span> {JSON.stringify(router.query)}</p>
                <p><span className="font-medium">瀏覽器:</span> {typeof window !== 'undefined' ? 'Client' : 'Server'}</p>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-lg font-semibold mb-4">快速操作</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  正式儀表板
                </button>
                <button
                  onClick={() => router.push('/test-login')}
                  className="p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  測試登入
                </button>
                <button
                  onClick={() => router.push('/')}
                  className="p-4 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  返回首頁
                </button>
              </div>
            </div>

            {/* 如果未認證，顯示提示 */}
            {!authInfo.isAuthenticated && (
              <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                <p className="text-yellow-800">
                  ⚠️ 您尚未登入，請先登入後再訪問儀表板。
                </p>
                <button
                  onClick={goToLogin}
                  className="mt-2 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
                >
                  前往登入
                </button>
              </div>
            )}
          </div>
        </main>
      </div>
    </>
  );
}
