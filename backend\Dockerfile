# 使用官方 Node.js 運行時作為基礎映像
FROM node:20-alpine AS base

# 設置工作目錄
WORKDIR /app

# 複製 package.json 和 package-lock.json
COPY package*.json ./

# 安裝依賴
RUN npm ci --only=production && npm cache clean --force

# 創建非 root 用戶
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 複製應用程式代碼
COPY --chown=nodejs:nodejs . .

# 切換到非 root 用戶
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# 啟動應用程式
CMD ["npm", "start"]

# 開發環境映像
FROM base AS development

# 切換回 root 用戶安裝開發依賴
USER root

# 安裝所有依賴（包括開發依賴）
RUN npm ci && npm cache clean --force

# 切換回 nodejs 用戶
USER nodejs

# 開發模式啟動
CMD ["npm", "run", "dev"]

# 測試環境映像
FROM development AS test

# 運行測試
RUN npm run test:ci

# 生產環境映像
FROM base AS production

# 設置環境變數
ENV NODE_ENV=production

# 啟動應用程式
CMD ["npm", "start"]
