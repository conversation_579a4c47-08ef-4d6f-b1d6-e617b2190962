// 服務數據模型
const { getDatabase, dbRun, dbGet, dbAll } = require('../init');

class Service {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.description = data.description;
    this.price = data.price;
    this.billing_cycle = data.billing_cycle || 'monthly';
    this.category = data.category;
    this.features = data.features;
    this.status = data.status || 'active';
    this.popular = data.popular || false;
    this.max_users = data.max_users || 1;
    this.api_limit = data.api_limit || 0;
    this.storage_limit = data.storage_limit;
    this.support_level = data.support_level || 'basic';
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // 獲取所有服務
  static async findAll(options = {}) {
    try {
      const db = await getDatabase();
      let sql = 'SELECT * FROM service_summary';
      const params = [];
      const conditions = [];

      // 狀態篩選
      if (options.status) {
        conditions.push('status = ?');
        params.push(options.status);
      }

      // 類別篩選
      if (options.category) {
        conditions.push('category = ?');
        params.push(options.category);
      }

      // 搜尋
      if (options.search) {
        conditions.push('(name LIKE ? OR description LIKE ? OR category LIKE ?)');
        const searchTerm = `%${options.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }

      // 排序
      if (options.orderBy) {
        sql += ` ORDER BY ${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ' ORDER BY id ASC';
      }

      // 分頁
      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit);
        
        if (options.offset) {
          sql += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const rows = await dbAll(db, sql, params);
      return rows.map(row => {
        // 解析 JSON 欄位
        if (row.features && typeof row.features === 'string') {
          try {
            row.features = JSON.parse(row.features);
          } catch (e) {
            row.features = [];
          }
        }
        return new Service(row);
      });
    } catch (error) {
      console.error('獲取服務列表失敗:', error);
      throw error;
    }
  }

  // 根據 ID 獲取服務
  static async findById(id) {
    try {
      const db = await getDatabase();
      const row = await dbGet(db, 'SELECT * FROM service_summary WHERE id = ?', [id]);
      
      if (!row) {
        return null;
      }

      // 解析 JSON 欄位
      if (row.features && typeof row.features === 'string') {
        try {
          row.features = JSON.parse(row.features);
        } catch (e) {
          row.features = [];
        }
      }

      return new Service(row);
    } catch (error) {
      console.error('獲取服務失敗:', error);
      throw error;
    }
  }

  // 創建新服務
  static async create(serviceData) {
    try {
      const db = await getDatabase();
      
      // 處理 features 陣列
      const features = Array.isArray(serviceData.features) 
        ? JSON.stringify(serviceData.features)
        : serviceData.features;

      const sql = `
        INSERT INTO services (
          name, description, price, billing_cycle, category, features,
          status, popular, max_users, api_limit, storage_limit, support_level
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        serviceData.name,
        serviceData.description,
        serviceData.price,
        serviceData.billing_cycle || 'monthly',
        serviceData.category,
        features,
        serviceData.status || 'active',
        serviceData.popular || false,
        serviceData.max_users || 1,
        serviceData.api_limit || 0,
        serviceData.storage_limit,
        serviceData.support_level || 'basic'
      ];

      const result = await dbRun(db, sql, params);
      
      // 初始化統計數據
      await dbRun(db, 
        'INSERT INTO service_stats (service_id) VALUES (?)', 
        [result.id]
      );

      return await Service.findById(result.id);
    } catch (error) {
      console.error('創建服務失敗:', error);
      throw error;
    }
  }

  // 更新服務
  async update(updateData) {
    try {
      const db = await getDatabase();
      
      // 處理 features 陣列
      if (updateData.features && Array.isArray(updateData.features)) {
        updateData.features = JSON.stringify(updateData.features);
      }

      const fields = [];
      const params = [];

      // 動態構建更新欄位
      const allowedFields = [
        'name', 'description', 'price', 'billing_cycle', 'category', 
        'features', 'status', 'popular', 'max_users', 'api_limit', 
        'storage_limit', 'support_level'
      ];

      allowedFields.forEach(field => {
        if (updateData.hasOwnProperty(field)) {
          fields.push(`${field} = ?`);
          params.push(updateData[field]);
        }
      });

      if (fields.length === 0) {
        throw new Error('沒有要更新的欄位');
      }

      params.push(this.id);

      const sql = `UPDATE services SET ${fields.join(', ')} WHERE id = ?`;
      await dbRun(db, sql, params);

      // 重新獲取更新後的數據
      const updated = await Service.findById(this.id);
      Object.assign(this, updated);

      return this;
    } catch (error) {
      console.error('更新服務失敗:', error);
      throw error;
    }
  }

  // 刪除服務
  async delete() {
    try {
      const db = await getDatabase();
      
      // 檢查是否有活躍訂閱
      const activeSubscriptions = await dbGet(db, 
        'SELECT COUNT(*) as count FROM member_services WHERE service_id = ? AND status = "active"',
        [this.id]
      );

      if (activeSubscriptions.count > 0) {
        throw new Error(`無法刪除服務：還有 ${activeSubscriptions.count} 個活躍訂閱`);
      }

      // 刪除統計數據
      await dbRun(db, 'DELETE FROM service_stats WHERE service_id = ?', [this.id]);
      
      // 刪除服務
      const result = await dbRun(db, 'DELETE FROM services WHERE id = ?', [this.id]);
      
      return result.changes > 0;
    } catch (error) {
      console.error('刪除服務失敗:', error);
      throw error;
    }
  }

  // 獲取服務統計
  static async getStats() {
    try {
      const db = await getDatabase();
      
      const stats = await dbGet(db, `
        SELECT 
          COUNT(*) as total_services,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_services,
          COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_services,
          COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_services,
          SUM(COALESCE(total_subscribers, 0)) as total_subscribers,
          SUM(COALESCE(total_revenue, 0)) as total_revenue,
          AVG(price) as avg_price
        FROM service_summary
      `);

      const categories = await dbAll(db, `
        SELECT category, COUNT(*) as count 
        FROM services 
        WHERE category IS NOT NULL 
        GROUP BY category 
        ORDER BY count DESC
      `);

      const mostPopular = await dbGet(db, `
        SELECT * FROM service_summary 
        WHERE total_subscribers > 0 
        ORDER BY total_subscribers DESC 
        LIMIT 1
      `);

      return {
        ...stats,
        categories: categories.map(c => ({ name: c.category, count: c.count })),
        most_popular: mostPopular ? new Service(mostPopular) : null
      };
    } catch (error) {
      console.error('獲取服務統計失敗:', error);
      throw error;
    }
  }

  // 更新統計數據
  static async updateStats(serviceId) {
    try {
      const db = await getDatabase();
      
      const stats = await dbGet(db, `
        SELECT 
          COUNT(*) as total_subscribers,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscribers,
          SUM(CASE WHEN status = 'active' THEN s.price ELSE 0 END) as monthly_revenue
        FROM member_services ms
        JOIN services s ON ms.service_id = s.id
        WHERE ms.service_id = ?
      `, [serviceId]);

      await dbRun(db, `
        UPDATE service_stats 
        SET 
          total_subscribers = ?,
          active_subscribers = ?,
          monthly_revenue = ?,
          last_calculated = CURRENT_TIMESTAMP
        WHERE service_id = ?
      `, [
        stats.total_subscribers || 0,
        stats.active_subscribers || 0,
        stats.monthly_revenue || 0,
        serviceId
      ]);

      return stats;
    } catch (error) {
      console.error('更新服務統計失敗:', error);
      throw error;
    }
  }

  // 轉換為 JSON 格式
  toJSON() {
    const json = { ...this };
    
    // 確保 features 是陣列
    if (typeof json.features === 'string') {
      try {
        json.features = JSON.parse(json.features);
      } catch (e) {
        json.features = [];
      }
    }

    return json;
  }
}

module.exports = Service;
