// 錢包交易記錄模型
const { getDatabase, dbRun, dbGet, dbAll } = require('../init');

class WalletTransaction {
  constructor(data = {}) {
    this.id = data.id;
    this.member_id = data.member_id;
    this.transaction_type = data.transaction_type;
    this.amount = data.amount;
    this.balance_before = data.balance_before;
    this.balance_after = data.balance_after;
    this.description = data.description;
    this.reference_id = data.reference_id;
    this.reference_type = data.reference_type;
    this.status = data.status || 'completed';
    this.payment_method = data.payment_method;
    this.payment_reference = data.payment_reference;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // 創建新的交易記錄
  static async create(data) {
    try {
      const db = await getDatabase();
      
      const result = await dbRun(db, `
        INSERT INTO wallet_transactions 
        (member_id, transaction_type, amount, balance_before, balance_after, description, reference_id, reference_type, status, payment_method, payment_reference)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        data.member_id,
        data.transaction_type,
        data.amount,
        data.balance_before,
        data.balance_after,
        data.description,
        data.reference_id || null,
        data.reference_type || null,
        data.status || 'completed',
        data.payment_method || null,
        data.payment_reference || null
      ]);

      // 返回創建的交易記錄
      return await WalletTransaction.findById(result.id);
    } catch (error) {
      console.error('創建交易記錄失敗:', error);
      throw error;
    }
  }

  // 根據 ID 查找交易記錄
  static async findById(id) {
    try {
      const db = await getDatabase();
      const row = await dbGet(db, 'SELECT * FROM wallet_transactions WHERE id = ?', [id]);
      
      if (!row) {
        return null;
      }

      return new WalletTransaction(row);
    } catch (error) {
      console.error('根據 ID 查找交易記錄失敗:', error);
      throw error;
    }
  }

  // 獲取會員的交易記錄
  static async findByMemberId(memberId, options = {}) {
    try {
      const db = await getDatabase();
      let sql = 'SELECT * FROM wallet_transactions WHERE member_id = ?';
      const params = [memberId];
      const conditions = [];

      // 交易類型篩選
      if (options.type) {
        conditions.push('transaction_type = ?');
        params.push(options.type);
      }

      // 狀態篩選
      if (options.status) {
        conditions.push('status = ?');
        params.push(options.status);
      }

      // 日期範圍篩選
      if (options.startDate) {
        conditions.push('created_at >= ?');
        params.push(options.startDate);
      }

      if (options.endDate) {
        conditions.push('created_at <= ?');
        params.push(options.endDate);
      }

      if (conditions.length > 0) {
        sql += ' AND ' + conditions.join(' AND ');
      }

      // 排序
      sql += ' ORDER BY created_at DESC';

      // 分頁
      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit);
        
        if (options.offset) {
          sql += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const rows = await dbAll(db, sql, params);
      return rows.map(row => new WalletTransaction(row));
    } catch (error) {
      console.error('獲取會員交易記錄失敗:', error);
      throw error;
    }
  }

  // 獲取所有交易記錄
  static async findAll(options = {}) {
    try {
      const db = await getDatabase();
      let sql = 'SELECT * FROM wallet_transactions';
      const params = [];
      const conditions = [];

      // 交易類型篩選
      if (options.type) {
        conditions.push('transaction_type = ?');
        params.push(options.type);
      }

      // 狀態篩選
      if (options.status) {
        conditions.push('status = ?');
        params.push(options.status);
      }

      // 會員篩選
      if (options.memberId) {
        conditions.push('member_id = ?');
        params.push(options.memberId);
      }

      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }

      // 排序
      sql += ' ORDER BY created_at DESC';

      // 分頁
      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit);
        
        if (options.offset) {
          sql += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const rows = await dbAll(db, sql, params);
      return rows.map(row => new WalletTransaction(row));
    } catch (error) {
      console.error('獲取所有交易記錄失敗:', error);
      throw error;
    }
  }

  // 更新交易記錄
  static async updateById(id, data) {
    try {
      const db = await getDatabase();
      
      const fields = [];
      const params = [];
      
      if (data.status !== undefined) {
        fields.push('status = ?');
        params.push(data.status);
      }
      
      if (data.description !== undefined) {
        fields.push('description = ?');
        params.push(data.description);
      }
      
      if (data.payment_reference !== undefined) {
        fields.push('payment_reference = ?');
        params.push(data.payment_reference);
      }
      
      if (fields.length === 0) {
        throw new Error('沒有要更新的字段');
      }
      
      fields.push('updated_at = CURRENT_TIMESTAMP');
      params.push(id);
      
      await dbRun(db, 
        `UPDATE wallet_transactions SET ${fields.join(', ')} WHERE id = ?`,
        params
      );
      
      return await WalletTransaction.findById(id);
    } catch (error) {
      console.error('更新交易記錄失敗:', error);
      throw error;
    }
  }

  // 獲取交易統計
  static async getStats(memberId = null) {
    try {
      const db = await getDatabase();
      
      let sql = `
        SELECT 
          COUNT(*) as total_transactions,
          COUNT(CASE WHEN transaction_type = 'recharge' THEN 1 END) as recharge_count,
          COUNT(CASE WHEN transaction_type = 'withdraw' THEN 1 END) as withdraw_count,
          COUNT(CASE WHEN transaction_type = 'payment' THEN 1 END) as payment_count,
          COUNT(CASE WHEN transaction_type = 'refund' THEN 1 END) as refund_count,
          COALESCE(SUM(CASE WHEN transaction_type = 'recharge' THEN amount ELSE 0 END), 0) as total_recharge,
          COALESCE(SUM(CASE WHEN transaction_type = 'withdraw' THEN amount ELSE 0 END), 0) as total_withdraw,
          COALESCE(SUM(CASE WHEN transaction_type = 'payment' THEN amount ELSE 0 END), 0) as total_payment,
          COALESCE(SUM(CASE WHEN transaction_type = 'refund' THEN amount ELSE 0 END), 0) as total_refund
        FROM wallet_transactions
      `;
      
      const params = [];
      
      if (memberId) {
        sql += ' WHERE member_id = ?';
        params.push(memberId);
      }

      const stats = await dbGet(db, sql, params);
      return stats;
    } catch (error) {
      console.error('獲取交易統計失敗:', error);
      throw error;
    }
  }

  // 轉換為 JSON 格式
  toJSON() {
    return {
      id: this.id,
      member_id: this.member_id,
      transaction_type: this.transaction_type,
      amount: parseFloat(this.amount),
      balance_before: parseFloat(this.balance_before),
      balance_after: parseFloat(this.balance_after),
      description: this.description,
      reference_id: this.reference_id,
      reference_type: this.reference_type,
      status: this.status,
      payment_method: this.payment_method,
      payment_reference: this.payment_reference,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = WalletTransaction;
