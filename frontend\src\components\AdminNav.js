import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function AdminNav({ currentPage = 'dashboard' }) {
  const router = useRouter();

  const navItems = [
    {
      id: 'dashboard',
      name: '儀表板',
      icon: '📊',
      path: '/admin/dashboard',
      description: '總覽和統計'
    },
    {
      id: 'services',
      name: '服務管理',
      icon: '🛠️',
      path: '/admin/services',
      description: '管理服務項目'
    },
    {
      id: 'users',
      name: '用戶管理',
      icon: '👥',
      path: '/admin/users',
      description: '管理用戶帳戶'
    },
    {
      id: 'subscriptions',
      name: '訂閱管理',
      icon: '📱',
      path: '/admin/subscriptions',
      description: '管理用戶訂閱'
    },
    {
      id: 'analytics',
      name: '數據分析',
      icon: '📈',
      path: '/admin/analytics',
      description: '業務數據分析'
    },
    {
      id: 'settings',
      name: '系統設定',
      icon: '⚙️',
      path: '/admin/settings',
      description: '系統配置'
    }
  ];

  return (
    <div className="bg-white shadow-lg rounded-xl p-6">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900">管理員控制台</h2>
        <p className="text-sm text-gray-500">系統管理和配置</p>
      </div>

      <nav className="space-y-2">
        {navItems.map((item) => (
          <motion.button
            key={item.id}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => router.push(item.path)}
            className={`w-full text-left p-4 rounded-lg transition-colors ${
              currentPage === item.id
                ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{item.icon}</span>
              <div>
                <div className="font-medium">{item.name}</div>
                <div className="text-xs text-gray-500">{item.description}</div>
              </div>
            </div>
          </motion.button>
        ))}
      </nav>

      <div className="mt-8 pt-6 border-t border-gray-200">
        <button
          onClick={() => router.push('/minimal-dashboard')}
          className="w-full text-left p-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
        >
          <div className="flex items-center space-x-3">
            <span className="text-xl">🏠</span>
            <span>返回用戶儀表板</span>
          </div>
        </button>
      </div>
    </div>
  );
}

// 管理員頁面布局組件
export function AdminLayout({ children, currentPage, title }) {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂部導航 */}
      <nav className="bg-white shadow-lg border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/minimal-dashboard')}
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                ← 返回儀表板
              </button>
              <div className="ml-6">
                <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
              </div>
            </div>
            
            <div className="flex items-center">
              <span className="text-sm text-gray-600">管理員模式</span>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要內容 */}
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 側邊欄 */}
          <div className="lg:col-span-1">
            <AdminNav currentPage={currentPage} />
          </div>

          {/* 主要內容區域 */}
          <div className="lg:col-span-3">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

// 管理員權限檢查 Hook
export function useAdminAuth() {
  const router = useRouter();

  const checkAdminAuth = () => {
    if (typeof window === 'undefined') return false;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login');
      return false;
    }

    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        // 這裡簡化處理，實際應該從後端驗證管理員權限
        if (userData.email !== '<EMAIL>') {
          alert('您沒有管理員權限');
          router.push('/minimal-dashboard');
          return false;
        }
        return true;
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
        router.push('/auth/login');
        return false;
      }
    }
    
    router.push('/auth/login');
    return false;
  };

  return { checkAdminAuth };
}
