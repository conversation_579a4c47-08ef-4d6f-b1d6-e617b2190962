# MemberService 文檔中心

歡迎來到 MemberService 會員管理系統的文檔中心！這裡包含了所有您需要的文檔和指南。

## 📚 文檔目錄

### 🎯 核心文檔
- **[功能說明文檔](FEATURES.md)** - 詳細的功能介紹和使用說明
- **[API 文檔](API.md)** - 完整的 API 接口說明
- **[部署指南](DEPLOYMENT.md)** - 詳細的部署和配置說明

### 📋 項目信息
- **[更新日誌](../CHANGELOG.md)** - 版本更新記錄
- **[主要 README](../README.md)** - 項目概述和快速開始

## 🚀 快速導航

### 👤 用戶指南
如果您是 **用戶**，想了解如何使用系統：
1. 先閱讀 [功能說明文檔](FEATURES.md) 了解所有功能
2. 查看 [認證系統](FEATURES.md#-認證系統) 學習如何登入
3. 探索 [錢包管理](FEATURES.md#-錢包管理系統) 了解充值提現
4. 了解 [服務訂閱](FEATURES.md#-服務訂閱系統) 管理您的訂閱

### 👨‍💻 開發者指南
如果您是 **開發者**，想了解技術實現：
1. 閱讀 [API 文檔](API.md) 了解所有接口
2. 查看 [部署指南](DEPLOYMENT.md) 學習如何部署
3. 參考 [更新日誌](../CHANGELOG.md) 了解版本變更
4. 查看主 [README](../README.md) 了解技術棧

### 🔧 運維指南
如果您負責 **系統運維**：
1. 重點閱讀 [部署指南](DEPLOYMENT.md)
2. 了解 [安全配置](DEPLOYMENT.md#-安全配置)
3. 設置 [監控和日誌](DEPLOYMENT.md#-監控和日誌)
4. 準備 [故障排除](DEPLOYMENT.md#-故障排除)

## 🎨 設計理念

### 極簡設計
MemberService 採用極簡設計理念：
- **無多餘圖標** - 移除所有不必要的裝飾性元素
- **純淨界面** - 通過顏色和間距建立視覺層次
- **一致性** - 統一的設計語言和交互模式

### 用戶體驗
- **直觀操作** - 清晰的操作流程
- **即時反饋** - 操作狀態的即時顯示
- **錯誤處理** - 友好的錯誤提示
- **響應式設計** - 適配各種設備

## 🔍 功能概覽

### 🔐 認證系統
- 安全的 JWT 認證
- 智能路由保護
- 自動重定向

### 📊 極簡儀表板
- 美觀的數據展示
- 快速操作按鈕
- 用戶資訊管理

### 💰 錢包管理
- **充值功能** - 4種支付方式
- **提現功能** - 銀行帳戶管理
- **轉帳功能** - 用戶間轉帳
- **交易記錄** - 完整的歷史記錄

### 📱 服務訂閱
- **訂閱管理** - 使用中和過期訂閱
- **可用服務** - 6種不同服務
- **使用量追蹤** - 實時監控
- **帳單記錄** - 完整的付款歷史

## 🛠️ 技術棧

### 前端
- **Next.js 14** - React 框架
- **Tailwind CSS** - 樣式框架
- **Framer Motion** - 動畫庫

### 後端
- **Node.js** - 運行環境
- **Express.js** - Web 框架
- **MongoDB** - 數據庫
- **JWT** - 身份驗證

## 📱 支援的頁面

```
/                          # 首頁
/auth/login               # 登入頁面
/minimal-dashboard        # 主儀表板
/wallet                   # 錢包管理
  /wallet/recharge        # 充值頁面
  /wallet/withdraw        # 提現頁面
  /wallet/transfer        # 轉帳頁面
/subscriptions            # 服務訂閱
/subscription-detail      # 訂閱詳情
```

## 🔧 開發環境

### 快速開始
```bash
# 克隆項目
git clone https://github.com/jihnyu/MemberService.git
cd MemberService

# 安裝依賴
cd frontend && npm install
cd ../backend && npm install

# 啟動開發服務器
cd frontend && npm run dev  # 端口 3001
cd backend && npm run dev   # 端口 3000
```

### 訪問地址
- **前端**: http://localhost:3001
- **後端 API**: http://localhost:3000

## 📞 獲取幫助

### 常見問題
1. **登入問題** - 查看 [認證系統文檔](FEATURES.md#-認證系統)
2. **部署問題** - 查看 [部署指南](DEPLOYMENT.md)
3. **API 問題** - 查看 [API 文檔](API.md)

### 聯繫方式
- **GitHub Issues**: https://github.com/jihnyu/MemberService/issues
- **項目地址**: https://github.com/jihnyu/MemberService

## 🎉 開始使用

1. **新用戶** → 從 [功能說明文檔](FEATURES.md) 開始
2. **開發者** → 從 [API 文檔](API.md) 開始
3. **運維人員** → 從 [部署指南](DEPLOYMENT.md) 開始

---

**文檔版本**: v1.0.0  
**最後更新**: 2025-06-26  
**維護者**: MemberService 開發團隊

歡迎貢獻和改進這些文檔！ 🚀
