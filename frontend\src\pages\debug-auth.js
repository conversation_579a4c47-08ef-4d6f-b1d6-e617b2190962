import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function DebugAuth() {
  const [authData, setAuthData] = useState(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    const cookies = document.cookie;

    const debugInfo = {
      token: token,
      tokenLength: token ? token.length : 0,
      userStr: userStr,
      cookies: cookies,
      localStorage: {
        length: localStorage.length,
        keys: []
      }
    };

    // 獲取所有 localStorage 鍵
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      debugInfo.localStorage.keys.push({
        key: key,
        value: localStorage.getItem(key)?.substring(0, 100) + (localStorage.getItem(key)?.length > 100 ? '...' : '')
      });
    }

    // 嘗試解析用戶數據
    if (userStr) {
      try {
        debugInfo.parsedUser = JSON.parse(userStr);
      } catch (error) {
        debugInfo.parseError = error.message;
      }
    }

    setAuthData(debugInfo);
  }, [mounted]);

  const clearAuth = () => {
    localStorage.clear();
    document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    window.location.reload();
  };

  const testLogin = async () => {
    try {
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        alert('登入成功！');
        window.location.reload();
      } else {
        alert('登入失敗: ' + result.error);
      }
    } catch (error) {
      alert('登入請求失敗: ' + error.message);
    }
  };

  if (!mounted) {
    return <div>載入中...</div>;
  }

  return (
    <>
      <Head>
        <title>認證調試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔍 認證調試</h1>
              <p className="text-gray-600 mt-2">檢查認證狀態和 localStorage 數據</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 認證狀態 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">認證狀態</h2>
                
                {authData ? (
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h3 className="font-medium text-blue-900 mb-2">🔑 Token</h3>
                      <div className="text-sm text-blue-800">
                        <div>存在: {authData.token ? '✅ 是' : '❌ 否'}</div>
                        <div>長度: {authData.tokenLength}</div>
                        {authData.token && (
                          <div className="mt-2 p-2 bg-white rounded border font-mono text-xs break-all">
                            {authData.token.substring(0, 100)}...
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <h3 className="font-medium text-green-900 mb-2">👤 用戶數據</h3>
                      <div className="text-sm text-green-800">
                        <div>存在: {authData.userStr ? '✅ 是' : '❌ 否'}</div>
                        {authData.parseError && (
                          <div className="text-red-600">解析錯誤: {authData.parseError}</div>
                        )}
                        {authData.parsedUser && (
                          <div className="mt-2 space-y-1">
                            <div>姓名: {authData.parsedUser.name}</div>
                            <div>Email: {authData.parsedUser.email}</div>
                            <div>角色: {authData.parsedUser.role}</div>
                            <div>ID: {authData.parsedUser.userId}</div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                      <h3 className="font-medium text-purple-900 mb-2">🍪 Cookies</h3>
                      <div className="text-sm text-purple-800">
                        {authData.cookies ? (
                          <div className="font-mono text-xs break-all">{authData.cookies}</div>
                        ) : (
                          <div>無 cookies</div>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div>載入認證數據中...</div>
                )}

                <div className="mt-6 space-y-3">
                  <button
                    onClick={testLogin}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    🔑 測試登入
                  </button>
                  
                  <button
                    onClick={clearAuth}
                    className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    🗑️ 清除認證數據
                  </button>

                  <button
                    onClick={() => window.location.href = '/dashboard-simple'}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    🏠 前往儀表板
                  </button>
                </div>
              </div>

              {/* localStorage 詳情 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">💾 localStorage 詳情</h2>
                
                {authData && (
                  <div className="space-y-4">
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                      <h3 className="font-medium text-gray-900 mb-2">📊 統計</h3>
                      <div className="text-sm text-gray-700">
                        <div>項目數量: {authData.localStorage.length}</div>
                      </div>
                    </div>

                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg max-h-96 overflow-y-auto">
                      <h3 className="font-medium text-gray-900 mb-2">📋 所有項目</h3>
                      <div className="space-y-2">
                        {authData.localStorage.keys.map((item, index) => (
                          <div key={index} className="p-2 bg-white rounded border">
                            <div className="font-medium text-sm text-gray-900">{item.key}</div>
                            <div className="text-xs text-gray-600 font-mono break-all mt-1">
                              {item.value}
                            </div>
                          </div>
                        ))}
                        {authData.localStorage.keys.length === 0 && (
                          <div className="text-gray-500 text-sm">localStorage 為空</div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 問題診斷 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">🔧 問題診斷</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>儀表板一直載入的可能原因：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>localStorage 中沒有 token 或 user 數據</li>
                  <li>user 數據格式錯誤，JSON 解析失敗</li>
                  <li>token 已過期或無效</li>
                  <li>瀏覽器 JavaScript 錯誤</li>
                </ul>
                <p><strong>解決方案：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>點擊「測試登入」重新獲取認證數據</li>
                  <li>如果仍有問題，點擊「清除認證數據」後重新登入</li>
                  <li>檢查瀏覽器控制台是否有 JavaScript 錯誤</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
