import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function UpdateDatabaseRole() {
  const router = useRouter();
  const [updateResults, setUpdateResults] = useState([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const [currentDbData, setCurrentDbData] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [allUsers, setAllUsers] = useState([]);
  const [selectedUserId, setSelectedUserId] = useState('');
  const [hasAdminAccess, setHasAdminAccess] = useState(false);

  useEffect(() => {
    const initializeData = async () => {
      await checkCurrentUser();
    };
    initializeData();
  }, []);

  useEffect(() => {
    if (hasAdminAccess) {
      loadAllUsers();
    }
  }, [hasAdminAccess]);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setUpdateResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const checkCurrentUser = async () => {
    try {
      console.log('檢查當前用戶...');
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');

      if (!token || !userStr) {
        console.log('沒有找到認證數據');
        setHasAdminAccess(false);
        return;
      }

      const userData = JSON.parse(userStr);
      console.log('當前用戶數據:', userData);
      setCurrentUser(userData);

      // 檢查是否為管理員
      if (userData.role === 'admin') {
        console.log('用戶是管理員，設置管理員權限');
        setHasAdminAccess(true);
      } else {
        console.log('用戶不是管理員');
        setHasAdminAccess(false);
      }
    } catch (error) {
      console.error('檢查當前用戶失敗:', error);
      setHasAdminAccess(false);
    }
  };

  const loadAllUsers = async () => {
    try {
      console.log('開始載入用戶列表...');
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('沒有找到 token');
        return;
      }

      console.log('發送資料庫查詢請求...');
      const response = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "SELECT id, name, email, role FROM members ORDER BY id"
        })
      });

      console.log('API 響應狀態:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('API 響應結果:', result);

        if (result.success) {
          console.log('載入的用戶數據:', result.data);
          setAllUsers(result.data);
          addResult('載入用戶', 'success', `成功載入 ${result.data.length} 個用戶`);
        } else {
          console.error('API 返回失敗:', result.error);
          addResult('載入用戶', 'error', `載入失敗: ${result.error}`);
        }
      } else {
        const errorResult = await response.json();
        console.error('API 請求失敗:', response.status, errorResult);
        addResult('載入用戶', 'error', `API 請求失敗: ${response.status}`);
      }
    } catch (error) {
      console.error('載入用戶列表失敗:', error);
      addResult('載入用戶', 'error', `載入用戶列表失敗: ${error.message}`);
    }
  };

  const checkSelectedUserRole = async (userId) => {
    try {
      const token = localStorage.getItem('token');
      if (!token || !userId) return;

      const response = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "SELECT id, name, email, role FROM members WHERE id = ?",
          params: [userId]
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.length > 0) {
          setCurrentDbData(result.data[0]);
        }
      }
    } catch (error) {
      console.error('檢查選中用戶角色失敗:', error);
    }
  };

  const updateDatabaseRole = async (newRole) => {
    if (!selectedUserId) {
      alert('請先選擇要更新的用戶');
      return;
    }

    if (!hasAdminAccess) {
      alert('只有管理員可以執行此操作');
      return;
    }

    setIsUpdating(true);
    setUpdateResults([]);

    try {
      addResult('開始', 'info', `開始更新用戶角色為 ${newRole === 'user' ? '一般會員' : '管理員'}`);

      // 檢查認證
      const token = localStorage.getItem('token');
      if (!token) {
        addResult('認證', 'error', '未找到認證 token');
        return;
      }

      // 步驟 1: 檢查選中用戶的當前角色
      addResult('步驟1', 'info', '檢查選中用戶的當前角色...');

      const checkResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "SELECT id, name, email, role FROM members WHERE id = ?",
          params: [selectedUserId]
        })
      });

      if (checkResponse.ok) {
        const checkResult = await checkResponse.json();
        if (checkResult.success && checkResult.data.length > 0) {
          const targetUser = checkResult.data[0];
          addResult('步驟1', 'success', `找到用戶: ${targetUser.name} (${targetUser.email})`);
          addResult('步驟1', 'info', `當前角色: ${targetUser.role}`);
          addResult('步驟1', 'info', `用戶 ID: ${targetUser.id}`);
        } else {
          addResult('步驟1', 'error', '在資料庫中找不到該用戶');
          return;
        }
      } else {
        addResult('步驟1', 'error', '無法查詢資料庫');
        return;
      }

      // 步驟 2: 更新資料庫中的角色
      const roleText = newRole === 'user' ? '一般會員' : '管理員';
      addResult('步驟2', 'info', `更新資料庫中的角色為 ${roleText}...`);

      const updateResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "UPDATE members SET role = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
          params: [newRole, selectedUserId]
        })
      });

      if (updateResponse.ok) {
        const updateResult = await updateResponse.json();
        if (updateResult.success) {
          addResult('步驟2', 'success', '資料庫角色更新成功');
        } else {
          addResult('步驟2', 'error', `更新失敗: ${updateResult.error}`);
          return;
        }
      } else {
        addResult('步驟2', 'error', '更新請求失敗');
        return;
      }

      // 步驟 3: 驗證更新結果
      addResult('步驟3', 'info', '驗證更新結果...');
      
      const verifyResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "SELECT id, name, email, role FROM members WHERE email = ?",
          params: ['<EMAIL>']
        })
      });

      if (verifyResponse.ok) {
        const verifyResult = await verifyResponse.json();
        if (verifyResult.success && verifyResult.data.length > 0) {
          const updatedUser = verifyResult.data[0];
          addResult('步驟3', 'success', `驗證成功，新角色: ${updatedUser.role}`);
          setCurrentDbData(updatedUser);
          
          if (updatedUser.role === 'user') {
            addResult('步驟3', 'success', '資料庫角色已成功更新為一般會員');
          } else {
            addResult('步驟3', 'warning', `角色更新可能未生效，當前角色: ${updatedUser.role}`);
          }
        }
      }

      // 步驟 4: 更新姓名為一般會員
      addResult('步驟4', 'info', '更新用戶姓名為一般會員...');
      
      const nameResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "UPDATE members SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE email = ?",
          params: ['一般會員', '<EMAIL>']
        })
      });

      if (nameResponse.ok) {
        const nameResult = await nameResponse.json();
        if (nameResult.success) {
          addResult('步驟4', 'success', '用戶姓名更新為一般會員');
        }
      }

      addResult('完成', 'success', '資料庫角色更新完成！');

    } catch (error) {
      addResult('錯誤', 'error', '更新過程中出現錯誤: ' + error.message);
    } finally {
      setIsUpdating(false);
    }
  };

  const revertToAdmin = async () => {
    setIsUpdating(true);
    setUpdateResults([]);

    try {
      addResult('開始', 'info', '開始恢復管理員角色');

      const token = localStorage.getItem('token');
      if (!token) {
        addResult('認證', 'error', '未找到認證 token');
        return;
      }

      // 更新為管理員角色
      const updateResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "UPDATE members SET role = ?, name = ?, updated_at = CURRENT_TIMESTAMP WHERE email = ?",
          params: ['admin', '系統管理員', '<EMAIL>']
        })
      });

      if (updateResponse.ok) {
        const updateResult = await updateResponse.json();
        if (updateResult.success) {
          addResult('完成', 'success', '已恢復為管理員角色');
          checkDatabaseRole();
        }
      }

    } catch (error) {
      addResult('錯誤', 'error', '恢復過程中出現錯誤: ' + error.message);
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>更新資料庫角色 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🗄️ 更新資料庫角色</h1>
                <p className="text-gray-600 mt-2">管理員專用：更新用戶在資料庫中的角色</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/switch-user-role')}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  👤 角色切換
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
              </div>
            </div>

            {/* 權限檢查 */}
            {!hasAdminAccess && (
              <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
                <h3 className="font-medium text-red-900 mb-2">❌ 權限不足</h3>
                <div className="text-sm text-red-800">
                  <div>只有管理員可以使用此功能。</div>
                  <div>當前用戶：{currentUser?.name || '未知'} ({currentUser?.role || '未知'})</div>
                  <div>請使用管理員帳號登入後再試。</div>
                </div>
              </div>
            )}

            {/* 當前操作者信息 */}
            {currentUser && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">👨‍💼 當前操作者</h2>
                <div className={`rounded-lg p-4 border ${
                  hasAdminAccess ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <div className="text-sm space-y-1">
                        <div className="font-medium">姓名</div>
                        <div>{currentUser.name}</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm space-y-1">
                        <div className="font-medium">Email</div>
                        <div>{currentUser.email}</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm space-y-1">
                        <div className="font-medium">角色</div>
                        <div className={`font-semibold ${
                          currentUser.role === 'admin' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {currentUser.role === 'admin' ? '👨‍💼 管理員' : '👤 一般會員'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 用戶選擇 */}
            {hasAdminAccess && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">👥 選擇要更新的用戶</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <label className="block text-sm font-medium text-gray-700">
                        選擇用戶
                      </label>
                      <button
                        onClick={loadAllUsers}
                        className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
                      >
                        🔄 重新載入
                      </button>
                    </div>
                    <select
                      value={selectedUserId}
                      onChange={(e) => {
                        setSelectedUserId(e.target.value);
                        if (e.target.value) {
                          checkSelectedUserRole(e.target.value);
                        }
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">
                        {allUsers.length === 0 ? '載入中...' : '請選擇用戶...'}
                      </option>
                      {allUsers.map(user => (
                        <option key={user.id} value={user.id}>
                          {user.name} ({user.email}) - {user.role === 'admin' ? '管理員' : '一般會員'}
                        </option>
                      ))}
                    </select>
                    <div className="mt-2 text-sm text-gray-600">
                      已載入 {allUsers.length} 個用戶
                    </div>
                  </div>

                  {selectedUserId && (
                    <div className="flex space-x-4">
                      <button
                        onClick={() => updateDatabaseRole('user')}
                        disabled={isUpdating}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                      >
                        {isUpdating ? '更新中...' : '👤 設為一般會員'}
                      </button>
                      <button
                        onClick={() => updateDatabaseRole('admin')}
                        disabled={isUpdating}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
                      >
                        {isUpdating ? '更新中...' : '👨‍💼 設為管理員'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 選中用戶的當前數據 */}
            {currentDbData && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 選中用戶的資料庫數據</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm space-y-2">
                        <div>用戶 ID: {currentDbData.id}</div>
                        <div>姓名: {currentDbData.name}</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm space-y-2">
                        <div>Email: {currentDbData.email}</div>
                        <div>角色: <span className={`font-semibold ${
                          currentDbData.role === 'admin' ? 'text-red-600' : 'text-blue-600'
                        }`}>
                          {currentDbData.role === 'admin' ? '👨‍💼 管理員' : '👤 一般會員'}
                        </span></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 更新結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 更新結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {updateResults.length > 0 ? (
                  <div className="space-y-3">
                    {updateResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「更新為一般會員」來更新資料庫中的角色
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📋 功能說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>權限要求:</strong> 只有管理員可以使用此功能</div>
                <div><strong>功能:</strong> 直接更新資料庫中任何用戶的角色</div>
                <div><strong>操作步驟:</strong> 1. 選擇要更新的用戶 → 2. 選擇新角色 → 3. 確認更新</div>
                <div><strong>影響:</strong> 更新後該用戶需要重新登入才能看到角色變化</div>
                <div><strong>安全性:</strong> 所有操作都會記錄在更新結果中</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
