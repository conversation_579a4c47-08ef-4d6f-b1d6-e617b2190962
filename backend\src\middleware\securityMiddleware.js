const SecurityAuditService = require('../services/SecurityAuditService');
const EncryptionService = require('../services/EncryptionService');
const { query } = require('../config/database');

// IP 黑名單檢查
const ipBlacklistCheck = async (req, res, next) => {
  try {
    const clientIP = req.ip || req.connection.remoteAddress;

    // 檢查 IP 是否被封鎖
    const result = await query(
      'SELECT * FROM blocked_ips WHERE ip_address = $1 AND expires_at > CURRENT_TIMESTAMP',
      [clientIP]
    );

    if (result.rows.length > 0) {
      const blockInfo = result.rows[0];

      SecurityAuditService.logSecurityEvent(
        null,
        'blocked_ip_access_attempt',
        `被封鎖的 IP ${clientIP} 嘗試存取`,
        {
          ipAddress: clientIP,
          blockReason: blockInfo.reason,
          blockedAt: blockInfo.blocked_at,
          expiresAt: blockInfo.expires_at
        }
      );

      return res.status(403).json({
        success: false,
        message: '您的 IP 地址已被封鎖',
        error: 'IP blocked',
        expiresAt: blockInfo.expires_at
      });
    }

    next();
  } catch (error) {
    console.error('IP blacklist check error:', error);
    next(); // 發生錯誤時繼續執行，避免阻斷正常流量
  }
};

// 用戶狀態檢查
const userStatusCheck = async (req, res, next) => {
  try {
    if (!req.user || !req.user.id) {
      return next();
    }

    const result = await query(
      'SELECT status, locked_at, lock_reason, requires_verification FROM users WHERE id = $1',
      [req.user.id]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用戶不存在',
        error: 'User not found'
      });
    }

    const user = result.rows[0];

    // 檢查用戶狀態
    if (user.status === 'suspended') {
      SecurityAuditService.logSecurityEvent(
        req.user.id,
        'suspended_user_access_attempt',
        '被暫停的用戶嘗試存取',
        {
          lockReason: user.lock_reason,
          lockedAt: user.locked_at,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      );

      return res.status(403).json({
        success: false,
        message: '您的帳號已被暫停',
        error: 'Account suspended',
        reason: user.lock_reason
      });
    }

    if (user.status === 'deleted') {
      return res.status(410).json({
        success: false,
        message: '帳號已刪除',
        error: 'Account deleted'
      });
    }

    // 檢查是否需要額外驗證
    if (user.requires_verification) {
      return res.status(403).json({
        success: false,
        message: '需要額外驗證',
        error: 'Additional verification required',
        requiresVerification: true
      });
    }

    next();
  } catch (error) {
    console.error('User status check error:', error);
    next();
  }
};

// 敏感操作額外驗證
const sensitiveOperationCheck = (req, res, next) => {
  const sensitiveEndpoints = [
    '/users/change-password',
    '/users/delete-account',
    '/payments/stripe',
    '/payments/paypal',
    '/wallets/transfer'
  ];

  const isSensitiveOperation = sensitiveEndpoints.some(endpoint =>
    req.originalUrl.includes(endpoint)
  );

  if (isSensitiveOperation) {
    // 檢查是否提供了額外的驗證
    const verificationCode = req.get('X-Verification-Code');
    const transactionPassword = req.body.transactionPassword;

    if (!verificationCode && !transactionPassword) {
      return res.status(403).json({
        success: false,
        message: '敏感操作需要額外驗證',
        error: 'Additional verification required',
        requiresVerification: true
      });
    }

    // 記錄敏感操作
    SecurityAuditService.logUserActivity(
      req.user?.id || null,
      'sensitive_operation',
      `敏感操作: ${req.method} ${req.originalUrl}`,
      {
        endpoint: req.originalUrl,
        method: req.method,
        hasVerificationCode: !!verificationCode,
        hasTransactionPassword: !!transactionPassword,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    );
  }

  next();
};

// 安全標頭增強
const enhancedSecurityHeaders = (req, res, next) => {
  // 生成 CSP Nonce
  const nonce = EncryptionService.generateCSPNonce();

  // 設置增強的安全標頭
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

  // 嚴格的 CSP
  const csp = [
    "default-src 'self'",
    `script-src 'self' 'nonce-${nonce}'`,
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self'",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ');

  res.setHeader('Content-Security-Policy', csp);

  next();
};

module.exports = {
  ipBlacklistCheck,
  userStatusCheck,
  sensitiveOperationCheck,
  enhancedSecurityHeaders
};