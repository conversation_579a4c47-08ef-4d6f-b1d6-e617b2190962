/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // 環境變數配置
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
    API_BASE_URL: process.env.API_BASE_URL || 'https://topyun.ngrok.app',
  },

  // API 代理配置 (暫時禁用，使用本地 API)
  // async rewrites() {
  //   return [
  //     {
  //       source: '/api/:path*',
  //       destination: `${process.env.API_BASE_URL || 'https://topyun.ngrok.app'}/api/:path*`,
  //     },
  //   ];
  // },

  // 圖片優化配置
  images: {
    domains: ['localhost', 'topyun.ngrok.app', 'example.com'],
    formats: ['image/webp', 'image/avif'],
  },

  // 國際化配置
  i18n: {
    locales: ['zh-TW', 'en'],
    defaultLocale: 'zh-TW',
  },

  // 編譯配置
  compiler: {
    // 移除 console.log (生產環境)
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // 實驗性功能
  experimental: {
    // 啟用 App Router (Next.js 13+)
    appDir: false,
    // 修復 Turbopack 相關問題
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },

  // 開發環境配置
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // 修復 HMR 相關問題
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };
    }
    return config;
  },

  // 輸出配置 (開發環境不需要)
  // output: 'standalone',

  // 安全標頭
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
