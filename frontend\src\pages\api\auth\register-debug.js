// 註冊 API 調試版本
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const path = require('path');
const fs = require('fs');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();
    
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];
    
    console.log('註冊調試: 開始搜尋資料庫文件...');
    
    for (const dbPath of possiblePaths) {
      console.log('註冊調試: 檢查路徑:', dbPath);
      if (fs.existsSync(dbPath)) {
        console.log('註冊調試: ✅ 找到資料庫文件:', dbPath);
        const stats = fs.statSync(dbPath);
        console.log('註冊調試: 文件大小:', Math.round(stats.size / 1024), 'KB');
        return new sqlite3.Database(dbPath);
      }
    }
    
    console.log('註冊調試: ❌ 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('註冊調試: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  console.log('註冊調試: API 被調用');
  console.log('註冊調試: 請求方法:', req.method);
  console.log('註冊調試: 請求體:', req.body);

  if (req.method !== 'POST') {
    console.log('註冊調試: 方法不允許');
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    const { name, email, password, confirmPassword, phone } = req.body;

    console.log('註冊調試: 解析請求數據:', { name, email, phone, password: '***', confirmPassword: '***' });

    // 基本驗證
    if (!name || !email || !password) {
      console.log('註冊調試: 缺少必要欄位');
      return res.status(400).json({
        success: false,
        error: '請提供姓名、email 和密碼'
      });
    }

    if (password !== confirmPassword) {
      console.log('註冊調試: 密碼確認不匹配');
      return res.status(400).json({
        success: false,
        error: '密碼確認不匹配'
      });
    }

    // Email 格式驗證
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.log('註冊調試: Email 格式錯誤');
      return res.status(400).json({
        success: false,
        error: 'email 格式不正確'
      });
    }

    // 密碼長度驗證
    if (password.length < 4) {
      console.log('註冊調試: 密碼太短');
      return res.status(400).json({
        success: false,
        error: '密碼長度至少需要 4 個字符'
      });
    }

    console.log('註冊調試: 基本驗證通過');

    // 連接資料庫
    const db = connectDatabase();
    if (!db) {
      console.log('註冊調試: 資料庫連接失敗');
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    console.log('註冊調試: 資料庫連接成功');

    // 檢查 email 是否已存在
    console.log('註冊調試: 檢查 email 是否已存在...');
    
    db.get('SELECT id FROM members WHERE email = ?', [email.toLowerCase()], async (err, row) => {
      if (err) {
        console.error('註冊調試: 查詢用戶失敗:', err);
        db.close();
        return res.status(500).json({
          success: false,
          error: '資料庫查詢失敗: ' + err.message
        });
      }

      if (row) {
        console.log('註冊調試: Email 已存在');
        db.close();
        return res.status(409).json({
          success: false,
          error: '此電子郵件已被註冊'
        });
      }

      console.log('註冊調試: Email 可用，開始創建用戶');

      try {
        // 生成密碼哈希
        console.log('註冊調試: 生成密碼哈希...');
        const saltRounds = 10;
        const passwordHash = await bcrypt.hash(password, saltRounds);
        console.log('註冊調試: 密碼哈希生成成功');

        // 插入新會員到資料庫
        console.log('註冊調試: 插入新會員到資料庫...');
        const insertQuery = `
          INSERT INTO members (email, name, password_hash, role, status, phone, email_verified)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `;

        db.run(insertQuery, [
          email.toLowerCase().trim(),
          name.trim(),
          passwordHash,
          'user',
          'active',
          phone?.trim() || null,
          0 // email_verified = false
        ], function(insertErr) {
          if (insertErr) {
            console.error('註冊調試: 插入用戶失敗:', insertErr);
            db.close();
            return res.status(500).json({
              success: false,
              error: '創建用戶失敗: ' + insertErr.message
            });
          }

          const newUserId = this.lastID;
          console.log('註冊調試: 用戶創建成功，ID:', newUserId);

          // 查詢剛創建的用戶完整信息
          console.log('註冊調試: 查詢新創建的用戶...');
          db.get('SELECT id, email, name, role, status, phone, email_verified, created_at FROM members WHERE id = ?', 
            [newUserId], (selectErr, member) => {
            
            if (selectErr || !member) {
              console.error('註冊調試: 查詢新用戶失敗:', selectErr);
              db.close();
              return res.status(500).json({
                success: false,
                error: '用戶創建後查詢失敗: ' + (selectErr?.message || '用戶不存在')
              });
            }

            console.log('註冊調試: 查詢新用戶成功:', member);

            // 生成 JWT Token
            console.log('註冊調試: 生成 JWT Token...');
            const token = jwt.sign(
              { 
                userId: member.id, 
                email: member.email,
                role: member.role 
              },
              JWT_SECRET,
              { expiresIn: '7d' }
            );

            console.log('註冊調試: JWT Token 生成成功');

            // 準備用戶數據
            const userData = {
              id: member.id,
              email: member.email,
              name: member.name,
              role: member.role,
              status: member.status,
              phone: member.phone,
              email_verified: member.email_verified,
              created_at: member.created_at
            };

            console.log('註冊調試: 準備返回響應...');

            // 關閉資料庫連接
            db.close((closeErr) => {
              if (closeErr) {
                console.error('註冊調試: 關閉資料庫失敗:', closeErr);
              } else {
                console.log('註冊調試: 資料庫連接已關閉');
              }
            });

            // 返回成功響應
            console.log('註冊調試: 註冊完成，返回成功響應');
            return res.status(201).json({
              success: true,
              data: {
                token,
                user: userData,
                subscriptions: []
              },
              message: '註冊成功！數據已保存到資料庫',
              debug: {
                userId: newUserId,
                email: email,
                timestamp: new Date().toISOString()
              }
            });
          });
        });

      } catch (hashError) {
        console.error('註冊調試: 密碼哈希生成失敗:', hashError);
        db.close();
        return res.status(500).json({
          success: false,
          error: '密碼處理失敗: ' + hashError.message
        });
      }
    });

  } catch (error) {
    console.error('註冊調試: 未預期的錯誤:', error);
    
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}
