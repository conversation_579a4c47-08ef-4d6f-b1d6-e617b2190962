import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function NoAuthLogicTest() {
  const router = useRouter();
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const runLogicTest = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      addResult('開始', 'info', '開始測試前端邏輯（無認證版本）');

      // 1. 載入訂閱數據（使用測試 API）
      addResult('步驟1', 'info', '載入訂閱數據（測試 API）...');
      const subscriptionsResponse = await fetch('/api/subscriptions/test', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const subscriptionsResult = await subscriptionsResponse.json();
      if (!subscriptionsResult.success) {
        addResult('步驟1', 'error', '載入訂閱數據失敗: ' + subscriptionsResult.error);
        return;
      }

      const subscriptions = subscriptionsResult.data;
      addResult('步驟1', 'success', `載入了 ${subscriptions.length} 個訂閱記錄`, subscriptions);

      // 2. 載入所有服務
      addResult('步驟2', 'info', '載入所有服務數據...');
      const servicesResponse = await fetch('/api/services/database');
      const servicesResult = await servicesResponse.json();
      
      if (!servicesResult.success) {
        addResult('步驟2', 'error', '載入服務數據失敗: ' + servicesResult.error);
        return;
      }

      const allServices = servicesResult.data.filter(s => s.status === 'active');
      addResult('步驟2', 'success', `載入了 ${allServices.length} 個活躍服務`, allServices);

      // 3. 分析訂閱數據
      addResult('步驟3', 'info', '分析訂閱數據...');
      const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
      const cancelledSubscriptions = subscriptions.filter(sub => sub.status === 'cancelled');
      const expiredSubscriptions = subscriptions.filter(sub => sub.status === 'expired');

      addResult('步驟3', 'success', `活躍訂閱: ${activeSubscriptions.length}, 已取消: ${cancelledSubscriptions.length}, 已過期: ${expiredSubscriptions.length}`);

      // 4. 計算活躍訂閱的服務 ID
      addResult('步驟4', 'info', '計算活躍訂閱的服務 ID...');
      const activeSubscribedServiceIds = activeSubscriptions.map(sub => sub.service_id);
      addResult('步驟4', 'success', `活躍訂閱的服務 ID: [${activeSubscribedServiceIds.join(', ')}]`, activeSubscribedServiceIds);

      // 5. 計算可用服務（前端邏輯）
      addResult('步驟5', 'info', '使用前端邏輯計算可用服務...');
      const availableServices = allServices.filter(service =>
        !activeSubscribedServiceIds.includes(service.id)
      );
      addResult('步驟5', 'success', `計算出 ${availableServices.length} 個可用服務`, availableServices.map(s => ({ id: s.id, name: s.name })));

      // 6. 驗證數量平衡
      addResult('步驟6', 'info', '驗證數量平衡...');
      const totalServices = allServices.length;
      const calculatedTotal = activeSubscriptions.length + availableServices.length;
      
      if (calculatedTotal === totalServices) {
        addResult('步驟6', 'success', `數量平衡正確: ${activeSubscriptions.length} + ${availableServices.length} = ${totalServices}`);
      } else {
        addResult('步驟6', 'error', `數量不平衡: ${activeSubscriptions.length} + ${availableServices.length} = ${calculatedTotal} ≠ ${totalServices}`);
      }

      // 7. 檢查重複
      addResult('步驟7', 'info', '檢查重複問題...');
      const duplicateIds = activeSubscribedServiceIds.filter(id =>
        availableServices.some(service => service.id === id)
      );

      if (duplicateIds.length === 0) {
        addResult('步驟7', 'success', '沒有發現重複的服務 ID');
      } else {
        addResult('步驟7', 'error', `發現重複的服務 ID: [${duplicateIds.join(', ')}]`, duplicateIds);
      }

      // 8. 測試訂閱功能（如果有可用服務）
      if (availableServices.length > 0) {
        addResult('步驟8', 'info', '測試訂閱功能...');
        const testService = availableServices[0];
        
        const subscribeResponse = await fetch('/api/subscriptions/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            serviceId: testService.id
          })
        });

        const subscribeResult = await subscribeResponse.json();
        
        if (subscribeResult.success) {
          addResult('步驟8', 'success', `成功訂閱服務 ${testService.id}: ${testService.name}`, subscribeResult.data);
          
          // 重新載入數據驗證
          const newSubscriptionsResponse = await fetch('/api/subscriptions/test');
          const newSubscriptionsResult = await newSubscriptionsResponse.json();
          
          if (newSubscriptionsResult.success) {
            const newActiveSubscriptions = newSubscriptionsResult.data.filter(sub => sub.status === 'active');
            const newActiveIds = newActiveSubscriptions.map(sub => sub.service_id);
            const newAvailableServices = allServices.filter(service =>
              !newActiveIds.includes(service.id)
            );
            
            addResult('步驟8', 'success', `訂閱後驗證: 活躍訂閱 ${newActiveSubscriptions.length}, 可用服務 ${newAvailableServices.length}`);
            
            const newCalculatedTotal = newActiveSubscriptions.length + newAvailableServices.length;
            if (newCalculatedTotal === totalServices) {
              addResult('步驟8', 'success', '訂閱後數量仍然平衡 ✅');
            } else {
              addResult('步驟8', 'error', `訂閱後數量不平衡: ${newCalculatedTotal} ≠ ${totalServices}`);
            }
          }
        } else {
          addResult('步驟8', 'warning', `訂閱測試失敗: ${subscribeResult.error}`);
        }
      } else {
        addResult('步驟8', 'info', '沒有可用服務可供測試訂閱');
      }

      addResult('完成', 'success', '前端邏輯測試完成（無認證版本）');

    } catch (error) {
      addResult('錯誤', 'error', '測試過程中出現錯誤: ' + error.message);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>無認證邏輯測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🧪 無認證邏輯測試</h1>
                <p className="text-gray-600 mt-2">測試前端邏輯（不需要 Token）</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/token-fix')}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  🔧 修復 Token
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={runLogicTest}
                  disabled={isRunning}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
                >
                  {isRunning ? '測試中...' : '🧪 開始測試'}
                </button>
              </div>
            </div>

            {/* 測試結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 測試結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {testResults.length > 0 ? (
                  <div className="space-y-3">
                    {testResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                            {result.data && (
                              <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono max-h-32 overflow-y-auto">
                                <pre className="whitespace-pre-wrap">
                                  {JSON.stringify(result.data, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「開始測試」來測試前端邏輯（無需認證）
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 測試內容</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>優勢:</strong> 不需要 Token，可以直接測試前端邏輯</div>
                <div><strong>步驟1-2:</strong> 載入訂閱和服務數據（使用測試 API）</div>
                <div><strong>步驟3-4:</strong> 分析訂閱狀態和活躍服務 ID</div>
                <div><strong>步驟5-6:</strong> 計算可用服務和驗證數量平衡</div>
                <div><strong>步驟7:</strong> 檢查重複問題</div>
                <div><strong>步驟8:</strong> 實際測試訂閱功能</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
