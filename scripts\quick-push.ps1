# 快速推送腳本 (PowerShell 版本)
# 用於快速提交和推送代碼到 GitHub

param(
    [string]$Message = "更新代碼",
    [string]$Branch = "",
    [string]$RepoUrl = ""
)

# 顏色函數
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-Host "🚀 快速推送到 GitHub..." -ForegroundColor Cyan

# 1. 檢查 Git 狀態
$gitStatus = git status --porcelain
if ($gitStatus) {
    Write-Info "發現未提交的變更"
    Write-Host "變更的文件:"
    git status --short
    
    # 添加所有變更
    Write-Info "添加所有變更..."
    git add .
    
    # 提交變更
    Write-Info "提交變更: $Message"
    git commit -m $Message --no-verify
    
    Write-Success "變更已提交"
} else {
    Write-Info "沒有未提交的變更"
}

# 2. 獲取當前分支
$currentBranch = git branch --show-current
if (-not $Branch) {
    $Branch = $currentBranch
}

Write-Info "當前分支: $currentBranch"
Write-Info "目標分支: $Branch"

# 3. 檢查遠端倉庫
$remoteUrl = git remote get-url origin 2>$null
if (-not $remoteUrl) {
    if ($RepoUrl) {
        Write-Info "設置遠端倉庫: $RepoUrl"
        git remote add origin $RepoUrl
    } else {
        Write-Error "未設置遠端倉庫，請提供 -RepoUrl 參數或手動設置"
        Write-Host "範例: git remote add origin https://github.com/username/repo.git"
        exit 1
    }
} else {
    Write-Info "遠端倉庫: $remoteUrl"
}

# 4. 推送到遠端
Write-Info "推送到 origin/$Branch..."
try {
    git push origin $Branch
    Write-Success "成功推送到 origin/$Branch"
} catch {
    Write-Warning "推送失敗，嘗試強制推送..."
    $forceChoice = Read-Host "是否要強制推送? (y/N)"
    if ($forceChoice -eq "y" -or $forceChoice -eq "Y") {
        git push origin $Branch --force
        Write-Success "強制推送完成"
    } else {
        Write-Error "推送取消"
        exit 1
    }
}

# 5. 顯示結果
Write-Host ""
Write-Success "🎉 推送完成！"
Write-Host ""
Write-Host "📋 推送摘要:"
Write-Host "   分支: $Branch"
Write-Host "   提交: $(git rev-parse --short HEAD)"
Write-Host "   時間: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Host ""

# 6. 顯示 GitHub 連結
if ($remoteUrl -match "github.com") {
    $repoName = ($remoteUrl -replace "\.git$", "") -replace ".*github\.com[:/]", ""
    $githubUrl = "https://github.com/$repoName"
    Write-Host "🔗 GitHub 倉庫: $githubUrl"
    Write-Host "🔗 Actions: $githubUrl/actions"
    Write-Host ""
}

Write-Host "✅ 完成！代碼已推送到 GitHub"
