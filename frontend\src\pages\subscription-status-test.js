import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function SubscriptionStatusTest() {
  const [subscriptions, setSubscriptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    loadSubscriptions();
  }, []);

  const loadSubscriptions = async () => {
    setIsLoading(true);
    addLog('🔄 載入訂閱數據...', 'info');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        addLog('❌ 未登入', 'error');
        return;
      }

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        setSubscriptions(result.data);
        addLog(`✅ 載入 ${result.data.length} 個訂閱`, 'success');
        
        // 統計各狀態的訂閱數量
        const statusCount = result.data.reduce((acc, sub) => {
          acc[sub.status] = (acc[sub.status] || 0) + 1;
          return acc;
        }, {});
        
        Object.entries(statusCount).forEach(([status, count]) => {
          addLog(`  📊 ${status}: ${count} 個`, 'info');
        });
      } else {
        addLog(`❌ 載入失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 請求失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const testCancelSubscription = async (subscriptionId) => {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    addLog(`🔄 測試取消訂閱: ${subscription.service_name}`, 'info');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/subscriptions/cancel', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription_id: subscriptionId
        })
      });

      const result = await response.json();

      if (result.success) {
        addLog(`✅ 取消成功: ${result.message}`, 'success');
        
        // 更新本地狀態
        setSubscriptions(prev => prev.map(sub => 
          sub.id === subscriptionId 
            ? { ...sub, status: 'cancelled' }
            : sub
        ));
      } else {
        addLog(`❌ 取消失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 取消請求失敗: ${error.message}`, 'error');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return '使用中';
      case 'cancelled': return '已取消';
      case 'expired': return '已過期';
      default: return status;
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <>
      <Head>
        <title>訂閱狀態測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">📊 訂閱狀態測試</h1>
              <p className="text-gray-600 mt-2">測試訂閱取消功能 - 狀態更新 vs 資料刪除</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 控制面板 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試控制</h2>
                
                <div className="space-y-4">
                  <button
                    onClick={loadSubscriptions}
                    disabled={isLoading}
                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isLoading ? '載入中...' : '🔄 重新載入訂閱'}
                  </button>

                  <button
                    onClick={clearLogs}
                    className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    🗑️ 清除日誌
                  </button>
                </div>

                {/* 訂閱列表 */}
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">我的訂閱</h3>
                  
                  {subscriptions.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      暫無訂閱數據
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {subscriptions.map((subscription) => (
                        <div key={subscription.id} className="p-4 border border-gray-200 rounded-lg">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h4 className="font-medium text-gray-900">
                                {subscription.service_name}
                              </h4>
                              <p className="text-sm text-gray-600">
                                ID: {subscription.id} | 服務ID: {subscription.service_id}
                              </p>
                            </div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                              {getStatusText(subscription.status)}
                            </span>
                          </div>
                          
                          <div className="text-sm text-gray-600 mb-3">
                            <div>價格: ${subscription.service_price}/{subscription.service_billing_cycle}</div>
                            <div>訂閱時間: {new Date(subscription.subscribed_at).toLocaleString()}</div>
                          </div>

                          {subscription.status === 'active' && (
                            <button
                              onClick={() => testCancelSubscription(subscription.id)}
                              className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                            >
                              🚫 測試取消
                            </button>
                          )}

                          {subscription.status === 'cancelled' && (
                            <div className="text-sm text-gray-500 italic">
                              ✅ 已取消（資料保留在資料庫中）
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* 測試日誌 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試日誌</h2>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                  {logs.map((log) => (
                    <div key={log.id} className={`mb-1 ${
                      log.type === 'error' ? 'text-red-400' : 
                      log.type === 'success' ? 'text-green-400' : 
                      'text-blue-400'
                    }`}>
                      [{log.timestamp}] {log.message}
                    </div>
                  ))}
                  {logs.length === 0 && (
                    <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                  )}
                </div>
              </div>
            </div>

            {/* 設計說明 */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 正確做法 */}
              <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-medium text-green-900 mb-3">✅ 正確做法：狀態更新</h3>
                <div className="text-sm text-green-800 space-y-2">
                  <p><strong>操作：</strong> UPDATE status = 'cancelled'</p>
                  <p><strong>優點：</strong></p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li>保留歷史記錄</li>
                    <li>支持數據分析</li>
                    <li>符合審計要求</li>
                    <li>可以重新激活</li>
                  </ul>
                </div>
              </div>

              {/* 錯誤做法 */}
              <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
                <h3 className="font-medium text-red-900 mb-3">❌ 錯誤做法：刪除資料</h3>
                <div className="text-sm text-red-800 space-y-2">
                  <p><strong>操作：</strong> DELETE FROM member_services</p>
                  <p><strong>問題：</strong></p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li>數據永久丟失</li>
                    <li>無法追蹤歷史</li>
                    <li>影響財務記錄</li>
                    <li>不符合最佳實踐</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* 資料庫狀態說明 */}
            <div className="mt-6 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🗄️ 資料庫狀態管理</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>狀態值定義：</strong></p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                  <div className="flex items-center">
                    <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                    <span>active - 使用中</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 bg-gray-500 rounded-full mr-2"></span>
                    <span>cancelled - 已取消</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                    <span>expired - 已過期</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                    <span>suspended - 暫停</span>
                  </div>
                </div>
                <p className="mt-3"><strong>取消流程：</strong> 用戶點擊取消 → API 更新狀態 → 前端顯示已取消 → 資料保留在資料庫</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
