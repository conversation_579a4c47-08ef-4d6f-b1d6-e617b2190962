// 密碼哈希生成工具 API
const bcrypt = require('bcrypt');

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允許'
    });
  }

  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        error: '密碼為必填欄位'
      });
    }

    console.log('生成密碼哈希:', password);

    // 生成 bcrypt 哈希 (10 rounds)
    const saltRounds = 10;
    const hash = await bcrypt.hash(password, saltRounds);

    console.log('生成的哈希:', hash);

    res.status(200).json({
      success: true,
      hash: hash,
      password: password,
      saltRounds: saltRounds
    });

  } catch (error) {
    console.error('密碼哈希生成錯誤:', error);
    
    res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: error.message
    });
  }
}
