// 測試資料庫連接
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = 'C:\\Users\\<USER>\\member\\frontend\\lib\\database\\database.sqlite';

console.log('測試資料庫連接...');
console.log('資料庫路徑:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 資料庫連接失敗:', err.message);
    process.exit(1);
  } else {
    console.log('✅ 資料庫連接成功');
    
    // 測試查詢
    db.get('SELECT COUNT(*) as count FROM members', (err, row) => {
      if (err) {
        console.error('❌ 查詢失敗:', err.message);
      } else {
        console.log('✅ 查詢成功，會員數量:', row.count);
      }
      
      // 查找管理員用戶
      db.get('SELECT * FROM members WHERE email = ?', ['<EMAIL>'], (err, user) => {
        if (err) {
          console.error('❌ 查找用戶失敗:', err.message);
        } else if (user) {
          console.log('✅ 找到管理員用戶:', { id: user.id, email: user.email, role: user.role });
        } else {
          console.log('❌ 未找到管理員用戶');
        }
        
        db.close();
        process.exit(0);
      });
    });
  }
});
