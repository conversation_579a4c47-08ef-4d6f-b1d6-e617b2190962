import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function SubscriptionTest() {
  const router = useRouter();
  const [allServices, setAllServices] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [subscriptions, setSubscriptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev]);
    console.log(message);
  };

  // 載入所有服務
  const loadAllServices = async () => {
    try {
      setIsLoading(true);
      addLog('🔄 載入所有服務...');
      
      const response = await fetch('/api/services');
      const result = await response.json();
      
      if (result.success && result.data) {
        setAllServices(result.data);
        addLog(`📦 載入 ${result.data.length} 個服務`);
        
        // 過濾啟用的服務作為可用服務
        const activeServices = result.data.filter(s => s.status === 'active');
        setAvailableServices(activeServices);
        addLog(`✅ ${activeServices.length} 個啟用服務可供訂閱`);
        
        return result.data;
      } else {
        addLog('❌ API 響應無效');
        return [];
      }
    } catch (error) {
      addLog(`❌ 載入失敗: ${error.message}`);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // 訂閱服務
  const handleSubscribe = (serviceId) => {
    const service = availableServices.find(s => s.id === serviceId);
    if (!service) {
      addLog(`❌ 找不到服務 ID: ${serviceId}`);
      return;
    }

    addLog(`🔄 開始訂閱: ${service.name}`);

    // 創建新訂閱
    const newSubscription = {
      ...service,
      status: 'active',
      nextBilling: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      usage: { used: 0, limit: 10000, unit: '次調用' }
    };

    // 更新狀態
    const updatedSubscriptions = [...subscriptions, newSubscription];
    setSubscriptions(updatedSubscriptions);
    addLog(`📋 新增訂閱: ${service.name}`);

    // 從可用服務中移除
    const updatedAvailable = availableServices.filter(s => s.id !== serviceId);
    setAvailableServices(updatedAvailable);
    addLog(`🎯 從可用服務中移除: ${service.name}`);

    addLog(`✅ 訂閱成功! 可用服務: ${updatedAvailable.length}, 已訂閱: ${updatedSubscriptions.length}`);
  };

  // 取消訂閱
  const handleCancelSubscription = (subscriptionId) => {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) {
      addLog(`❌ 找不到訂閱 ID: ${subscriptionId}`);
      return;
    }

    addLog(`🔄 取消訂閱: ${subscription.name}`);

    // 從訂閱中移除
    const updatedSubscriptions = subscriptions.filter(s => s.id !== subscriptionId);
    setSubscriptions(updatedSubscriptions);
    addLog(`📋 移除訂閱: ${subscription.name}`);

    // 如果服務仍然啟用，加回可用服務
    const originalService = allServices.find(s => s.id === subscriptionId);
    if (originalService && originalService.status === 'active') {
      const updatedAvailable = [...availableServices, originalService];
      setAvailableServices(updatedAvailable);
      addLog(`🎯 重新加入可用服務: ${subscription.name}`);
    }

    addLog(`✅ 取消成功! 可用服務: ${availableServices.length + 1}, 已訂閱: ${updatedSubscriptions.length}`);
  };

  useEffect(() => {
    loadAllServices();
  }, []);

  return (
    <>
      <Head>
        <title>訂閱功能測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">訂閱功能測試</h1>
            
            <div className="mb-8 flex space-x-4">
              <button
                onClick={loadAllServices}
                disabled={isLoading}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isLoading ? '載入中...' : '🔄 重新載入服務'}
              </button>
              
              <a
                href="/subscriptions"
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors inline-block"
              >
                👤 實際訂閱頁面
              </a>
              
              <button
                onClick={() => {
                  setSubscriptions([]);
                  setAvailableServices(allServices.filter(s => s.status === 'active'));
                  addLog('🔄 重置所有訂閱狀態');
                }}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                🔄 重置狀態
              </button>
            </div>

            {/* 統計信息 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-blue-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">所有服務</h3>
                <p className="text-3xl font-bold text-blue-600">{allServices.length}</p>
                <p className="text-sm text-blue-700">
                  啟用: {allServices.filter(s => s.status === 'active').length}
                </p>
              </div>
              
              <div className="bg-green-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-900 mb-2">可用服務</h3>
                <p className="text-3xl font-bold text-green-600">{availableServices.length}</p>
                <p className="text-sm text-green-700">可以訂閱的服務</p>
              </div>
              
              <div className="bg-orange-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-orange-900 mb-2">已訂閱</h3>
                <p className="text-3xl font-bold text-orange-600">{subscriptions.length}</p>
                <p className="text-sm text-orange-700">當前訂閱數量</p>
              </div>
              
              <div className="bg-purple-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-purple-900 mb-2">測試狀態</h3>
                <p className="text-lg font-bold text-purple-600">
                  {availableServices.length + subscriptions.length === allServices.filter(s => s.status === 'active').length ? '✅ 正常' : '❌ 異常'}
                </p>
                <p className="text-sm text-purple-700">數量平衡檢查</p>
              </div>
            </div>

            {/* 服務列表 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* 可用服務 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  可用服務 ({availableServices.length})
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                  {availableServices.map((service) => (
                    <div key={service.id} className="mb-2 p-3 bg-white rounded border">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">{service.name}</div>
                          <div className="text-sm text-gray-500">
                            ID: {service.id} | 價格: NT$ {service.price}
                          </div>
                        </div>
                        <button
                          onClick={() => handleSubscribe(service.id)}
                          className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                        >
                          訂閱
                        </button>
                      </div>
                    </div>
                  ))}
                  {availableServices.length === 0 && (
                    <div className="text-gray-500 text-center py-4">沒有可用服務</div>
                  )}
                </div>
              </div>

              {/* 已訂閱服務 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  已訂閱服務 ({subscriptions.length})
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                  {subscriptions.map((subscription) => (
                    <div key={subscription.id} className="mb-2 p-3 bg-white rounded border">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">{subscription.name}</div>
                          <div className="text-sm text-gray-500">
                            ID: {subscription.id} | 下次計費: {subscription.nextBilling}
                          </div>
                        </div>
                        <button
                          onClick={() => handleCancelSubscription(subscription.id)}
                          className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
                        >
                          取消
                        </button>
                      </div>
                    </div>
                  ))}
                  {subscriptions.length === 0 && (
                    <div className="text-gray-500 text-center py-4">沒有訂閱服務</div>
                  )}
                </div>
              </div>
            </div>

            {/* 調試日誌 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">操作日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
                {logs.map((log, index) => (
                  <div key={index} className="mb-1">{log}</div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">尚無操作記錄</div>
                )}
              </div>
            </div>

            {/* 測試說明 */}
            <div className="mt-8 p-6 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-3">測試說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>測試目標：</strong>驗證訂閱和取消訂閱功能是否正確更新服務列表。</p>
                <p><strong>預期行為：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>訂閱服務後，該服務應從「可用服務」移除，出現在「已訂閱服務」中</li>
                  <li>取消訂閱後，該服務應從「已訂閱服務」移除，重新出現在「可用服務」中</li>
                  <li>「可用服務數 + 已訂閱數 = 啟用服務總數」應該始終成立</li>
                  <li>操作日誌應該清楚顯示每個步驟的執行過程</li>
                </ul>
                <p><strong>測試步驟：</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>點擊任一可用服務的「訂閱」按鈕</li>
                  <li>確認該服務移動到「已訂閱服務」列表</li>
                  <li>點擊已訂閱服務的「取消」按鈕</li>
                  <li>確認該服務重新出現在「可用服務」列表</li>
                  <li>檢查統計數字是否正確</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
