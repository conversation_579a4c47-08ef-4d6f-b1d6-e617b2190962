import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletInit() {
  const router = useRouter();
  const [status, setStatus] = useState('準備初始化...');
  const [isInitialized, setIsInitialized] = useState(false);
  const [walletData, setWalletData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    // 自動開始初始化
    initializeWallet();
  }, []);

  const initializeWallet = async () => {
    try {
      setStatus('🔍 檢查認證狀態...');
      setError(null);

      // 檢查認證
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');

      if (!token || !userStr) {
        setStatus('❌ 未找到認證數據');
        setError('請先登錄或使用認證修復工具');
        return;
      }

      let user;
      try {
        user = JSON.parse(userStr);
        setStatus(`✅ 認證檢查通過 - 用戶: ${user.name}`);
      } catch (parseError) {
        setStatus('❌ 用戶數據解析失敗');
        setError('用戶數據格式錯誤');
        return;
      }

      // 初始化錢包餘額（如果不存在）
      setStatus('💰 初始化錢包餘額...');
      
      const initResponse = await fetch('/api/wallet/init', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          initial_balance: 1000.00 // 初始餘額 $1000
        })
      });

      if (!initResponse.ok) {
        // 如果初始化 API 不存在，嘗試直接獲取餘額
        setStatus('📊 獲取錢包數據...');
        
        const balanceResponse = await fetch('/api/wallet/balance', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const balanceResult = await balanceResponse.json();
        
        if (balanceResult.success) {
          setStatus('✅ 錢包數據獲取成功');
          setWalletData({
            balance: balanceResult.data.balance,
            user: user
          });
          setIsInitialized(true);
        } else {
          setStatus('❌ 錢包數據獲取失敗');
          setError(balanceResult.error);
        }
      } else {
        const initResult = await initResponse.json();
        
        if (initResult.success) {
          setStatus('✅ 錢包初始化成功');
          setWalletData({
            balance: initResult.data.balance,
            user: user
          });
          setIsInitialized(true);
        } else {
          setStatus('❌ 錢包初始化失敗');
          setError(initResult.error);
        }
      }

      // 獲取交易記錄
      if (isInitialized) {
        setStatus('📋 載入交易記錄...');
        
        const transactionsResponse = await fetch('/api/wallet/transactions?limit=5', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const transactionsResult = await transactionsResponse.json();
        
        if (transactionsResult.success) {
          setStatus(`✅ 初始化完成！錢包餘額: $${walletData?.balance?.toFixed(2) || '0.00'}`);
        }
      }

    } catch (error) {
      setStatus('❌ 初始化失敗');
      setError(error.message);
    }
  };

  const goToWallet = () => {
    router.push('/wallet');
  };

  const goToAuthFix = () => {
    router.push('/wallet-auth-fix');
  };

  const goToDashboard = () => {
    router.push('/dashboard-simple');
  };

  const retryInit = () => {
    setIsInitialized(false);
    setError(null);
    initializeWallet();
  };

  return (
    <>
      <Head>
        <title>錢包初始化 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 flex items-center justify-center">
        <div className="max-w-lg w-full mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {isInitialized ? (
                  <span className="text-2xl">✅</span>
                ) : error ? (
                  <span className="text-2xl">❌</span>
                ) : (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
                )}
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">💰 錢包初始化</h1>
              <p className="text-gray-600">設置您的錢包數據</p>
            </div>

            {/* 狀態顯示 */}
            <div className="mb-8">
              <div className={`p-4 rounded-lg ${
                isInitialized ? 'bg-green-50 border border-green-200' : 
                error ? 'bg-red-50 border border-red-200' :
                'bg-blue-50 border border-blue-200'
              }`}>
                <p className={`font-medium ${
                  isInitialized ? 'text-green-800' : 
                  error ? 'text-red-800' :
                  'text-blue-800'
                }`}>
                  {status}
                </p>
                {error && (
                  <p className="text-red-600 text-sm mt-2">
                    錯誤: {error}
                  </p>
                )}
              </div>
            </div>

            {/* 錢包信息 */}
            {walletData && (
              <div className="mb-8">
                <h3 className="font-medium text-gray-900 mb-3">💰 錢包信息</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>用戶:</span>
                    <span className="text-green-600">✅ {walletData.user.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Email:</span>
                    <span className="text-green-600">✅ {walletData.user.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>錢包餘額:</span>
                    <span className="text-blue-600 font-semibold">💰 ${walletData.balance.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            )}

            {/* 操作按鈕 */}
            <div className="space-y-3">
              {isInitialized ? (
                <div className="space-y-3">
                  <button
                    onClick={goToWallet}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    💰 前往錢包頁面
                  </button>
                  
                  <button
                    onClick={goToDashboard}
                    className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                  >
                    🏠 回到儀表板
                  </button>
                </div>
              ) : error ? (
                <div className="space-y-3">
                  <button
                    onClick={retryInit}
                    className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                  >
                    🔄 重新初始化
                  </button>
                  
                  <button
                    onClick={goToAuthFix}
                    className="w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
                  >
                    🔧 修復認證
                  </button>
                </div>
              ) : (
                <button
                  onClick={retryInit}
                  className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                >
                  🔄 重新嘗試
                </button>
              )}
              
              <button
                onClick={goToDashboard}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🏠 返回儀表板
              </button>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">🎯 初始化流程</h3>
              <div className="text-sm text-yellow-800 space-y-1">
                <div>1. 檢查用戶認證狀態</div>
                <div>2. 初始化錢包餘額（如需要）</div>
                <div>3. 驗證錢包 API 訪問</div>
                <div>4. 載入交易記錄</div>
                <div>5. 完成初始化設置</div>
              </div>
            </div>

            {/* 進度指示 */}
            {!isInitialized && !error && (
              <div className="mt-6">
                <div className="flex justify-center">
                  <div className="animate-pulse flex space-x-1">
                    <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                  </div>
                </div>
                <p className="text-center text-sm text-gray-500 mt-2">
                  正在初始化錢包數據...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
