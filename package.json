{"name": "member-management-system", "version": "1.0.0", "description": "專業的會員管理與訂閱服務系統", "scripts": {"setup:env": "node scripts/setup-env.js", "check:env": "node scripts/check-env.js", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "dependencies": {"node-fetch": "^3.3.2", "sqlite3": "^5.1.7"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["member-management", "subscription-service", "user-management", "nextjs", "nodejs", "sqlite"], "author": "Member Management System Team", "license": "MIT"}