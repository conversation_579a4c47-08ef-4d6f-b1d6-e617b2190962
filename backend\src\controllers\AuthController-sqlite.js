const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { getDatabase, dbGet, dbRun } = require('../config/database-sqlite');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

class AuthController {
  // 用戶登入
  static async login(req, res) {
    console.log('🔐 登入請求開始:', { email: req.body?.email });
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({
          success: false,
          error: '請提供電子郵件和密碼'
        });
      }

      console.log('🔐 獲取資料庫連接...');
      const db = await getDatabase();
      console.log('🔐 資料庫連接成功');

      // 查找用戶
      console.log('🔐 查找用戶:', email);
      const user = await dbGet(db, 
        'SELECT * FROM members WHERE email = ?', 
        [email]
      );

      if (!user) {
        return res.status(401).json({
          success: false,
          error: '電子郵件或密碼錯誤'
        });
      }

      // 驗證密碼
      console.log('🔐 驗證密碼...');
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          error: '電子郵件或密碼錯誤'
        });
      }

      // 生成 JWT token
      const token = jwt.sign(
        {
          id: user.id,
          userId: user.id, // 添加 userId 字段以兼容前端 API
          email: user.email,
          role: user.role
        },
        JWT_SECRET,
        { expiresIn: '7d' }
      );

      // 返回用戶信息（不包含密碼）
      const userInfo = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        createdAt: user.created_at
      };

      res.json({
        success: true,
        data: {
          token,
          user: userInfo
        }
      });

    } catch (error) {
      console.error('登入錯誤:', error);
      res.status(500).json({
        success: false,
        error: '服務器內部錯誤'
      });
    }
  }

  // 用戶註冊
  static async register(req, res) {
    try {
      const { name, email, password } = req.body;

      if (!name || !email || !password) {
        return res.status(400).json({
          success: false,
          error: '請提供所有必需的字段'
        });
      }

      const db = await getDatabase();
      
      // 檢查用戶是否已存在
      const existingUser = await dbGet(db, 
        'SELECT id FROM members WHERE email = ?', 
        [email]
      );

      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: '該電子郵件已被註冊'
        });
      }

      // 加密密碼
      const hashedPassword = await bcrypt.hash(password, 12);

      // 創建新用戶
      const result = await dbRun(db,
        'INSERT INTO members (name, email, password, role, created_at) VALUES (?, ?, ?, ?, ?)',
        [name, email, hashedPassword, 'member', new Date().toISOString()]
      );

      // 生成 JWT token
      const token = jwt.sign(
        { 
          id: result.id, 
          email: email,
          role: 'member' 
        },
        JWT_SECRET,
        { expiresIn: '7d' }
      );

      // 返回用戶信息
      const userInfo = {
        id: result.id,
        email: email,
        name: name,
        role: 'member',
        createdAt: new Date().toISOString()
      };

      res.status(201).json({
        success: true,
        data: {
          token,
          user: userInfo
        }
      });

    } catch (error) {
      console.error('註冊錯誤:', error);
      res.status(500).json({
        success: false,
        error: '服務器內部錯誤'
      });
    }
  }

  // 驗證 token
  static async verifyToken(req, res) {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return res.status(401).json({
          success: false,
          error: '未提供認證令牌'
        });
      }

      const decoded = jwt.verify(token, JWT_SECRET);
      const db = await getDatabase();
      
      const user = await dbGet(db, 
        'SELECT id, email, name, role, created_at FROM members WHERE id = ?', 
        [decoded.id]
      );

      if (!user) {
        return res.status(401).json({
          success: false,
          error: '無效的認證令牌'
        });
      }

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            createdAt: user.created_at
          }
        }
      });

    } catch (error) {
      console.error('Token 驗證錯誤:', error);
      res.status(401).json({
        success: false,
        error: '無效的認證令牌'
      });
    }
  }
}

module.exports = AuthController;
