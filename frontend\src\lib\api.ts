import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { AuthResponse, ApiError } from '@/types/auth';

// 創建 axios 實例
const api: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 請求攔截器 - 添加認證 token
api.interceptors.request.use(
  (config) => {
    // 從 localStorage 獲取 token
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('access_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 響應攔截器 - 處理錯誤和 token 刷新
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    // 如果是 401 錯誤且不是刷新 token 請求
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // 嘗試刷新 token
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/refresh`,
            { refresh_token: refreshToken }
          );

          const { access_token, refresh_token: newRefreshToken } = response.data.data;

          // 更新 token
          localStorage.setItem('access_token', access_token);
          localStorage.setItem('refresh_token', newRefreshToken);

          // 重新發送原始請求
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // 刷新失敗，清除 token 並重定向到登入頁
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/auth/login';
        return Promise.reject(refreshError);
      }
    }

    // 格式化錯誤響應
    const responseData = error.response?.data as any;
    const apiError: ApiError = {
      message: responseData?.message || error.message || '發生未知錯誤',
      errors: responseData?.errors || {},
      status: error.response?.status || 500,
    };

    return Promise.reject(apiError);
  }
);

// API 方法
export const authAPI = {
  // 註冊
  register: (data: any): Promise<AuthResponse> =>
    api.post('/auth/register', data).then(res => res.data),

  // 登入
  login: (data: any): Promise<AuthResponse> =>
    api.post('/auth/login', data).then(res => res.data),

  // 登出
  logout: (): Promise<void> =>
    api.post('/auth/logout').then(res => res.data),

  // 忘記密碼
  forgotPassword: (data: any): Promise<any> =>
    api.post('/auth/forgot-password', data).then(res => res.data),

  // 重設密碼
  resetPassword: (data: any): Promise<any> =>
    api.post('/auth/reset-password', data).then(res => res.data),

  // 驗證郵件
  verifyEmail: (data: any): Promise<any> =>
    api.post('/auth/verify-email', data).then(res => res.data),

  // 驗證手機
  verifyPhone: (data: any): Promise<any> =>
    api.post('/auth/verify-phone', data).then(res => res.data),

  // 重新發送驗證碼
  resendVerification: (data: any): Promise<any> =>
    api.post('/auth/resend-verification', data).then(res => res.data),

  // 獲取用戶資料
  getProfile: (): Promise<any> =>
    api.get('/auth/profile').then(res => res.data),

  // 更新用戶資料
  updateProfile: (data: any): Promise<any> =>
    api.put('/auth/profile', data).then(res => res.data),

  // OAuth 登入
  oauthLogin: (provider: string, code: string): Promise<AuthResponse> =>
    api.post(`/auth/oauth/${provider}`, { code }).then(res => res.data),
};

export const userAPI = {
  // 檢查用戶名是否可用
  checkUsername: (username: string): Promise<{ available: boolean }> =>
    api.get(`/users/check-username?username=${username}`).then(res => res.data),

  // 檢查郵件是否可用
  checkEmail: (email: string): Promise<{ available: boolean }> =>
    api.get(`/users/check-email?email=${email}`).then(res => res.data),

  // 檢查手機號碼是否可用
  checkPhone: (phone: string): Promise<{ available: boolean }> =>
    api.get(`/users/check-phone?phone=${phone}`).then(res => res.data),
};

export const paymentAPI = {
  // 獲取支付方式列表
  getPaymentMethods: (currency?: string): Promise<any> =>
    api.get(`/payments/methods${currency ? `?currency=${currency}` : ''}`).then(res => res.data),

  // 獲取充值獎勵預覽
  getRewardPreview: (amount: number): Promise<any> =>
    api.get(`/payments/reward-preview?amount=${amount}`).then(res => res.data),

  // 創建 Stripe 支付
  createStripePayment: (data: any): Promise<any> =>
    api.post('/payments/stripe', data).then(res => res.data),

  // 創建 PayPal 支付
  createPayPalPayment: (data: any): Promise<any> =>
    api.post('/payments/paypal', data).then(res => res.data),

  // 創建綠界科技支付
  createECPayPayment: (data: any): Promise<any> =>
    api.post('/payments/ecpay', data).then(res => res.data),
};

export const walletAPI = {
  // 獲取錢包列表
  getWallets: (): Promise<any> =>
    api.get('/wallets').then(res => res.data),

  // 獲取錢包詳情
  getWallet: (currency: string): Promise<any> =>
    api.get(`/wallets/${currency}`).then(res => res.data),

  // 獲取交易記錄
  getTransactions: (params?: any): Promise<any> =>
    api.get('/wallets/transactions', { params }).then(res => res.data),

  // 獲取支付記錄
  getPayments: (params?: any): Promise<any> =>
    api.get('/payments', { params }).then(res => res.data),
};

export const subscriptionAPI = {
  // 獲取所有可用服務
  getAvailableServices: (): Promise<any> =>
    api.get('/subscriptions/services').then(res => res.data),

  // 獲取服務詳情和方案
  getServiceDetails: (serviceId: string): Promise<any> =>
    api.get(`/subscriptions/services/${serviceId}`).then(res => res.data),

  // 獲取用戶訂閱列表
  getUserSubscriptions: (): Promise<any> =>
    api.get('/subscriptions').then(res => res.data),

  // 創建訂閱
  createSubscription: (data: any): Promise<any> =>
    api.post('/subscriptions', data).then(res => res.data),

  // 取消訂閱
  cancelSubscription: (subscriptionId: string, data?: any): Promise<any> =>
    api.post(`/subscriptions/${subscriptionId}/cancel`, data).then(res => res.data),

  // 恢復訂閱
  resumeSubscription: (subscriptionId: string): Promise<any> =>
    api.post(`/subscriptions/${subscriptionId}/resume`).then(res => res.data),

  // 獲取訂閱使用統計
  getSubscriptionUsage: (subscriptionId: string): Promise<any> =>
    api.get(`/subscriptions/${subscriptionId}/usage`).then(res => res.data),

  // 升級/降級訂閱
  changeSubscriptionPlan: (subscriptionId: string, data: any): Promise<any> =>
    api.post(`/subscriptions/${subscriptionId}/change-plan`, data).then(res => res.data),
};

export default api;