import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function FixAuth() {
  const [status, setStatus] = useState('檢查中...');
  const [isLoading, setIsLoading] = useState(true);
  const [authData, setAuthData] = useState(null);
  const router = useRouter();

  useEffect(() => {
    checkAndFixAuth();
  }, []);

  const checkAndFixAuth = async () => {
    setStatus('🔍 檢查認證狀態...');
    
    try {
      // 檢查當前認證數據
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      setAuthData({
        hasToken: !!token,
        hasUser: !!userStr,
        tokenLength: token ? token.length : 0,
        userStr: userStr
      });

      if (!token || !userStr) {
        setStatus('❌ 缺少認證數據，開始自動登入...');
        await autoLogin();
      } else {
        try {
          const userData = JSON.parse(userStr);
          setStatus('✅ 認證數據完整，跳轉到儀表板...');
          
          setTimeout(() => {
            router.push('/dashboard-simple');
          }, 2000);
        } catch (error) {
          setStatus('❌ 用戶數據格式錯誤，重新登入...');
          await autoLogin();
        }
      }
    } catch (error) {
      setStatus('❌ 檢查失敗: ' + error.message);
      setIsLoading(false);
    }
  };

  const autoLogin = async () => {
    try {
      setStatus('🔑 正在自動登入...');
      
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setStatus('✅ 登入成功，保存認證數據...');
        
        // 保存認證數據
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        
        setAuthData({
          hasToken: true,
          hasUser: true,
          tokenLength: result.token.length,
          userStr: JSON.stringify(result.user)
        });
        
        setStatus('✅ 認證修復完成，跳轉到儀表板...');
        
        setTimeout(() => {
          router.push('/dashboard-simple');
        }, 2000);
      } else {
        setStatus('❌ 自動登入失敗: ' + result.error);
        setIsLoading(false);
      }
    } catch (error) {
      setStatus('❌ 自動登入請求失敗: ' + error.message);
      setIsLoading(false);
    }
  };

  const manualLogin = () => {
    router.push('/quick-login');
  };

  const goToDashboard = () => {
    router.push('/dashboard-simple');
  };

  const clearAndRetry = () => {
    localStorage.clear();
    document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    setStatus('🗑️ 已清除認證數據，重新檢查...');
    setAuthData(null);
    setTimeout(() => {
      checkAndFixAuth();
    }, 1000);
  };

  return (
    <>
      <Head>
        <title>修復認證 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔧 認證修復</h1>
              <p className="text-gray-600 mt-2">自動檢查和修復認證問題</p>
            </div>

            {/* 狀態顯示 */}
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="text-center">
                {isLoading && (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                )}
                <p className="text-blue-800 font-medium">{status}</p>
              </div>
            </div>

            {/* 認證數據詳情 */}
            {authData && (
              <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-2">📊 認證數據狀態</h3>
                <div className="text-sm text-gray-700 space-y-1">
                  <div>Token: {authData.hasToken ? '✅ 存在' : '❌ 缺失'} ({authData.tokenLength} 字符)</div>
                  <div>User: {authData.hasUser ? '✅ 存在' : '❌ 缺失'}</div>
                  {authData.userStr && (
                    <div className="mt-2 p-2 bg-white rounded border font-mono text-xs break-all">
                      {authData.userStr.substring(0, 100)}...
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 操作按鈕 */}
            <div className="space-y-3">
              {!isLoading && (
                <>
                  <button
                    onClick={goToDashboard}
                    className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                  >
                    🏠 前往儀表板
                  </button>

                  <button
                    onClick={checkAndFixAuth}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    🔄 重新檢查
                  </button>

                  <button
                    onClick={manualLogin}
                    className="w-full px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 font-medium"
                  >
                    🔑 手動登入
                  </button>

                  <button
                    onClick={clearAndRetry}
                    className="w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
                  >
                    🗑️ 清除並重試
                  </button>
                </>
              )}
            </div>

            {/* 說明 */}
            <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">💡 說明</h3>
              <div className="text-sm text-yellow-800 space-y-1">
                <div>• 此頁面會自動檢查和修復認證問題</div>
                <div>• 如果缺少認證數據，會自動嘗試登入</div>
                <div>• 修復完成後會自動跳轉到儀表板</div>
                <div>• 如果自動修復失敗，請使用手動登入</div>
              </div>
            </div>

            {/* 問題診斷 */}
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-medium text-red-900 mb-2">🚨 常見問題</h3>
              <div className="text-sm text-red-800 space-y-1">
                <div>• Mounted: ✅ User: ❌ → 缺少用戶數據</div>
                <div>• 一直載入中 → 認證檢查失敗</div>
                <div>• 跳轉到登入頁 → Token 無效或過期</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
