// 錢包充值 API
const jwt = require('jsonwebtoken');
const Wallet = require('../../../../lib/database/models/Wallet');
const PaymentMethod = require('../../../../lib/database/models/PaymentMethod');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

export default async function handler(req, res) {
  try {
    console.log('錢包充值 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    // 驗證認證
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('錢包充值 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    const userId = decoded.userId;
    console.log('錢包充值 API: 用戶 ID:', userId);

    // 驗證請求數據
    const { amount, payment_method_id, description } = req.body;

    if (!amount || !payment_method_id) {
      return res.status(400).json({
        success: false,
        error: '請提供充值金額和支付方式'
      });
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      return res.status(400).json({
        success: false,
        error: '充值金額必須大於 0'
      });
    }

    // 獲取支付方式
    const paymentMethod = await PaymentMethod.findById(payment_method_id);
    if (!paymentMethod) {
      return res.status(400).json({
        success: false,
        error: '支付方式不存在'
      });
    }

    if (!paymentMethod.is_active) {
      return res.status(400).json({
        success: false,
        error: '支付方式已停用'
      });
    }

    // 驗證充值金額
    const validation = paymentMethod.validateAmount(amountNum);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: validation.error
      });
    }

    // 計算手續費和總金額
    const fee = paymentMethod.calculateFee(amountNum);
    const totalAmount = paymentMethod.calculateTotal(amountNum);

    console.log('錢包充值 API: 充值詳情', {
      amount: amountNum,
      fee: fee,
      total: totalAmount,
      paymentMethod: paymentMethod.name
    });

    // 模擬支付處理（實際應用中這裡會調用第三方支付 API）
    const paymentReference = `PAY_${Date.now()}_${userId}`;
    
    // 執行充值
    const transaction = await Wallet.recharge(
      userId,
      amountNum,
      paymentMethod.name,
      description || `使用${paymentMethod.name}充值`,
      paymentReference
    );

    console.log('錢包充值 API: 充值成功', transaction.toJSON());

    // 獲取更新後的餘額
    const newBalance = await Wallet.getBalance(userId);

    return res.status(200).json({
      success: true,
      data: {
        transaction: transaction.toJSON(),
        new_balance: newBalance,
        payment_details: {
          amount: amountNum,
          fee: fee,
          total: totalAmount,
          payment_method: paymentMethod.name,
          payment_reference: paymentReference
        }
      },
      message: '充值成功'
    });

  } catch (error) {
    console.error('錢包充值 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '充值失敗: ' + error.message
    });
  }
}
