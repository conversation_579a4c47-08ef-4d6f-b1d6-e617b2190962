# 會員管理系統 - 資料庫設計文件

## 📋 概覽

本文件描述了會員管理系統的完整資料庫設計，包含所有資料表結構、關聯關係和 ERD 圖表。

### 🗄️ 資料庫類型
- **資料庫**: SQLite
- **位置**: `frontend/lib/database/database.sqlite`
- **編碼**: UTF-8
- **版本**: SQLite 3.x

### 📊 資料表總覽
- **會員表** (`members`) - 存儲用戶基本信息
- **服務表** (`services`) - 存儲可訂閱的服務
- **會員服務關聯表** (`member_services`) - 存儲訂閱關係
- **交易記錄表** (`transactions`) - 存儲付款記錄
- **審計日誌表** (`audit_logs`) - 存儲系統操作記錄

---

## 🏗️ 資料表結構

### 1. 會員表 (members)

存儲系統用戶的基本信息和認證數據。

```sql
CREATE TABLE members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    phone VARCHAR(20),
    avatar_url VARCHAR(500),
    email_verified BOOLEAN DEFAULT FALSE,
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 欄位說明
| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| `id` | INTEGER | 主鍵，自動遞增 | PRIMARY KEY |
| `email` | VARCHAR(255) | 用戶 Email | UNIQUE, NOT NULL |
| `name` | VARCHAR(100) | 用戶姓名 | NOT NULL |
| `password_hash` | VARCHAR(255) | bcrypt 加密密碼 | NOT NULL |
| `role` | ENUM | 用戶角色 (user/admin) | DEFAULT 'user' |
| `status` | ENUM | 帳戶狀態 | DEFAULT 'active' |
| `phone` | VARCHAR(20) | 電話號碼 | NULLABLE |
| `avatar_url` | VARCHAR(500) | 頭像 URL | NULLABLE |
| `email_verified` | BOOLEAN | Email 驗證狀態 | DEFAULT FALSE |
| `last_login_at` | DATETIME | 最後登入時間 | NULLABLE |
| `created_at` | DATETIME | 創建時間 | DEFAULT CURRENT_TIMESTAMP |
| `updated_at` | DATETIME | 更新時間 | DEFAULT CURRENT_TIMESTAMP |

#### 索引
```sql
CREATE INDEX idx_members_email ON members(email);
CREATE INDEX idx_members_status ON members(status);
CREATE INDEX idx_members_role ON members(role);
```

---

### 2. 服務表 (services)

存儲系統提供的可訂閱服務信息。

```sql
CREATE TABLE services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
    status ENUM('active', 'inactive', 'deprecated') DEFAULT 'active',
    category VARCHAR(100),
    features JSON,
    max_users INTEGER DEFAULT 1,
    api_quota INTEGER,
    storage_limit INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 欄位說明
| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| `id` | INTEGER | 主鍵，自動遞增 | PRIMARY KEY |
| `name` | VARCHAR(200) | 服務名稱 | NOT NULL |
| `description` | TEXT | 服務描述 | NULLABLE |
| `price` | DECIMAL(10,2) | 服務價格 | NOT NULL |
| `billing_cycle` | ENUM | 計費週期 (monthly/yearly) | DEFAULT 'monthly' |
| `status` | ENUM | 服務狀態 | DEFAULT 'active' |
| `category` | VARCHAR(100) | 服務分類 | NULLABLE |
| `features` | JSON | 服務功能列表 | NULLABLE |
| `max_users` | INTEGER | 最大用戶數 | DEFAULT 1 |
| `api_quota` | INTEGER | API 調用配額 | NULLABLE |
| `storage_limit` | INTEGER | 存儲限制 (MB) | NULLABLE |
| `created_at` | DATETIME | 創建時間 | DEFAULT CURRENT_TIMESTAMP |
| `updated_at` | DATETIME | 更新時間 | DEFAULT CURRENT_TIMESTAMP |

#### 索引
```sql
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_services_category ON services(category);
CREATE INDEX idx_services_price ON services(price);
```

---

### 3. 會員服務關聯表 (member_services)

存儲會員與服務的訂閱關係。

```sql
CREATE TABLE member_services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER NOT NULL,
    service_id INTEGER NOT NULL,
    status ENUM('active', 'cancelled', 'expired', 'suspended') DEFAULT 'active',
    subscribed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    next_billing_date DATE,
    auto_renew BOOLEAN DEFAULT TRUE,
    usage_data JSON,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE(member_id, service_id)
);
```

#### 欄位說明
| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| `id` | INTEGER | 主鍵，自動遞增 | PRIMARY KEY |
| `member_id` | INTEGER | 會員 ID | FOREIGN KEY |
| `service_id` | INTEGER | 服務 ID | FOREIGN KEY |
| `status` | ENUM | 訂閱狀態 | DEFAULT 'active' |
| `subscribed_at` | DATETIME | 訂閱時間 | DEFAULT CURRENT_TIMESTAMP |
| `expires_at` | DATETIME | 到期時間 | NULLABLE |
| `next_billing_date` | DATE | 下次計費日期 | NULLABLE |
| `auto_renew` | BOOLEAN | 自動續費 | DEFAULT TRUE |
| `usage_data` | JSON | 使用數據統計 | NULLABLE |
| `notes` | TEXT | 備註 | NULLABLE |
| `created_at` | DATETIME | 創建時間 | DEFAULT CURRENT_TIMESTAMP |
| `updated_at` | DATETIME | 更新時間 | DEFAULT CURRENT_TIMESTAMP |

#### 索引
```sql
CREATE INDEX idx_member_services_member ON member_services(member_id);
CREATE INDEX idx_member_services_service ON member_services(service_id);
CREATE INDEX idx_member_services_status ON member_services(status);
CREATE INDEX idx_member_services_billing ON member_services(next_billing_date);
```

---

### 4. 交易記錄表 (transactions)

存儲所有付款和交易記錄。

```sql
CREATE TABLE transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER NOT NULL,
    service_id INTEGER,
    member_service_id INTEGER,
    transaction_type ENUM('payment', 'refund', 'adjustment') DEFAULT 'payment',
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'TWD',
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_gateway VARCHAR(50),
    gateway_transaction_id VARCHAR(255),
    description TEXT,
    metadata JSON,
    processed_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE SET NULL,
    FOREIGN KEY (member_service_id) REFERENCES member_services(id) ON DELETE SET NULL
);
```

#### 欄位說明
| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| `id` | INTEGER | 主鍵，自動遞增 | PRIMARY KEY |
| `member_id` | INTEGER | 會員 ID | FOREIGN KEY |
| `service_id` | INTEGER | 服務 ID | FOREIGN KEY |
| `member_service_id` | INTEGER | 訂閱關係 ID | FOREIGN KEY |
| `transaction_type` | ENUM | 交易類型 | DEFAULT 'payment' |
| `amount` | DECIMAL(10,2) | 交易金額 | NOT NULL |
| `currency` | VARCHAR(3) | 貨幣代碼 | DEFAULT 'TWD' |
| `status` | ENUM | 交易狀態 | DEFAULT 'pending' |
| `payment_method` | VARCHAR(50) | 付款方式 | NULLABLE |
| `payment_gateway` | VARCHAR(50) | 付款閘道 | NULLABLE |
| `gateway_transaction_id` | VARCHAR(255) | 閘道交易 ID | NULLABLE |
| `description` | TEXT | 交易描述 | NULLABLE |
| `metadata` | JSON | 額外數據 | NULLABLE |
| `processed_at` | DATETIME | 處理時間 | NULLABLE |
| `created_at` | DATETIME | 創建時間 | DEFAULT CURRENT_TIMESTAMP |
| `updated_at` | DATETIME | 更新時間 | DEFAULT CURRENT_TIMESTAMP |

#### 索引
```sql
CREATE INDEX idx_transactions_member ON transactions(member_id);
CREATE INDEX idx_transactions_service ON transactions(service_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_date ON transactions(created_at);
CREATE INDEX idx_transactions_gateway ON transactions(gateway_transaction_id);
```

---

### 5. 審計日誌表 (audit_logs)

存儲系統操作的審計記錄。

```sql
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id INTEGER,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL
);
```

#### 欄位說明
| 欄位名 | 類型 | 說明 | 約束 |
|--------|------|------|------|
| `id` | INTEGER | 主鍵，自動遞增 | PRIMARY KEY |
| `member_id` | INTEGER | 操作用戶 ID | FOREIGN KEY |
| `action` | VARCHAR(100) | 操作動作 | NOT NULL |
| `resource_type` | VARCHAR(50) | 資源類型 | NULLABLE |
| `resource_id` | INTEGER | 資源 ID | NULLABLE |
| `old_values` | JSON | 修改前的值 | NULLABLE |
| `new_values` | JSON | 修改後的值 | NULLABLE |
| `ip_address` | VARCHAR(45) | IP 地址 | NULLABLE |
| `user_agent` | TEXT | 用戶代理 | NULLABLE |
| `created_at` | DATETIME | 創建時間 | DEFAULT CURRENT_TIMESTAMP |

#### 索引
```sql
CREATE INDEX idx_audit_logs_member ON audit_logs(member_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_date ON audit_logs(created_at);
```

---

## 🔗 資料表關聯關係

### 主要關聯

1. **members ↔ member_services** (一對多)
   - 一個會員可以有多個服務訂閱
   - `members.id` → `member_services.member_id`

2. **services ↔ member_services** (一對多)
   - 一個服務可以被多個會員訂閱
   - `services.id` → `member_services.service_id`

3. **members ↔ transactions** (一對多)
   - 一個會員可以有多筆交易記錄
   - `members.id` → `transactions.member_id`

4. **services ↔ transactions** (一對多)
   - 一個服務可以有多筆交易記錄
   - `services.id` → `transactions.service_id`

5. **member_services ↔ transactions** (一對多)
   - 一個訂閱關係可以有多筆交易記錄
   - `member_services.id` → `transactions.member_service_id`

6. **members ↔ audit_logs** (一對多)
   - 一個會員可以有多條審計記錄
   - `members.id` → `audit_logs.member_id`

### 約束規則

- **CASCADE DELETE**: 刪除會員時，同時刪除其訂閱和交易記錄
- **SET NULL**: 刪除服務時，相關交易記錄的 service_id 設為 NULL
- **UNIQUE**: 同一會員不能重複訂閱同一服務

---

## 📈 ERD 關聯圖

```
┌─────────────┐       ┌──────────────────┐       ┌─────────────┐
│   members   │       │  member_services │       │  services   │
├─────────────┤       ├──────────────────┤       ├─────────────┤
│ id (PK)     │◄─────►│ id (PK)          │◄─────►│ id (PK)     │
│ email       │   1:M │ member_id (FK)   │   M:1 │ name        │
│ name        │       │ service_id (FK)  │       │ description │
│ password_hash│       │ status           │       │ price       │
│ role        │       │ subscribed_at    │       │ billing_cycle│
│ status      │       │ expires_at       │       │ status      │
│ phone       │       │ next_billing_date│       │ category    │
│ created_at  │       │ auto_renew       │       │ features    │
│ updated_at  │       │ usage_data       │       │ created_at  │
└─────────────┘       │ created_at       │       │ updated_at  │
       │               │ updated_at       │       └─────────────┘
       │               └──────────────────┘              │
       │                        │                        │
       │ 1:M                    │ 1:M                    │ 1:M
       ▼                        ▼                        ▼
┌─────────────┐       ┌──────────────────┐       
│transactions │       │   audit_logs     │       
├─────────────┤       ├──────────────────┤       
│ id (PK)     │       │ id (PK)          │       
│ member_id(FK)│       │ member_id (FK)   │       
│ service_id(FK)│      │ action           │       
│ member_service_id(FK)│ resource_type    │       
│ transaction_type│    │ resource_id      │       
│ amount      │       │ old_values       │       
│ currency    │       │ new_values       │       
│ status      │       │ ip_address       │       
│ payment_method│     │ user_agent       │       
│ created_at  │       │ created_at       │       
│ updated_at  │       └──────────────────┘       
└─────────────┘                                  
```

### 關聯說明

1. **核心關聯**: `members` ↔ `member_services` ↔ `services`
   - 形成多對多關係，透過中間表 `member_services` 連接

2. **交易追蹤**: `transactions` 記錄所有財務活動
   - 關聯到會員、服務和具體的訂閱關係

3. **審計追蹤**: `audit_logs` 記錄所有系統操作
   - 提供完整的操作歷史和安全審計

4. **數據完整性**: 使用外鍵約束確保數據一致性
   - 防止孤立記錄和數據不一致

---

## 🔧 資料庫初始化

### 創建資料庫腳本

```sql
-- 創建資料庫文件
-- 位置: frontend/lib/database/database.sqlite

-- 啟用外鍵約束
PRAGMA foreign_keys = ON;

-- 創建所有資料表
-- (包含上述所有 CREATE TABLE 語句)

-- 插入初始數據
-- (管理員帳戶、預設服務等)
```

### 初始數據

```sql
-- 插入管理員帳戶
INSERT INTO members (email, name, password_hash, role, status) VALUES 
('<EMAIL>', '系統管理員', '$2b$10$...', 'admin', 'active');

-- 插入預設服務
INSERT INTO services (name, description, price, billing_cycle, category) VALUES 
('AI 文字生成服務', '使用先進的 AI 技術生成高質量文字內容', 299.00, 'monthly', 'AI'),
('圖片處理服務', '專業的圖片編輯和處理工具', 199.00, 'monthly', '圖片處理');
```

---

## 📊 資料庫統計

### 預估數據量

| 資料表 | 預估記錄數 | 成長率 | 備註 |
|--------|------------|--------|------|
| `members` | 1,000 - 10,000 | 中等 | 用戶註冊成長 |
| `services` | 10 - 100 | 低 | 服務種類有限 |
| `member_services` | 5,000 - 50,000 | 高 | 每用戶多訂閱 |
| `transactions` | 10,000 - 100,000 | 高 | 每月產生交易 |
| `audit_logs` | 50,000 - 500,000 | 很高 | 所有操作記錄 |

### 性能考量

1. **索引策略**: 在常用查詢欄位建立索引
2. **分頁查詢**: 大量數據使用分頁載入
3. **數據歸檔**: 定期歸檔舊的審計日誌
4. **查詢優化**: 避免 N+1 查詢問題

---

## 🔒 安全考量

### 數據保護

1. **密碼加密**: 使用 bcrypt 加密存儲
2. **敏感數據**: 避免明文存儲敏感信息
3. **審計追蹤**: 記錄所有重要操作
4. **備份策略**: 定期備份資料庫

### 訪問控制

1. **角色權限**: 基於角色的訪問控制
2. **API 保護**: 所有 API 需要認證
3. **數據隔離**: 用戶只能訪問自己的數據
4. **管理員功能**: 管理員有完整權限

---

*最後更新: 2025-01-26*
*版本: 1.0*
