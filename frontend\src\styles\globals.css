@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局樣式 */
html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  line-height: 1.6;
  color: #333;
}

* {
  box-sizing: border-box;
}

/* 自定義組件樣式 */
@layer components {
  .btn-primary {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-outline {
    @apply border border-indigo-600 text-indigo-600 hover:bg-indigo-50 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-xl shadow-lg p-6;
  }

  .card-hover {
    @apply bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent;
  }

  .label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .error-text {
    @apply text-red-600 text-sm mt-1;
  }

  .success-text {
    @apply text-green-600 text-sm mt-1;
  }
}

/* 動畫效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.6s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 響應式設計 */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* 載入動畫 */
.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 通知樣式 */
.notification {
  @apply fixed top-4 right-4 z-50 max-w-sm w-full;
}

.notification-success {
  @apply bg-green-500 text-white p-4 rounded-lg shadow-lg;
}

.notification-error {
  @apply bg-red-500 text-white p-4 rounded-lg shadow-lg;
}

.notification-warning {
  @apply bg-yellow-500 text-white p-4 rounded-lg shadow-lg;
}

.notification-info {
  @apply bg-blue-500 text-white p-4 rounded-lg shadow-lg;
}

/* 表格樣式 */
.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-row {
  @apply hover:bg-gray-50;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* 模態框樣式 */
.modal-overlay {
  @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-container {
  @apply relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white;
}

/* 側邊欄樣式 */
.sidebar {
  @apply fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 overflow-y-auto;
}

.sidebar-item {
  @apply block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white;
}

.sidebar-item-active {
  @apply block px-4 py-2 text-sm bg-gray-900 text-white;
}

/* 進度條樣式 */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2.5;
}

.progress-fill {
  @apply bg-indigo-600 h-2.5 rounded-full transition-all duration-300;
}

/* 標籤樣式 */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-error {
  @apply bg-red-100 text-red-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

/* 工具提示樣式 */
.tooltip {
  @apply relative inline-block;
}

.tooltip-text {
  @apply invisible absolute z-10 w-32 bg-gray-900 text-white text-center text-sm rounded-lg py-2 px-3 opacity-0 transition-opacity duration-300;
  bottom: 125%;
  left: 50%;
  margin-left: -64px;
}

.tooltip:hover .tooltip-text {
  @apply visible opacity-100;
}

/* 滾動條樣式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 打印樣式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .page-break {
    page-break-before: always;
  }
}
