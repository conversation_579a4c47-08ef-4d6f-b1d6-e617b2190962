// 資料庫表格管理 API
const jwt = require('jsonwebtoken');
const path = require('path');
const fs = require('fs');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// 簡化的認證中間件
const authenticateAdmin = (req) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return null;
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded;
  } catch (error) {
    console.error('Token 驗證失敗:', error);
    return null;
  }
};

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();
    
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];
    
    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('資料庫管理 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }
    
    console.log('資料庫管理 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('資料庫管理 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    // 驗證管理員權限
    const user = authenticateAdmin(req);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: '需要管理員權限'
      });
    }

    console.log('資料庫管理 API: 管理員已認證', { userId: user.userId });

    // 連接資料庫
    const db = connectDatabase();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    const { table } = req.query;

    // 如果沒有指定表格，返回所有表格列表
    if (!table) {
      return new Promise((resolve) => {
        db.all("SELECT name FROM sqlite_master WHERE type='table'", [], (err, tables) => {
          db.close();
          
          if (err) {
            console.error('資料庫管理 API: 查詢表格列表失敗:', err);
            resolve(res.status(500).json({
              success: false,
              error: '查詢表格列表失敗'
            }));
            return;
          }

          resolve(res.status(200).json({
            success: true,
            data: {
              tables: tables.map(t => t.name),
              message: '表格列表查詢成功'
            }
          }));
        });
      });
    }

    // 查詢指定表格的數據
    return new Promise((resolve) => {
      // 首先獲取表格結構
      db.all(`PRAGMA table_info(${table})`, [], (err, columns) => {
        if (err) {
          console.error('資料庫管理 API: 查詢表格結構失敗:', err);
          db.close();
          resolve(res.status(500).json({
            success: false,
            error: '查詢表格結構失敗'
          }));
          return;
        }

        // 查詢表格數據
        let query = `SELECT * FROM ${table}`;
        
        // 根據表格類型添加排序
        if (table === 'members') {
          query += ' ORDER BY created_at DESC';
        } else if (table === 'services') {
          query += ' ORDER BY created_at ASC';
        } else if (table === 'member_services') {
          query += ' ORDER BY created_at DESC';
        }

        db.all(query, [], (dataErr, rows) => {
          // 如果是 member_services 表，獲取關聯數據
          if (table === 'member_services' && !dataErr && rows.length > 0) {
            // 獲取會員和服務的詳細信息
            const memberIds = [...new Set(rows.map(r => r.member_id))];
            const serviceIds = [...new Set(rows.map(r => r.service_id))];
            
            // 查詢會員信息
            const memberQuery = `SELECT id, name, email FROM members WHERE id IN (${memberIds.join(',')})`;
            db.all(memberQuery, [], (memberErr, members) => {
              if (memberErr) {
                console.error('查詢會員信息失敗:', memberErr);
              }
              
              // 查詢服務信息
              const serviceQuery = `SELECT id, name, price, billing_cycle FROM services WHERE id IN (${serviceIds.join(',')})`;
              db.all(serviceQuery, [], (serviceErr, services) => {
                db.close();
                
                if (serviceErr) {
                  console.error('查詢服務信息失敗:', serviceErr);
                }
                
                // 組合數據
                const enrichedRows = rows.map(row => {
                  const member = members?.find(m => m.id === row.member_id);
                  const service = services?.find(s => s.id === row.service_id);
                  
                  return {
                    ...row,
                    member_name: member?.name || '未知',
                    member_email: member?.email || '未知',
                    service_name: service?.name || '未知',
                    service_price: service?.price || 0,
                    service_billing_cycle: service?.billing_cycle || '未知'
                  };
                });
                
                resolve(res.status(200).json({
                  success: true,
                  data: {
                    table: table,
                    columns: columns,
                    rows: enrichedRows,
                    count: enrichedRows.length,
                    enriched: true
                  }
                }));
              });
            });
          } else {
            db.close();
            
            if (dataErr) {
              console.error('資料庫管理 API: 查詢表格數據失敗:', dataErr);
              resolve(res.status(500).json({
                success: false,
                error: '查詢表格數據失敗'
              }));
              return;
            }

            resolve(res.status(200).json({
              success: true,
              data: {
                table: table,
                columns: columns,
                rows: rows,
                count: rows.length,
                enriched: false
              }
            }));
          }
        });
      });
    });

  } catch (error) {
    console.error('資料庫管理 API 錯誤:', error);
    
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
