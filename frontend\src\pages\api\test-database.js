// 測試資料庫連接 API
const path = require('path');
const fs = require('fs');

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    console.log('資料庫測試 API: 開始測試...');

    // 檢查 sqlite3 模組
    let sqlite3;
    try {
      sqlite3 = require('sqlite3').verbose();
      console.log('資料庫測試 API: ✅ sqlite3 模組載入成功');
    } catch (error) {
      console.error('資料庫測試 API: ❌ sqlite3 模組載入失敗:', error);
      return res.status(500).json({
        success: false,
        error: 'sqlite3 模組載入失敗',
        details: error.message
      });
    }

    // 檢查資料庫文件
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'database.sqlite')
    ];

    let dbPath = null;
    let dbStats = null;

    for (const testPath of possiblePaths) {
      console.log('資料庫測試 API: 檢查路徑:', testPath);
      if (fs.existsSync(testPath)) {
        dbPath = testPath;
        dbStats = fs.statSync(testPath);
        console.log('資料庫測試 API: ✅ 找到資料庫文件:', testPath);
        break;
      }
    }

    if (!dbPath) {
      console.log('資料庫測試 API: ❌ 未找到資料庫文件');
      return res.status(404).json({
        success: false,
        error: '未找到資料庫文件',
        searchedPaths: possiblePaths
      });
    }

    // 測試資料庫連接和查詢
    return new Promise((resolve) => {
      const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          console.error('資料庫測試 API: 連接失敗:', err);
          resolve(res.status(500).json({
            success: false,
            error: '資料庫連接失敗',
            details: err.message,
            dbPath
          }));
          return;
        }

        console.log('資料庫測試 API: ✅ 資料庫連接成功');

        // 測試查詢會員表
        db.all('SELECT COUNT(*) as count FROM members', [], (err, rows) => {
          if (err) {
            console.error('資料庫測試 API: 查詢失敗:', err);
            db.close();
            resolve(res.status(500).json({
              success: false,
              error: '資料庫查詢失敗',
              details: err.message,
              dbPath
            }));
            return;
          }

          const memberCount = rows[0].count;
          console.log('資料庫測試 API: ✅ 會員數量:', memberCount);

          // 測試查詢服務表
          db.all('SELECT COUNT(*) as count FROM services', [], (err, serviceRows) => {
            if (err) {
              console.error('資料庫測試 API: 服務表查詢失敗:', err);
            }

            const serviceCount = serviceRows ? serviceRows[0].count : 0;
            console.log('資料庫測試 API: ✅ 服務數量:', serviceCount);

            // 測試查詢訂閱表
            db.all('SELECT COUNT(*) as count FROM member_services', [], (err, subRows) => {
              if (err) {
                console.error('資料庫測試 API: 訂閱表查詢失敗:', err);
              }

              const subscriptionCount = subRows ? subRows[0].count : 0;
              console.log('資料庫測試 API: ✅ 訂閱數量:', subscriptionCount);

              // 關閉資料庫連接
              db.close((closeErr) => {
                if (closeErr) {
                  console.error('資料庫測試 API: 關閉連接失敗:', closeErr);
                } else {
                  console.log('資料庫測試 API: ✅ 資料庫連接已關閉');
                }

                // 返回測試結果
                resolve(res.status(200).json({
                  success: true,
                  message: '資料庫測試成功',
                  data: {
                    dbPath,
                    fileSize: Math.round(dbStats.size / 1024) + ' KB',
                    lastModified: dbStats.mtime,
                    memberCount,
                    serviceCount,
                    subscriptionCount
                  }
                }));
              });
            });
          });
        });
      });
    });

  } catch (error) {
    console.error('資料庫測試 API: 未預期的錯誤:', error);
    
    return res.status(500).json({
      success: false,
      error: '資料庫測試失敗',
      details: error.message
    });
  }
}
