import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletAuthFix() {
  const router = useRouter();
  const [status, setStatus] = useState('開始修復...');
  const [isFixed, setIsFixed] = useState(false);
  const [tokenInfo, setTokenInfo] = useState(null);

  useEffect(() => {
    // 自動開始修復
    fixAuthAndRedirect();
  }, []);

  const fixAuthAndRedirect = async () => {
    try {
      setStatus('🔍 檢查當前認證狀態...');
      
      // 檢查現有認證
      const existingToken = localStorage.getItem('token');
      const existingUser = localStorage.getItem('user');
      
      if (existingToken && existingUser) {
        setStatus('🧪 測試現有 Token...');
        
        // 測試現有 token
        const testResponse = await fetch('/api/wallet/balance', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${existingToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (testResponse.ok) {
          const testResult = await testResponse.json();
          if (testResult.success) {
            setStatus('✅ 現有 Token 有效！');
            setTokenInfo({
              token: existingToken,
              user: JSON.parse(existingUser),
              isValid: true,
              balance: testResult.data.balance
            });
            setIsFixed(true);
            
            // 自動跳轉到錢包頁面
            setTimeout(() => {
              router.push('/wallet');
            }, 2000);
            return;
          }
        }
      }

      setStatus('🗑️ 清除無效的認證數據...');
      localStorage.clear();

      setStatus('🔑 自動登錄獲取新 Token...');
      
      const loginResponse = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const loginResult = await loginResponse.json();
      
      if (loginResult.success) {
        setStatus('💾 保存新的認證數據...');
        
        localStorage.setItem('token', loginResult.token);
        localStorage.setItem('user', JSON.stringify(loginResult.user));

        setStatus('🧪 驗證新 Token 並載入錢包數據...');
        
        // 驗證新 token 並載入錢包數據
        const verifyResponse = await fetch('/api/wallet/balance', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${loginResult.token}`,
            'Content-Type': 'application/json'
          }
        });

        if (verifyResponse.ok) {
          const verifyData = await verifyResponse.json();
          if (verifyData.success) {
            setTokenInfo({
              token: loginResult.token,
              user: loginResult.user,
              isValid: true,
              balance: verifyData.data.balance
            });

            setStatus(`✅ 認證修復成功！錢包餘額: $${verifyData.data.balance.toFixed(2)}`);
            setIsFixed(true);
            
            // 自動跳轉到錢包頁面
            setTimeout(() => {
              setStatus('🚀 正在跳轉到錢包頁面...');
              router.push('/wallet');
            }, 3000);
          } else {
            setStatus('❌ Token 驗證失敗');
          }
        } else {
          setStatus('❌ Token 驗證失敗');
        }
      } else {
        setStatus('❌ 自動登錄失敗: ' + loginResult.error);
      }

    } catch (error) {
      setStatus('❌ 修復失敗: ' + error.message);
    }
  };

  const manualGoToWallet = () => {
    router.push('/wallet');
  };

  const goToDashboard = () => {
    router.push('/dashboard-simple');
  };

  const goToTokenGenerator = () => {
    router.push('/token-generator');
  };

  return (
    <>
      <Head>
        <title>錢包認證修復 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-lg w-full mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {isFixed ? (
                  <span className="text-2xl">✅</span>
                ) : (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                )}
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">💰 錢包認證修復</h1>
              <p className="text-gray-600">自動修復錢包認證問題</p>
            </div>

            {/* 狀態顯示 */}
            <div className="mb-8">
              <div className={`p-4 rounded-lg ${
                isFixed ? 'bg-green-50 border border-green-200' : 'bg-blue-50 border border-blue-200'
              }`}>
                <p className={`font-medium ${
                  isFixed ? 'text-green-800' : 'text-blue-800'
                }`}>
                  {status}
                </p>
              </div>
            </div>

            {/* Token 和錢包信息 */}
            {tokenInfo && (
              <div className="mb-8">
                <h3 className="font-medium text-gray-900 mb-3">📋 認證和錢包信息</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Token:</span>
                    <span className="text-green-600">✅ {tokenInfo.token.length} 字符</span>
                  </div>
                  <div className="flex justify-between">
                    <span>用戶:</span>
                    <span className="text-green-600">✅ {tokenInfo.user.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>角色:</span>
                    <span className="text-green-600">✅ {tokenInfo.user.role}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Email:</span>
                    <span className="text-green-600">✅ {tokenInfo.user.email}</span>
                  </div>
                  {tokenInfo.balance !== undefined && (
                    <div className="flex justify-between">
                      <span>錢包餘額:</span>
                      <span className="text-blue-600 font-semibold">💰 ${tokenInfo.balance.toFixed(2)}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 操作按鈕 */}
            <div className="space-y-3">
              {isFixed ? (
                <div className="space-y-3">
                  <button
                    onClick={manualGoToWallet}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    💰 前往錢包頁面
                  </button>
                  
                  <button
                    onClick={goToDashboard}
                    className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                  >
                    🏠 回到儀表板
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  <button
                    onClick={fixAuthAndRedirect}
                    className="w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
                  >
                    🔄 重新嘗試修復
                  </button>
                  
                  <button
                    onClick={goToTokenGenerator}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    🔑 手動生成 Token
                  </button>
                </div>
              )}
              
              <button
                onClick={goToDashboard}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🏠 返回儀表板
              </button>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">🎯 修復流程</h3>
              <div className="text-sm text-yellow-800 space-y-1">
                <div>1. 檢查現有認證數據</div>
                <div>2. 測試 Token 是否可以訪問錢包 API</div>
                <div>3. 如無效，自動重新登錄</div>
                <div>4. 保存新的認證數據</div>
                <div>5. 驗證錢包 API 訪問</div>
                <div>6. 自動跳轉到錢包頁面</div>
              </div>
            </div>

            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2">📋 管理員帳號</h3>
              <div className="text-sm text-green-800">
                <div>Email: <EMAIL></div>
                <div>Password: password123</div>
                <div>Role: admin</div>
              </div>
            </div>

            {/* 進度指示 */}
            {!isFixed && (
              <div className="mt-6">
                <div className="flex justify-center">
                  <div className="animate-pulse flex space-x-1">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  </div>
                </div>
                <p className="text-center text-sm text-gray-500 mt-2">
                  正在自動修復錢包認證問題...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
