import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function DatabaseViewer() {
  const [dbTest, setDbTest] = useState(null);
  const [members, setMembers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadDatabaseInfo();
  }, []);

  const loadDatabaseInfo = async () => {
    setIsLoading(true);
    setError('');

    try {
      // 測試資料庫連接
      console.log('測試資料庫連接...');
      const dbResponse = await fetch('/api/test-database');
      const dbResult = await dbResponse.json();
      
      console.log('資料庫測試結果:', dbResult);
      setDbTest(dbResult);

      if (dbResult.success) {
        // 載入會員數據
        console.log('載入會員數據...');
        const membersResponse = await fetch('/api/members/list');
        const membersResult = await membersResponse.json();
        
        console.log('會員數據結果:', membersResult);
        
        if (membersResult.success) {
          setMembers(membersResult.data || []);
        } else {
          setError('載入會員數據失敗: ' + membersResult.error);
        }
      } else {
        setError('資料庫連接失敗: ' + dbResult.error);
      }

    } catch (err) {
      console.error('載入失敗:', err);
      setError('載入失敗: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '從未';
    try {
      return new Date(dateString).toLocaleString('zh-TW');
    } catch {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入資料庫信息中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>資料庫查看器 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🗄️ 資料庫查看器</h1>
                <p className="text-gray-600 mt-2">直接查看 SQLite 資料庫中的會員數據</p>
              </div>
              <button
                onClick={loadDatabaseInfo}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                🔄 重新載入
              </button>
            </div>

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600">❌ {error}</p>
              </div>
            )}

            {/* 資料庫信息 */}
            {dbTest && (
              <div className="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
                <h2 className="text-xl font-semibold text-blue-900 mb-4">📊 資料庫信息</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>狀態:</strong> 
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${
                      dbTest.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {dbTest.success ? '✅ 連接成功' : '❌ 連接失敗'}
                    </span>
                  </div>
                  {dbTest.data && (
                    <>
                      <div><strong>文件路徑:</strong> <code className="text-xs bg-gray-100 px-1 rounded">{dbTest.data.dbPath}</code></div>
                      <div><strong>文件大小:</strong> {dbTest.data.fileSize}</div>
                      <div><strong>最後修改:</strong> {formatDate(dbTest.data.lastModified)}</div>
                      <div><strong>會員數量:</strong> {dbTest.data.memberCount} 人</div>
                      <div><strong>服務數量:</strong> {dbTest.data.serviceCount} 個</div>
                      <div><strong>訂閱數量:</strong> {dbTest.data.subscriptionCount} 個</div>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* 會員列表 */}
            {dbTest && dbTest.success && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">👥 會員列表 (來自 SQLite 資料庫)</h2>
                
                {members.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300 bg-white rounded-lg overflow-hidden shadow">
                      <thead>
                        <tr className="bg-gray-100">
                          <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">ID</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">姓名</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">Email</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">角色</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">狀態</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">電話</th>
                          <th className="border border-gray-300 px-4 py-3 text-center font-semibold text-gray-900">Email驗證</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">最後登入</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">註冊時間</th>
                        </tr>
                      </thead>
                      <tbody>
                        {members.map((member, index) => (
                          <tr key={member.id} className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                            <td className="border border-gray-300 px-4 py-3 font-bold text-blue-600">{member.id}</td>
                            <td className="border border-gray-300 px-4 py-3 font-medium">{member.name}</td>
                            <td className="border border-gray-300 px-4 py-3">
                              <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">{member.email}</div>
                            </td>
                            <td className="border border-gray-300 px-4 py-3">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                member.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                              }`}>
                                {member.role === 'admin' ? '管理員' : '一般用戶'}
                              </span>
                            </td>
                            <td className="border border-gray-300 px-4 py-3">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                              }`}>
                                {member.status === 'active' ? '活躍' : member.status}
                              </span>
                            </td>
                            <td className="border border-gray-300 px-4 py-3">
                              {member.phone ? (
                                <span className="font-mono text-sm">{member.phone}</span>
                              ) : (
                                <span className="text-gray-400">未提供</span>
                              )}
                            </td>
                            <td className="border border-gray-300 px-4 py-3 text-center">
                              {member.email_verified ? (
                                <span className="text-green-600 text-lg">✅</span>
                              ) : (
                                <span className="text-red-600 text-lg">❌</span>
                              )}
                            </td>
                            <td className="border border-gray-300 px-4 py-3 text-sm">
                              <div className={member.last_login_at ? 'text-gray-700' : 'text-gray-400'}>
                                {formatDate(member.last_login_at)}
                              </div>
                            </td>
                            <td className="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                              {formatDate(member.created_at)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-12 bg-gray-50 rounded-lg">
                    <div className="text-gray-500 text-lg">📭 暫無會員數據</div>
                    <p className="text-gray-400 mt-2">資料庫中沒有找到會員記錄</p>
                  </div>
                )}
              </div>
            )}

            {/* 說明信息 */}
            <div className="mt-8 p-6 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-900 mb-3">✅ 確認資料庫讀取</h3>
              <div className="text-sm text-green-800 space-y-2">
                <p><strong>數據來源:</strong> 直接從 SQLite 資料庫文件讀取</p>
                <p><strong>資料庫位置:</strong> {dbTest?.data?.dbPath || '未知'}</p>
                <p><strong>實時性:</strong> 每次重新載入都會從資料庫重新查詢最新數據</p>
                <p><strong>安全性:</strong> 密碼哈希等敏感信息不會顯示在此頁面</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
