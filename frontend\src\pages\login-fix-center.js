import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function LoginFixCenter() {
  const router = useRouter();
  const [authStatus, setAuthStatus] = useState(null);
  const [isFixing, setIsFixing] = useState(false);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let user = null;
    if (userStr) {
      try {
        user = JSON.parse(userStr);
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setAuthStatus({
      hasToken: !!token,
      hasUser: !!user,
      user: user,
      isLoggedIn: !!(token && user)
    });
  };

  const quickFix = async () => {
    setIsFixing(true);
    
    try {
      // 清除舊數據
      localStorage.clear();
      
      // 自動登錄
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        
        // 更新狀態
        checkAuthStatus();
        
        // 跳轉到儀表板
        setTimeout(() => {
          router.push('/dashboard-simple');
        }, 1500);
      } else {
        alert('自動修復失敗: ' + result.error);
      }
    } catch (error) {
      alert('修復過程中出錯: ' + error.message);
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <>
      <Head>
        <title>登錄修復中心 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-lg w-full mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {authStatus?.isLoggedIn ? (
                  <span className="text-3xl">✅</span>
                ) : (
                  <span className="text-3xl">🔧</span>
                )}
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">登錄修復中心</h1>
              <p className="text-gray-600">一站式解決所有登錄問題</p>
            </div>

            {/* 認證狀態 */}
            {authStatus && (
              <div className={`mb-8 p-4 rounded-lg border ${
                authStatus.isLoggedIn 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <h3 className={`font-medium mb-2 ${
                  authStatus.isLoggedIn ? 'text-green-900' : 'text-red-900'
                }`}>
                  {authStatus.isLoggedIn ? '✅ 認證狀態正常' : '❌ 認證狀態異常'}
                </h3>
                <div className={`text-sm space-y-1 ${
                  authStatus.isLoggedIn ? 'text-green-800' : 'text-red-800'
                }`}>
                  <div>Token: {authStatus.hasToken ? '✅ 存在' : '❌ 缺失'}</div>
                  <div>User: {authStatus.hasUser ? '✅ 存在' : '❌ 缺失'}</div>
                  {authStatus.user && (
                    <div className="mt-2 p-2 bg-white rounded border">
                      <div className="text-xs">
                        <div>姓名: {authStatus.user.name}</div>
                        <div>Email: {authStatus.user.email}</div>
                        <div>角色: {authStatus.user.role}</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 操作按鈕 */}
            <div className="space-y-4">
              {authStatus?.isLoggedIn ? (
                <div className="space-y-3">
                  <button
                    onClick={() => router.push('/dashboard-simple')}
                    className="w-full px-6 py-4 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium text-lg"
                  >
                    🏠 前往儀表板
                  </button>
                  
                  <button
                    onClick={checkAuthStatus}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    🔄 重新檢查狀態
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  <button
                    onClick={quickFix}
                    disabled={isFixing}
                    className="w-full px-6 py-4 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 font-medium text-lg"
                  >
                    {isFixing ? '修復中...' : '⚡ 一鍵修復登錄'}
                  </button>

                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={() => router.push('/super-simple-login')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      🔑 手動登錄
                    </button>

                    <button
                      onClick={() => router.push('/login-system-test')}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                    >
                      🧪 系統測試
                    </button>
                  </div>

                  <button
                    onClick={() => router.push('/debug-auth')}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    🔍 詳細調試
                  </button>
                </div>
              )}
            </div>

            {/* 說明 */}
            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 修復說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>一鍵修復:</strong> 自動清除舊數據並重新登錄</div>
                <div><strong>手動登錄:</strong> 提供多種登錄選項</div>
                <div><strong>系統測試:</strong> 全面測試登錄功能</div>
                <div><strong>詳細調試:</strong> 查看認證數據詳情</div>
              </div>
            </div>

            {/* 測試帳號 */}
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2">📋 可用帳號</h3>
              <div className="text-sm text-green-800 space-y-1">
                <div>管理員: <EMAIL> / password123</div>
                <div>舊管理員: <EMAIL> / password</div>
                <div>一般用戶: <EMAIL> / password</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
