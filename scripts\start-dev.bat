@echo off
REM 開發環境快速啟動腳本 (Windows)
REM 使用方法: scripts\start-dev.bat [ngrok]

echo 🚀 啟動 MemberService 開發環境...

REM 檢查參數
set USE_NGROK=false
if "%1"=="ngrok" (
    set USE_NGROK=true
    echo 🌐 使用 ngrok 域名模式
) else (
    echo 🏠 使用本地開發模式
)

REM 檢查 Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安裝，請先安裝 Node.js 18+
    pause
    exit /b 1
)

REM 檢查 npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm 未安裝，請先安裝 npm
    pause
    exit /b 1
)

REM 安裝依賴 (如果需要)
if not exist "frontend\node_modules" (
    echo 📦 安裝前端依賴...
    cd frontend
    npm install
    cd ..
)

if not exist "backend\node_modules" (
    echo 📦 安裝後端依賴...
    cd backend
    npm install
    cd ..
)

REM 檢查環境變數文件
if not exist "frontend\.env.local" (
    echo ⚠️  前端環境變數文件不存在，請配置 frontend\.env.local
)

if not exist "backend\.env" (
    echo ⚠️  後端環境變數文件不存在，請配置 backend\.env
)

REM 啟動後端
echo 🔧 啟動後端服務 (Port 3000)...
start "Backend" cmd /k "cd backend && npm run dev"

REM 等待後端啟動
timeout /t 3 /nobreak >nul

REM 啟動前端
echo 🎨 啟動前端服務...
if "%USE_NGROK%"=="true" (
    echo 🌐 前端運行在 0.0.0.0:3001 (ngrok 模式)
    start "Frontend" cmd /k "cd frontend && npm run dev:ngrok"
    
    REM 等待前端啟動
    timeout /t 5 /nobreak >nul
    
    REM 啟動 ngrok
    where ngrok >nul 2>nul
    if %errorlevel% equ 0 (
        echo 🚇 啟動 ngrok 隧道...
        start "ngrok" cmd /k "cd frontend && npm run ngrok"
    ) else (
        echo ⚠️  ngrok 未安裝，請手動啟動 ngrok
    )
) else (
    echo 🏠 前端運行在 localhost:3001 (本地模式)
    start "Frontend" cmd /k "cd frontend && npm run dev"
)

REM 等待服務啟動
timeout /t 5 /nobreak >nul

REM 顯示訪問信息
echo.
echo ✅ 服務啟動完成！
echo.
echo 📍 訪問地址:
if "%USE_NGROK%"=="true" (
    echo    🌐 ngrok 域名: https://topyun.ngrok.app
    echo    📊 ngrok 控制台: http://localhost:4040
)
echo    🏠 本地前端: http://localhost:3001
echo    🔧 後端 API: http://localhost:3000
echo.
echo 🛑 關閉所有服務窗口來停止服務
echo.

pause
