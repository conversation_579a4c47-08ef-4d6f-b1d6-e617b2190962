import { useState } from 'react';
import Head from 'next/head';

export default function RegisterDebugTest() {
  const [formData, setFormData] = useState({
    name: '測試用戶',
    email: '<EMAIL>',
    password: '1234',
    confirmPassword: '1234',
    phone: '0912345678'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const testOriginalAPI = async () => {
    setIsLoading(true);
    setResult(null);
    addLog('🧪 測試原始註冊 API...', 'info');

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        addLog('✅ 原始 API 註冊成功', 'success');
        setResult({ type: 'success', api: 'original', data });
      } else {
        addLog(`❌ 原始 API 註冊失敗: ${data.error}`, 'error');
        setResult({ type: 'error', api: 'original', data });
      }
    } catch (error) {
      addLog(`❌ 原始 API 請求失敗: ${error.message}`, 'error');
      setResult({ type: 'error', api: 'original', error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const testDebugAPI = async () => {
    setIsLoading(true);
    setResult(null);
    addLog('🔍 測試調試註冊 API...', 'info');

    try {
      const response = await fetch('/api/auth/register-debug', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        addLog('✅ 調試 API 註冊成功', 'success');
        setResult({ type: 'success', api: 'debug', data });
      } else {
        addLog(`❌ 調試 API 註冊失敗: ${data.error}`, 'error');
        setResult({ type: 'error', api: 'debug', data });
      }
    } catch (error) {
      addLog(`❌ 調試 API 請求失敗: ${error.message}`, 'error');
      setResult({ type: 'error', api: 'debug', error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const checkDatabase = async () => {
    addLog('🗄️ 檢查資料庫中的會員...', 'info');
    
    try {
      const response = await fetch('/api/members/list');
      const data = await response.json();
      
      if (data.success) {
        const member = data.data.find(m => m.email === formData.email);
        if (member) {
          addLog(`✅ 在資料庫中找到會員: ${member.name} (ID: ${member.id})`, 'success');
        } else {
          addLog(`❌ 在資料庫中未找到 ${formData.email}`, 'error');
        }
      } else {
        addLog(`❌ 查詢資料庫失敗: ${data.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 資料庫查詢請求失敗: ${error.message}`, 'error');
    }
  };

  const clearLogs = () => {
    setLogs([]);
    setResult(null);
  };

  return (
    <>
      <Head>
        <title>註冊調試測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔍 註冊調試測試</h1>
              <p className="text-gray-600 mt-2">測試註冊功能並檢查資料庫寫入</p>
            </div>

            {/* 表單 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">註冊表單</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">密碼</label>
                    <input
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">確認密碼</label>
                    <input
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">電話</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                {/* 測試按鈕 */}
                <div className="mt-6 space-y-3">
                  <button
                    onClick={testOriginalAPI}
                    disabled={isLoading}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isLoading ? '測試中...' : '🧪 測試原始註冊 API'}
                  </button>
                  
                  <button
                    onClick={testDebugAPI}
                    disabled={isLoading}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                  >
                    {isLoading ? '測試中...' : '🔍 測試調試註冊 API'}
                  </button>
                  
                  <button
                    onClick={checkDatabase}
                    className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                  >
                    🗄️ 檢查資料庫
                  </button>
                  
                  <button
                    onClick={clearLogs}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  >
                    🗑️ 清除日誌
                  </button>
                </div>
              </div>

              {/* 結果顯示 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試結果</h2>
                
                {/* API 響應 */}
                {result && (
                  <div className={`mb-4 p-4 rounded-lg ${
                    result.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                  }`}>
                    <h3 className={`font-medium ${
                      result.type === 'success' ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {result.api === 'original' ? '原始 API' : '調試 API'} 響應
                    </h3>
                    <pre className={`mt-2 text-xs overflow-auto ${
                      result.type === 'success' ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {JSON.stringify(result.data || result.error, null, 2)}
                    </pre>
                  </div>
                )}

                {/* 測試日誌 */}
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
                  {logs.map((log) => (
                    <div key={log.id} className={`mb-1 ${
                      log.type === 'error' ? 'text-red-400' : 
                      log.type === 'success' ? 'text-green-400' : 
                      'text-blue-400'
                    }`}>
                      [{log.timestamp}] {log.message}
                    </div>
                  ))}
                  {logs.length === 0 && (
                    <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                  )}
                </div>
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">🔍 調試說明</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>原始 API:</strong> 使用 /api/auth/register，可能有錯誤處理問題</p>
                <p><strong>調試 API:</strong> 使用 /api/auth/register-debug，包含詳細的調試信息</p>
                <p><strong>資料庫檢查:</strong> 查詢 /api/members/list 確認數據是否寫入</p>
                <p><strong>目標:</strong> 確保 <EMAIL> 能成功註冊並寫入資料庫</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
