# 資料庫設置腳本 - 簡化版本

Write-Host "=== 資料庫設置指南 ===" -ForegroundColor Cyan
Write-Host ""

# 檢查 Docker 狀態
Write-Host "檢查 Docker 狀態..." -ForegroundColor Blue
$dockerInstalled = $false
$dockerRunning = $false

try {
    $null = docker --version 2>$null
    $dockerInstalled = $true
    Write-Host "Docker 已安裝" -ForegroundColor Green
    
    try {
        $null = docker info 2>$null
        $dockerRunning = $true
        Write-Host "Docker 服務正在運行" -ForegroundColor Green
    } catch {
        Write-Host "Docker 已安裝但未運行" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Docker 未安裝" -ForegroundColor Red
}

Write-Host ""
Write-Host "請選擇資料庫安裝方式:" -ForegroundColor Cyan
Write-Host "1. Docker 安裝 (推薦)"
Write-Host "2. 本地安裝指南"
Write-Host "3. 雲端資料庫指南"
Write-Host ""

$choice = Read-Host "請選擇 (1-3)"

if ($choice -eq "1") {
    Write-Host ""
    if ($dockerInstalled -and $dockerRunning) {
        Write-Host "使用 Docker 安裝資料庫..." -ForegroundColor Blue
        
        if (Test-Path "backend\docker-compose.yml") {
            Write-Host "啟動資料庫服務..."
            Set-Location backend
            docker-compose up -d postgres redis
            
            Write-Host "等待服務啟動..." -ForegroundColor Yellow
            Start-Sleep -Seconds 10
            
            Write-Host ""
            Write-Host "資料庫服務已啟動！" -ForegroundColor Green
            Write-Host "PostgreSQL: localhost:5432"
            Write-Host "Redis: localhost:6379"
            Write-Host "用戶名: postgres"
            Write-Host "密碼: password"
            Write-Host ""
            Write-Host "下一步:"
            Write-Host "1. npm install"
            Write-Host "2. npm run migrate"
            Write-Host "3. npm run dev"
        } else {
            Write-Host "找不到 docker-compose.yml" -ForegroundColor Red
        }
    } else {
        Write-Host "Docker 不可用" -ForegroundColor Red
        Write-Host ""
        Write-Host "請安裝 Docker Desktop:"
        Write-Host "https://www.docker.com/products/docker-desktop"
    }
}
elseif ($choice -eq "2") {
    Write-Host ""
    Write-Host "本地安裝 PostgreSQL" -ForegroundColor Blue
    Write-Host ""
    Write-Host "1. 下載 PostgreSQL:"
    Write-Host "   https://www.postgresql.org/download/windows/"
    Write-Host ""
    Write-Host "2. 安裝設置:"
    Write-Host "   - 密碼: password"
    Write-Host "   - 端口: 5432"
    Write-Host ""
    Write-Host "3. 創建資料庫:"
    Write-Host "   psql -U postgres"
    Write-Host "   CREATE DATABASE member_system;"
    Write-Host ""
    Write-Host "4. 更新 backend\.env 文件"
}
elseif ($choice -eq "3") {
    Write-Host ""
    Write-Host "雲端資料庫選項" -ForegroundColor Blue
    Write-Host ""
    Write-Host "PostgreSQL 雲端服務:"
    Write-Host "- Supabase: https://supabase.com/ (推薦)"
    Write-Host "- ElephantSQL: https://www.elephantsql.com/"
    Write-Host ""
    Write-Host "Redis 雲端服務:"
    Write-Host "- Redis Cloud: https://redis.com/redis-enterprise-cloud/"
    Write-Host ""
    Write-Host "設置完成後更新 backend\.env 文件"
} else {
    Write-Host "無效選擇" -ForegroundColor Red
}

Write-Host ""
Write-Host "更多資訊請查看: docs\DATABASE_SETUP.md" -ForegroundColor Yellow
Write-Host "設置完成" -ForegroundColor Green
