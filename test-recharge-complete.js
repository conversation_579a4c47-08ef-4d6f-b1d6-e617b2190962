// 完整的充值功能測試
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testRechargeComplete() {
  try {
    console.log('🔋 完整充值功能測試...');
    
    // 1. 登入獲取 token
    console.log('\n1️⃣ 登入獲取 token...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: '1234'
    });

    if (loginResult.status !== 200 || !loginResult.data.success) {
      console.error('❌ 登入失敗:', loginResult.data);
      return;
    }

    const token = loginResult.data.data.token;
    const user = loginResult.data.data.user;
    console.log('✅ 登入成功 - 用戶:', user.name);

    // 2. 檢查初始餘額
    console.log('\n2️⃣ 檢查初始餘額...');
    const balanceOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/balance',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const initialBalanceResult = await makeRequest(balanceOptions);
    
    if (initialBalanceResult.status === 200 && initialBalanceResult.data.success) {
      console.log('✅ 初始餘額:', initialBalanceResult.data.data.formatted_balance);
    } else {
      console.log('❌ 無法獲取初始餘額');
      return;
    }

    // 3. 獲取支付方式
    console.log('\n3️⃣ 獲取支付方式...');
    const paymentMethodsOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/payment-methods',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const paymentMethodsResult = await makeRequest(paymentMethodsOptions);
    
    if (paymentMethodsResult.status !== 200 || !paymentMethodsResult.data.success) {
      console.log('❌ 無法獲取支付方式:', paymentMethodsResult.data);
      return;
    }

    const paymentMethods = paymentMethodsResult.data.data.payment_methods;
    console.log(`✅ 找到 ${paymentMethods.length} 個支付方式:`);
    paymentMethods.forEach(method => {
      const fee = method.fee_type === 'none' ? '免費' : 
                 method.fee_type === 'fixed' ? `固定 $${method.fee_amount}` :
                 `${method.fee_amount}%`;
      console.log(`   - ${method.name} (${fee}) - $${method.min_amount}~$${method.max_amount}`);
    });

    // 4. 測試充值 - 使用第一個支付方式
    if (paymentMethods.length === 0) {
      console.log('❌ 沒有可用的支付方式');
      return;
    }

    const selectedMethod = paymentMethods[0];
    const rechargeAmount = 500;

    console.log(`\n4️⃣ 測試充值 - 使用 ${selectedMethod.name} 充值 $${rechargeAmount}...`);
    
    const rechargeOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/recharge',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const rechargeResult = await makeRequest(rechargeOptions, {
      amount: rechargeAmount,
      payment_method_id: selectedMethod.id,
      description: `測試充值 - 使用${selectedMethod.name}`
    });
    
    console.log('充值 API 狀態:', rechargeResult.status);
    
    if (rechargeResult.status === 200 && rechargeResult.data.success) {
      console.log('✅ 充值成功！');
      console.log('💰 新餘額:', rechargeResult.data.data.new_balance.formatted_balance);
      console.log('📊 充值詳情:');
      console.log('   - 充值金額:', `$${rechargeResult.data.data.payment_details.amount}`);
      console.log('   - 手續費:', `$${rechargeResult.data.data.payment_details.fee}`);
      console.log('   - 總計:', `$${rechargeResult.data.data.payment_details.total}`);
      console.log('   - 支付方式:', rechargeResult.data.data.payment_details.payment_method);
      console.log('   - 支付參考號:', rechargeResult.data.data.payment_details.payment_reference);
    } else {
      console.log('❌ 充值失敗:', rechargeResult.data);
      return;
    }

    // 5. 驗證餘額更新
    console.log('\n5️⃣ 驗證餘額更新...');
    const finalBalanceResult = await makeRequest(balanceOptions);
    
    if (finalBalanceResult.status === 200 && finalBalanceResult.data.success) {
      console.log('✅ 最終餘額:', finalBalanceResult.data.data.formatted_balance);
      
      const initialBalance = initialBalanceResult.data.data.balance;
      const finalBalance = finalBalanceResult.data.data.balance;
      const expectedIncrease = rechargeAmount;
      const actualIncrease = finalBalance - initialBalance;
      
      console.log('📈 餘額變化:');
      console.log(`   - 初始餘額: $${initialBalance}`);
      console.log(`   - 最終餘額: $${finalBalance}`);
      console.log(`   - 預期增加: $${expectedIncrease}`);
      console.log(`   - 實際增加: $${actualIncrease}`);
      
      if (Math.abs(actualIncrease - expectedIncrease) < 0.01) {
        console.log('✅ 餘額更新正確！');
      } else {
        console.log('❌ 餘額更新不正確');
      }
    }

    // 6. 檢查交易記錄
    console.log('\n6️⃣ 檢查交易記錄...');
    const transactionsOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/transactions',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const transactionsResult = await makeRequest(transactionsOptions);
    
    if (transactionsResult.status === 200 && transactionsResult.data.success) {
      const transactions = transactionsResult.data.data.transactions;
      console.log(`✅ 找到 ${transactions.length} 筆交易記錄`);
      
      if (transactions.length > 0) {
        const latestTransaction = transactions[0];
        console.log('📋 最新交易:');
        console.log(`   - 類型: ${latestTransaction.transaction_type}`);
        console.log(`   - 金額: $${latestTransaction.amount}`);
        console.log(`   - 說明: ${latestTransaction.description}`);
        console.log(`   - 狀態: ${latestTransaction.status}`);
        console.log(`   - 時間: ${latestTransaction.created_at}`);
      }
    } else {
      console.log('❌ 無法獲取交易記錄:', transactionsResult.data);
    }

    console.log('\n🎉 充值功能測試完成！');

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testRechargeComplete();
