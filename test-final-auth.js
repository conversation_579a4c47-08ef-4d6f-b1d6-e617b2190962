// 最終認證測試
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testAuth() {
  try {
    console.log('🔐 最終認證測試...');
    
    // 1. 登入
    console.log('1️⃣ 登入...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: '1234'
    });

    console.log('登入狀態:', loginResult.status);
    console.log('登入響應:', JSON.stringify(loginResult.data, null, 2));

    if (loginResult.status !== 200 || !loginResult.data.success) {
      console.error('❌ 登入失敗');
      return;
    }

    const token = loginResult.data.data.token;
    console.log('✅ 登入成功，獲得 token');

    // 2. 測試訂閱 API
    console.log('\n2️⃣ 測試訂閱 API...');
    const subscriptionOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/subscriptions/simple',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const subscriptionResult = await makeRequest(subscriptionOptions);
    
    console.log('訂閱 API 狀態:', subscriptionResult.status);
    console.log('訂閱 API 響應:', JSON.stringify(subscriptionResult.data, null, 2));

    if (subscriptionResult.status === 200 && subscriptionResult.data.success) {
      console.log('✅ 認證修復成功！訂閱 API 可以正常使用了');
    } else {
      console.log('❌ 訂閱 API 認證仍然失敗');
    }

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testAuth();
