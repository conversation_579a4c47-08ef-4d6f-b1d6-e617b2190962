import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function SubscriptionLogicTest() {
  const [subscriptions, setSubscriptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [allServices, setAllServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [analysis, setAnalysis] = useState(null);

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setIsLoading(true);
    
    try {
      await Promise.all([
        loadSubscriptions(),
        loadAllServices()
      ]);
    } catch (error) {
      console.error('載入數據失敗:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubscriptions = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        const formattedSubscriptions = result.data.map(sub => ({
          id: sub.id,
          service_id: sub.service_id,
          service_name: sub.service_name,
          status: sub.status,
          subscribed_at: sub.subscribed_at
        }));
        
        setSubscriptions(formattedSubscriptions);
      }
    } catch (error) {
      console.error('載入訂閱失敗:', error);
    }
  };

  const loadAllServices = async () => {
    try {
      const response = await fetch('/api/services/database');
      const result = await response.json();

      if (result.success) {
        setAllServices(result.data);
      }
    } catch (error) {
      console.error('載入服務失敗:', error);
    }
  };

  // 計算可用服務
  useEffect(() => {
    if (allServices.length > 0) {
      const activeServices = allServices.filter(service => service.status === 'active');
      const activeSubscribedServiceIds = subscriptions
        .filter(sub => sub.status === 'active')
        .map(sub => sub.service_id);
      
      const available = activeServices.filter(service =>
        !activeSubscribedServiceIds.includes(service.id)
      );

      setAvailableServices(available);
      
      // 分析邏輯關係
      analyzeLogic(activeServices, available);
    }
  }, [allServices, subscriptions]);

  const analyzeLogic = (activeServices, available) => {
    // 基本統計
    const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
    const cancelledSubscriptions = subscriptions.filter(sub => sub.status === 'cancelled');
    const expiredSubscriptions = subscriptions.filter(sub => sub.status === 'expired');

    // 服務 ID 分析
    const activeSubscribedServiceIds = activeSubscriptions.map(sub => sub.service_id);
    const cancelledServiceIds = cancelledSubscriptions.map(sub => sub.service_id);
    const expiredServiceIds = expiredSubscriptions.map(sub => sub.service_id);
    const availableServiceIds = available.map(service => service.id);

    // 重複分析
    const reappearingCancelledServices = cancelledServiceIds.filter(id => availableServiceIds.includes(id));
    const reappearingExpiredServices = expiredServiceIds.filter(id => availableServiceIds.includes(id));

    // 邏輯驗證
    const totalActiveServices = activeServices.length;
    const activeSubscriptionsCount = activeSubscriptions.length;
    const availableCount = available.length;
    const serviceCountMatch = activeSubscriptionsCount + availableCount === totalActiveServices;

    // 重複檢查
    const activeVsCancelled = activeSubscribedServiceIds.filter(id => cancelledServiceIds.includes(id));
    const activeVsExpired = activeSubscribedServiceIds.filter(id => expiredServiceIds.includes(id));

    setAnalysis({
      // 基本統計
      totalServices: allServices.length,
      activeServices: activeServices.length,
      totalSubscriptions: subscriptions.length,
      activeSubscriptions: activeSubscriptions.length,
      cancelledSubscriptions: cancelledSubscriptions.length,
      expiredSubscriptions: expiredSubscriptions.length,
      availableServices: available.length,

      // ID 列表
      activeSubscribedServiceIds,
      cancelledServiceIds,
      expiredServiceIds,
      availableServiceIds,

      // 重複分析
      reappearingCancelledServices,
      reappearingExpiredServices,
      activeVsCancelled,
      activeVsExpired,

      // 邏輯驗證
      serviceCountMatch,
      expectedTotal: totalActiveServices,
      actualTotal: activeSubscriptionsCount + availableCount,

      // 邏輯關係
      relationships: {
        activeVsCancelledOverlap: activeVsCancelled.length > 0,
        activeVsExpiredOverlap: activeVsExpired.length > 0,
        cancelledReappearing: reappearingCancelledServices.length > 0,
        expiredReappearing: reappearingExpiredServices.length > 0
      }
    });
  };

  const testCancelSubscription = async (subscriptionId) => {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/subscriptions/cancel', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription_id: subscriptionId
        })
      });

      const result = await response.json();

      if (result.success) {
        // 更新本地狀態
        const updatedSubscriptions = subscriptions.map(sub =>
          sub.id === subscriptionId
            ? { ...sub, status: 'cancelled' }
            : sub
        );
        setSubscriptions(updatedSubscriptions);
        
        alert(`✅ 已取消「${subscription.service_name}」的訂閱`);
      } else {
        alert(`❌ 取消失敗: ${result.error}`);
      }
    } catch (error) {
      alert(`❌ 取消失敗: ${error.message}`);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>訂閱邏輯分析 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🧠 訂閱邏輯分析</h1>
              <p className="text-gray-600 mt-2">分析訂閱和服務之間的邏輯關係</p>
            </div>

            {analysis && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 基本統計 */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 基本統計</h2>
                  
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h3 className="font-medium text-blue-900 mb-2">🛍️ 服務統計</h3>
                      <div className="text-sm text-blue-800 space-y-1">
                        <div>總服務數: {analysis.totalServices}</div>
                        <div>活躍服務數: {analysis.activeServices}</div>
                      </div>
                    </div>

                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <h3 className="font-medium text-green-900 mb-2">📦 訂閱統計</h3>
                      <div className="text-sm text-green-800 space-y-1">
                        <div>總訂閱數: {analysis.totalSubscriptions}</div>
                        <div>使用中: {analysis.activeSubscriptions}</div>
                        <div>已取消: {analysis.cancelledSubscriptions}</div>
                        <div>已過期: {analysis.expiredSubscriptions}</div>
                      </div>
                    </div>

                    <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                      <h3 className="font-medium text-purple-900 mb-2">🎯 可用服務</h3>
                      <div className="text-sm text-purple-800">
                        可用服務數: {analysis.availableServices}
                      </div>
                    </div>

                    {/* 數量驗證 */}
                    <div className={`p-4 border rounded-lg ${
                      analysis.serviceCountMatch 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-red-50 border-red-200'
                    }`}>
                      <h3 className={`font-medium mb-2 ${
                        analysis.serviceCountMatch 
                          ? 'text-green-900' 
                          : 'text-red-900'
                      }`}>
                        {analysis.serviceCountMatch ? '✅' : '❌'} 數量驗證
                      </h3>
                      <div className={`text-sm space-y-1 ${
                        analysis.serviceCountMatch 
                          ? 'text-green-800' 
                          : 'text-red-800'
                      }`}>
                        <div>使用中訂閱 + 可用服務 = 總活躍服務</div>
                        <div>{analysis.activeSubscriptions} + {analysis.availableServices} = {analysis.actualTotal}</div>
                        <div>預期: {analysis.expectedTotal}</div>
                        <div>{analysis.serviceCountMatch ? '數量正確' : '數量不匹配'}</div>
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={loadAllData}
                    disabled={isLoading}
                    className="mt-4 w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    🔄 重新載入數據
                  </button>
                </div>

                {/* 邏輯關係分析 */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">🔍 邏輯關係分析</h2>
                  
                  <div className="space-y-4">
                    {/* 正確的邏輯關係 */}
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <h3 className="font-medium text-green-900 mb-2">✅ 正確的邏輯關係</h3>
                      <div className="text-sm text-green-800 space-y-1">
                        <div>• 使用中訂閱 ≠ 已取消訂閱 (不重複)</div>
                        <div>• 使用中訂閱 ≠ 可用服務 (不重複)</div>
                        <div>• 已取消服務 = 可用服務 (會重複)</div>
                      </div>
                    </div>

                    {/* 重複檢查 */}
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <h3 className="font-medium text-yellow-900 mb-2">🔄 重複檢查</h3>
                      <div className="text-sm text-yellow-800 space-y-2">
                        <div>
                          <strong>使用中 vs 已取消:</strong>
                          <div className={`ml-2 ${analysis.relationships.activeVsCancelledOverlap ? 'text-red-600' : 'text-green-600'}`}>
                            {analysis.relationships.activeVsCancelledOverlap ? '❌ 有重複' : '✅ 無重複'}
                            {analysis.activeVsCancelled.length > 0 && ` [${analysis.activeVsCancelled.join(', ')}]`}
                          </div>
                        </div>
                        
                        <div>
                          <strong>使用中 vs 已過期:</strong>
                          <div className={`ml-2 ${analysis.relationships.activeVsExpiredOverlap ? 'text-red-600' : 'text-green-600'}`}>
                            {analysis.relationships.activeVsExpiredOverlap ? '❌ 有重複' : '✅ 無重複'}
                            {analysis.activeVsExpired.length > 0 && ` [${analysis.activeVsExpired.join(', ')}]`}
                          </div>
                        </div>
                        
                        <div>
                          <strong>已取消 vs 可用服務:</strong>
                          <div className={`ml-2 ${analysis.relationships.cancelledReappearing ? 'text-green-600' : 'text-yellow-600'}`}>
                            {analysis.relationships.cancelledReappearing ? '✅ 正常重複' : '⚠️ 無重複'}
                            {analysis.reappearingCancelledServices.length > 0 && ` [${analysis.reappearingCancelledServices.join(', ')}]`}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 服務 ID 詳情 */}
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                      <h3 className="font-medium text-gray-900 mb-2">📋 服務 ID 詳情</h3>
                      <div className="text-xs text-gray-700 space-y-2">
                        <div>
                          <strong>使用中訂閱的服務:</strong>
                          <div className="font-mono bg-white p-2 rounded border mt-1">
                            [{analysis.activeSubscribedServiceIds.join(', ')}]
                          </div>
                        </div>
                        
                        <div>
                          <strong>已取消訂閱的服務:</strong>
                          <div className="font-mono bg-white p-2 rounded border mt-1">
                            [{analysis.cancelledServiceIds.join(', ')}]
                          </div>
                        </div>
                        
                        <div>
                          <strong>可用的服務:</strong>
                          <div className="font-mono bg-white p-2 rounded border mt-1">
                            [{analysis.availableServiceIds.join(', ')}]
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 測試操作 */}
                    <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                      <h3 className="font-medium text-orange-900 mb-2">🧪 測試操作</h3>
                      <div className="space-y-2">
                        {subscriptions.filter(sub => sub.status === 'active').map(sub => (
                          <button
                            key={sub.id}
                            onClick={() => testCancelSubscription(sub.id)}
                            className="block w-full text-left px-3 py-2 bg-white border border-orange-200 rounded text-sm hover:bg-orange-100"
                          >
                            取消「{sub.service_name}」(服務ID: {sub.service_id})
                          </button>
                        ))}
                        {subscriptions.filter(sub => sub.status === 'active').length === 0 && (
                          <div className="text-sm text-orange-700">暫無可取消的訂閱</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📚 邏輯說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>正確的邏輯關係：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li><strong>使用中訂閱 vs 已取消訂閱：</strong> 不會重複（同一訂閱記錄不能同時是兩種狀態）</li>
                  <li><strong>使用中訂閱 vs 可用服務：</strong> 不會重複（已訂閱的服務不在可用列表）</li>
                  <li><strong>已取消訂閱 vs 可用服務：</strong> 會重複（被取消的服務重新變為可用）</li>
                </ul>
                <p><strong>數量驗證公式：</strong></p>
                <p className="ml-4 font-mono">使用中訂閱數 + 可用服務數 = 總活躍服務數</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
