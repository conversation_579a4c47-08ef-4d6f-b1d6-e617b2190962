import { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function SuperSimpleLogin() {
  const router = useRouter();
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const login = async (email, password) => {
    setIsLoading(true);
    setResult(`嘗試登錄: ${email}\n`);

    try {
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();
      
      setResult(prev => prev + `狀態: ${response.status}\n`);
      setResult(prev => prev + `結果: ${JSON.stringify(data, null, 2)}\n`);

      if (data.success) {
        setResult(prev => prev + `\n✅ 登錄成功！\n`);
        
        // 保存認證數據
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));
        
        setResult(prev => prev + `💾 已保存認證數據\n`);
        setResult(prev => prev + `🏠 3秒後跳轉到儀表板...\n`);
        
        // 跳轉到儀表板
        setTimeout(() => {
          router.push('/dashboard-simple');
        }, 3000);
      } else {
        setResult(prev => prev + `\n❌ 登錄失敗: ${data.error}\n`);
      }
    } catch (error) {
      setResult(prev => prev + `\n❌ 請求失敗: ${error.message}\n`);
    } finally {
      setIsLoading(false);
    }
  };

  const quickFix = async () => {
    setResult('🔧 開始一鍵修復...\n');
    
    // 清除舊數據
    localStorage.clear();
    setResult(prev => prev + '🗑️ 清除舊數據\n');
    
    // 嘗試登錄
    await login('<EMAIL>', 'password123');
  };

  return (
    <>
      <Head>
        <title>超級簡化登錄 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-md w-full mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔑</span>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">超級簡化登錄</h1>
              <p className="text-gray-600">確保登錄功能正常工作</p>
            </div>

            <div className="space-y-4 mb-6">
              <button
                onClick={quickFix}
                disabled={isLoading}
                className="w-full px-6 py-4 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 font-medium text-lg"
              >
                {isLoading ? '修復中...' : '⚡ 一鍵修復登錄'}
              </button>

              <div className="grid grid-cols-1 gap-2">
                <button
                  onClick={() => login('<EMAIL>', 'password123')}
                  disabled={isLoading}
                  className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  管理員登錄
                </button>

                <button
                  onClick={() => login('<EMAIL>', 'password')}
                  disabled={isLoading}
                  className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  舊管理員登錄
                </button>

                <button
                  onClick={() => login('<EMAIL>', 'password')}
                  disabled={isLoading}
                  className="w-full px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                >
                  用戶登錄
                </button>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="font-medium text-gray-900 mb-2">測試結果</h3>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 h-48 overflow-y-auto font-mono text-sm">
                {result ? (
                  <pre className="whitespace-pre-wrap">{result}</pre>
                ) : (
                  <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <button
                onClick={() => router.push('/dashboard-simple')}
                className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
              >
                🏠 前往儀表板
              </button>

              <button
                onClick={() => setResult('')}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🗑️ 清除結果
              </button>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">📋 測試帳號</h3>
              <div className="text-sm text-blue-800 space-y-1">
                <div>管理員: <EMAIL> / password123</div>
                <div>舊管理員: <EMAIL> / password</div>
                <div>一般用戶: <EMAIL> / password</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
