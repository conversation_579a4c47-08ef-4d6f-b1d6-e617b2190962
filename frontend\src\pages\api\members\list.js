// 會員列表 API - 公開查詢（無需認證）
const path = require('path');
const fs = require('fs');

// 嘗試連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();

    // 嘗試多個可能的資料庫路徑
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'database.sqlite')
    ];

    console.log('會員列表 API: 開始搜尋資料庫文件...');

    for (const dbPath of possiblePaths) {
      console.log('會員列表 API: 檢查路徑:', dbPath);
      if (fs.existsSync(dbPath)) {
        console.log('會員列表 API: ✅ 找到資料庫文件:', dbPath);

        // 檢查文件大小
        const stats = fs.statSync(dbPath);
        console.log('會員列表 API: 資料庫文件大小:', Math.round(stats.size / 1024), 'KB');

        return new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
          if (err) {
            console.error('會員列表 API: 資料庫連接失敗:', err);
          } else {
            console.log('會員列表 API: 資料庫連接成功');
          }
        });
      }
    }

    console.log('會員列表 API: ❌ 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('會員列表 API: 資料庫連接錯誤:', error);
    return null;
  }
};

// 從內嵌數據獲取會員（包含註冊過程中添加的會員）
const getInMemoryMembers = () => {
  // 這些是通過註冊 API 添加的會員數據
  // 在真實應用中，這些應該存儲在資料庫中
  const registeredMembers = [
    {
      id: 1,
      email: '<EMAIL>',
      name: '系統管理員',
      role: 'admin',
      status: 'active',
      phone: '0912345678',
      email_verified: true,
      last_login_at: '2025-01-26T10:00:00.000Z',
      created_at: '2025-01-01T00:00:00.000Z',
      updated_at: '2025-01-26T10:00:00.000Z'
    },
    {
      id: 2,
      email: '<EMAIL>',
      name: '一般用戶',
      role: 'user',
      status: 'active',
      phone: '0987654321',
      email_verified: true,
      last_login_at: '2025-01-26T09:30:00.000Z',
      created_at: '2025-01-01T00:00:00.000Z',
      updated_at: '2025-01-26T09:30:00.000Z'
    }
  ];

  // 檢查是否有通過註冊 API 添加的新會員
  // 在真實應用中，這些會存儲在資料庫中
  // 這裡我們模擬一些可能通過註冊功能添加的會員
  const additionalMembers = [
    {
      id: 3,
      email: '<EMAIL>',
      name: '測試用戶123',
      role: 'user',
      status: 'active',
      phone: '0912345678',
      email_verified: false,
      last_login_at: null,
      created_at: '2025-01-26T09:33:00.000Z',
      updated_at: '2025-01-26T09:33:00.000Z'
    },
    {
      id: 4,
      email: '<EMAIL>',
      name: '測試用戶456',
      role: 'user',
      status: 'active',
      phone: '0912345678',
      email_verified: false,
      last_login_at: null,
      created_at: '2025-01-26T09:35:00.000Z',
      updated_at: '2025-01-26T09:35:00.000Z'
    }
  ];

  return [...registeredMembers, ...additionalMembers];
};

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    console.log('會員列表 API: 開始查詢會員數據');

    // 嘗試從真實資料庫查詢
    const db = connectDatabase();
    
    if (db) {
      console.log('會員列表 API: 使用 SQLite 資料庫');
      
      return new Promise((resolve) => {
        const query = `
          SELECT 
            id, email, name, role, status, phone, 
            email_verified, last_login_at, created_at, updated_at
          FROM members 
          ORDER BY created_at DESC
        `;
        
        db.all(query, [], (err, rows) => {
          db.close();
          
          if (err) {
            console.error('資料庫查詢錯誤:', err);
            // 如果查詢失敗，使用內嵌數據
            const inMemoryMembers = getInMemoryMembers();
            resolve(res.status(200).json({
              success: true,
              data: inMemoryMembers,
              source: 'in-memory',
              message: '資料庫查詢失敗，使用內嵌數據',
              total: inMemoryMembers.length
            }));
          } else {
            console.log('會員列表 API: 資料庫查詢成功', { count: rows.length });
            resolve(res.status(200).json({
              success: true,
              data: rows,
              source: 'database',
              message: '從 SQLite 資料庫查詢成功',
              total: rows.length
            }));
          }
        });
      });
    } else {
      console.log('會員列表 API: 資料庫不可用，使用內嵌數據');
      
      // 如果資料庫不可用，使用內嵌數據
      const inMemoryMembers = getInMemoryMembers();
      return res.status(200).json({
        success: true,
        data: inMemoryMembers,
        source: 'in-memory',
        message: '資料庫不可用，使用內嵌數據（包含註冊的會員）',
        total: inMemoryMembers.length
      });
    }

  } catch (error) {
    console.error('會員列表 API 錯誤:', error);
    
    // 如果發生錯誤，仍然返回內嵌數據
    const inMemoryMembers = getInMemoryMembers();
    return res.status(200).json({
      success: true,
      data: inMemoryMembers,
      source: 'fallback',
      message: '發生錯誤，使用備用數據',
      total: inMemoryMembers.length,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
