const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// 導入路由
const apiRoutes = require('./routes');

// 創建 Express 應用
const app = express();

// 基本中間件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3001',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 分鐘
  max: 100, // 限制每個 IP 每 15 分鐘最多 100 個請求
  message: {
    error: '請求過於頻繁，請稍後再試'
  }
});
app.use('/api/', limiter);

// 健康檢查端點
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: process.env.API_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: '會員管理系統 API',
    version: process.env.API_VERSION || '1.0.0',
    documentation: '/api/docs',
    health: '/health',
    endpoints: {
      home: '/api/v1/home',
      auth: '/api/v1/auth',
      users: '/api/v1/users',
      payments: '/api/v1/payments',
      subscriptions: '/api/v1/subscriptions',
      wallets: '/api/v1/wallets'
    }
  });
});

// API 路由
app.use('/api/v1', apiRoutes);

// Swagger 文檔 (開發環境)
if (process.env.NODE_ENV === 'development') {
  const swaggerUi = require('swagger-ui-express');
  const swaggerJsdoc = require('swagger-jsdoc');

  const options = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: '會員管理系統 API',
        version: '1.0.0',
        description: '功能完整的會員管理系統 RESTful API',
        contact: {
          name: 'API 支援',
          email: '<EMAIL>'
        }
      },
      servers: [
        {
          url: `http://localhost:${process.env.PORT || 3000}`,
          description: '開發服務器'
        }
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        }
      }
    },
    apis: ['./src/routes/*.js']
  };

  const specs = swaggerJsdoc(options);
  app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }'
  }));
}

// 404 處理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '端點不存在',
    error: `路徑 ${req.originalUrl} 未找到`,
    availableEndpoints: [
      '/',
      '/health',
      '/api/v1/home',
      '/api/v1/auth',
      '/api/v1/users',
      '/api/v1/payments',
      '/api/v1/subscriptions',
      '/api/v1/wallets'
    ]
  });
});

// 全局錯誤處理
app.use((error, req, res, next) => {
  console.error('全局錯誤:', error);

  // 處理不同類型的錯誤
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: '資料驗證失敗',
      errors: error.details || error.message
    });
  }

  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      message: '未授權訪問',
      error: 'Token 無效或已過期'
    });
  }

  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      success: false,
      message: '文件大小超出限制',
      error: '文件大小不能超過 10MB'
    });
  }

  // 預設錯誤處理
  res.status(error.status || 500).json({
    success: false,
    message: error.message || '內部服務器錯誤',
    error: process.env.NODE_ENV === 'development' ? error.stack : undefined
  });
});

// 優雅關閉處理
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信號，正在優雅關閉...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信號，正在優雅關閉...');
  process.exit(0);
});

// 未捕獲的異常處理
process.on('uncaughtException', (error) => {
  console.error('未捕獲的異常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未處理的 Promise 拒絕:', reason);
  process.exit(1);
});

// 啟動服務器
const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, () => {
  console.log(`🚀 服務器運行在端口 ${PORT}`);
  console.log(`📖 API 文檔: http://localhost:${PORT}/api/docs`);
  console.log(`🏥 健康檢查: http://localhost:${PORT}/health`);
  console.log(`🌍 環境: ${process.env.NODE_ENV || 'development'}`);
});

// 設置服務器超時
server.timeout = 30000; // 30 秒

module.exports = app;
