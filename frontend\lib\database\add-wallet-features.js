const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 連接資料庫
const dbPath = path.join(__dirname, 'database.sqlite');

if (!fs.existsSync(dbPath)) {
  console.error('資料庫文件不存在:', dbPath);
  process.exit(1);
}

const db = new sqlite3.Database(dbPath);

console.log('開始添加錢包功能...');

// 添加錢包功能的遷移
async function addWalletFeatures() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // 1. 為 members 表添加錢包餘額欄位
      console.log('1. 為 members 表添加錢包餘額欄位...');
      db.run(`
        ALTER TABLE members 
        ADD COLUMN wallet_balance DECIMAL(10,2) DEFAULT 0.00
      `, (err) => {
        if (err && !err.message.includes('duplicate column name')) {
          console.error('添加 wallet_balance 欄位失敗:', err);
        } else {
          console.log('✅ wallet_balance 欄位添加成功');
        }
      });

      // 2. 創建錢包交易記錄表
      console.log('2. 創建錢包交易記錄表...');
      db.run(`
        CREATE TABLE IF NOT EXISTS wallet_transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          member_id INTEGER NOT NULL,
          transaction_type TEXT NOT NULL CHECK (transaction_type IN ('recharge', 'withdraw', 'payment', 'refund', 'bonus')),
          amount DECIMAL(10,2) NOT NULL,
          balance_before DECIMAL(10,2) NOT NULL,
          balance_after DECIMAL(10,2) NOT NULL,
          description TEXT,
          reference_id TEXT,
          reference_type TEXT,
          status TEXT DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
          payment_method TEXT,
          payment_reference TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (member_id) REFERENCES members (id) ON DELETE CASCADE
        )
      `, (err) => {
        if (err) {
          console.error('創建 wallet_transactions 表失敗:', err);
        } else {
          console.log('✅ wallet_transactions 表創建成功');
        }
      });

      // 3. 創建充值方式配置表
      console.log('3. 創建充值方式配置表...');
      db.run(`
        CREATE TABLE IF NOT EXISTS payment_methods (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('credit_card', 'bank_transfer', 'digital_wallet', 'crypto', 'other')),
          description TEXT,
          icon_url TEXT,
          min_amount DECIMAL(10,2) DEFAULT 1.00,
          max_amount DECIMAL(10,2) DEFAULT 10000.00,
          fee_type TEXT DEFAULT 'none' CHECK (fee_type IN ('none', 'fixed', 'percentage')),
          fee_amount DECIMAL(10,2) DEFAULT 0.00,
          is_active BOOLEAN DEFAULT 1,
          sort_order INTEGER DEFAULT 0,
          config_json TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('創建 payment_methods 表失敗:', err);
        } else {
          console.log('✅ payment_methods 表創建成功');
        }
      });

      // 4. 插入預設的充值方式
      console.log('4. 插入預設的充值方式...');
      const paymentMethods = [
        {
          name: '信用卡',
          type: 'credit_card',
          description: '使用信用卡快速充值',
          icon_url: '💳',
          min_amount: 10.00,
          max_amount: 5000.00,
          fee_type: 'percentage',
          fee_amount: 2.5,
          sort_order: 1
        },
        {
          name: '銀行轉帳',
          type: 'bank_transfer',
          description: '銀行轉帳，手續費較低',
          icon_url: '🏦',
          min_amount: 50.00,
          max_amount: 10000.00,
          fee_type: 'fixed',
          fee_amount: 15.00,
          sort_order: 2
        },
        {
          name: '數位錢包',
          type: 'digital_wallet',
          description: '使用數位錢包支付',
          icon_url: '📱',
          min_amount: 1.00,
          max_amount: 3000.00,
          fee_type: 'none',
          fee_amount: 0.00,
          sort_order: 3
        },
        {
          name: '加密貨幣',
          type: 'crypto',
          description: '使用加密貨幣充值',
          icon_url: '₿',
          min_amount: 20.00,
          max_amount: 50000.00,
          fee_type: 'percentage',
          fee_amount: 1.0,
          sort_order: 4
        }
      ];

      const insertPaymentMethod = (method, callback) => {
        db.run(`
          INSERT OR IGNORE INTO payment_methods 
          (name, type, description, icon_url, min_amount, max_amount, fee_type, fee_amount, sort_order)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          method.name, method.type, method.description, method.icon_url,
          method.min_amount, method.max_amount, method.fee_type, method.fee_amount, method.sort_order
        ], callback);
      };

      let insertedCount = 0;
      paymentMethods.forEach((method, index) => {
        insertPaymentMethod(method, (err) => {
          if (err) {
            console.error(`插入充值方式 ${method.name} 失敗:`, err);
          } else {
            console.log(`✅ 充值方式 ${method.name} 添加成功`);
          }
          
          insertedCount++;
          if (insertedCount === paymentMethods.length) {
            // 5. 為現有用戶初始化錢包餘額
            console.log('5. 為現有用戶初始化錢包餘額...');
            db.run(`
              UPDATE members 
              SET wallet_balance = 0.00 
              WHERE wallet_balance IS NULL
            `, (err) => {
              if (err) {
                console.error('初始化錢包餘額失敗:', err);
              } else {
                console.log('✅ 現有用戶錢包餘額初始化完成');
              }

              // 6. 創建索引以提高查詢性能
              console.log('6. 創建索引...');
              db.run(`CREATE INDEX IF NOT EXISTS idx_wallet_transactions_member_id ON wallet_transactions(member_id)`, (err) => {
                if (err) {
                  console.error('創建 member_id 索引失敗:', err);
                } else {
                  console.log('✅ wallet_transactions member_id 索引創建成功');
                }
              });

              db.run(`CREATE INDEX IF NOT EXISTS idx_wallet_transactions_created_at ON wallet_transactions(created_at)`, (err) => {
                if (err) {
                  console.error('創建 created_at 索引失敗:', err);
                } else {
                  console.log('✅ wallet_transactions created_at 索引創建成功');
                }
              });

              db.run(`CREATE INDEX IF NOT EXISTS idx_wallet_transactions_type ON wallet_transactions(transaction_type)`, (err) => {
                if (err) {
                  console.error('創建 transaction_type 索引失敗:', err);
                } else {
                  console.log('✅ wallet_transactions transaction_type 索引創建成功');
                }

                // 完成
                console.log('\n🎉 錢包功能添加完成！');
                console.log('\n新增的功能:');
                console.log('- members.wallet_balance: 會員錢包餘額');
                console.log('- wallet_transactions: 錢包交易記錄表');
                console.log('- payment_methods: 充值方式配置表');
                console.log('- 相關索引: 提高查詢性能');
                
                db.close();
                resolve();
              });
            });
          }
        });
      });
    });
  });
}

// 執行遷移
addWalletFeatures().catch(console.error);
