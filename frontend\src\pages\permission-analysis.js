import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function PermissionAnalysis() {
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState(null);
  const [permissionTests, setPermissionTests] = useState([]);
  const [isTesting, setIsTesting] = useState(false);

  useEffect(() => {
    checkCurrentUser();
  }, []);

  const checkCurrentUser = () => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const userData = JSON.parse(userStr);
        setCurrentUser(userData);
      } catch (error) {
        console.error('解析用戶數據失敗:', error);
      }
    }
  };

  const addTestResult = (api, status, message, details = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setPermissionTests(prev => [...prev, {
      timestamp,
      api,
      status, // 'success', 'error', 'forbidden', 'info'
      message,
      details
    }]);
  };

  const testAllPermissions = async () => {
    setIsTesting(true);
    setPermissionTests([]);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        addTestResult('認證', 'error', '未找到認證 token');
        return;
      }

      addTestResult('開始', 'info', `開始測試權限 - 當前角色: ${currentUser?.role || '未知'}`);

      // 測試 1: 錢包餘額 API (所有用戶都應該可以訪問)
      addTestResult('錢包API', 'info', '測試錢包餘額 API...');
      try {
        const walletResponse = await fetch('/api/wallet/balance', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (walletResponse.ok) {
          const walletResult = await walletResponse.json();
          if (walletResult.success) {
            addTestResult('錢包API', 'success', `✅ 可以訪問 - 餘額: $${walletResult.data.balance}`);
          } else {
            addTestResult('錢包API', 'error', `❌ 業務失敗: ${walletResult.error}`);
          }
        } else {
          addTestResult('錢包API', 'error', `❌ HTTP 失敗: ${walletResponse.status}`);
        }
      } catch (error) {
        addTestResult('錢包API', 'error', `❌ 請求失敗: ${error.message}`);
      }

      // 測試 2: 管理員資料庫查詢 API (只有管理員可以訪問)
      addTestResult('管理員API', 'info', '測試管理員資料庫查詢 API...');
      try {
        const adminResponse = await fetch('/api/admin/database-query', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            query: "SELECT COUNT(*) as count FROM members"
          })
        });

        if (adminResponse.ok) {
          const adminResult = await adminResponse.json();
          if (adminResult.success) {
            addTestResult('管理員API', 'success', `✅ 可以訪問 - 找到 ${adminResult.data[0].count} 個會員`);
          } else {
            addTestResult('管理員API', 'error', `❌ 業務失敗: ${adminResult.error}`);
          }
        } else if (adminResponse.status === 403) {
          addTestResult('管理員API', 'forbidden', '🚫 權限不足 - 需要管理員權限');
        } else {
          addTestResult('管理員API', 'error', `❌ HTTP 失敗: ${adminResponse.status}`);
        }
      } catch (error) {
        addTestResult('管理員API', 'error', `❌ 請求失敗: ${error.message}`);
      }

      // 測試 3: 錢包交易記錄 API
      addTestResult('交易API', 'info', '測試錢包交易記錄 API...');
      try {
        const transactionsResponse = await fetch('/api/wallet/transactions?limit=5', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (transactionsResponse.ok) {
          const transactionsResult = await transactionsResponse.json();
          if (transactionsResult.success) {
            addTestResult('交易API', 'success', `✅ 可以訪問 - 找到 ${transactionsResult.data.length} 筆交易記錄`);
          } else {
            addTestResult('交易API', 'error', `❌ 業務失敗: ${transactionsResult.error}`);
          }
        } else {
          addTestResult('交易API', 'error', `❌ HTTP 失敗: ${transactionsResponse.status}`);
        }
      } catch (error) {
        addTestResult('交易API', 'error', `❌ 請求失敗: ${error.message}`);
      }

      // 測試 4: 服務管理 API (如果存在)
      addTestResult('服務API', 'info', '測試服務管理 API...');
      try {
        const servicesResponse = await fetch('/api/admin/services', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (servicesResponse.ok) {
          const servicesResult = await servicesResponse.json();
          addTestResult('服務API', 'success', '✅ 可以訪問服務管理 API');
        } else if (servicesResponse.status === 403) {
          addTestResult('服務API', 'forbidden', '🚫 權限不足 - 需要管理員權限');
        } else if (servicesResponse.status === 404) {
          addTestResult('服務API', 'info', 'ℹ️ API 不存在');
        } else {
          addTestResult('服務API', 'error', `❌ HTTP 失敗: ${servicesResponse.status}`);
        }
      } catch (error) {
        addTestResult('服務API', 'error', `❌ 請求失敗: ${error.message}`);
      }

      addTestResult('完成', 'success', '權限測試完成');

    } catch (error) {
      addTestResult('錯誤', 'error', `測試過程中出現錯誤: ${error.message}`);
    } finally {
      setIsTesting(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'forbidden': return 'text-orange-600';
      case 'info': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'forbidden': return '🚫';
      case 'info': return 'ℹ️';
      default: return '📝';
    }
  };

  const getBgColor = (status) => {
    switch (status) {
      case 'success': return 'bg-green-50 border-green-200';
      case 'error': return 'bg-red-50 border-red-200';
      case 'forbidden': return 'bg-orange-50 border-orange-200';
      case 'info': return 'bg-blue-50 border-blue-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <>
      <Head>
        <title>權限分析 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔐 權限分析</h1>
                <p className="text-gray-600 mt-2">分析當前用戶的 API 訪問權限</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={testAllPermissions}
                  disabled={isTesting}
                  className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 font-medium"
                >
                  {isTesting ? '測試中...' : '🔐 測試權限'}
                </button>
              </div>
            </div>

            {/* 當前用戶信息 */}
            {currentUser && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">👤 當前用戶</h2>
                <div className={`rounded-lg p-4 border ${
                  currentUser.role === 'admin' ? 'bg-red-50 border-red-200' : 'bg-blue-50 border-blue-200'
                }`}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <div className="text-sm font-medium text-gray-700">姓名</div>
                      <div>{currentUser.name}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-700">Email</div>
                      <div>{currentUser.email}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-700">角色</div>
                      <div className={`font-semibold ${
                        currentUser.role === 'admin' ? 'text-red-600' : 'text-blue-600'
                      }`}>
                        {currentUser.role === 'admin' ? '👨‍💼 管理員' : '👤 一般會員'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 權限測試結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 權限測試結果</h2>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {permissionTests.length > 0 ? (
                  permissionTests.map((test, index) => (
                    <div key={index} className={`rounded-lg p-4 border ${getBgColor(test.status)}`}>
                      <div className="flex items-start space-x-3">
                        <span className={getStatusColor(test.status)}>
                          {getStatusIcon(test.status)}
                        </span>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 text-sm">
                            <span className="text-gray-500">[{test.timestamp}]</span>
                            <span className="font-medium text-gray-700">{test.api}:</span>
                            <span className={getStatusColor(test.status)}>{test.message}</span>
                          </div>
                          {test.details && (
                            <div className="mt-2 text-sm text-gray-600">
                              {test.details}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「測試權限」來分析當前用戶的 API 訪問權限
                  </div>
                )}
              </div>
            </div>

            {/* 權限說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 權限說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>錢包 API:</strong> 所有登入用戶都可以訪問自己的錢包功能</div>
                <div><strong>管理員 API:</strong> 只有角色為 'admin' 的用戶可以訪問</div>
                <div><strong>交易 API:</strong> 所有登入用戶都可以查看自己的交易記錄</div>
                <div><strong>服務 API:</strong> 管理員專用的服務管理功能</div>
                <div><strong>權限檢查:</strong> 基於 JWT Token 中的 role 欄位進行驗證</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
