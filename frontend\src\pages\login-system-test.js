import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function LoginSystemTest() {
  const router = useRouter();
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentAuth, setCurrentAuth] = useState(null);

  useEffect(() => {
    checkCurrentAuth();
  }, []);

  const addResult = (test, status, message, details = null) => {
    const result = {
      id: Date.now(),
      test,
      status, // 'success', 'error', 'warning', 'info'
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [...prev, result]);
    console.log(`[${result.timestamp}] ${test}: ${status} - ${message}`);
  };

  const checkCurrentAuth = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let user = null;
    if (userStr) {
      try {
        user = JSON.parse(userStr);
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setCurrentAuth({
      hasToken: !!token,
      hasUser: !!user,
      tokenLength: token ? token.length : 0,
      user: user
    });
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    addResult('系統測試', 'info', '開始會員登錄系統測試');

    // 測試 1: 檢查登錄頁面
    await testLoginPage();
    
    // 測試 2: 檢查登錄 API
    await testLoginAPI();
    
    // 測試 3: 測試錯誤處理
    await testErrorHandling();
    
    // 測試 4: 測試認證狀態
    await testAuthState();
    
    // 測試 5: 測試登出功能
    await testLogout();

    addResult('系統測試', 'success', '所有測試完成');
    setIsRunning(false);
  };

  const testLoginPage = async () => {
    try {
      addResult('登錄頁面', 'info', '檢查登錄頁面可訪問性...');
      
      const response = await fetch('/auth/login');
      if (response.ok) {
        addResult('登錄頁面', 'success', '登錄頁面可正常訪問');
      } else {
        addResult('登錄頁面', 'error', `登錄頁面訪問失敗: ${response.status}`);
      }
    } catch (error) {
      addResult('登錄頁面', 'error', `登錄頁面測試失敗: ${error.message}`);
    }
  };

  const testLoginAPI = async () => {
    try {
      addResult('登錄 API', 'info', '測試正確的登錄憑證...');
      
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const result = await response.json();
      
      if (response.ok && result.success) {
        addResult('登錄 API', 'success', '登錄 API 正常工作', {
          token: result.token ? '已獲取' : '未獲取',
          user: result.user ? result.user.name : '未獲取',
          tokenLength: result.token ? result.token.length : 0
        });
        
        // 保存認證數據用於後續測試
        if (result.token && result.user) {
          localStorage.setItem('token', result.token);
          localStorage.setItem('user', JSON.stringify(result.user));
          checkCurrentAuth();
        }
      } else {
        addResult('登錄 API', 'error', `登錄失敗: ${result.error || '未知錯誤'}`, result);
      }
    } catch (error) {
      addResult('登錄 API', 'error', `登錄 API 測試失敗: ${error.message}`);
    }
  };

  const testErrorHandling = async () => {
    try {
      addResult('錯誤處理', 'info', '測試錯誤的登錄憑證...');
      
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
      });

      const result = await response.json();
      
      if (!response.ok || !result.success) {
        addResult('錯誤處理', 'success', '錯誤處理正常工作', {
          error: result.error,
          status: response.status
        });
      } else {
        addResult('錯誤處理', 'warning', '錯誤處理可能有問題 - 錯誤憑證被接受了');
      }
    } catch (error) {
      addResult('錯誤處理', 'error', `錯誤處理測試失敗: ${error.message}`);
    }
  };

  const testAuthState = async () => {
    try {
      addResult('認證狀態', 'info', '檢查認證狀態管理...');
      
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (token && userStr) {
        try {
          const user = JSON.parse(userStr);
          addResult('認證狀態', 'success', '認證數據完整', {
            tokenLength: token.length,
            userName: user.name,
            userEmail: user.email,
            userRole: user.role
          });
        } catch (parseError) {
          addResult('認證狀態', 'error', '用戶數據格式錯誤', {
            parseError: parseError.message,
            userStr: userStr.substring(0, 100)
          });
        }
      } else {
        addResult('認證狀態', 'warning', '認證數據不完整', {
          hasToken: !!token,
          hasUser: !!userStr
        });
      }
    } catch (error) {
      addResult('認證狀態', 'error', `認證狀態測試失敗: ${error.message}`);
    }
  };

  const testLogout = async () => {
    try {
      addResult('登出功能', 'info', '測試登出功能...');
      
      // 模擬登出
      const beforeToken = localStorage.getItem('token');
      const beforeUser = localStorage.getItem('user');
      
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      const afterToken = localStorage.getItem('token');
      const afterUser = localStorage.getItem('user');
      
      if (!afterToken && !afterUser) {
        addResult('登出功能', 'success', '登出功能正常工作', {
          before: { hasToken: !!beforeToken, hasUser: !!beforeUser },
          after: { hasToken: !!afterToken, hasUser: !!afterUser }
        });
      } else {
        addResult('登出功能', 'error', '登出功能可能有問題');
      }
      
      checkCurrentAuth();
    } catch (error) {
      addResult('登出功能', 'error', `登出功能測試失敗: ${error.message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const quickLogin = async () => {
    try {
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        checkCurrentAuth();
        alert('登錄成功！');
      } else {
        alert('登錄失敗: ' + result.error);
      }
    } catch (error) {
      alert('登錄請求失敗: ' + error.message);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50 border-green-200';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'info': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '📝';
    }
  };

  return (
    <>
      <Head>
        <title>登錄系統測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔐 登錄系統測試</h1>
              <p className="text-gray-600 mt-2">全面檢查會員登錄系統的各項功能</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* 控制面板 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試控制</h2>
                
                <div className="space-y-4">
                  <button
                    onClick={runAllTests}
                    disabled={isRunning}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
                  >
                    {isRunning ? '測試中...' : '🧪 開始全面測試'}
                  </button>

                  <button
                    onClick={quickLogin}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    🔑 快速登錄
                  </button>

                  <button
                    onClick={clearResults}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    🗑️ 清除結果
                  </button>

                  <button
                    onClick={() => router.push('/dashboard-simple')}
                    className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                  >
                    🏠 前往儀表板
                  </button>
                </div>

                {/* 當前認證狀態 */}
                {currentAuth && (
                  <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">🔍 當前認證狀態</h3>
                    <div className="text-sm text-gray-700 space-y-1">
                      <div>Token: {currentAuth.hasToken ? '✅ 存在' : '❌ 缺失'} ({currentAuth.tokenLength} 字符)</div>
                      <div>User: {currentAuth.hasUser ? '✅ 存在' : '❌ 缺失'}</div>
                      {currentAuth.user && (
                        <div className="mt-2 p-2 bg-white rounded border">
                          <div className="text-xs">
                            <div>姓名: {currentAuth.user.name}</div>
                            <div>Email: {currentAuth.user.email}</div>
                            <div>角色: {currentAuth.user.role}</div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* 測試結果 */}
              <div className="lg:col-span-2">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試結果</h2>
                
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {testResults.map((result) => (
                    <div key={result.id} className={`p-4 border rounded-lg ${getStatusColor(result.status)}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          <span>{getStatusIcon(result.status)}</span>
                          <div>
                            <div className="font-medium">{result.test}</div>
                            <div className="text-sm mt-1">{result.message}</div>
                            {result.details && (
                              <div className="mt-2 p-2 bg-white bg-opacity-50 rounded text-xs">
                                <pre className="whitespace-pre-wrap">
                                  {typeof result.details === 'object' 
                                    ? JSON.stringify(result.details, null, 2)
                                    : result.details
                                  }
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-xs opacity-75">
                          {result.timestamp}
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {testResults.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      點擊「開始全面測試」來檢查登錄系統
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 測試說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📋 測試項目</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>1. 登錄頁面測試</strong> - 檢查登錄頁面是否可以正常訪問</div>
                <div><strong>2. 登錄 API 測試</strong> - 測試正確憑證的登錄功能</div>
                <div><strong>3. 錯誤處理測試</strong> - 測試錯誤憑證的處理</div>
                <div><strong>4. 認證狀態測試</strong> - 檢查認證數據的存儲和格式</div>
                <div><strong>5. 登出功能測試</strong> - 測試登出時的數據清理</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
