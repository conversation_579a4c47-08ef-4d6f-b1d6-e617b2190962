# 資料庫設計文檔

## 概述

本文檔詳細描述會員系統的資料庫設計，包含所有核心資料表的結構、關係和索引設計。

## 資料庫選型

- **主資料庫**: PostgreSQL 15
- **快取**: Redis 7
- **搜尋引擎**: Elasticsearch (可選)

## 核心資料表設計

### 1. 用戶相關表

#### users (用戶基本資料表)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'deleted')),
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### user_profiles (用戶詳細資料表)
```sql
CREATE TABLE user_profiles (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    display_name VARCHAR(100),
    avatar_url VARCHAR(500),
    birth_date DATE,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
    country VARCHAR(2), -- ISO 3166-1 alpha-2
    timezone VARCHAR(50),
    language VARCHAR(10) DEFAULT 'zh-TW',
    currency VARCHAR(3) DEFAULT 'TWD', -- ISO 4217
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_user_profiles_country ON user_profiles(country);
CREATE INDEX idx_user_profiles_language ON user_profiles(language);
```

#### user_auth (認證資訊表)
```sql
CREATE TABLE user_auth (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL, -- 'local', 'google', 'facebook', 'line', 'apple'
    provider_id VARCHAR(255),
    access_token TEXT,
    refresh_token TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(provider, provider_id)
);

-- 索引
CREATE INDEX idx_user_auth_user_id ON user_auth(user_id);
CREATE INDEX idx_user_auth_provider ON user_auth(provider);
```

#### user_sessions (用戶會話表)
```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token_hash ON user_sessions(token_hash);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
```

### 2. 錢包相關表

#### wallets (錢包表)
```sql
CREATE TABLE wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    currency VARCHAR(3) NOT NULL DEFAULT 'TWD',
    balance DECIMAL(15,2) DEFAULT 0.00 CHECK (balance >= 0),
    frozen_balance DECIMAL(15,2) DEFAULT 0.00 CHECK (frozen_balance >= 0),
    total_deposited DECIMAL(15,2) DEFAULT 0.00,
    total_withdrawn DECIMAL(15,2) DEFAULT 0.00,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'frozen', 'closed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, currency)
);

-- 索引
CREATE INDEX idx_wallets_user_id ON wallets(user_id);
CREATE INDEX idx_wallets_currency ON wallets(currency);
CREATE INDEX idx_wallets_status ON wallets(status);
```

#### transactions (交易記錄表)
```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id UUID NOT NULL REFERENCES wallets(id),
    type VARCHAR(20) NOT NULL CHECK (type IN ('deposit', 'withdraw', 'payment', 'refund', 'bonus', 'penalty')),
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    description TEXT,
    reference_id UUID, -- 關聯到其他表的ID (如payment_id, subscription_id)
    reference_type VARCHAR(50), -- 'payment', 'subscription', 'bonus', etc.
    metadata JSONB,
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE
);

-- 索引
CREATE INDEX idx_transactions_wallet_id ON transactions(wallet_id);
CREATE INDEX idx_transactions_type ON transactions(type);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_reference ON transactions(reference_type, reference_id);
```

### 3. 支付相關表

#### payments (支付記錄表)
```sql
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    wallet_id UUID REFERENCES wallets(id),
    payment_method VARCHAR(50) NOT NULL, -- 'credit_card', 'paypal', 'line_pay', 'bank_transfer', etc.
    provider VARCHAR(50) NOT NULL, -- 'stripe', 'paypal', 'ecpay', etc.
    provider_payment_id VARCHAR(255), -- 第三方支付ID
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    exchange_rate DECIMAL(10,6), -- 匯率 (如果涉及幣種轉換)
    original_amount DECIMAL(15,2), -- 原始金額
    original_currency VARCHAR(3), -- 原始幣種
    fee DECIMAL(15,2) DEFAULT 0.00, -- 手續費
    net_amount DECIMAL(15,2) NOT NULL, -- 實際到帳金額
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
    failure_reason TEXT,
    metadata JSONB, -- 存儲第三方支付的額外資訊
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- 索引
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_wallet_id ON payments(wallet_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_provider ON payments(provider);
CREATE INDEX idx_payments_created_at ON payments(created_at);
CREATE INDEX idx_payments_provider_payment_id ON payments(provider_payment_id);
```

#### payment_methods (用戶支付方式表)
```sql
CREATE TABLE payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'credit_card', 'bank_account', 'digital_wallet'
    provider VARCHAR(50) NOT NULL, -- 'stripe', 'paypal', etc.
    provider_method_id VARCHAR(255), -- 第三方支付方式ID
    is_default BOOLEAN DEFAULT FALSE,
    nickname VARCHAR(100), -- 用戶自定義名稱
    last_four VARCHAR(4), -- 卡號或帳號後四碼
    brand VARCHAR(50), -- 卡片品牌 (Visa, MasterCard, etc.)
    expires_at DATE, -- 到期日 (信用卡)
    metadata JSONB, -- 其他相關資訊
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_payment_methods_user_id ON payment_methods(user_id);
CREATE INDEX idx_payment_methods_type ON payment_methods(type);
CREATE INDEX idx_payment_methods_status ON payment_methods(status);
CREATE INDEX idx_payment_methods_default ON payment_methods(user_id, is_default) WHERE is_default = TRUE;
```

### 4. 服務與訂閱相關表

#### services (服務目錄表)
```sql
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL, -- URL友好的標識符
    description TEXT,
    short_description VARCHAR(500),
    icon_url VARCHAR(500),
    category VARCHAR(100),
    tags TEXT[], -- PostgreSQL 陣列類型
    features JSONB, -- 功能列表
    pricing_model VARCHAR(50) DEFAULT 'subscription' CHECK (pricing_model IN ('subscription', 'one_time', 'usage_based')),
    trial_days INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_services_slug ON services(slug);
CREATE INDEX idx_services_category ON services(category);
CREATE INDEX idx_services_active ON services(is_active);
CREATE INDEX idx_services_featured ON services(is_featured);
CREATE INDEX idx_services_sort_order ON services(sort_order);
```

#### service_plans (服務方案表)
```sql
CREATE TABLE service_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_id UUID NOT NULL REFERENCES services(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'TWD',
    billing_period VARCHAR(20) NOT NULL CHECK (billing_period IN ('monthly', 'quarterly', 'yearly', 'one_time')),
    billing_interval INTEGER DEFAULT 1, -- 計費間隔 (例如：每3個月)
    features JSONB, -- 方案特定功能
    limits JSONB, -- 使用限制
    is_popular BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(service_id, slug)
);

-- 索引
CREATE INDEX idx_service_plans_service_id ON service_plans(service_id);
CREATE INDEX idx_service_plans_active ON service_plans(is_active);
CREATE INDEX idx_service_plans_popular ON service_plans(is_popular);
```

#### subscriptions (訂閱記錄表)
```sql
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    service_id UUID NOT NULL REFERENCES services(id),
    plan_id UUID NOT NULL REFERENCES service_plans(id),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('trial', 'active', 'paused', 'cancelled', 'expired')),
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    auto_renew BOOLEAN DEFAULT TRUE,
    price DECIMAL(10,2) NOT NULL, -- 訂閱時的價格 (歷史記錄)
    currency VARCHAR(3) NOT NULL,
    billing_period VARCHAR(20) NOT NULL,
    next_billing_date TIMESTAMP WITH TIME ZONE,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_service_id ON subscriptions(service_id);
CREATE INDEX idx_subscriptions_plan_id ON subscriptions(plan_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_next_billing ON subscriptions(next_billing_date);
CREATE INDEX idx_subscriptions_period_end ON subscriptions(current_period_end);
```

#### subscription_usage (訂閱使用記錄表)
```sql
CREATE TABLE subscription_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id UUID NOT NULL REFERENCES subscriptions(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL, -- 使用指標名稱
    usage_count INTEGER DEFAULT 0,
    usage_limit INTEGER, -- 使用限制
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(subscription_id, metric_name, period_start)
);

-- 索引
CREATE INDEX idx_subscription_usage_subscription_id ON subscription_usage(subscription_id);
CREATE INDEX idx_subscription_usage_period ON subscription_usage(period_start, period_end);
```

### 5. 通知相關表

#### notifications (通知記錄表)
```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'email', 'sms', 'push', 'in_app'
    category VARCHAR(50) NOT NULL, -- 'payment', 'subscription', 'security', 'marketing'
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    data JSONB, -- 額外資料
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'read')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    failure_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_category ON notifications(category);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
```

#### notification_preferences (通知偏好設定表)
```sql
CREATE TABLE notification_preferences (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    email_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,
    marketing_emails BOOLEAN DEFAULT FALSE,
    payment_notifications BOOLEAN DEFAULT TRUE,
    subscription_notifications BOOLEAN DEFAULT TRUE,
    security_notifications BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 6. 審計與日誌表

#### audit_logs (審計日誌表)
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL, -- 操作類型
    resource_type VARCHAR(50) NOT NULL, -- 資源類型
    resource_id UUID, -- 資源ID
    old_values JSONB, -- 變更前的值
    new_values JSONB, -- 變更後的值
    ip_address INET,
    user_agent TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

#### system_logs (系統日誌表)
```sql
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level VARCHAR(20) NOT NULL CHECK (level IN ('debug', 'info', 'warn', 'error', 'fatal')),
    service VARCHAR(50) NOT NULL, -- 服務名稱
    message TEXT NOT NULL,
    context JSONB, -- 上下文資訊
    stack_trace TEXT, -- 錯誤堆疊
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_system_logs_level ON system_logs(level);
CREATE INDEX idx_system_logs_service ON system_logs(service);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
```

## 資料庫關係說明

### 主要關係

1. **用戶中心關係**
   - `users` 1:1 `user_profiles` (用戶基本資料與詳細資料)
   - `users` 1:N `user_auth` (用戶與多種認證方式)
   - `users` 1:N `user_sessions` (用戶與多個會話)

2. **錢包關係**
   - `users` 1:N `wallets` (用戶與多幣種錢包)
   - `wallets` 1:N `transactions` (錢包與交易記錄)
   - `users` 1:N `payments` (用戶與支付記錄)
   - `users` 1:N `payment_methods` (用戶與支付方式)

3. **服務訂閱關係**
   - `services` 1:N `service_plans` (服務與方案)
   - `users` 1:N `subscriptions` (用戶與訂閱)
   - `subscriptions` 1:N `subscription_usage` (訂閱與使用記錄)

4. **通知關係**
   - `users` 1:N `notifications` (用戶與通知)
   - `users` 1:1 `notification_preferences` (用戶與通知偏好)

5. **審計關係**
   - `users` 1:N `audit_logs` (用戶與審計日誌)

## 資料完整性約束

### 外鍵約束
- 所有外鍵都設置了適當的 `ON DELETE` 行為
- 用戶刪除時，相關資料採用 `CASCADE` 刪除
- 重要業務資料採用 `RESTRICT` 防止意外刪除

### 檢查約束
- 餘額不能為負數
- 狀態欄位限制為預定義值
- 金額欄位精度控制

### 唯一約束
- 用戶名、郵件、手機號碼唯一性
- 服務 slug 唯一性
- 複合唯一約束防止重複記錄

## 索引策略

### 主要索引類型

1. **主鍵索引** - 所有表的 UUID 主鍵
2. **唯一索引** - 用戶名、郵件等唯一欄位
3. **外鍵索引** - 所有外鍵欄位
4. **查詢索引** - 常用查詢條件欄位
5. **複合索引** - 多欄位組合查詢
6. **部分索引** - 條件性索引 (如 WHERE is_default = TRUE)

### 索引維護
- 定期分析索引使用情況
- 移除未使用的索引
- 根據查詢模式調整索引

## 資料分割策略

### 時間分割
- `audit_logs` 按月分割
- `system_logs` 按週分割
- `transactions` 按年分割 (大量資料時)

### 範圍分割
- `notifications` 按用戶ID範圍分割 (大量用戶時)

## 備份與恢復策略

### 備份類型
1. **完整備份** - 每週執行
2. **增量備份** - 每日執行
3. **事務日誌備份** - 每15分鐘執行

### 恢復策略
- 點時間恢復 (PITR)
- 異地備份存儲
- 自動化恢復測試

## 效能優化建議

### 查詢優化
1. 使用適當的索引
2. 避免 N+1 查詢問題
3. 使用連接查詢替代子查詢
4. 分頁查詢使用 LIMIT/OFFSET

### 快取策略
1. **Redis 快取**
   - 用戶會話資訊
   - 服務目錄資料
   - 匯率資訊
   - 系統配置

2. **應用層快取**
   - 查詢結果快取
   - 計算結果快取

### 連接池配置
- 最大連接數：100
- 最小連接數：10
- 連接超時：30秒
- 空閒超時：10分鐘

## 安全考量

### 資料加密
1. **欄位級加密**
   - 密碼雜湊 (bcrypt)
   - 敏感個人資訊加密
   - 支付資訊加密

2. **傳輸加密**
   - TLS 1.3 連接
   - 憑證管理

### 存取控制
1. **資料庫用戶權限**
   - 應用程式專用用戶
   - 只讀用戶 (報表查詢)
   - 管理員用戶 (維護操作)

2. **行級安全性**
   - 用戶只能存取自己的資料
   - 管理員權限控制

### 審計追蹤
- 所有資料變更記錄
- 登入失敗記錄
- 敏感操作記錄

## 合規性要求

### GDPR 合規
- 資料可攜性支援
- 被遺忘權實現
- 資料處理記錄

### PCI DSS 合規
- 支付資料加密
- 存取日誌記錄
- 定期安全掃描

### 資料保留政策
- 交易記錄：7年
- 用戶資料：帳號刪除後90天
- 日誌資料：1年
- 備份資料：3年