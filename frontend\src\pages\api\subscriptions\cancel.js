// 取消訂閱 API - 將狀態改為 cancelled 而不是刪除資料
const jwt = require('jsonwebtoken');
const path = require('path');
const fs = require('fs');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// 簡化的認證中間件
const authenticateUser = (req) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return null;
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded;
  } catch (error) {
    console.error('Token 驗證失敗:', error);
    return null;
  }
};

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();
    
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];
    
    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('取消訂閱 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }
    
    console.log('取消訂閱 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('取消訂閱 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    // 驗證用戶
    const user = authenticateUser(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    console.log('取消訂閱 API: 用戶已認證', { userId: user.userId });

    const { subscription_id } = req.body;

    // 驗證必要參數
    if (!subscription_id) {
      return res.status(400).json({
        success: false,
        error: '請提供訂閱 ID'
      });
    }

    // 連接資料庫
    const db = connectDatabase();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    return new Promise((resolve) => {
      // 首先檢查訂閱是否存在且屬於當前用戶
      db.get(`
        SELECT ms.*, s.name as service_name 
        FROM member_services ms
        JOIN services s ON ms.service_id = s.id
        WHERE ms.id = ? AND ms.member_id = ?
      `, [subscription_id, user.userId], (err, subscription) => {
        
        if (err) {
          console.error('取消訂閱 API: 查詢訂閱失敗:', err);
          db.close();
          resolve(res.status(500).json({
            success: false,
            error: '查詢訂閱失敗'
          }));
          return;
        }

        if (!subscription) {
          console.log('取消訂閱 API: 訂閱不存在或不屬於當前用戶');
          db.close();
          resolve(res.status(404).json({
            success: false,
            error: '訂閱不存在或無權限操作'
          }));
          return;
        }

        // 檢查訂閱狀態
        if (subscription.status === 'cancelled') {
          console.log('取消訂閱 API: 訂閱已經被取消');
          db.close();
          resolve(res.status(400).json({
            success: false,
            error: '訂閱已經被取消'
          }));
          return;
        }

        console.log('取消訂閱 API: 找到訂閱', { 
          id: subscription.id, 
          service: subscription.service_name,
          currentStatus: subscription.status 
        });

        // 更新訂閱狀態為 cancelled（保留歷史記錄）
        db.run(`
          UPDATE member_services 
          SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP 
          WHERE id = ?
        `, [subscription_id], function(updateErr) {
          
          db.close();

          if (updateErr) {
            console.error('取消訂閱 API: 更新狀態失敗:', updateErr);
            resolve(res.status(500).json({
              success: false,
              error: '取消訂閱失敗'
            }));
            return;
          }

          console.log('取消訂閱 API: 訂閱已成功取消', {
            subscriptionId: subscription_id,
            serviceName: subscription.service_name,
            affectedRows: this.changes
          });

          resolve(res.status(200).json({
            success: true,
            message: `已成功取消「${subscription.service_name}」的訂閱`,
            data: {
              subscription_id: subscription_id,
              service_name: subscription.service_name,
              previous_status: subscription.status,
              new_status: 'cancelled',
              cancelled_at: new Date().toISOString()
            }
          }));
        });
      });
    });

  } catch (error) {
    console.error('取消訂閱 API 錯誤:', error);
    
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
