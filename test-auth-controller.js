// 測試 AuthController
const AuthController = require('./backend/src/controllers/AuthController-sqlite');

console.log('測試 AuthController...');

// 模擬請求和響應對象
const mockReq = {
  body: {
    email: '<EMAIL>',
    password: '1234'
  }
};

const mockRes = {
  status: function(code) {
    this.statusCode = code;
    return this;
  },
  json: function(data) {
    console.log('響應狀態碼:', this.statusCode);
    console.log('響應數據:', JSON.stringify(data, null, 2));
    return this;
  }
};

// 測試登入
AuthController.login(mockReq, mockRes)
  .then(() => {
    console.log('✅ AuthController 測試完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ AuthController 測試失敗:', error);
    process.exit(1);
  });
