import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function FixUserData() {
  const router = useRouter();
  const [fixResults, setFixResults] = useState([]);
  const [isFixing, setIsFixing] = useState(false);
  const [currentData, setCurrentData] = useState(null);

  useEffect(() => {
    checkCurrentData();
  }, []);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setFixResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const checkCurrentData = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let tokenData = null;
    let userData = null;

    if (token) {
      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          tokenData = JSON.parse(atob(tokenParts[1]));
        }
      } catch (error) {
        console.error('Token 解析失敗:', error);
      }
    }

    if (userStr) {
      try {
        userData = JSON.parse(userStr);
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setCurrentData({
      token: token,
      tokenData: tokenData,
      userData: userData,
      hasToken: !!token,
      hasUser: !!userData,
      tokenUserId: tokenData?.userId,
      userUserId: userData?.userId,
      isConsistent: tokenData?.userId === userData?.userId
    });
  };

  const fixUserData = async () => {
    setIsFixing(true);
    setFixResults([]);

    try {
      addResult('開始', 'info', '開始修復用戶數據');

      // 步驟 1: 檢查當前數據
      addResult('步驟1', 'info', '檢查當前認證數據...');
      
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');

      if (!token) {
        addResult('步驟1', 'error', '沒有找到 token');
        return;
      }

      if (!userStr) {
        addResult('步驟1', 'error', '沒有找到用戶數據');
        return;
      }

      // 解析 token
      let tokenData;
      try {
        const tokenParts = token.split('.');
        tokenData = JSON.parse(atob(tokenParts[1]));
        addResult('步驟1', 'success', `Token 解析成功，用戶 ID: ${tokenData.userId}`);
      } catch (error) {
        addResult('步驟1', 'error', `Token 解析失敗: ${error.message}`);
        return;
      }

      // 解析用戶數據
      let userData;
      try {
        userData = JSON.parse(userStr);
        addResult('步驟1', 'info', `用戶數據解析成功，用戶 ID: ${userData.userId || 'undefined'}`);
      } catch (error) {
        addResult('步驟1', 'error', `用戶數據解析失敗: ${error.message}`);
        return;
      }

      // 步驟 2: 修復用戶數據
      addResult('步驟2', 'info', '修復用戶數據結構...');
      addResult('步驟2', 'info', `Token Email: ${tokenData.email}`);
      addResult('步驟2', 'info', `localStorage Email: ${userData.email}`);

      const fixedUserData = {
        userId: tokenData.userId,
        name: userData.name || '系統管理員',
        email: tokenData.email,  // 使用 Token 中的正確 Email
        role: tokenData.role
      };

      addResult('步驟2', 'info', `修復後 Email: ${fixedUserData.email}`);

      // 保存修復後的數據
      localStorage.setItem('user', JSON.stringify(fixedUserData));
      addResult('步驟2', 'success', '用戶數據已修復並保存');
      addResult('步驟2', 'info', `修復後的用戶 ID: ${fixedUserData.userId}`);

      // 步驟 3: 驗證修復結果
      addResult('步驟3', 'info', '驗證修復結果...');

      const verifyUserStr = localStorage.getItem('user');
      const verifyUserData = JSON.parse(verifyUserStr);

      if (verifyUserData.userId === tokenData.userId) {
        addResult('步驟3', 'success', '用戶數據修復成功，ID 一致');
      } else {
        addResult('步驟3', 'error', '用戶數據修復失敗，ID 仍不一致');
        return;
      }

      // 步驟 4: 測試錢包 API
      addResult('步驟4', 'info', '測試錢包 API...');

      const walletResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (walletResponse.ok) {
        const walletResult = await walletResponse.json();
        if (walletResult.success) {
          addResult('步驟4', 'success', `錢包 API 正常！餘額: $${walletResult.data.balance}`);
        } else {
          addResult('步驟4', 'error', `錢包 API 業務失敗: ${walletResult.error}`);
        }
      } else {
        addResult('步驟4', 'error', `錢包 API HTTP 失敗: ${walletResponse.status}`);
      }

      addResult('完成', 'success', '用戶數據修復完成！');
      
      // 更新當前數據顯示
      checkCurrentData();

    } catch (error) {
      addResult('錯誤', 'error', '修復過程中出現錯誤: ' + error.message);
    } finally {
      setIsFixing(false);
    }
  };

  const relogin = async () => {
    try {
      addResult('重新登入', 'info', '開始重新登入...');
      
      // 清除舊數據
      localStorage.clear();
      
      const loginResponse = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const loginResult = await loginResponse.json();
      
      if (loginResult.success) {
        localStorage.setItem('token', loginResult.token);
        localStorage.setItem('user', JSON.stringify(loginResult.user));
        
        addResult('重新登入', 'success', '重新登入成功');
        addResult('重新登入', 'info', `新用戶 ID: ${loginResult.user.userId}`);
        
        checkCurrentData();
      } else {
        addResult('重新登入', 'error', `重新登入失敗: ${loginResult.error}`);
      }
    } catch (error) {
      addResult('重新登入', 'error', `重新登入錯誤: ${error.message}`);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>修復用戶數據 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔧 修復用戶數據</h1>
                <p className="text-gray-600 mt-2">修復 Token 和 localStorage 數據不一致問題</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={relogin}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  🔄 重新登入
                </button>
                <button
                  onClick={() => router.push('/wallet-api-debug')}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  🔍 API 調試
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={fixUserData}
                  disabled={isFixing}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-medium"
                >
                  {isFixing ? '修復中...' : '🔧 修復數據'}
                </button>
              </div>
            </div>

            {/* 當前數據狀態 */}
            {currentData && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 當前數據狀態</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="font-medium text-blue-900 mb-2">🔑 JWT Token 數據</h3>
                    <div className="text-sm text-blue-800 space-y-1">
                      <div>存在: {currentData.hasToken ? '✅' : '❌'}</div>
                      <div>用戶 ID: {currentData.tokenUserId || 'undefined'}</div>
                      <div>角色: {currentData.tokenData?.role || 'undefined'}</div>
                      <div>Email: {currentData.tokenData?.email || 'undefined'}</div>
                    </div>
                  </div>
                  
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="font-medium text-green-900 mb-2">👤 localStorage 用戶數據</h3>
                    <div className="text-sm text-green-800 space-y-1">
                      <div>存在: {currentData.hasUser ? '✅' : '❌'}</div>
                      <div>用戶 ID: {currentData.userUserId || 'undefined'}</div>
                      <div>姓名: {currentData.userData?.name || 'undefined'}</div>
                      <div>Email: {currentData.userData?.email || 'undefined'}</div>
                    </div>
                  </div>
                </div>
                
                <div className={`mt-4 p-4 rounded-lg border ${
                  currentData.isConsistent ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}>
                  <div className={`font-medium ${
                    currentData.isConsistent ? 'text-green-900' : 'text-red-900'
                  }`}>
                    數據一致性: {currentData.isConsistent ? '✅ 一致' : '❌ 不一致'}
                  </div>
                  {!currentData.isConsistent && (
                    <div className="text-sm text-red-800 mt-1">
                      Token 用戶 ID ({currentData.tokenUserId}) 與 localStorage 用戶 ID ({currentData.userUserId}) 不一致
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 修復結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 修復結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {fixResults.length > 0 ? (
                  <div className="space-y-3">
                    {fixResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「修復數據」或「重新登入」來修復用戶數據問題
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">⚠️ 問題說明</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <div><strong>問題:</strong> JWT Token 中的用戶 ID 與 localStorage 中的用戶數據不一致</div>
                <div><strong>影響:</strong> 導致錢包 API 認證失敗，返回 401 Unauthorized</div>
                <div><strong>解決方案1:</strong> 修復 localStorage 中的用戶數據，使其與 Token 一致</div>
                <div><strong>解決方案2:</strong> 重新登入，獲取一致的認證數據</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
