const { Pool } = require('pg');
const bcrypt = require('bcrypt');

// 資料庫連接
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'member_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

class UserController {
  // 獲取用戶個人資料
  static async getProfile(req, res) {
    try {
      const userId = req.user.userId;
      const client = await pool.connect();

      try {
        const result = await client.query(
          `SELECT id, username, email, first_name, last_name, phone, date_of_birth,
                  gender, avatar_url, timezone, language, status, email_verified_at,
                  phone_verified_at, created_at, updated_at
           FROM users 
           WHERE id = $1 AND deleted_at IS NULL`,
          [userId]
        );

        if (result.rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: '用戶不存在'
          });
        }

        const user = result.rows[0];
        client.release();

        res.json({
          success: true,
          data: {
            id: user.id,
            username: user.username,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            phone: user.phone,
            dateOfBirth: user.date_of_birth,
            gender: user.gender,
            avatarUrl: user.avatar_url,
            timezone: user.timezone,
            language: user.language,
            status: user.status,
            emailVerified: !!user.email_verified_at,
            phoneVerified: !!user.phone_verified_at,
            createdAt: user.created_at,
            updatedAt: user.updated_at
          }
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('獲取用戶資料失敗:', error);
      res.status(500).json({
        success: false,
        message: '獲取用戶資料失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 更新用戶個人資料
  static async updateProfile(req, res) {
    try {
      const userId = req.user.userId;
      const { firstName, lastName, phone, dateOfBirth, gender, timezone, language } = req.body;

      const client = await pool.connect();

      try {
        const result = await client.query(
          `UPDATE users 
           SET first_name = COALESCE($1, first_name),
               last_name = COALESCE($2, last_name),
               phone = COALESCE($3, phone),
               date_of_birth = COALESCE($4, date_of_birth),
               gender = COALESCE($5, gender),
               timezone = COALESCE($6, timezone),
               language = COALESCE($7, language),
               updated_at = CURRENT_TIMESTAMP
           WHERE id = $8 AND deleted_at IS NULL
           RETURNING id, username, email, first_name, last_name, phone, date_of_birth,
                     gender, timezone, language, updated_at`,
          [firstName, lastName, phone, dateOfBirth, gender, timezone, language, userId]
        );

        if (result.rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: '用戶不存在'
          });
        }

        const user = result.rows[0];

        // 記錄用戶活動
        await client.query(
          `INSERT INTO user_activity_logs (user_id, activity_type, description, ip_address, user_agent)
           VALUES ($1, 'profile_update', '更新個人資料', $2, $3)`,
          [userId, req.ip, req.get('User-Agent')]
        );

        client.release();

        res.json({
          success: true,
          message: '個人資料更新成功',
          data: {
            id: user.id,
            username: user.username,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            phone: user.phone,
            dateOfBirth: user.date_of_birth,
            gender: user.gender,
            timezone: user.timezone,
            language: user.language,
            updatedAt: user.updated_at
          }
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('更新用戶資料失敗:', error);
      res.status(500).json({
        success: false,
        message: '更新用戶資料失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 修改密碼
  static async changePassword(req, res) {
    try {
      const userId = req.user.userId;
      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          message: '當前密碼和新密碼為必填項'
        });
      }

      if (newPassword.length < 8) {
        return res.status(400).json({
          success: false,
          message: '新密碼長度至少需要 8 個字符'
        });
      }

      const client = await pool.connect();

      try {
        // 獲取當前密碼
        const userResult = await client.query(
          'SELECT password FROM users WHERE id = $1 AND deleted_at IS NULL',
          [userId]
        );

        if (userResult.rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: '用戶不存在'
          });
        }

        const user = userResult.rows[0];

        // 驗證當前密碼
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
          return res.status(400).json({
            success: false,
            message: '當前密碼錯誤'
          });
        }

        // 加密新密碼
        const hashedNewPassword = await bcrypt.hash(newPassword, 12);

        // 更新密碼
        await client.query(
          `UPDATE users 
           SET password = $1, updated_at = CURRENT_TIMESTAMP
           WHERE id = $2`,
          [hashedNewPassword, userId]
        );

        // 記錄安全活動
        await client.query(
          `INSERT INTO security_audit_logs (user_id, event_type, description, severity, ip_address, user_agent)
           VALUES ($1, 'password_change', '用戶修改密碼', 'medium', $2, $3)`,
          [userId, req.ip, req.get('User-Agent')]
        );

        client.release();

        res.json({
          success: true,
          message: '密碼修改成功'
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('修改密碼失敗:', error);
      res.status(500).json({
        success: false,
        message: '修改密碼失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 獲取用戶活動記錄
  static async getActivityLogs(req, res) {
    try {
      const userId = req.user.userId;
      const { page = 1, limit = 20 } = req.query;
      const offset = (page - 1) * limit;

      const client = await pool.connect();

      try {
        const result = await client.query(
          `SELECT activity_type, description, ip_address, user_agent, created_at
           FROM user_activity_logs
           WHERE user_id = $1
           ORDER BY created_at DESC
           LIMIT $2 OFFSET $3`,
          [userId, limit, offset]
        );

        const countResult = await client.query(
          'SELECT COUNT(*) FROM user_activity_logs WHERE user_id = $1',
          [userId]
        );

        client.release();

        res.json({
          success: true,
          data: {
            activities: result.rows,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: parseInt(countResult.rows[0].count),
              totalPages: Math.ceil(countResult.rows[0].count / limit)
            }
          }
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('獲取活動記錄失敗:', error);
      res.status(500).json({
        success: false,
        message: '獲取活動記錄失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 刪除帳號
  static async deleteAccount(req, res) {
    try {
      const userId = req.user.userId;
      const { password } = req.body;

      if (!password) {
        return res.status(400).json({
          success: false,
          message: '請輸入密碼以確認刪除帳號'
        });
      }

      const client = await pool.connect();

      try {
        // 驗證密碼
        const userResult = await client.query(
          'SELECT password FROM users WHERE id = $1 AND deleted_at IS NULL',
          [userId]
        );

        if (userResult.rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: '用戶不存在'
          });
        }

        const user = userResult.rows[0];
        const isPasswordValid = await bcrypt.compare(password, user.password);

        if (!isPasswordValid) {
          return res.status(400).json({
            success: false,
            message: '密碼錯誤'
          });
        }

        // 軟刪除用戶 (GDPR 合規)
        await client.query(
          `UPDATE users 
           SET deleted_at = CURRENT_TIMESTAMP, 
               status = 'deleted',
               updated_at = CURRENT_TIMESTAMP
           WHERE id = $1`,
          [userId]
        );

        // 記錄刪除活動
        await client.query(
          `INSERT INTO security_audit_logs (user_id, event_type, description, severity, ip_address, user_agent)
           VALUES ($1, 'account_deletion', '用戶刪除帳號', 'high', $2, $3)`,
          [userId, req.ip, req.get('User-Agent')]
        );

        client.release();

        res.json({
          success: true,
          message: '帳號已成功刪除'
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('刪除帳號失敗:', error);
      res.status(500).json({
        success: false,
        message: '刪除帳號失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = UserController;
