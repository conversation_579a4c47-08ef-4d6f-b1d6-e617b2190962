-- 會員管理系統 - 資料庫初始化腳本
-- 版本: 1.0
-- 創建日期: 2025-01-26

-- 啟用外鍵約束
PRAGMA foreign_keys = ON;

-- ============================================================================
-- 1. 會員表 (members)
-- ============================================================================
CREATE TABLE IF NOT EXISTS members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role TEXT CHECK(role IN ('user', 'admin')) DEFAULT 'user',
    status TEXT CHECK(status IN ('active', 'inactive', 'suspended')) DEFAULT 'active',
    phone VARCHAR(20),
    avatar_url VARCHAR(500),
    email_verified BOOLEAN DEFAULT FALSE,
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 會員表索引
CREATE INDEX IF NOT EXISTS idx_members_email ON members(email);
CREATE INDEX IF NOT EXISTS idx_members_status ON members(status);
CREATE INDEX IF NOT EXISTS idx_members_role ON members(role);

-- ============================================================================
-- 2. 服務表 (services)
-- ============================================================================
CREATE TABLE IF NOT EXISTS services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    billing_cycle TEXT CHECK(billing_cycle IN ('monthly', 'yearly')) DEFAULT 'monthly',
    status TEXT CHECK(status IN ('active', 'inactive', 'deprecated')) DEFAULT 'active',
    category VARCHAR(100),
    features TEXT, -- JSON 格式存儲
    max_users INTEGER DEFAULT 1,
    api_quota INTEGER,
    storage_limit INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 服務表索引
CREATE INDEX IF NOT EXISTS idx_services_status ON services(status);
CREATE INDEX IF NOT EXISTS idx_services_category ON services(category);
CREATE INDEX IF NOT EXISTS idx_services_price ON services(price);

-- ============================================================================
-- 3. 會員服務關聯表 (member_services)
-- ============================================================================
CREATE TABLE IF NOT EXISTS member_services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER NOT NULL,
    service_id INTEGER NOT NULL,
    status TEXT CHECK(status IN ('active', 'cancelled', 'expired', 'suspended')) DEFAULT 'active',
    subscribed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    next_billing_date DATE,
    auto_renew BOOLEAN DEFAULT TRUE,
    usage_data TEXT, -- JSON 格式存儲
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE(member_id, service_id)
);

-- 會員服務關聯表索引
CREATE INDEX IF NOT EXISTS idx_member_services_member ON member_services(member_id);
CREATE INDEX IF NOT EXISTS idx_member_services_service ON member_services(service_id);
CREATE INDEX IF NOT EXISTS idx_member_services_status ON member_services(status);
CREATE INDEX IF NOT EXISTS idx_member_services_billing ON member_services(next_billing_date);

-- ============================================================================
-- 4. 交易記錄表 (transactions)
-- ============================================================================
CREATE TABLE IF NOT EXISTS transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER NOT NULL,
    service_id INTEGER,
    member_service_id INTEGER,
    transaction_type TEXT CHECK(transaction_type IN ('payment', 'refund', 'adjustment')) DEFAULT 'payment',
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'TWD',
    status TEXT CHECK(status IN ('pending', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_gateway VARCHAR(50),
    gateway_transaction_id VARCHAR(255),
    description TEXT,
    metadata TEXT, -- JSON 格式存儲
    processed_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE SET NULL,
    FOREIGN KEY (member_service_id) REFERENCES member_services(id) ON DELETE SET NULL
);

-- 交易記錄表索引
CREATE INDEX IF NOT EXISTS idx_transactions_member ON transactions(member_id);
CREATE INDEX IF NOT EXISTS idx_transactions_service ON transactions(service_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_gateway ON transactions(gateway_transaction_id);

-- ============================================================================
-- 5. 審計日誌表 (audit_logs)
-- ============================================================================
CREATE TABLE IF NOT EXISTS audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id INTEGER,
    old_values TEXT, -- JSON 格式存儲
    new_values TEXT, -- JSON 格式存儲
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL
);

-- 審計日誌表索引
CREATE INDEX IF NOT EXISTS idx_audit_logs_member ON audit_logs(member_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_date ON audit_logs(created_at);

-- ============================================================================
-- 初始數據插入
-- ============================================================================

-- 插入管理員帳戶
INSERT OR IGNORE INTO members (id, email, name, password_hash, role, status, phone, created_at) VALUES 
(1, '<EMAIL>', '系統管理員', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', '0912345678', datetime('now'));

-- 插入一般用戶帳戶
INSERT OR IGNORE INTO members (id, email, name, password_hash, role, status, phone, created_at) VALUES 
(2, '<EMAIL>', '一般用戶', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'active', '0987654321', datetime('now'));

-- 插入預設服務
INSERT OR IGNORE INTO services (id, name, description, price, billing_cycle, status, category, features, created_at) VALUES 
(1, 'AI 文字生成服務', '使用先進的 AI 技術生成高質量文字內容', 299.00, 'monthly', 'active', 'AI', '["無限文字生成", "多種寫作風格", "24/7 技術支援"]', datetime('now')),
(2, '圖片處理服務', '專業的圖片編輯和處理工具', 199.00, 'monthly', 'active', '圖片處理', '["批量處理", "多種濾鏡", "雲端存儲"]', datetime('now')),
(3, '數據分析服務', '強大的數據分析和視覺化工具', 499.00, 'monthly', 'active', '數據分析', '["即時分析", "自定義報表", "數據視覺化"]', datetime('now')),
(4, '語音轉文字服務', '高精度的語音識別和轉換服務', 149.00, 'monthly', 'active', '語音處理', '["多語言支援", "即時轉換", "高精度識別"]', datetime('now')),
(5, '雲端存儲服務', '安全可靠的雲端文件存儲解決方案', 99.00, 'monthly', 'active', '存儲', '["100GB 存儲空間", "自動備份", "多設備同步"]', datetime('now')),
(6, '企業級 API 服務', '為企業提供的高性能 API 接口服務', 999.00, 'monthly', 'active', 'API', '["無限 API 調用", "專屬技術支援", "SLA 保證"]', datetime('now'));

-- 插入示例訂閱關係
INSERT OR IGNORE INTO member_services (id, member_id, service_id, status, subscribed_at, next_billing_date, created_at) VALUES 
(1, 1, 1, 'active', datetime('now'), date('now', '+1 month'), datetime('now')),
(2, 1, 2, 'active', datetime('now'), date('now', '+1 month'), datetime('now')),
(3, 2, 1, 'active', datetime('now'), date('now', '+1 month'), datetime('now'));

-- 插入示例交易記錄
INSERT OR IGNORE INTO transactions (id, member_id, service_id, member_service_id, transaction_type, amount, currency, status, payment_method, description, processed_at, created_at) VALUES 
(1, 1, 1, 1, 'payment', 299.00, 'TWD', 'completed', 'credit_card', 'AI 文字生成服務 - 月費', datetime('now'), datetime('now')),
(2, 1, 2, 2, 'payment', 199.00, 'TWD', 'completed', 'credit_card', '圖片處理服務 - 月費', datetime('now'), datetime('now')),
(3, 2, 1, 3, 'payment', 299.00, 'TWD', 'completed', 'bank_transfer', 'AI 文字生成服務 - 月費', datetime('now'), datetime('now'));

-- 插入示例審計日誌
INSERT OR IGNORE INTO audit_logs (id, member_id, action, resource_type, resource_id, new_values, ip_address, created_at) VALUES 
(1, 1, 'login', 'member', 1, '{"login_time": "2025-01-26T10:00:00Z"}', '*************', datetime('now')),
(2, 1, 'subscribe', 'member_service', 1, '{"service_id": 1, "status": "active"}', '*************', datetime('now')),
(3, 2, 'login', 'member', 2, '{"login_time": "2025-01-26T11:00:00Z"}', '*************', datetime('now'));

-- ============================================================================
-- 觸發器 (Triggers)
-- ============================================================================

-- 自動更新 updated_at 欄位的觸發器

-- members 表觸發器
CREATE TRIGGER IF NOT EXISTS update_members_updated_at 
    AFTER UPDATE ON members
    FOR EACH ROW
BEGIN
    UPDATE members SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- services 表觸發器
CREATE TRIGGER IF NOT EXISTS update_services_updated_at 
    AFTER UPDATE ON services
    FOR EACH ROW
BEGIN
    UPDATE services SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- member_services 表觸發器
CREATE TRIGGER IF NOT EXISTS update_member_services_updated_at 
    AFTER UPDATE ON member_services
    FOR EACH ROW
BEGIN
    UPDATE member_services SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- transactions 表觸發器
CREATE TRIGGER IF NOT EXISTS update_transactions_updated_at 
    AFTER UPDATE ON transactions
    FOR EACH ROW
BEGIN
    UPDATE transactions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- ============================================================================
-- 視圖 (Views)
-- ============================================================================

-- 會員訂閱詳情視圖
CREATE VIEW IF NOT EXISTS member_subscription_details AS
SELECT 
    ms.id as subscription_id,
    m.id as member_id,
    m.name as member_name,
    m.email as member_email,
    s.id as service_id,
    s.name as service_name,
    s.price as service_price,
    s.billing_cycle,
    ms.status as subscription_status,
    ms.subscribed_at,
    ms.expires_at,
    ms.next_billing_date,
    ms.auto_renew
FROM member_services ms
JOIN members m ON ms.member_id = m.id
JOIN services s ON ms.service_id = s.id;

-- 交易統計視圖
CREATE VIEW IF NOT EXISTS transaction_summary AS
SELECT 
    m.id as member_id,
    m.name as member_name,
    COUNT(t.id) as total_transactions,
    SUM(CASE WHEN t.status = 'completed' THEN t.amount ELSE 0 END) as total_paid,
    SUM(CASE WHEN t.status = 'pending' THEN t.amount ELSE 0 END) as pending_amount,
    MAX(t.created_at) as last_transaction_date
FROM members m
LEFT JOIN transactions t ON m.id = t.member_id
GROUP BY m.id, m.name;

-- ============================================================================
-- 完成初始化
-- ============================================================================

-- 檢查資料庫完整性
PRAGMA integrity_check;

-- 顯示資料庫統計
SELECT 'members' as table_name, COUNT(*) as record_count FROM members
UNION ALL
SELECT 'services' as table_name, COUNT(*) as record_count FROM services
UNION ALL
SELECT 'member_services' as table_name, COUNT(*) as record_count FROM member_services
UNION ALL
SELECT 'transactions' as table_name, COUNT(*) as record_count FROM transactions
UNION ALL
SELECT 'audit_logs' as table_name, COUNT(*) as record_count FROM audit_logs;

-- 初始化完成
SELECT 'Database initialization completed successfully!' as status;
