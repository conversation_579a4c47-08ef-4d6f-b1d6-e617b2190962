// 測試用訂閱 API - 不需要認證
const path = require('path');
const fs = require('fs');

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();

    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];

    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('測試訂閱 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }

    console.log('測試訂閱 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('測試訂閱 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  try {
    console.log('測試訂閱 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method === 'GET') {
      // 載入用戶訂閱數據 - 固定使用用戶 ID 1 (管理員)
      const userId = 1;
      
      const db = connectDatabase();
      if (!db) {
        return res.status(500).json({
          success: false,
          error: '資料庫連接失敗'
        });
      }

      return new Promise((resolve) => {
        const query = `
          SELECT 
            ms.id,
            ms.member_id,
            ms.service_id,
            ms.status,
            ms.subscribed_at,
            ms.next_billing_date,
            s.name as service_name,
            s.description as service_description,
            s.price as service_price,
            s.billing_cycle as service_billing_cycle,
            s.features
          FROM member_services ms
          JOIN services s ON ms.service_id = s.id
          WHERE ms.member_id = ?
          ORDER BY ms.subscribed_at DESC
        `;

        db.all(query, [userId], (err, rows) => {
          db.close();

          if (err) {
            console.error('測試訂閱 API: 查詢失敗:', err);
            resolve(res.status(500).json({
              success: false,
              error: '查詢訂閱數據失敗'
            }));
            return;
          }

          console.log(`測試訂閱 API: 找到 ${rows.length} 個訂閱記錄`);

          // 格式化數據
          const subscriptions = rows.map(row => ({
            id: row.id,
            member_id: row.member_id,
            service_id: row.service_id,
            service_name: row.service_name,
            service_description: row.service_description,
            service_price: row.service_price,
            service_billing_cycle: row.service_billing_cycle,
            status: row.status,
            subscribed_at: row.subscribed_at,
            next_billing_date: row.next_billing_date,
            features: row.features ? JSON.parse(row.features) : []
          }));

          resolve(res.status(200).json({
            success: true,
            data: subscriptions,
            message: `成功載入 ${subscriptions.length} 個訂閱記錄`
          }));
        });
      });
    }

    if (req.method === 'POST') {
      // 訂閱服務 - 固定使用用戶 ID 1
      const { serviceId } = req.body;
      const userId = 1;

      if (!serviceId) {
        return res.status(400).json({
          success: false,
          error: '請提供服務 ID'
        });
      }

      const db = connectDatabase();
      if (!db) {
        return res.status(500).json({
          success: false,
          error: '資料庫連接失敗'
        });
      }

      return new Promise((resolve) => {
        // 檢查是否已經訂閱
        const checkQuery = `
          SELECT id, status FROM member_services 
          WHERE member_id = ? AND service_id = ?
        `;

        db.get(checkQuery, [userId, serviceId], (err, existingRecord) => {
          if (err) {
            db.close();
            console.error('測試訂閱 API: 檢查訂閱失敗:', err);
            resolve(res.status(500).json({
              success: false,
              error: '檢查訂閱狀態失敗'
            }));
            return;
          }

          if (existingRecord) {
            if (existingRecord.status === 'active') {
              db.close();
              resolve(res.status(400).json({
                success: false,
                error: '您已經訂閱了這個服務'
              }));
              return;
            } else {
              // 重新激活已取消的訂閱
              const updateQuery = `
                UPDATE member_services 
                SET status = 'active', subscribed_at = CURRENT_TIMESTAMP, next_billing_date = date('now', '+1 month')
                WHERE id = ?
              `;

              db.run(updateQuery, [existingRecord.id], function(updateErr) {
                db.close();

                if (updateErr) {
                  console.error('測試訂閱 API: 重新激活失敗:', updateErr);
                  resolve(res.status(500).json({
                    success: false,
                    error: '重新激活訂閱失敗'
                  }));
                  return;
                }

                resolve(res.status(200).json({
                  success: true,
                  action: 'reactivated',
                  data: {
                    id: existingRecord.id,
                    member_id: userId,
                    service_id: serviceId,
                    status: 'active',
                    subscribed_at: new Date().toISOString(),
                    next_billing_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
                  },
                  message: '服務訂閱已重新激活'
                }));
              });
            }
          } else {
            // 創建新訂閱
            const insertQuery = `
              INSERT INTO member_services (member_id, service_id, status, subscribed_at, next_billing_date)
              VALUES (?, ?, 'active', CURRENT_TIMESTAMP, date('now', '+1 month'))
            `;

            db.run(insertQuery, [userId, serviceId], function(insertErr) {
              db.close();

              if (insertErr) {
                console.error('測試訂閱 API: 創建訂閱失敗:', insertErr);
                resolve(res.status(500).json({
                  success: false,
                  error: '創建訂閱失敗'
                }));
                return;
              }

              resolve(res.status(200).json({
                success: true,
                action: 'subscribed',
                data: {
                  id: this.lastID,
                  member_id: userId,
                  service_id: serviceId,
                  status: 'active',
                  subscribed_at: new Date().toISOString(),
                  next_billing_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
                },
                message: '服務訂閱成功'
              }));
            });
          }
        });
      });
    }

    // 不支持的方法
    res.setHeader('Allow', ['GET', 'POST', 'OPTIONS']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });

  } catch (error) {
    console.error('測試訂閱 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
