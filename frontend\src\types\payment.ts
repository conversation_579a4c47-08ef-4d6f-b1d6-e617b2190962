// 支付方式
export interface PaymentMethod {
  id: string;
  name: string;
  fees: {
    percentage: number;
    fixed: number;
  };
}

// 充值獎勵
export interface RewardInfo {
  originalAmount: number;
  bonusAmount: number;
  totalAmount: number;
  bonusPercent: number;
  isFirstTime: boolean;
}

// 充值表單數據
export interface DepositFormData {
  amount: number;
  currency: string;
  paymentMethod: string;
}

// 支付結果
export interface PaymentResult {
  paymentId: string;
  clientSecret?: string; // Stripe
  approvalUrl?: string;  // PayPal
  reward: RewardInfo;
}

// 錢包資訊
export interface Wallet {
  id: string;
  user_id: string;
  currency: string;
  balance: number;
  frozen_balance: number;
  total_deposited: number;
  total_withdrawn: number;
  status: 'active' | 'frozen' | 'closed';
  created_at: string;
  updated_at: string;
}

// 交易記錄
export interface Transaction {
  id: string;
  wallet_id: string;
  type: 'deposit' | 'withdraw' | 'payment' | 'refund' | 'bonus' | 'penalty';
  amount: number;
  balance_before: number;
  balance_after: number;
  currency: string;
  description: string;
  reference_id?: string;
  reference_type?: string;
  metadata?: any;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
  processed_at?: string;
}

// 支付記錄
export interface Payment {
  id: string;
  user_id: string;
  wallet_id?: string;
  payment_method: string;
  provider: string;
  provider_payment_id?: string;
  amount: number;
  currency: string;
  exchange_rate?: number;
  original_amount?: number;
  original_currency?: string;
  fee: number;
  net_amount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  failure_reason?: string;
  metadata?: any;
  created_at: string;
  completed_at?: string;
  expires_at?: string;
}

// 支援的幣種
export interface Currency {
  code: string;
  symbol: string;
  decimals: number;
}

// API 響應
export interface PaymentResponse {
  success: boolean;
  message: string;
  data?: PaymentResult;
  errors?: Record<string, string[]>;
}