// 初始化支付方式數據
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 資料庫路徑
const dbPath = path.join(__dirname, 'frontend', 'lib', 'database', 'database.sqlite');

console.log('💳 開始初始化支付方式...');
console.log('📁 資料庫路徑:', dbPath);

if (!fs.existsSync(dbPath)) {
  console.error('❌ 資料庫文件不存在:', dbPath);
  process.exit(1);
}

const db = new sqlite3.Database(dbPath);

// 支付方式數據
const paymentMethods = [
  {
    name: '信用卡',
    type: 'credit_card',
    description: '使用 Visa、MasterCard、JCB 等信用卡充值',
    icon_url: '💳',
    min_amount: 100.00,
    max_amount: 50000.00,
    fee_type: 'percentage',
    fee_amount: 2.5,
    sort_order: 1,
    is_active: 1
  },
  {
    name: '銀行轉帳',
    type: 'bank_transfer',
    description: '透過網路銀行或 ATM 轉帳充值',
    icon_url: '🏦',
    min_amount: 100.00,
    max_amount: 100000.00,
    fee_type: 'fixed',
    fee_amount: 15.00,
    sort_order: 2,
    is_active: 1
  },
  {
    name: 'LINE Pay',
    type: 'line_pay',
    description: '使用 LINE Pay 快速充值',
    icon_url: '💚',
    min_amount: 50.00,
    max_amount: 20000.00,
    fee_type: 'none',
    fee_amount: 0.00,
    sort_order: 3,
    is_active: 1
  },
  {
    name: 'Apple Pay',
    type: 'apple_pay',
    description: '使用 Apple Pay 安全充值',
    icon_url: '🍎',
    min_amount: 50.00,
    max_amount: 20000.00,
    fee_type: 'none',
    fee_amount: 0.00,
    sort_order: 4,
    is_active: 1
  },
  {
    name: 'Google Pay',
    type: 'google_pay',
    description: '使用 Google Pay 快速充值',
    icon_url: '🔵',
    min_amount: 50.00,
    max_amount: 20000.00,
    fee_type: 'none',
    fee_amount: 0.00,
    sort_order: 5,
    is_active: 1
  },
  {
    name: '超商代碼',
    type: 'convenience_store',
    description: '7-11、全家、萊爾富等超商代碼繳費',
    icon_url: '🏪',
    min_amount: 100.00,
    max_amount: 20000.00,
    fee_type: 'fixed',
    fee_amount: 10.00,
    sort_order: 6,
    is_active: 1
  },
  {
    name: 'PayPal',
    type: 'paypal',
    description: '使用 PayPal 國際支付',
    icon_url: '🌐',
    min_amount: 100.00,
    max_amount: 30000.00,
    fee_type: 'percentage',
    fee_amount: 3.0,
    sort_order: 7,
    is_active: 1
  }
];

// 初始化支付方式
async function initPaymentMethods() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n1️⃣ 檢查 payment_methods 表...');
      
      // 檢查表是否存在
      db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='payment_methods'", (err, row) => {
        if (err) {
          console.error('❌ 檢查表失敗:', err);
          reject(err);
          return;
        }

        if (!row) {
          console.log('📋 創建 payment_methods 表...');
          createPaymentMethodsTable();
        } else {
          console.log('✅ payment_methods 表已存在');
          checkExistingData();
        }
      });

      function createPaymentMethodsTable() {
        const createTableSql = `
          CREATE TABLE payment_methods (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            description TEXT,
            icon_url TEXT,
            min_amount DECIMAL(15,2) DEFAULT 1.00,
            max_amount DECIMAL(15,2) DEFAULT 10000.00,
            fee_type TEXT DEFAULT 'none' CHECK(fee_type IN ('none', 'fixed', 'percentage')),
            fee_amount DECIMAL(15,2) DEFAULT 0.00,
            is_active INTEGER DEFAULT 1,
            sort_order INTEGER DEFAULT 0,
            config_json TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `;

        db.run(createTableSql, (err) => {
          if (err) {
            console.error('❌ 創建 payment_methods 表失敗:', err);
            reject(err);
            return;
          }
          
          console.log('✅ payment_methods 表創建成功');
          
          // 創建索引
          createIndexes();
        });
      }

      function createIndexes() {
        console.log('\n2️⃣ 創建索引...');
        
        const indexes = [
          "CREATE INDEX IF NOT EXISTS idx_payment_methods_type ON payment_methods(type)",
          "CREATE INDEX IF NOT EXISTS idx_payment_methods_active ON payment_methods(is_active)",
          "CREATE INDEX IF NOT EXISTS idx_payment_methods_sort ON payment_methods(sort_order)"
        ];

        let indexCompleted = 0;
        indexes.forEach((indexSql, i) => {
          db.run(indexSql, (err) => {
            if (err) {
              console.error(`❌ 創建索引 ${i + 1} 失敗:`, err);
            } else {
              console.log(`✅ 索引 ${i + 1} 創建成功`);
            }
            
            indexCompleted++;
            if (indexCompleted === indexes.length) {
              insertPaymentMethods();
            }
          });
        });
      }

      function checkExistingData() {
        console.log('\n2️⃣ 檢查現有支付方式...');
        
        db.all("SELECT * FROM payment_methods ORDER BY sort_order ASC", (err, methods) => {
          if (err) {
            console.error('❌ 查詢支付方式失敗:', err);
            reject(err);
            return;
          }

          console.log(`📊 找到 ${methods.length} 個支付方式:`);
          methods.forEach(method => {
            console.log(`   - ${method.name} (${method.type}) - ${method.is_active ? '啟用' : '停用'}`);
          });

          if (methods.length === 0) {
            insertPaymentMethods();
          } else {
            console.log('\n✅ 支付方式已存在，跳過初始化');
            resolve();
          }
        });
      }

      function insertPaymentMethods() {
        console.log('\n3️⃣ 插入支付方式數據...');
        
        const insertSql = `
          INSERT INTO payment_methods 
          (name, type, description, icon_url, min_amount, max_amount, fee_type, fee_amount, is_active, sort_order)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        let completed = 0;
        paymentMethods.forEach((method, index) => {
          const params = [
            method.name,
            method.type,
            method.description,
            method.icon_url,
            method.min_amount,
            method.max_amount,
            method.fee_type,
            method.fee_amount,
            method.is_active,
            method.sort_order
          ];

          db.run(insertSql, params, function(err) {
            if (err) {
              console.error(`❌ 插入支付方式 ${method.name} 失敗:`, err);
            } else {
              console.log(`✅ 支付方式 ${method.name} 插入成功 (ID: ${this.lastID})`);
            }
            
            completed++;
            if (completed === paymentMethods.length) {
              finalCheck();
            }
          });
        });
      }

      function finalCheck() {
        console.log('\n4️⃣ 最終驗證...');
        
        db.all("SELECT COUNT(*) as count FROM payment_methods WHERE is_active = 1", (err, result) => {
          if (err) {
            console.error('❌ 最終驗證失敗:', err);
            reject(err);
            return;
          }
          
          console.log(`✅ 共有 ${result[0].count} 個啟用的支付方式`);
          
          // 顯示所有支付方式
          db.all("SELECT * FROM payment_methods ORDER BY sort_order ASC", (err, methods) => {
            if (err) {
              console.error('❌ 查詢支付方式失敗:', err);
              reject(err);
              return;
            }

            console.log('\n📋 支付方式列表:');
            methods.forEach(method => {
              const fee = method.fee_type === 'none' ? '免手續費' : 
                         method.fee_type === 'fixed' ? `固定 $${method.fee_amount}` :
                         `${method.fee_amount}%`;
              console.log(`   ${method.icon_url} ${method.name} - $${method.min_amount}~$${method.max_amount} (${fee})`);
            });
            
            console.log('\n🎉 支付方式初始化完成！');
            resolve();
          });
        });
      }
    });
  });
}

// 執行初始化
initPaymentMethods()
  .then(() => {
    console.log('\n✅ 所有初始化操作完成');
    db.close();
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 初始化失敗:', error);
    db.close();
    process.exit(1);
  });
