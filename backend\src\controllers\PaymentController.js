const PaymentService = require('../services/PaymentService');
const { validationResult } = require('express-validator');
const { query } = require('../config/database');
const { stripe } = require('../config/payment');

class PaymentController {
  // 獲取支付方式列表
  static async getPaymentMethods(req, res) {
    try {
      const { currency = 'TWD' } = req.query;
      const methods = PaymentService.getPaymentMethods(currency);

      res.json({
        success: true,
        data: methods,
      });
    } catch (error) {
      console.error('Get payment methods error:', error);
      res.status(500).json({
        success: false,
        message: '獲取支付方式失敗',
        error: error.message,
      });
    }
  }

  // 獲取充值獎勵預覽
  static async getRewardPreview(req, res) {
    try {
      const { amount } = req.query;
      const userId = req.user.id;

      if (!amount || isNaN(amount) || amount <= 0) {
        return res.status(400).json({
          success: false,
          message: '請提供有效的充值金額',
        });
      }

      // 檢查是否為首次充值
      const isFirstTime = await PaymentService.isFirstTimeDeposit(userId);
      const reward = PaymentService.getRewardPreview(parseFloat(amount), isFirstTime);

      res.json({
        success: true,
        data: reward,
      });
    } catch (error) {
      console.error('Get reward preview error:', error);
      res.status(500).json({
        success: false,
        message: '獲取獎勵預覽失敗',
        error: error.message,
      });
    }
  }

  // 創建 Stripe 支付
  static async createStripePayment(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '輸入資料有誤',
          errors: errors.array(),
        });
      }

      const { amount, currency = 'TWD' } = req.body;
      const userId = req.user.id;

      const result = await PaymentService.createStripePayment(userId, amount, currency);

      res.json({
        success: true,
        message: '支付創建成功',
        data: result,
      });
    } catch (error) {
      console.error('Create Stripe payment error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '創建支付失敗',
      });
    }
  }

  // 創建 PayPal 支付
  static async createPayPalPayment(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '輸入資料有誤',
          errors: errors.array(),
        });
      }

      const { amount, currency = 'USD' } = req.body;
      const userId = req.user.id;

      const result = await PaymentService.createPayPalPayment(userId, amount, currency);

      res.json({
        success: true,
        message: '支付創建成功',
        data: result,
      });
    } catch (error) {
      console.error('Create PayPal payment error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '創建支付失敗',
      });
    }
  }

  // Stripe Webhook 處理
  static async handleStripeWebhook(req, res) {
    try {
      const sig = req.headers['stripe-signature'];
      const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

      let event;
      try {
        event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
      } catch (err) {
        console.error('Webhook signature verification failed:', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
      }

      // 處理事件
      switch (event.type) {
        case 'payment_intent.succeeded':
          const paymentIntent = event.data.object;

          // 查找對應的支付記錄
          const paymentResult = await query(
            'SELECT id FROM payments WHERE provider_payment_id = $1',
            [paymentIntent.id]
          );

          if (paymentResult.rows.length > 0) {
            await PaymentService.handlePaymentSuccess(
              paymentResult.rows[0].id,
              paymentIntent.id
            );
          }
          break;

        case 'payment_intent.payment_failed':
          const failedPayment = event.data.object;

          const failedPaymentResult = await query(
            'SELECT id FROM payments WHERE provider_payment_id = $1',
            [failedPayment.id]
          );

          if (failedPaymentResult.rows.length > 0) {
            await PaymentService.handlePaymentFailure(
              failedPaymentResult.rows[0].id,
              failedPayment.last_payment_error?.message || '支付失敗'
            );
          }
          break;

        default:
          console.log(`Unhandled event type ${event.type}`);
      }

      res.json({ received: true });
    } catch (error) {
      console.error('Stripe webhook error:', error);
      res.status(500).json({
        success: false,
        message: 'Webhook 處理失敗',
      });
    }
  }
}

module.exports = PaymentController;