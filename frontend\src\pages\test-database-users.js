import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function TestDatabaseUsers() {
  const router = useRouter();
  const [testResults, setTestResults] = useState([]);
  const [isTesting, setIsTesting] = useState(false);
  const [users, setUsers] = useState([]);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, {
      timestamp,
      step,
      status,
      message,
      data
    }]);
  };

  const testDatabaseConnection = async () => {
    setIsTesting(true);
    setTestResults([]);
    setUsers([]);

    try {
      addResult('開始', 'info', '開始測試資料庫連接');

      // 檢查認證
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (!token || !userStr) {
        addResult('認證', 'error', '未找到認證數據');
        return;
      }

      const userData = JSON.parse(userStr);
      addResult('認證', 'success', `當前用戶: ${userData.name} (${userData.role})`);

      // 測試基本資料庫查詢
      addResult('測試1', 'info', '測試基本資料庫查詢...');
      
      const basicResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "SELECT name FROM sqlite_master WHERE type='table'"
        })
      });

      addResult('測試1', 'info', `API 響應狀態: ${basicResponse.status}`);

      if (basicResponse.ok) {
        const basicResult = await basicResponse.json();
        addResult('測試1', 'success', `基本查詢成功，找到 ${basicResult.data?.length || 0} 個表`);
        addResult('測試1', 'info', `表列表: ${basicResult.data?.map(t => t.name).join(', ')}`);
      } else {
        const errorResult = await basicResponse.json();
        addResult('測試1', 'error', `基本查詢失敗: ${basicResponse.status} - ${errorResult.error}`);
        return;
      }

      // 測試 members 表查詢
      addResult('測試2', 'info', '測試 members 表查詢...');
      
      const membersResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "SELECT id, name, email, role FROM members ORDER BY id"
        })
      });

      if (membersResponse.ok) {
        const membersResult = await membersResponse.json();
        if (membersResult.success) {
          addResult('測試2', 'success', `members 表查詢成功，找到 ${membersResult.data.length} 個用戶`);
          setUsers(membersResult.data);
          
          membersResult.data.forEach((user, index) => {
            addResult('測試2', 'info', `用戶 ${index + 1}: ${user.name} (${user.email}) - ${user.role}`);
          });
        } else {
          addResult('測試2', 'error', `members 表查詢失敗: ${membersResult.error}`);
        }
      } else {
        const errorResult = await membersResponse.json();
        addResult('測試2', 'error', `members 表查詢 HTTP 失敗: ${membersResponse.status} - ${errorResult.error}`);
      }

      addResult('完成', 'success', '資料庫連接測試完成');

    } catch (error) {
      addResult('錯誤', 'error', `測試過程中出現錯誤: ${error.message}`);
    } finally {
      setIsTesting(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>測試資料庫用戶 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🧪 測試資料庫用戶</h1>
                <p className="text-gray-600 mt-2">測試資料庫連接和用戶數據載入</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/update-database-role')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  🗄️ 角色更新工具
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={testDatabaseConnection}
                  disabled={isTesting}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-medium"
                >
                  {isTesting ? '測試中...' : '🧪 開始測試'}
                </button>
              </div>
            </div>

            {/* 用戶列表 */}
            {users.length > 0 && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">👥 載入的用戶列表</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-3">
                    {users.map(user => (
                      <div key={user.id} className="bg-white rounded-lg p-3 border">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div>
                            <div className="text-sm font-medium text-gray-700">ID</div>
                            <div>{user.id}</div>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-700">姓名</div>
                            <div>{user.name}</div>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-700">Email</div>
                            <div>{user.email}</div>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-700">角色</div>
                            <div className={`font-semibold ${
                              user.role === 'admin' ? 'text-red-600' : 'text-blue-600'
                            }`}>
                              {user.role === 'admin' ? '👨‍💼 管理員' : '👤 一般會員'}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* 測試結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 測試結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {testResults.length > 0 ? (
                  <div className="space-y-3">
                    {testResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「開始測試」來測試資料庫連接
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 測試項目</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>認證檢查:</strong> 驗證當前用戶的認證狀態</div>
                <div><strong>基本查詢:</strong> 測試資料庫連接和表列表查詢</div>
                <div><strong>用戶查詢:</strong> 測試 members 表的用戶數據查詢</div>
                <div><strong>數據顯示:</strong> 顯示所有載入的用戶信息</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
