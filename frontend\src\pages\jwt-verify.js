import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function JWTVerify() {
  const router = useRouter();
  const [tokenInfo, setTokenInfo] = useState(null);
  const [verificationResult, setVerificationResult] = useState(null);
  const [isVerifying, setIsVerifying] = useState(false);

  useEffect(() => {
    checkCurrentToken();
  }, []);

  const checkCurrentToken = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    if (token && userStr) {
      try {
        const user = JSON.parse(userStr);
        setTokenInfo({
          token: token,
          user: user,
          tokenLength: token.length,
          isJWT: token.includes('.'), // JWT 包含點號
          tokenPreview: token.substring(0, 50) + '...'
        });
      } catch (error) {
        setTokenInfo({
          token: token,
          user: null,
          tokenLength: token.length,
          isJWT: token.includes('.'),
          tokenPreview: token.substring(0, 50) + '...',
          userError: error.message
        });
      }
    } else {
      setTokenInfo(null);
    }
  };

  const verifyToken = async () => {
    setIsVerifying(true);
    setVerificationResult(null);

    try {
      if (!tokenInfo?.token) {
        setVerificationResult({
          success: false,
          error: '沒有找到 Token'
        });
        return;
      }

      // 測試錢包 API
      const response = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${tokenInfo.token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      
      setVerificationResult({
        success: result.success,
        status: response.status,
        data: result,
        error: result.success ? null : result.error
      });

    } catch (error) {
      setVerificationResult({
        success: false,
        error: error.message
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const generateNewToken = async () => {
    try {
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        checkCurrentToken();
        alert('新 Token 生成成功！');
      } else {
        alert('Token 生成失敗: ' + result.error);
      }
    } catch (error) {
      alert('Token 生成失敗: ' + error.message);
    }
  };

  const clearToken = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setTokenInfo(null);
    setVerificationResult(null);
  };

  const goToWallet = () => {
    router.push('/wallet');
  };

  const goToDashboard = () => {
    router.push('/dashboard-simple');
  };

  return (
    <>
      <Head>
        <title>JWT Token 驗證 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔐 JWT Token 驗證</h1>
                <p className="text-gray-600 mt-2">檢查和驗證當前的認證 Token</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={generateNewToken}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  🔑 生成新 Token
                </button>
                <button
                  onClick={clearToken}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  🗑️ 清除 Token
                </button>
                <button
                  onClick={goToWallet}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={goToDashboard}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
              </div>
            </div>

            {/* Token 信息 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 當前 Token 信息</h2>
              
              {tokenInfo ? (
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div>
                        <span className="font-medium text-gray-700">Token 長度:</span>
                        <span className="ml-2 text-blue-600">{tokenInfo.tokenLength} 字符</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Token 類型:</span>
                        <span className={`ml-2 ${tokenInfo.isJWT ? 'text-green-600' : 'text-red-600'}`}>
                          {tokenInfo.isJWT ? '✅ JWT Token' : '❌ 非 JWT Token'}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Token 預覽:</span>
                        <div className="mt-1 p-2 bg-white rounded border font-mono text-xs break-all">
                          {tokenInfo.tokenPreview}
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      {tokenInfo.user ? (
                        <>
                          <div>
                            <span className="font-medium text-gray-700">用戶名:</span>
                            <span className="ml-2 text-green-600">✅ {tokenInfo.user.name}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Email:</span>
                            <span className="ml-2 text-green-600">✅ {tokenInfo.user.email}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">角色:</span>
                            <span className="ml-2 text-green-600">✅ {tokenInfo.user.role}</span>
                          </div>
                        </>
                      ) : (
                        <div>
                          <span className="font-medium text-gray-700">用戶數據:</span>
                          <span className="ml-2 text-red-600">
                            ❌ {tokenInfo.userError || '無用戶數據'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                  <p className="text-yellow-800">⚠️ 沒有找到 Token，請先登入</p>
                </div>
              )}
            </div>

            {/* 驗證按鈕 */}
            <div className="mb-8 text-center">
              <button
                onClick={verifyToken}
                disabled={!tokenInfo || isVerifying}
                className="px-8 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {isVerifying ? '驗證中...' : '🧪 驗證 Token'}
              </button>
            </div>

            {/* 驗證結果 */}
            {verificationResult && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 驗證結果</h2>
                
                <div className={`rounded-lg p-6 ${
                  verificationResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                }`}>
                  <div className="flex items-center mb-4">
                    <span className={`text-2xl mr-3 ${
                      verificationResult.success ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {verificationResult.success ? '✅' : '❌'}
                    </span>
                    <h3 className={`text-lg font-medium ${
                      verificationResult.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {verificationResult.success ? 'Token 驗證成功' : 'Token 驗證失敗'}
                    </h3>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <span className="font-medium text-gray-700">HTTP 狀態:</span>
                      <span className={`ml-2 ${
                        verificationResult.status === 200 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {verificationResult.status}
                      </span>
                    </div>

                    {verificationResult.success && verificationResult.data?.data?.balance !== undefined && (
                      <div>
                        <span className="font-medium text-gray-700">錢包餘額:</span>
                        <span className="ml-2 text-blue-600 font-semibold">
                          ${verificationResult.data.data.balance.toFixed(2)}
                        </span>
                      </div>
                    )}

                    {verificationResult.error && (
                      <div>
                        <span className="font-medium text-gray-700">錯誤信息:</span>
                        <span className="ml-2 text-red-600">{verificationResult.error}</span>
                      </div>
                    )}

                    <div>
                      <span className="font-medium text-gray-700">完整響應:</span>
                      <div className="mt-2 p-3 bg-white rounded border font-mono text-xs max-h-40 overflow-y-auto">
                        <pre className="whitespace-pre-wrap">
                          {JSON.stringify(verificationResult.data, null, 2)}
                        </pre>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 Token 驗證說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>JWT Token:</strong> 正確的 JWT Token 應該包含三個部分，用點號分隔</div>
                <div><strong>驗證方式:</strong> 使用錢包餘額 API 測試 Token 的有效性</div>
                <div><strong>成功標準:</strong> API 返回 200 狀態碼且 success: true</div>
                <div><strong>失敗原因:</strong> Token 過期、格式錯誤、或 JWT 密鑰不匹配</div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="mt-8 flex flex-wrap gap-4 justify-center">
              <button
                onClick={() => router.push('/wallet-login-fix')}
                className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
              >
                🔧 修復錢包登入
              </button>
              
              <button
                onClick={() => router.push('/wallet-test')}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium"
              >
                🧪 錢包 API 測試
              </button>
              
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 font-medium"
              >
                🔄 重新載入
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
