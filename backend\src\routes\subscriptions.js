const express = require('express');
const SubscriptionController = require('../controllers/SubscriptionController');
const { authenticateToken } = require('../middleware/authMiddleware');
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Subscriptions
 *   description: 用戶訂閱相關 API
 */

/**
 * @swagger
 * /api/v1/subscriptions:
 *   get:
 *     summary: 獲取用戶的訂閱列表
 *     tags: [Subscriptions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取成功
 *       401:
 *         description: 未授權
 */
router.get('/', authenticateToken, SubscriptionController.getUserSubscriptions);

// Add other subscription routes here if needed, for example:
// router.post('/', authenticateToken, SubscriptionController.createSubscription);
// router.delete('/:subscriptionId', authenticateToken, SubscriptionController.cancelSubscription);

module.exports = router;