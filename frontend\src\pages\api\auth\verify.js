// Token 驗證 API
const jwt = require('jsonwebtoken');
const path = require('path');

// 動態導入 Member 模型
const getMember = () => {
  try {
    // 嘗試多個可能的路徑
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'models', 'Member'),
      path.join(process.cwd(), 'lib', 'database', 'models', 'Member.js'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'Member'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'Member.js')
    ];

    for (const memberPath of possiblePaths) {
      try {
        return require(memberPath);
      } catch (err) {
        continue;
      }
    }

    throw new Error('無法找到 Member 模型');
  } catch (error) {
    console.error('Member 模型載入失敗:', error);
    throw error;
  }
};

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    // 從 Authorization header 或 cookie 中獲取 token
    let token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      // 嘗試從 cookie 中獲取
      const cookies = req.headers.cookie;
      if (cookies) {
        const tokenMatch = cookies.match(/access_token=([^;]+)/);
        if (tokenMatch) {
          token = tokenMatch[1];
        }
      }
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        error: '未提供認證 Token'
      });
    }

    // 驗證 JWT Token
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // 查找會員
    const Member = getMember();
    const member = await Member.findById(decoded.userId);
    if (!member) {
      return res.status(401).json({
        success: false,
        error: '會員不存在'
      });
    }

    // 檢查帳號狀態
    if (member.status !== 'active') {
      return res.status(403).json({
        success: false,
        error: '帳號已被停用'
      });
    }

    // 獲取會員的訂閱信息
    const subscriptions = await member.getSubscriptions();

    // 返回會員信息
    return res.status(200).json({
      success: true,
      data: {
        user: member.toPublic(),
        subscriptions: subscriptions.map(sub => sub.toJSON())
      },
      message: 'Token 驗證成功'
    });

  } catch (error) {
    console.error('Token 驗證錯誤:', error);
    
    // JWT 相關錯誤
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: '無效的 Token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token 已過期'
      });
    }

    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
