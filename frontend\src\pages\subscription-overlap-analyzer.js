import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function SubscriptionOverlapAnalyzer() {
  const router = useRouter();
  const [subscriptions, setSubscriptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [allServices, setAllServices] = useState([]);
  const [analysis, setAnalysis] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadSubscriptions(),
        loadAllServices(),
        loadAvailableServices()
      ]);
    } catch (error) {
      console.error('載入數據失敗:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubscriptions = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      if (result.success) {
        setSubscriptions(result.data);
      }
    } catch (error) {
      console.error('載入訂閱失敗:', error);
    }
  };

  const loadAllServices = async () => {
    try {
      const response = await fetch('/api/services/database');
      const result = await response.json();
      if (result.success) {
        setAllServices(result.data);
      }
    } catch (error) {
      console.error('載入服務失敗:', error);
    }
  };

  const loadAvailableServices = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/services/available', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      if (result.success) {
        setAvailableServices(result.data);
      }
    } catch (error) {
      console.error('載入可用服務失敗:', error);
    }
  };

  useEffect(() => {
    if (subscriptions.length > 0 && allServices.length > 0 && availableServices.length > 0) {
      analyzeOverlap();
    }
  }, [subscriptions, allServices, availableServices]);

  const analyzeOverlap = () => {
    // 分析各種服務 ID 集合
    const activeSubscriptionIds = subscriptions
      .filter(sub => sub.status === 'active')
      .map(sub => sub.service_id);

    const cancelledSubscriptionIds = subscriptions
      .filter(sub => sub.status === 'cancelled')
      .map(sub => sub.service_id);

    const expiredSubscriptionIds = subscriptions
      .filter(sub => sub.status === 'expired')
      .map(sub => sub.service_id);

    const availableServiceIds = availableServices.map(service => service.id);
    const allServiceIds = allServices.filter(s => s.status === 'active').map(s => s.id);

    // 檢查重複
    const activeVsAvailable = activeSubscriptionIds.filter(id => availableServiceIds.includes(id));
    const cancelledVsAvailable = cancelledSubscriptionIds.filter(id => availableServiceIds.includes(id));

    // 檢查數量關係
    const totalActiveServices = allServiceIds.length;
    const calculatedTotal = activeSubscriptionIds.length + availableServiceIds.length;

    // 詳細分析
    const serviceDetails = allServiceIds.map(serviceId => {
      const service = allServices.find(s => s.id === serviceId);
      const activeSubscription = subscriptions.find(sub => sub.service_id === serviceId && sub.status === 'active');
      const cancelledSubscription = subscriptions.find(sub => sub.service_id === serviceId && sub.status === 'cancelled');
      const isAvailable = availableServiceIds.includes(serviceId);

      return {
        id: serviceId,
        name: service?.name || 'Unknown',
        hasActiveSubscription: !!activeSubscription,
        hasCancelledSubscription: !!cancelledSubscription,
        isAvailable: isAvailable,
        subscriptionCount: subscriptions.filter(sub => sub.service_id === serviceId).length,
        subscriptions: subscriptions.filter(sub => sub.service_id === serviceId)
      };
    });

    // 找出問題服務
    const problemServices = serviceDetails.filter(service => 
      service.hasActiveSubscription && service.isAvailable
    );

    const duplicateSubscriptions = serviceDetails.filter(service => 
      service.subscriptionCount > 1
    );

    setAnalysis({
      counts: {
        totalActiveServices,
        activeSubscriptions: activeSubscriptionIds.length,
        availableServices: availableServiceIds.length,
        calculatedTotal,
        isBalanced: calculatedTotal === totalActiveServices
      },
      overlaps: {
        activeVsAvailable,
        cancelledVsAvailable
      },
      serviceDetails,
      problemServices,
      duplicateSubscriptions,
      serviceIds: {
        active: activeSubscriptionIds,
        cancelled: cancelledSubscriptionIds,
        expired: expiredSubscriptionIds,
        available: availableServiceIds,
        all: allServiceIds
      }
    });
  };

  const fixOverlapIssues = async () => {
    if (!analysis || analysis.problemServices.length === 0) {
      alert('沒有發現重複問題需要修復');
      return;
    }

    const confirmed = confirm(`發現 ${analysis.problemServices.length} 個服務同時出現在訂閱和可用列表中，是否修復？`);
    if (!confirmed) return;

    try {
      const token = localStorage.getItem('token');
      
      for (const service of analysis.problemServices) {
        console.log(`修復服務 ${service.name} (ID: ${service.id}) 的重複問題`);
        
        // 這裡可以添加具體的修復邏輯
        // 例如：重新計算可用服務列表
      }
      
      // 重新載入數據
      await loadAllData();
      alert('重複問題修復完成！');
      
    } catch (error) {
      console.error('修復失敗:', error);
      alert('修復失敗: ' + error.message);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>訂閱重複分析 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔍 訂閱重複分析</h1>
                <p className="text-gray-600 mt-2">分析訂閱服務與可用服務的重複問題</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
                >
                  <span>🏠</span>
                  <span>回儀表板</span>
                </button>
                <button
                  onClick={loadAllData}
                  disabled={isLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {isLoading ? '載入中...' : '🔄 重新分析'}
                </button>
              </div>
            </div>

            {analysis && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 數量分析 */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 數量分析</h2>
                  
                  <div className="space-y-4">
                    <div className={`p-4 border rounded-lg ${
                      analysis.counts.isBalanced ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                    }`}>
                      <h3 className={`font-medium mb-2 ${
                        analysis.counts.isBalanced ? 'text-green-900' : 'text-red-900'
                      }`}>
                        {analysis.counts.isBalanced ? '✅' : '❌'} 數量平衡檢查
                      </h3>
                      <div className={`text-sm space-y-1 ${
                        analysis.counts.isBalanced ? 'text-green-800' : 'text-red-800'
                      }`}>
                        <div>總活躍服務: {analysis.counts.totalActiveServices}</div>
                        <div>活躍訂閱: {analysis.counts.activeSubscriptions}</div>
                        <div>可用服務: {analysis.counts.availableServices}</div>
                        <div>計算總和: {analysis.counts.calculatedTotal}</div>
                        <div className="font-medium">
                          {analysis.counts.isBalanced ? '數量平衡正確' : '數量不平衡 - 有重複'}
                        </div>
                      </div>
                    </div>

                    {/* 重複檢查 */}
                    <div className={`p-4 border rounded-lg ${
                      analysis.overlaps.activeVsAvailable.length === 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                    }`}>
                      <h3 className={`font-medium mb-2 ${
                        analysis.overlaps.activeVsAvailable.length === 0 ? 'text-green-900' : 'text-red-900'
                      }`}>
                        {analysis.overlaps.activeVsAvailable.length === 0 ? '✅' : '❌'} 重複檢查
                      </h3>
                      <div className={`text-sm space-y-1 ${
                        analysis.overlaps.activeVsAvailable.length === 0 ? 'text-green-800' : 'text-red-800'
                      }`}>
                        <div>活躍訂閱 vs 可用服務: {
                          analysis.overlaps.activeVsAvailable.length === 0 
                            ? '無重複' 
                            : `重複 [${analysis.overlaps.activeVsAvailable.join(', ')}]`
                        }</div>
                        <div>已取消 vs 可用服務: {
                          analysis.overlaps.cancelledVsAvailable.length === 0 
                            ? '無重複' 
                            : `重複 [${analysis.overlaps.cancelledVsAvailable.join(', ')}] (正常)`
                        }</div>
                      </div>
                    </div>

                    {/* 修復按鈕 */}
                    {analysis.problemServices.length > 0 && (
                      <button
                        onClick={fixOverlapIssues}
                        className="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
                      >
                        🔧 修復重複問題 ({analysis.problemServices.length} 個)
                      </button>
                    )}
                  </div>
                </div>

                {/* 詳細分析 */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">🔍 詳細分析</h2>
                  
                  <div className="space-y-4">
                    {/* 問題服務 */}
                    {analysis.problemServices.length > 0 && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <h3 className="font-medium text-red-900 mb-2">🚨 問題服務</h3>
                        <div className="text-sm text-red-800 space-y-2">
                          {analysis.problemServices.map(service => (
                            <div key={service.id} className="p-2 bg-white rounded border">
                              <div className="font-medium">服務 {service.id}: {service.name}</div>
                              <div className="text-xs mt-1">
                                ❌ 同時出現在活躍訂閱和可用服務中
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 重複訂閱 */}
                    {analysis.duplicateSubscriptions.length > 0 && (
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h3 className="font-medium text-yellow-900 mb-2">⚠️ 重複訂閱</h3>
                        <div className="text-sm text-yellow-800 space-y-2">
                          {analysis.duplicateSubscriptions.map(service => (
                            <div key={service.id} className="p-2 bg-white rounded border">
                              <div className="font-medium">服務 {service.id}: {service.name}</div>
                              <div className="text-xs mt-1">
                                有 {service.subscriptionCount} 個訂閱記錄
                              </div>
                              <div className="text-xs mt-1">
                                {service.subscriptions.map(sub => (
                                  <span key={sub.id} className="mr-2">
                                    記錄{sub.id}({sub.status})
                                  </span>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 服務 ID 分布 */}
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg max-h-64 overflow-y-auto">
                      <h3 className="font-medium text-gray-900 mb-2">📋 服務 ID 分布</h3>
                      <div className="text-xs text-gray-700 space-y-2">
                        <div>
                          <strong>活躍訂閱:</strong>
                          <div className="font-mono bg-white p-1 rounded mt-1">
                            [{analysis.serviceIds.active.join(', ')}]
                          </div>
                        </div>
                        <div>
                          <strong>可用服務:</strong>
                          <div className="font-mono bg-white p-1 rounded mt-1">
                            [{analysis.serviceIds.available.join(', ')}]
                          </div>
                        </div>
                        <div>
                          <strong>已取消訂閱:</strong>
                          <div className="font-mono bg-white p-1 rounded mt-1">
                            [{analysis.serviceIds.cancelled.join(', ')}]
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📚 分析說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>正確的邏輯關係:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>活躍訂閱 + 可用服務 = 總活躍服務數</li>
                  <li>活躍訂閱與可用服務不應重複</li>
                  <li>已取消的服務可以重新出現在可用服務中</li>
                  <li>每個服務最多只應有一個訂閱記錄</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
