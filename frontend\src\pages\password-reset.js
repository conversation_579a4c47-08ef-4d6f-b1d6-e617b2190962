import { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function PasswordReset() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '<EMAIL>',
    newPassword: '123456',
    confirmPassword: '123456'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const resetPassword = async () => {
    if (formData.newPassword !== formData.confirmPassword) {
      addLog('❌ 密碼確認不匹配', 'error');
      return;
    }

    setIsLoading(true);
    setResult(null);
    addLog('🔄 開始重置密碼...', 'info');

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          newPassword: formData.newPassword
        })
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        addLog('✅ 密碼重置成功！', 'success');
        addLog(`👤 用戶: ${data.data.name}`, 'success');
        addLog(`📧 Email: ${data.data.email}`, 'success');
        setResult({ type: 'success', data });
      } else {
        addLog(`❌ 密碼重置失敗: ${data.error}`, 'error');
        setResult({ type: 'error', data });
      }
    } catch (error) {
      addLog(`❌ 請求失敗: ${error.message}`, 'error');
      setResult({ type: 'error', error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const testLogin = async () => {
    addLog('🔑 測試新密碼登入...', 'info');

    try {
      const response = await fetch('/api/auth/login-db', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.newPassword
        })
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        addLog('✅ 新密碼登入成功！', 'success');
        
        // 保存登入信息
        localStorage.setItem('token', data.data.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
        
        addLog('💾 登入信息已保存', 'success');
        addLog('🚀 準備跳轉到儀表板...', 'success');
        
        // 延遲跳轉
        setTimeout(() => {
          router.push('/dashboard-simple');
        }, 2000);
      } else {
        addLog(`❌ 新密碼登入失敗: ${data.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 登入測試失敗: ${error.message}`, 'error');
    }
  };

  const goToLogin = () => {
    router.push('/auth/login');
  };

  const clearLogs = () => {
    setLogs([]);
    setResult(null);
  };

  return (
    <>
      <Head>
        <title>密碼重置 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔐 密碼重置工具</h1>
              <p className="text-gray-600 mt-2">重置 <EMAIL> 的密碼</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 重置表單 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">密碼重置</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      readOnly
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">新密碼</label>
                    <input
                      type="password"
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">確認新密碼</label>
                    <input
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                {/* 操作按鈕 */}
                <div className="mt-6 space-y-3">
                  <button
                    onClick={resetPassword}
                    disabled={isLoading}
                    className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                  >
                    {isLoading ? '重置中...' : '🔐 重置密碼'}
                  </button>
                  
                  {result && result.type === 'success' && (
                    <button
                      onClick={testLogin}
                      className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                      🔑 測試新密碼登入
                    </button>
                  )}
                  
                  <button
                    onClick={goToLogin}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    🚀 前往登入頁面
                  </button>
                  
                  <button
                    onClick={clearLogs}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  >
                    🗑️ 清除日誌
                  </button>
                </div>

                {/* 結果顯示 */}
                {result && (
                  <div className={`mt-6 p-4 rounded-lg ${
                    result.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                  }`}>
                    <h3 className={`font-medium ${
                      result.type === 'success' ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {result.type === 'success' ? '✅ 重置成功' : '❌ 重置失敗'}
                    </h3>
                    <pre className={`mt-2 text-xs overflow-auto ${
                      result.type === 'success' ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {JSON.stringify(result.data || result.error, null, 2)}
                    </pre>
                  </div>
                )}
              </div>

              {/* 日誌顯示 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">操作日誌</h2>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                  {logs.map((log) => (
                    <div key={log.id} className={`mb-1 ${
                      log.type === 'error' ? 'text-red-400' : 
                      log.type === 'success' ? 'text-green-400' : 
                      'text-blue-400'
                    }`}>
                      [{log.timestamp}] {log.message}
                    </div>
                  ))}
                  {logs.length === 0 && (
                    <div className="text-gray-500">點擊上方按鈕開始操作...</div>
                  )}
                </div>
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🔍 問題說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>問題:</strong> <EMAIL> 無法使用密碼 123456 登入</p>
                <p><strong>原因:</strong> 註冊時使用的密碼可能是 1234，而不是 123456</p>
                <p><strong>解決方案:</strong> 將密碼重置為您想要的密碼 (123456)</p>
                <p><strong>操作流程:</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>點擊 "重置密碼" 將密碼改為 123456</li>
                  <li>點擊 "測試新密碼登入" 確認可以登入</li>
                  <li>點擊 "前往登入頁面" 正常登入系統</li>
                </ol>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">⚡ 快速解決</h3>
              <p className="text-sm text-yellow-800 mb-3">
                密碼已預設為 123456，直接點擊重置即可解決登入問題
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={resetPassword}
                  disabled={isLoading}
                  className="px-4 py-2 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700 disabled:opacity-50"
                >
                  🚀 一鍵重置密碼
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
