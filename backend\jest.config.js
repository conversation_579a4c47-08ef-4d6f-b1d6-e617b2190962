module.exports = {
  // 測試環境
  testEnvironment: 'node',
  
  // 測試文件匹配模式
  testMatch: [
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],
  
  // 覆蓋率收集
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/server.js',
    '!src/config/**',
    '!src/migrations/**',
    '!src/seeds/**'
  ],
  
  // 覆蓋率報告格式
  coverageReporters: [
    'text',
    'lcov',
    'html',
    'json'
  ],
  
  // 覆蓋率閾值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // 測試設置文件
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  
  // 測試超時時間
  testTimeout: 30000,
  
  // 清除模擬
  clearMocks: true,
  
  // 詳細輸出
  verbose: true,
  
  // 環境變數
  setupFiles: ['<rootDir>/tests/env.js']
};
