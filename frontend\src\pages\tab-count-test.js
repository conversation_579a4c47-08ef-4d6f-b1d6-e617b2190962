import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function TabCountTest() {
  const [subscriptions, setSubscriptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [allServices, setAllServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('active');

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setIsLoading(true);
    
    try {
      await Promise.all([
        loadSubscriptions(),
        loadAllServices(),
        loadAvailableServices()
      ]);
    } catch (error) {
      console.error('載入數據失敗:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubscriptions = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        // 使用正確的數據格式
        const formattedSubscriptions = result.data.map(sub => ({
          id: sub.id,
          service_id: sub.service_id,
          service_name: sub.service_name,
          name: sub.service_name,
          status: sub.status,
          subscribed_at: sub.subscribed_at
        }));
        
        setSubscriptions(formattedSubscriptions);
      }
    } catch (error) {
      console.error('載入訂閱失敗:', error);
    }
  };

  const loadAllServices = async () => {
    try {
      const response = await fetch('/api/services/database');
      const result = await response.json();

      if (result.success) {
        setAllServices(result.data);
      }
    } catch (error) {
      console.error('載入服務失敗:', error);
    }
  };

  const loadAvailableServices = async () => {
    try {
      // 模擬訂閱頁面的邏輯
      const activeServices = allServices.filter(service => service.status === 'active');
      const activeSubscribedServiceIds = subscriptions
        .filter(sub => sub.status === 'active')
        .map(sub => sub.service_id);
      
      const available = activeServices.filter(service =>
        !activeSubscribedServiceIds.includes(service.id)
      );

      setAvailableServices(available);
    } catch (error) {
      console.error('計算可用服務失敗:', error);
    }
  };

  // 重新計算可用服務當訂閱或服務變化時
  useEffect(() => {
    if (allServices.length > 0 && subscriptions.length >= 0) {
      loadAvailableServices();
    }
  }, [allServices, subscriptions]);

  // 計算各標籤的數量
  const activeSubscriptionsCount = subscriptions.filter(sub => sub.status === 'active').length;
  const expiredCancelledCount = subscriptions.filter(sub => sub.status === 'expired' || sub.status === 'cancelled').length;
  const availableServicesCount = availableServices.length;

  const filteredSubscriptions = subscriptions.filter(sub => {
    if (activeTab === 'active') return sub.status === 'active';
    if (activeTab === 'expired') return sub.status === 'expired' || sub.status === 'cancelled';
    return true;
  });

  const testCancelSubscription = async (subscriptionId) => {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/subscriptions/cancel', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription_id: subscriptionId
        })
      });

      const result = await response.json();

      if (result.success) {
        // 更新本地狀態
        const updatedSubscriptions = subscriptions.map(sub =>
          sub.id === subscriptionId
            ? { ...sub, status: 'cancelled' }
            : sub
        );
        setSubscriptions(updatedSubscriptions);
        
        alert(`✅ 已取消「${subscription.service_name}」的訂閱`);
      } else {
        alert(`❌ 取消失敗: ${result.error}`);
      }
    } catch (error) {
      alert(`❌ 取消失敗: ${error.message}`);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>標籤數量測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🏷️ 標籤數量測試</h1>
              <p className="text-gray-600 mt-2">測試訂閱頁面標籤中的數字同步</p>
            </div>

            {/* 標籤切換 (模擬訂閱頁面) */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">標籤切換 (帶數字)</h2>
              <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setActiveTab('active')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'active'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <span className="flex items-center justify-center space-x-2">
                    <span>使用中訂閱</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                      activeTab === 'active'
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {activeSubscriptionsCount}
                    </span>
                  </span>
                </button>
                <button
                  onClick={() => setActiveTab('expired')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'expired'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <span className="flex items-center justify-center space-x-2">
                    <span>已過期/已取消</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                      activeTab === 'expired'
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {expiredCancelledCount}
                    </span>
                  </span>
                </button>
                <button
                  onClick={() => setActiveTab('available')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'available'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <span className="flex items-center justify-center space-x-2">
                    <span>可用服務</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                      activeTab === 'available'
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {availableServicesCount}
                    </span>
                  </span>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 數量統計 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">數量統計</h2>
                
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 className="font-medium text-green-900 mb-2">🟢 使用中訂閱</h3>
                    <div className="text-2xl font-bold text-green-600">{activeSubscriptionsCount}</div>
                    <div className="text-sm text-green-700">活躍狀態的訂閱</div>
                  </div>

                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">⚫ 已過期/已取消</h3>
                    <div className="text-2xl font-bold text-gray-600">{expiredCancelledCount}</div>
                    <div className="text-sm text-gray-700">過期或取消的訂閱</div>
                  </div>

                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">🎯 可用服務</h3>
                    <div className="text-2xl font-bold text-blue-600">{availableServicesCount}</div>
                    <div className="text-sm text-blue-700">可以訂閱的服務</div>
                  </div>

                  <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                    <h3 className="font-medium text-purple-900 mb-2">📊 驗證</h3>
                    <div className="text-sm text-purple-800 space-y-1">
                      <div>總服務數: {allServices.filter(s => s.status === 'active').length}</div>
                      <div>計算: {activeSubscriptionsCount} + {availableServicesCount} = {activeSubscriptionsCount + availableServicesCount}</div>
                      <div className={activeSubscriptionsCount + availableServicesCount === allServices.filter(s => s.status === 'active').length ? 'text-green-600' : 'text-red-600'}>
                        {activeSubscriptionsCount + availableServicesCount === allServices.filter(s => s.status === 'active').length ? '✅ 數量正確' : '❌ 數量不匹配'}
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  onClick={loadAllData}
                  disabled={isLoading}
                  className="mt-4 w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  🔄 重新載入數據
                </button>
              </div>

              {/* 訂閱列表 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {activeTab === 'active' && '使用中訂閱'}
                  {activeTab === 'expired' && '已過期/已取消訂閱'}
                  {activeTab === 'available' && '可用服務'}
                </h2>
                
                {activeTab === 'available' ? (
                  <div className="space-y-3">
                    {availableServices.map((service) => (
                      <div key={service.id} className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
                        <h4 className="font-medium text-blue-900">{service.name}</h4>
                        <p className="text-sm text-blue-700">服務ID: {service.id} | 價格: ${service.price}</p>
                        <span className="inline-block mt-2 px-2 py-1 bg-blue-600 text-white rounded text-xs">
                          可訂閱
                        </span>
                      </div>
                    ))}
                    {availableServices.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        暫無可用服務
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredSubscriptions.map((subscription) => (
                      <div key={subscription.id} className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900">{subscription.service_name}</h4>
                            <p className="text-sm text-gray-600">
                              訂閱ID: {subscription.id} | 服務ID: {subscription.service_id}
                            </p>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                            {subscription.status}
                          </span>
                        </div>

                        {subscription.status === 'active' && (
                          <button
                            onClick={() => testCancelSubscription(subscription.id)}
                            className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                          >
                            🧪 測試取消
                          </button>
                        )}
                      </div>
                    ))}
                    {filteredSubscriptions.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        {activeTab === 'active' ? '暫無使用中的訂閱' : '暫無過期/已取消的訂閱'}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">🎯 測試目標</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>功能:</strong> 標籤中的數字應該實時同步更新</p>
                <p><strong>測試步驟:</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>觀察初始的標籤數字</li>
                  <li>取消一個活躍訂閱</li>
                  <li>檢查「使用中訂閱」數字是否減少</li>
                  <li>檢查「已過期/已取消」數字是否增加</li>
                  <li>檢查「可用服務」數字是否增加</li>
                </ol>
                <p><strong>預期結果:</strong> 所有標籤數字都應該正確同步更新</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
