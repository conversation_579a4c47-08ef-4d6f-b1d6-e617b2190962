const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const db = new sqlite3.Database(path.join(__dirname, 'lib/database/database.sqlite'));

console.log('檢查重複的訂閱記錄...');

// 檢查重複的會員-服務組合
db.all(`
  SELECT member_id, service_id, COUNT(*) as count, GROUP_CONCAT(id) as record_ids, GROUP_CONCAT(status) as statuses
  FROM member_services 
  GROUP BY member_id, service_id 
  HAVING COUNT(*) > 1
  ORDER BY member_id, service_id
`, [], (err, duplicates) => {
  if (err) {
    console.error('查詢失敗:', err);
    db.close();
    return;
  }

  if (duplicates.length > 0) {
    console.log('發現重複的會員-服務組合:');
    duplicates.forEach(dup => {
      console.log(`會員 ${dup.member_id} - 服務 ${dup.service_id}: ${dup.count} 個記錄 [${dup.record_ids}] 狀態 [${dup.statuses}]`);
    });
  } else {
    console.log('✅ 沒有發現重複的會員-服務組合');
  }

  // 顯示所有記錄
  db.all('SELECT * FROM member_services ORDER BY member_id, service_id, id', [], (err2, all) => {
    if (!err2) {
      console.log('\n所有訂閱記錄:');
      all.forEach(record => {
        console.log(`ID ${record.id}: 會員 ${record.member_id} - 服務 ${record.service_id} - 狀態 ${record.status}`);
      });
    }
    db.close();
  });
});
