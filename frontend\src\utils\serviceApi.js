// 服務管理 API 模擬
// 在實際項目中，這些應該是真實的 API 調用

// 模擬延遲
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 模擬服務數據存儲 (實際應該在後端數據庫)
let servicesData = [
  {
    id: 1,
    name: 'AI 文字生成服務',
    description: '強大的 AI 文字生成工具，支援多種語言和格式',
    price: 299,
    billingCycle: 'monthly',
    category: 'AI 服務',
    features: ['無限文字生成', '多語言支援', '模板庫', '24/7 客服'],
    status: 'active',
    popular: true,
    subscribers: 1250,
    revenue: 373750,
    maxUsers: 1,
    apiLimit: 50000,
    storageLimit: '10GB',
    supportLevel: 'premium',
    createdAt: '2025-01-15',
    updatedAt: '2025-06-20'
  },
  {
    id: 2,
    name: '圖片處理服務',
    description: '專業的圖片編輯和處理工具',
    price: 199,
    billingCycle: 'monthly',
    category: '媒體處理',
    features: ['批量處理', '格式轉換', '智能壓縮', '雲端存儲'],
    status: 'active',
    popular: false,
    subscribers: 890,
    revenue: 177110,
    maxUsers: 3,
    apiLimit: 1000,
    storageLimit: '50GB',
    supportLevel: 'standard',
    createdAt: '2025-02-01',
    updatedAt: '2025-06-18'
  },
  {
    id: 3,
    name: '數據分析服務',
    description: '深度數據分析和可視化工具',
    price: 499,
    billingCycle: 'monthly',
    category: '數據分析',
    features: ['實時分析', '自定義報表', '數據可視化', 'API 接入'],
    status: 'inactive',
    popular: false,
    subscribers: 320,
    revenue: 159680,
    maxUsers: 5,
    apiLimit: 100,
    storageLimit: '100GB',
    supportLevel: 'premium',
    createdAt: '2025-03-10',
    updatedAt: '2025-06-15'
  },
  {
    id: 4,
    name: '語音轉文字服務',
    description: '高精度語音識別和轉換服務',
    price: 149,
    billingCycle: 'monthly',
    category: 'AI 服務',
    features: ['多語言識別', '實時轉換', '批量處理', '高精度識別'],
    status: 'active',
    popular: false,
    subscribers: 560,
    revenue: 83440,
    maxUsers: 1,
    apiLimit: 10000,
    storageLimit: '5GB',
    supportLevel: 'basic',
    createdAt: '2025-04-05',
    updatedAt: '2025-06-22'
  },
  {
    id: 5,
    name: '雲端存儲服務',
    description: '安全可靠的雲端存儲解決方案',
    price: 99,
    billingCycle: 'monthly',
    category: '存儲服務',
    features: ['無限存儲', '自動備份', '版本控制', '分享功能'],
    status: 'active',
    popular: true,
    subscribers: 2100,
    revenue: 207900,
    maxUsers: 10,
    apiLimit: 0,
    storageLimit: '無限',
    supportLevel: 'standard',
    createdAt: '2025-01-20',
    updatedAt: '2025-06-25'
  },
  {
    id: 6,
    name: '企業級 API 服務',
    description: '為企業提供的高級 API 接入服務',
    price: 999,
    billingCycle: 'monthly',
    category: '企業服務',
    features: ['無限 API 調用', '專屬支援', '自定義功能', 'SLA 保證'],
    status: 'active',
    popular: false,
    subscribers: 85,
    revenue: 84915,
    maxUsers: 50,
    apiLimit: 0,
    storageLimit: '1TB',
    supportLevel: 'enterprise',
    createdAt: '2025-05-01',
    updatedAt: '2025-06-24'
  }
];

// 獲取所有服務
export const getAllServices = async () => {
  await delay(500); // 模擬網路延遲
  return {
    success: true,
    data: servicesData
  };
};

// 根據 ID 獲取服務
export const getServiceById = async (id) => {
  await delay(300);
  const service = servicesData.find(s => s.id === parseInt(id));
  if (service) {
    return {
      success: true,
      data: service
    };
  } else {
    return {
      success: false,
      error: '服務不存在'
    };
  }
};

// 創建新服務
export const createService = async (serviceData) => {
  await delay(800);
  
  const newService = {
    id: Math.max(...servicesData.map(s => s.id)) + 1,
    ...serviceData,
    subscribers: 0,
    revenue: 0,
    createdAt: new Date().toISOString().split('T')[0],
    updatedAt: new Date().toISOString().split('T')[0]
  };
  
  servicesData.push(newService);
  
  return {
    success: true,
    data: newService,
    message: '服務創建成功'
  };
};

// 更新服務
export const updateService = async (id, serviceData) => {
  await delay(600);
  
  const index = servicesData.findIndex(s => s.id === parseInt(id));
  if (index !== -1) {
    servicesData[index] = {
      ...servicesData[index],
      ...serviceData,
      updatedAt: new Date().toISOString().split('T')[0]
    };
    
    return {
      success: true,
      data: servicesData[index],
      message: '服務更新成功'
    };
  } else {
    return {
      success: false,
      error: '服務不存在'
    };
  }
};

// 刪除服務
export const deleteService = async (id) => {
  await delay(400);
  
  const index = servicesData.findIndex(s => s.id === parseInt(id));
  if (index !== -1) {
    const deletedService = servicesData.splice(index, 1)[0];
    return {
      success: true,
      data: deletedService,
      message: '服務刪除成功'
    };
  } else {
    return {
      success: false,
      error: '服務不存在'
    };
  }
};

// 批量更新服務狀態
export const batchUpdateServiceStatus = async (serviceIds, status) => {
  await delay(700);
  
  const updatedServices = [];
  serviceIds.forEach(id => {
    const index = servicesData.findIndex(s => s.id === parseInt(id));
    if (index !== -1) {
      servicesData[index].status = status;
      servicesData[index].updatedAt = new Date().toISOString().split('T')[0];
      updatedServices.push(servicesData[index]);
    }
  });
  
  return {
    success: true,
    data: updatedServices,
    message: `${updatedServices.length} 個服務狀態已更新`
  };
};

// 獲取服務統計
export const getServiceStats = async () => {
  await delay(400);
  
  const stats = {
    total: servicesData.length,
    active: servicesData.filter(s => s.status === 'active').length,
    inactive: servicesData.filter(s => s.status === 'inactive').length,
    draft: servicesData.filter(s => s.status === 'draft').length,
    totalSubscribers: servicesData.reduce((sum, s) => sum + s.subscribers, 0),
    totalRevenue: servicesData.reduce((sum, s) => sum + s.revenue, 0),
    avgPrice: servicesData.reduce((sum, s) => sum + s.price, 0) / servicesData.length,
    mostPopular: servicesData.reduce((prev, current) => 
      (prev.subscribers > current.subscribers) ? prev : current
    ),
    categories: [...new Set(servicesData.map(s => s.category))],
    recentlyUpdated: servicesData
      .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
      .slice(0, 5)
  };
  
  return {
    success: true,
    data: stats
  };
};

// 搜索服務
export const searchServices = async (query, filters = {}) => {
  await delay(300);
  
  let filteredServices = servicesData;
  
  // 文字搜索
  if (query) {
    const searchTerm = query.toLowerCase();
    filteredServices = filteredServices.filter(service =>
      service.name.toLowerCase().includes(searchTerm) ||
      service.description.toLowerCase().includes(searchTerm) ||
      service.category.toLowerCase().includes(searchTerm)
    );
  }
  
  // 狀態篩選
  if (filters.status && filters.status !== 'all') {
    filteredServices = filteredServices.filter(service => service.status === filters.status);
  }
  
  // 類別篩選
  if (filters.category) {
    filteredServices = filteredServices.filter(service => service.category === filters.category);
  }
  
  // 價格範圍篩選
  if (filters.minPrice !== undefined) {
    filteredServices = filteredServices.filter(service => service.price >= filters.minPrice);
  }
  
  if (filters.maxPrice !== undefined) {
    filteredServices = filteredServices.filter(service => service.price <= filters.maxPrice);
  }
  
  // 排序
  if (filters.sortBy) {
    switch (filters.sortBy) {
      case 'name':
        filteredServices.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'price':
        filteredServices.sort((a, b) => a.price - b.price);
        break;
      case 'subscribers':
        filteredServices.sort((a, b) => b.subscribers - a.subscribers);
        break;
      case 'revenue':
        filteredServices.sort((a, b) => b.revenue - a.revenue);
        break;
      case 'updated':
        filteredServices.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
        break;
      default:
        break;
    }
  }
  
  return {
    success: true,
    data: filteredServices,
    total: filteredServices.length
  };
};

// 導出服務數據
export const exportServices = async (format = 'json') => {
  await delay(1000);
  
  const exportData = {
    exportDate: new Date().toISOString(),
    totalServices: servicesData.length,
    services: servicesData
  };
  
  let content, mimeType, fileName;
  
  switch (format) {
    case 'csv':
      const csvHeaders = 'ID,名稱,描述,價格,計費週期,類別,狀態,訂閱數,收入,創建時間,更新時間\n';
      const csvRows = servicesData.map(service => 
        `${service.id},"${service.name}","${service.description}",${service.price},${service.billingCycle},"${service.category}",${service.status},${service.subscribers},${service.revenue},${service.createdAt},${service.updatedAt}`
      ).join('\n');
      content = csvHeaders + csvRows;
      mimeType = 'text/csv';
      fileName = `services_${new Date().toISOString().split('T')[0]}.csv`;
      break;
      
    case 'json':
    default:
      content = JSON.stringify(exportData, null, 2);
      mimeType = 'application/json';
      fileName = `services_${new Date().toISOString().split('T')[0]}.json`;
      break;
  }
  
  return {
    success: true,
    data: {
      content,
      mimeType,
      fileName
    }
  };
};
