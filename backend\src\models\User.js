const { query, transaction } = require('../config/database');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

class User {
  constructor(data) {
    this.id = data.id;
    this.username = data.username;
    this.email = data.email;
    this.phone = data.phone;
    this.password_hash = data.password_hash;
    this.status = data.status;
    this.email_verified = data.email_verified;
    this.phone_verified = data.phone_verified;
    this.two_factor_enabled = data.two_factor_enabled;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    this.last_login_at = data.last_login_at;
    this.login_attempts = data.login_attempts;
    this.locked_until = data.locked_until;
  }

  // 創建新用戶
  static async create(userData) {
    const {
      username,
      email,
      phone,
      password,
      provider = 'local',
      provider_id = null,
      profile = {}
    } = userData;

    return await transaction(async (client) => {
      // 檢查用戶名是否已存在
      if (username) {
        const existingUsername = await client.query(
          'SELECT id FROM users WHERE username = $1',
          [username]
        );
        if (existingUsername.rows.length > 0) {
          throw new Error('用戶名已存在');
        }
      }

      // 檢查郵件是否已存在
      if (email) {
        const existingEmail = await client.query(
          'SELECT id FROM users WHERE email = $1',
          [email]
        );
        if (existingEmail.rows.length > 0) {
          throw new Error('郵件地址已存在');
        }
      }

      // 檢查手機號碼是否已存在
      if (phone) {
        const existingPhone = await client.query(
          'SELECT id FROM users WHERE phone = $1',
          [phone]
        );
        if (existingPhone.rows.length > 0) {
          throw new Error('手機號碼已存在');
        }
      }

      // 加密密碼
      let password_hash = null;
      if (password) {
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
        password_hash = await bcrypt.hash(password, saltRounds);
      }

      // 創建用戶
      const userResult = await client.query(
        `INSERT INTO users (username, email, phone, password_hash, status)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [username, email, phone, password_hash, 'active']
      );

      const user = userResult.rows[0];

      // 創建用戶資料
      await client.query(
        `INSERT INTO user_profiles (user_id, first_name, last_name, display_name, language, currency)
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          user.id,
          profile.first_name || null,
          profile.last_name || null,
          profile.display_name || username,
          profile.language || 'zh-TW',
          profile.currency || 'TWD'
        ]
      );

      // 如果是第三方登入，創建認證記錄
      if (provider !== 'local') {
        await client.query(
          `INSERT INTO user_auth (user_id, provider, provider_id)
           VALUES ($1, $2, $3)`,
          [user.id, provider, provider_id]
        );
      }

      // 創建預設錢包
      await client.query(
        `INSERT INTO wallets (user_id, currency, balance)
         VALUES ($1, $2, $3)`,
        [user.id, profile.currency || 'TWD', 0.00]
      );

      // 創建通知偏好設定
      await client.query(
        `INSERT INTO notification_preferences (user_id)
         VALUES ($1)`,
        [user.id]
      );

      return new User(user);
    });
  }

  // 根據ID查找用戶
  static async findById(id) {
    const result = await query(
      `SELECT u.*, up.first_name, up.last_name, up.display_name, up.avatar_url,
              up.birth_date, up.gender, up.country, up.timezone, up.language, up.currency
       FROM users u
       LEFT JOIN user_profiles up ON u.id = up.user_id
       WHERE u.id = $1 AND u.status != 'deleted'`,
      [id]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return new User(result.rows[0]);
  }

  // 根據郵件查找用戶
  static async findByEmail(email) {
    const result = await query(
      `SELECT u.*, up.first_name, up.last_name, up.display_name, up.avatar_url,
              up.birth_date, up.gender, up.country, up.timezone, up.language, up.currency
       FROM users u
       LEFT JOIN user_profiles up ON u.id = up.user_id
       WHERE u.email = $1 AND u.status != 'deleted'`,
      [email]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return new User(result.rows[0]);
  }

  // 根據用戶名查找用戶
  static async findByUsername(username) {
    const result = await query(
      `SELECT u.*, up.first_name, up.last_name, up.display_name, up.avatar_url,
              up.birth_date, up.gender, up.country, up.timezone, up.language, up.currency
       FROM users u
       LEFT JOIN user_profiles up ON u.id = up.user_id
       WHERE u.username = $1 AND u.status != 'deleted'`,
      [username]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return new User(result.rows[0]);
  }