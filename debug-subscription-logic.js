// 訂閱邏輯調試腳本
const https = require('https');
const http = require('http');

// 簡單的 fetch 實現
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

const API_BASE = 'http://localhost:3000';
const FRONTEND_BASE = 'http://localhost:3001';

// 測試用戶憑證
const testCredentials = {
  email: '<EMAIL>',
  password: '1234'
};

let authToken = null;

async function login() {
  console.log('🔐 正在登入...');
  
  try {
    const response = await fetch(`${API_BASE}/api/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testCredentials)
    });

    const result = await response.json();
    
    if (result.success && result.data.token) {
      authToken = result.data.token;
      console.log('✅ 登入成功');
      return true;
    } else {
      console.error('❌ 登入失敗:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ 登入請求失敗:', error.message);
    return false;
  }
}

async function testSubscriptionAPI() {
  console.log('\n📋 測試訂閱 API...');
  
  try {
    const response = await fetch(`${FRONTEND_BASE}/api/subscriptions/simple`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();
    console.log('📊 訂閱 API 響應:', JSON.stringify(result, null, 2));
    
    return result.data || [];
  } catch (error) {
    console.error('❌ 訂閱 API 失敗:', error.message);
    return [];
  }
}

async function testServicesAPI() {
  console.log('\n🛠️ 測試服務 API...');
  
  try {
    const response = await fetch(`${FRONTEND_BASE}/api/services/database`);
    const result = await response.json();
    console.log('🔧 服務 API 響應:', JSON.stringify(result, null, 2));
    
    return result.data || [];
  } catch (error) {
    console.error('❌ 服務 API 失敗:', error.message);
    return [];
  }
}

async function testSubscribeService(serviceId) {
  console.log(`\n➕ 測試訂閱服務 ${serviceId}...`);
  
  try {
    const response = await fetch(`${FRONTEND_BASE}/api/subscriptions/simple`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        service_id: serviceId
      })
    });

    const result = await response.json();
    console.log('📝 訂閱響應:', JSON.stringify(result, null, 2));
    
    return result.success;
  } catch (error) {
    console.error('❌ 訂閱失敗:', error.message);
    return false;
  }
}

async function testCancelSubscription(subscriptionId) {
  console.log(`\n❌ 測試取消訂閱 ${subscriptionId}...`);
  
  try {
    const response = await fetch(`${FRONTEND_BASE}/api/subscriptions/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        subscription_id: subscriptionId
      })
    });

    const result = await response.json();
    console.log('🗑️ 取消響應:', JSON.stringify(result, null, 2));
    
    return result.success;
  } catch (error) {
    console.error('❌ 取消失敗:', error.message);
    return false;
  }
}

async function analyzeLogic() {
  console.log('\n🔍 分析訂閱邏輯...');
  
  // 獲取所有服務
  const allServices = await testServicesAPI();
  const activeServices = allServices.filter(s => s.status === 'active');
  
  // 獲取當前訂閱
  const subscriptions = await testSubscriptionAPI();
  const activeSubscriptions = subscriptions.filter(s => s.status === 'active');
  
  // 計算可用服務
  const activeSubscribedServiceIds = activeSubscriptions.map(s => s.service_id);
  const availableServices = activeServices.filter(s => 
    !activeSubscribedServiceIds.includes(s.id)
  );
  
  console.log('\n📊 邏輯分析結果:');
  console.log(`  總活躍服務: ${activeServices.length}`);
  console.log(`  活躍訂閱數: ${activeSubscriptions.length}`);
  console.log(`  可用服務數: ${availableServices.length}`);
  console.log(`  計算驗證: ${activeSubscriptions.length} + ${availableServices.length} = ${activeSubscriptions.length + availableServices.length} (應該等於 ${activeServices.length})`);
  
  if (activeSubscriptions.length + availableServices.length === activeServices.length) {
    console.log('✅ 邏輯正確');
  } else {
    console.log('❌ 邏輯錯誤');
  }
  
  console.log('\n📋 活躍訂閱詳情:');
  activeSubscriptions.forEach(sub => {
    console.log(`  - ${sub.service_name} (ID: ${sub.service_id}, 訂閱ID: ${sub.id})`);
  });
  
  console.log('\n🎯 可用服務詳情:');
  availableServices.forEach(service => {
    console.log(`  - ${service.name} (ID: ${service.id})`);
  });
  
  return { allServices, subscriptions, activeServices, activeSubscriptions, availableServices };
}

async function testFullWorkflow() {
  console.log('🚀 開始完整測試流程...');
  
  // 登入
  if (!await login()) {
    console.error('❌ 登入失敗，終止測試');
    return;
  }
  
  // 分析初始狀態
  console.log('\n=== 初始狀態 ===');
  const initialState = await analyzeLogic();
  
  // 如果有可用服務，測試訂閱
  if (initialState.availableServices.length > 0) {
    const serviceToSubscribe = initialState.availableServices[0];
    console.log(`\n=== 測試訂閱服務: ${serviceToSubscribe.name} ===`);
    
    const subscribeSuccess = await testSubscribeService(serviceToSubscribe.id);
    
    if (subscribeSuccess) {
      console.log('\n=== 訂閱後狀態 ===');
      const afterSubscribeState = await analyzeLogic();
      
      // 找到新創建的訂閱
      const newSubscription = afterSubscribeState.activeSubscriptions.find(sub => 
        sub.service_id === serviceToSubscribe.id
      );
      
      if (newSubscription) {
        console.log(`\n=== 測試取消訂閱: ${newSubscription.service_name} ===`);
        const cancelSuccess = await testCancelSubscription(newSubscription.id);
        
        if (cancelSuccess) {
          console.log('\n=== 取消後狀態 ===');
          await analyzeLogic();
        }
      }
    }
  } else {
    console.log('⚠️ 沒有可用服務可供測試');
  }
  
  console.log('\n🏁 測試完成');
}

// 執行測試
testFullWorkflow().catch(console.error);
