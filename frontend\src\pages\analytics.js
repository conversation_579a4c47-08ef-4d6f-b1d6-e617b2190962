import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, LineC<PERSON>, PieChart, ProgressRing, MiniTrendChart } from '../components/SimpleChart';

export default function Analytics() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('7d');
  const [analyticsData, setAnalyticsData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // 模擬分析數據
  const mockAnalyticsData = {
    overview: {
      totalSpending: 2847.50,
      totalTransactions: 24,
      activeSubscriptions: 3,
      avgMonthlySpending: 949.17,
      spendingGrowth: 12.5,
      transactionGrowth: 8.3,
      subscriptionGrowth: 50.0,
      avgGrowth: 15.2
    },
    spending: {
      daily: [
        { date: '2025-06-20', amount: 299.00, transactions: 1 },
        { date: '2025-06-21', amount: 149.00, transactions: 1 },
        { date: '2025-06-22', amount: 0, transactions: 0 },
        { date: '2025-06-23', amount: 500.00, transactions: 1 },
        { date: '2025-06-24', amount: 199.00, transactions: 1 },
        { date: '2025-06-25', amount: 299.00, transactions: 1 },
        { date: '2025-06-26', amount: 500.00, transactions: 1 }
      ],
      categories: [
        { name: 'AI 服務', amount: 897.00, percentage: 31.5, color: 'bg-blue-500' },
        { name: '圖片處理', amount: 597.00, percentage: 21.0, color: 'bg-green-500' },
        { name: '數據分析', amount: 499.00, percentage: 17.5, color: 'bg-purple-500' },
        { name: '錢包充值', amount: 500.00, percentage: 17.6, color: 'bg-yellow-500' },
        { name: '其他', amount: 354.50, percentage: 12.4, color: 'bg-gray-500' }
      ]
    },
    subscriptions: {
      usage: [
        {
          name: 'AI 文字生成服務',
          used: 15000,
          limit: 50000,
          percentage: 30,
          trend: '+15%',
          color: 'bg-blue-500'
        },
        {
          name: '圖片處理服務',
          used: 250,
          limit: 1000,
          percentage: 25,
          trend: '+8%',
          color: 'bg-green-500'
        },
        {
          name: '數據分析服務',
          used: 0,
          limit: 100,
          percentage: 0,
          trend: '已過期',
          color: 'bg-gray-400'
        }
      ],
      monthly: [
        { month: '2025-04', subscriptions: 1, spending: 299 },
        { month: '2025-05', subscriptions: 2, spending: 498 },
        { month: '2025-06', subscriptions: 3, spending: 797 }
      ]
    },
    wallet: {
      balance: 1250.50,
      transactions: [
        { date: '2025-06-26', type: 'income', amount: 500, description: '信用卡充值' },
        { date: '2025-06-25', type: 'expense', amount: 299, description: 'AI 文字生成服務' },
        { date: '2025-06-24', type: 'expense', amount: 199, description: '圖片處理服務' },
        { date: '2025-06-23', type: 'income', amount: 1000, description: '銀行轉帳充值' },
        { date: '2025-06-22', type: 'expense', amount: 300, description: '提現到銀行帳戶' }
      ],
      monthlyFlow: [
        { month: '2025-04', income: 1000, expense: 299, net: 701 },
        { month: '2025-05', income: 1500, expense: 498, net: 1002 },
        { month: '2025-06', income: 1500, expense: 797, net: 703 }
      ]
    }
  };

  useEffect(() => {
    // 檢查認證
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=' + encodeURIComponent('/analytics'));
      return;
    }

    // 載入用戶數據
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    // 載入分析數據
    setAnalyticsData(mockAnalyticsData);
    setIsLoading(false);
  }, [router]);

  const formatCurrency = (amount) => {
    return `NT$ ${amount.toLocaleString()}`;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-TW', { month: 'short', day: 'numeric' });
  };

  const getGrowthColor = (growth) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getGrowthIcon = (growth) => {
    if (growth > 0) return '↗️';
    if (growth < 0) return '↘️';
    return '➡️';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入分析數據中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>數據分析 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回儀表板
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">數據分析</h1>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* 導出按鈕 */}
                <button
                  onClick={() => router.push('/analytics/export')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                >
                  📊 導出數據
                </button>

                {/* 時間範圍選擇 */}
                <select
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value)}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="7d">最近 7 天</option>
                  <option value="30d">最近 30 天</option>
                  <option value="90d">最近 90 天</option>
                  <option value="1y">最近 1 年</option>
                </select>
                <span className="text-sm text-gray-600">歡迎，{user?.name}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 概覽統計 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            >
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">總消費金額</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(analyticsData.overview.totalSpending)}
                    </p>
                  </div>
                  <div className={`text-sm ${getGrowthColor(analyticsData.overview.spendingGrowth)}`}>
                    {getGrowthIcon(analyticsData.overview.spendingGrowth)} {analyticsData.overview.spendingGrowth}%
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">交易次數</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analyticsData.overview.totalTransactions}
                    </p>
                  </div>
                  <div className={`text-sm ${getGrowthColor(analyticsData.overview.transactionGrowth)}`}>
                    {getGrowthIcon(analyticsData.overview.transactionGrowth)} {analyticsData.overview.transactionGrowth}%
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">活躍訂閱</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analyticsData.overview.activeSubscriptions}
                    </p>
                  </div>
                  <div className={`text-sm ${getGrowthColor(analyticsData.overview.subscriptionGrowth)}`}>
                    {getGrowthIcon(analyticsData.overview.subscriptionGrowth)} {analyticsData.overview.subscriptionGrowth}%
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">月均消費</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(analyticsData.overview.avgMonthlySpending)}
                    </p>
                  </div>
                  <div className={`text-sm ${getGrowthColor(analyticsData.overview.avgGrowth)}`}>
                    {getGrowthIcon(analyticsData.overview.avgGrowth)} {analyticsData.overview.avgGrowth}%
                  </div>
                </div>
              </div>
            </motion.div>

            {/* 標籤切換 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'overview'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  總覽分析
                </button>
                <button
                  onClick={() => setActiveTab('spending')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'spending'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  消費分析
                </button>
                <button
                  onClick={() => setActiveTab('subscriptions')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'subscriptions'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  訂閱分析
                </button>
                <button
                  onClick={() => setActiveTab('wallet')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'wallet'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  錢包分析
                </button>
              </div>
            </div>

            {/* 動態內容區域 */}
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4 }}
              className="space-y-8"
            >
              {/* 總覽分析 */}
              {activeTab === 'overview' && (
                <>
                  {/* 每日消費趨勢 */}
                  <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-6">每日消費趨勢</h3>
                    <div className="space-y-4">
                      {analyticsData.spending.daily.map((day, index) => (
                        <div key={day.date} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-4">
                            <div className="text-sm text-gray-500 w-16">
                              {formatDate(day.date)}
                            </div>
                            <div className="flex-1">
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                                  style={{ width: `${Math.min((day.amount / 500) * 100, 100)}%` }}
                                ></div>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-gray-900">
                              {formatCurrency(day.amount)}
                            </div>
                            <div className="text-sm text-gray-500">
                              {day.transactions} 筆交易
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 消費類別分布 */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div className="bg-white rounded-xl shadow-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">消費類別分布</h3>
                      <div className="space-y-4">
                        {analyticsData.spending.categories.map((category, index) => (
                          <div key={category.name} className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-gray-700">{category.name}</span>
                              <span className="text-sm text-gray-500">{category.percentage}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-3">
                              <motion.div
                                initial={{ width: 0 }}
                                animate={{ width: `${category.percentage}%` }}
                                transition={{ duration: 1, delay: index * 0.1 }}
                                className={`${category.color} h-3 rounded-full`}
                              ></motion.div>
                            </div>
                            <div className="text-right">
                              <span className="text-sm font-semibold text-gray-900">
                                {formatCurrency(category.amount)}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 圓餅圖 */}
                    <div className="bg-white rounded-xl shadow-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">消費比例圖</h3>
                      <div className="flex justify-center">
                        <PieChart
                          data={analyticsData.spending.categories.map(cat => ({
                            name: cat.name,
                            value: cat.amount,
                            color: cat.color.replace('bg-', '#').replace('500', '').replace('blue', '3B82F6').replace('green', '10B981').replace('purple', '8B5CF6').replace('yellow', 'F59E0B').replace('gray', '6B7280')
                          }))}
                          size={250}
                        />
                      </div>
                    </div>
                  </div>

                  {/* 每日消費條形圖 */}
                  <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-6">每日消費條形圖</h3>
                    <BarChart
                      data={analyticsData.spending.daily.map(day => ({
                        label: formatDate(day.date),
                        value: day.amount
                      }))}
                      height={250}
                      color="bg-gradient-to-t from-blue-400 to-blue-600"
                    />
                  </div>
                </>
              )}

              {/* 消費分析 */}
              {activeTab === 'spending' && (
                <>
                  {/* 消費統計 */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div className="bg-white rounded-xl shadow-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">消費統計</h3>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center p-4 bg-blue-50 rounded-lg">
                          <span className="text-blue-700 font-medium">最高單日消費</span>
                          <span className="text-blue-900 font-bold">NT$ 500</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-green-50 rounded-lg">
                          <span className="text-green-700 font-medium">平均每日消費</span>
                          <span className="text-green-900 font-bold">NT$ 278</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-purple-50 rounded-lg">
                          <span className="text-purple-700 font-medium">最常用服務</span>
                          <span className="text-purple-900 font-bold">AI 服務</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-orange-50 rounded-lg">
                          <span className="text-orange-700 font-medium">消費頻率</span>
                          <span className="text-orange-900 font-bold">每週 3.4 次</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">消費建議</h3>
                      <div className="space-y-4">
                        <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded-r-lg">
                          <h4 className="font-medium text-yellow-800 mb-2">💡 節省建議</h4>
                          <p className="text-yellow-700 text-sm">
                            您的 AI 服務使用量較高，建議升級到更高級的方案以獲得更好的性價比。
                          </p>
                        </div>
                        <div className="p-4 bg-green-50 border-l-4 border-green-400 rounded-r-lg">
                          <h4 className="font-medium text-green-800 mb-2">📈 使用優化</h4>
                          <p className="text-green-700 text-sm">
                            圖片處理服務使用率僅 25%，您可以考慮降級方案或更充分利用現有額度。
                          </p>
                        </div>
                        <div className="p-4 bg-blue-50 border-l-4 border-blue-400 rounded-r-lg">
                          <h4 className="font-medium text-blue-800 mb-2">🎯 推薦服務</h4>
                          <p className="text-blue-700 text-sm">
                            基於您的使用模式，語音轉文字服務可能對您很有幫助。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* 訂閱分析 */}
              {activeTab === 'subscriptions' && (
                <>
                  {/* 訂閱使用量 */}
                  <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-6">訂閱使用量分析</h3>

                    {/* 進度環展示 */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                      {analyticsData.subscriptions.usage.map((subscription, index) => (
                        <div key={subscription.name} className="text-center">
                          <ProgressRing
                            percentage={subscription.percentage}
                            size={120}
                            color={subscription.color.replace('bg-', '#').replace('500', '').replace('blue', '3B82F6').replace('green', '10B981').replace('gray', '6B7280')}
                          />
                          <h4 className="font-medium text-gray-900 mt-4 mb-2">{subscription.name}</h4>
                          <p className="text-sm text-gray-600">
                            {subscription.used.toLocaleString()} / {subscription.limit.toLocaleString()}
                          </p>
                          <span className={`inline-block text-xs px-2 py-1 rounded-full mt-2 ${
                            subscription.trend === '已過期'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-green-100 text-green-800'
                          }`}>
                            {subscription.trend}
                          </span>
                        </div>
                      ))}
                    </div>

                    {/* 詳細列表 */}
                    <div className="space-y-4">
                      {analyticsData.subscriptions.usage.map((subscription, index) => (
                        <div key={subscription.name} className="p-4 border border-gray-200 rounded-lg">
                          <div className="flex justify-between items-center mb-3">
                            <h4 className="font-medium text-gray-900">{subscription.name}</h4>
                            <span className={`text-sm px-2 py-1 rounded-full ${
                              subscription.trend === '已過期'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {subscription.trend}
                            </span>
                          </div>

                          <div className="mb-3">
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                              <span>已使用: {subscription.used.toLocaleString()}</span>
                              <span>總額度: {subscription.limit.toLocaleString()}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-3">
                              <motion.div
                                initial={{ width: 0 }}
                                animate={{ width: `${subscription.percentage}%` }}
                                transition={{ duration: 1, delay: index * 0.2 }}
                                className={`${subscription.color} h-3 rounded-full`}
                              ></motion.div>
                            </div>
                          </div>

                          <div className="flex justify-between items-center text-sm">
                            <span className="text-gray-600">使用率: {subscription.percentage}%</span>
                            <span className={`font-medium ${
                              subscription.percentage > 80 ? 'text-red-600' :
                              subscription.percentage > 50 ? 'text-yellow-600' : 'text-green-600'
                            }`}>
                              {subscription.percentage > 80 ? '使用量偏高' :
                               subscription.percentage > 50 ? '使用量適中' : '使用量較低'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 訂閱趨勢 */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div className="bg-white rounded-xl shadow-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">訂閱數量趨勢</h3>
                      <div className="space-y-4">
                        {analyticsData.subscriptions.monthly.map((month, index) => (
                          <div key={month.month} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-4">
                              <div className="text-sm text-gray-500 w-16">
                                {month.month.slice(-2)}月
                              </div>
                              <div className="flex space-x-1">
                                {Array.from({ length: month.subscriptions }, (_, i) => (
                                  <div key={i} className="w-3 h-3 bg-blue-500 rounded-full"></div>
                                ))}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold text-gray-900">
                                {month.subscriptions} 個訂閱
                              </div>
                              <div className="text-sm text-gray-500">
                                {formatCurrency(month.spending)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">訂閱建議</h3>
                      <div className="space-y-4">
                        <div className="p-4 bg-blue-50 border-l-4 border-blue-400 rounded-r-lg">
                          <h4 className="font-medium text-blue-800 mb-2">🎯 使用優化</h4>
                          <p className="text-blue-700 text-sm">
                            AI 文字生成服務使用率 30%，建議更充分利用現有額度。
                          </p>
                        </div>
                        <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded-r-lg">
                          <h4 className="font-medium text-yellow-800 mb-2">⚠️ 注意事項</h4>
                          <p className="text-yellow-700 text-sm">
                            數據分析服務已過期，如需繼續使用請及時續費。
                          </p>
                        </div>
                        <div className="p-4 bg-green-50 border-l-4 border-green-400 rounded-r-lg">
                          <h4 className="font-medium text-green-800 mb-2">💰 節省建議</h4>
                          <p className="text-green-700 text-sm">
                            圖片處理服務使用率較低，可考慮降級到基礎方案。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* 錢包分析 */}
              {activeTab === 'wallet' && (
                <>
                  {/* 錢包概覽 */}
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white">
                      <h3 className="text-lg font-semibold mb-4">當前餘額</h3>
                      <p className="text-3xl font-bold mb-2">
                        {formatCurrency(analyticsData.wallet.balance)}
                      </p>
                      <p className="text-blue-100 text-sm">
                        較上月增長 15.2%
                      </p>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">交易統計</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">總交易數</span>
                          <span className="font-semibold">{analyticsData.wallet.transactions.length}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">收入交易</span>
                          <span className="font-semibold text-green-600">
                            {analyticsData.wallet.transactions.filter(t => t.type === 'income').length}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">支出交易</span>
                          <span className="font-semibold text-red-600">
                            {analyticsData.wallet.transactions.filter(t => t.type === 'expense').length}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">月度資金流</h3>
                      <div className="space-y-3">
                        {analyticsData.wallet.monthlyFlow.slice(-1).map((month) => (
                          <div key={month.month}>
                            <div className="flex justify-between text-sm">
                              <span className="text-green-600">收入</span>
                              <span className="font-semibold text-green-600">
                                +{formatCurrency(month.income)}
                              </span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-red-600">支出</span>
                              <span className="font-semibold text-red-600">
                                -{formatCurrency(month.expense)}
                              </span>
                            </div>
                            <div className="border-t pt-2 mt-2">
                              <div className="flex justify-between">
                                <span className="text-gray-600">淨流入</span>
                                <span className={`font-semibold ${month.net > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {month.net > 0 ? '+' : ''}{formatCurrency(month.net)}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* 最近交易 */}
                  <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-6">最近交易記錄</h3>
                    <div className="space-y-4">
                      {analyticsData.wallet.transactions.map((transaction, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-4">
                            <div className={`w-3 h-3 rounded-full ${
                              transaction.type === 'income' ? 'bg-green-500' : 'bg-red-500'
                            }`}></div>
                            <div>
                              <h4 className="font-medium text-gray-900">{transaction.description}</h4>
                              <p className="text-sm text-gray-500">{formatDate(transaction.date)}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`font-semibold ${
                              transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                            </div>
                            <div className="text-sm text-gray-500 capitalize">
                              {transaction.type === 'income' ? '收入' : '支出'}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </motion.div>
          </div>
        </main>
      </div>
    </>
  );
}
