const express = require('express');
const WalletController = require('../controllers/WalletController');
const { authenticateToken } = require('../middleware/authMiddleware');
const router = express.Router();

// 獲取用戶錢包列表
router.get('/', authenticateToken, WalletController.getUserWallets);

// 獲取錢包詳情
router.get('/:walletId', authenticateToken, WalletController.getWalletDetails);

// 獲取錢包交易記錄
router.get('/:walletId/transactions', authenticateToken, WalletController.getWalletTransactions);

// 錢包轉帳
router.post('/transfer', authenticateToken, WalletController.transferFunds);

module.exports = router;