# GitHub 設置指南

本指南將幫助您設置 GitHub 倉庫並配置自動化 CI/CD 流程。

## 📋 前置準備

1. **GitHub 帳號** - 確保您有 GitHub 帳號
2. **Git 安裝** - 確保本地已安裝 Git
3. **專案代碼** - 確保專案代碼已準備完成

## 🚀 快速設置

### 1. 運行自動設置腳本

```bash
# 給腳本執行權限 (Linux/Mac)
chmod +x scripts/setup-git.sh

# 運行設置腳本
./scripts/setup-git.sh
```

### 2. 在 GitHub 創建倉庫

1. 登入 GitHub
2. 點擊右上角的 "+" → "New repository"
3. 填寫倉庫資訊:
   - **Repository name**: `member-management-system`
   - **Description**: `功能完整的會員管理系統，支援多元支付和訂閱服務`
   - **Visibility**: 選擇 Public 或 Private
   - **不要** 勾選 "Initialize this repository with a README"

### 3. 連接本地倉庫到 GitHub

```bash
# 添加遠端倉庫
git remote add origin https://github.com/YOUR_USERNAME/member-management-system.git

# 推送代碼
git push -u origin main
```

## 🔧 詳細配置

### 分支策略

我們使用 Git Flow 分支策略:

- **main** - 生產環境分支
- **develop** - 開發環境分支
- **feature/** - 功能開發分支
- **hotfix/** - 緊急修復分支

```bash
# 創建並切換到 develop 分支
git checkout -b develop
git push -u origin develop

# 設置 develop 為預設分支 (在 GitHub 設置中)
```

### GitHub Secrets 配置

在 GitHub 倉庫設置中添加以下 Secrets:

#### 資料庫配置
```
DB_HOST=your-database-host
DB_PORT=5432
DB_NAME=member_system
DB_USER=your-db-user
DB_PASSWORD=your-db-password
```

#### 加密配置
```
ENCRYPTION_KEY=your-super-secret-encryption-key
ENCRYPTION_SALT=your-encryption-salt
HMAC_SECRET=your-hmac-secret
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-jwt-refresh-secret
```

#### 支付配置
```
STRIPE_SECRET_KEY=sk_live_your_stripe_secret
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
ECPAY_MERCHANT_ID=your_ecpay_merchant_id
ECPAY_HASH_KEY=your_ecpay_hash_key
ECPAY_HASH_IV=your_ecpay_hash_iv
```

#### 其他配置
```
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
```

### 分支保護規則

為 `main` 和 `develop` 分支設置保護規則:

1. 進入 GitHub 倉庫 → Settings → Branches
2. 點擊 "Add rule"
3. 設置以下規則:
   - **Branch name pattern**: `main` 或 `develop`
   - ✅ **Require pull request reviews before merging**
   - ✅ **Require status checks to pass before merging**
   - ✅ **Require branches to be up to date before merging**
   - ✅ **Include administrators**

## 🔄 CI/CD 工作流程

### GitHub Actions 工作流程

我們的 CI/CD 流程包含以下階段:

1. **測試階段** (所有分支)
   - 代碼風格檢查
   - 單元測試
   - 整合測試
   - E2E 測試
   - 安全審計

2. **部署階段**
   - **develop 分支** → Staging 環境
   - **main 分支** → Production 環境

### 觸發條件

- **Push 到 main/develop** - 觸發完整 CI/CD
- **Pull Request** - 觸發測試流程
- **手動觸發** - 可在 Actions 頁面手動運行

## 📊 監控和通知

### 狀態徽章

在 README.md 中添加狀態徽章:

```markdown
![CI/CD](https://github.com/YOUR_USERNAME/member-management-system/workflows/CI/CD%20Pipeline/badge.svg)
![Tests](https://github.com/YOUR_USERNAME/member-management-system/workflows/Tests/badge.svg)
![Security](https://github.com/YOUR_USERNAME/member-management-system/workflows/Security/badge.svg)
```

### 通知設置

1. **Email 通知** - GitHub 會自動發送構建結果
2. **Slack 通知** - 可在 Actions 中配置 Slack webhook
3. **Teams 通知** - 可配置 Microsoft Teams 通知

## 🚀 自動部署

### 使用部署腳本

```bash
# 部署到 staging (develop 分支)
./scripts/deploy.sh staging "新增支付功能"

# 部署到 production (main 分支)
./scripts/deploy.sh production "v1.0.0 正式發布"
```

### 手動推送

```bash
# 推送到 develop 分支
git checkout develop
git add .
git commit -m "feat: 新增功能"
git push origin develop

# 推送到 main 分支 (通過 PR)
git checkout main
git merge develop
git push origin main
```

## 🔒 安全最佳實踐

### 1. Secrets 管理
- 永不在代碼中硬編碼敏感資訊
- 使用 GitHub Secrets 存儲敏感配置
- 定期輪換密鑰和 Token

### 2. 分支保護
- 啟用分支保護規則
- 要求 PR 審查
- 要求狀態檢查通過

### 3. 依賴安全
- 啟用 Dependabot 安全更新
- 定期運行 `npm audit`
- 監控安全漏洞

### 4. 代碼掃描
- 啟用 GitHub Code Scanning
- 使用 CodeQL 分析
- 集成第三方安全工具

## 📚 相關文檔

- [GitHub Actions 文檔](https://docs.github.com/en/actions)
- [Git Flow 工作流程](https://nvie.com/posts/a-successful-git-branching-model/)
- [語義化版本控制](https://semver.org/)
- [常規提交格式](https://www.conventionalcommits.org/)

## 🆘 故障排除

### 常見問題

1. **推送被拒絕**
   ```bash
   git pull origin main --rebase
   git push origin main
   ```

2. **測試失敗**
   ```bash
   npm run test:ci
   # 檢查錯誤並修復
   ```

3. **權限問題**
   - 檢查 GitHub Token 權限
   - 確認倉庫協作者權限

4. **環境變數問題**
   - 檢查 GitHub Secrets 配置
   - 確認變數名稱正確

### 聯絡支援

如果遇到問題，請:
1. 檢查 GitHub Actions 日誌
2. 查看相關文檔
3. 在 Issues 中提問
4. 聯絡維護團隊

---

🎉 **恭喜！** 您已成功設置 GitHub 倉庫和 CI/CD 流程！
