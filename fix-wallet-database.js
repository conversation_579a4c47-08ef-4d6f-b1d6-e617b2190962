// 修復錢包資料庫結構
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 資料庫路徑
const dbPath = path.join(__dirname, 'frontend', 'lib', 'database', 'database.sqlite');

console.log('🔧 開始修復錢包資料庫結構...');
console.log('📁 資料庫路徑:', dbPath);

if (!fs.existsSync(dbPath)) {
  console.error('❌ 資料庫文件不存在:', dbPath);
  process.exit(1);
}

const db = new sqlite3.Database(dbPath);

// 檢查和修復資料庫結構
async function fixWalletDatabase() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n1️⃣ 檢查 members 表結構...');
      
      // 檢查 members 表的欄位
      db.all("PRAGMA table_info(members)", (err, columns) => {
        if (err) {
          console.error('❌ 檢查表結構失敗:', err);
          reject(err);
          return;
        }

        console.log('📋 當前 members 表欄位:');
        columns.forEach(col => {
          console.log(`   - ${col.name} (${col.type})`);
        });

        // 檢查是否有 wallet_balance 欄位
        const hasWalletBalance = columns.some(col => col.name === 'wallet_balance');
        
        if (hasWalletBalance) {
          console.log('✅ wallet_balance 欄位已存在');
          
          // 檢查現有數據
          checkExistingData();
        } else {
          console.log('❌ wallet_balance 欄位不存在，開始添加...');
          
          // 添加 wallet_balance 欄位
          db.run(`
            ALTER TABLE members 
            ADD COLUMN wallet_balance DECIMAL(15,2) DEFAULT 0.00
          `, (err) => {
            if (err) {
              console.error('❌ 添加 wallet_balance 欄位失敗:', err);
              reject(err);
              return;
            }
            
            console.log('✅ wallet_balance 欄位添加成功');
            checkExistingData();
          });
        }
      });

      function checkExistingData() {
        console.log('\n2️⃣ 檢查現有會員數據...');
        
        db.all("SELECT id, name, email, wallet_balance FROM members", (err, members) => {
          if (err) {
            console.error('❌ 查詢會員數據失敗:', err);
            reject(err);
            return;
          }

          console.log(`📊 找到 ${members.length} 個會員:`);
          members.forEach(member => {
            console.log(`   - ID: ${member.id}, 姓名: ${member.name}, 餘額: $${member.wallet_balance || 0}`);
          });

          // 初始化空的錢包餘額
          initializeWalletBalances(members);
        });
      }

      function initializeWalletBalances(members) {
        console.log('\n3️⃣ 初始化錢包餘額...');
        
        const membersToUpdate = members.filter(m => !m.wallet_balance || m.wallet_balance === 0);
        
        if (membersToUpdate.length === 0) {
          console.log('✅ 所有會員都已有錢包餘額');
          createWalletTransactionsTable();
          return;
        }

        console.log(`🔄 需要初始化 ${membersToUpdate.length} 個會員的錢包餘額`);
        
        let completed = 0;
        membersToUpdate.forEach(member => {
          // 給每個會員初始化 1000 元
          const initialBalance = 1000.00;
          
          db.run(
            "UPDATE members SET wallet_balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            [initialBalance, member.id],
            function(err) {
              if (err) {
                console.error(`❌ 初始化會員 ${member.name} 錢包失敗:`, err);
              } else {
                console.log(`✅ 會員 ${member.name} 錢包餘額初始化為 $${initialBalance}`);
              }
              
              completed++;
              if (completed === membersToUpdate.length) {
                createWalletTransactionsTable();
              }
            }
          );
        });
      }

      function createWalletTransactionsTable() {
        console.log('\n4️⃣ 檢查/創建錢包交易記錄表...');
        
        db.run(`
          CREATE TABLE IF NOT EXISTS wallet_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            member_id INTEGER NOT NULL,
            transaction_type TEXT NOT NULL CHECK(transaction_type IN ('recharge', 'withdraw', 'payment', 'refund', 'bonus', 'penalty')),
            amount DECIMAL(15,2) NOT NULL,
            balance_before DECIMAL(15,2) NOT NULL,
            balance_after DECIMAL(15,2) NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'completed' CHECK(status IN ('pending', 'completed', 'failed', 'cancelled')),
            payment_method TEXT,
            payment_reference TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members(id)
          )
        `, (err) => {
          if (err) {
            console.error('❌ 創建錢包交易表失敗:', err);
            reject(err);
            return;
          }
          
          console.log('✅ 錢包交易記錄表已準備就緒');
          
          // 創建索引
          createIndexes();
        });
      }

      function createIndexes() {
        console.log('\n5️⃣ 創建索引...');
        
        const indexes = [
          "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_member ON wallet_transactions(member_id)",
          "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_type ON wallet_transactions(transaction_type)",
          "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_status ON wallet_transactions(status)",
          "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_date ON wallet_transactions(created_at)"
        ];

        let indexCompleted = 0;
        indexes.forEach((indexSql, i) => {
          db.run(indexSql, (err) => {
            if (err) {
              console.error(`❌ 創建索引 ${i + 1} 失敗:`, err);
            } else {
              console.log(`✅ 索引 ${i + 1} 創建成功`);
            }
            
            indexCompleted++;
            if (indexCompleted === indexes.length) {
              finalCheck();
            }
          });
        });
      }

      function finalCheck() {
        console.log('\n6️⃣ 最終驗證...');
        
        db.get("SELECT COUNT(*) as count FROM members WHERE wallet_balance > 0", (err, result) => {
          if (err) {
            console.error('❌ 最終驗證失敗:', err);
            reject(err);
            return;
          }
          
          console.log(`✅ 有 ${result.count} 個會員擁有錢包餘額`);
          
          // 測試錢包查詢
          db.get("SELECT id, name, wallet_balance FROM members WHERE id = 1", (err, admin) => {
            if (err) {
              console.error('❌ 測試查詢失敗:', err);
              reject(err);
              return;
            }
            
            if (admin) {
              console.log(`✅ 管理員錢包餘額: $${admin.wallet_balance}`);
            }
            
            console.log('\n🎉 錢包資料庫修復完成！');
            resolve();
          });
        });
      }
    });
  });
}

// 執行修復
fixWalletDatabase()
  .then(() => {
    console.log('\n✅ 所有修復操作完成');
    db.close();
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 修復失敗:', error);
    db.close();
    process.exit(1);
  });
