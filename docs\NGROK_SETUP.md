# ngrok 域名配置指南

本指南將幫助您配置 ngrok 域名 `https://topyun.ngrok.app/` 來替代 `localhost:3001`。

## 🌐 ngrok 配置

### 前置要求

1. **安裝 ngrok**
   ```bash
   # macOS (使用 Homebrew)
   brew install ngrok/ngrok/ngrok
   
   # Windows (使用 Chocolatey)
   choco install ngrok
   
   # 或直接下載
   # https://ngrok.com/download
   ```

2. **註冊 ngrok 帳戶**
   - 訪問 https://ngrok.com/
   - 註冊免費帳戶
   - 獲取認證令牌

3. **配置認證令牌**
   ```bash
   ngrok authtoken YOUR_AUTH_TOKEN
   ```

### 快速啟動

#### 方法一：使用 npm 腳本
```bash
# 開發模式
cd frontend
npm run dev:ngrok

# 在另一個終端啟動 ngrok
npm run ngrok
```

#### 方法二：使用配置文件
```bash
# 使用 ngrok.yml 配置文件
ngrok start frontend --config=ngrok.yml
```

#### 方法三：使用腳本
```bash
# Linux/macOS
chmod +x scripts/start-ngrok.sh
./scripts/start-ngrok.sh

# Windows
scripts\start-ngrok.bat
```

## 📝 配置文件說明

### 1. 前端環境變數 (`frontend/.env.local`)
```env
# API 配置
NEXT_PUBLIC_API_URL=https://topyun.ngrok.app/api/v1

# 應用配置
NEXT_PUBLIC_APP_NAME=會員系統
NEXT_PUBLIC_APP_URL=https://topyun.ngrok.app
```

### 2. 後端環境變數 (`backend/.env`)
```env
# 前端配置
FRONTEND_URL=https://topyun.ngrok.app
CORS_ORIGINS=https://topyun.ngrok.app,http://localhost:3001,http://localhost:3000

# OAuth 回調 URL
GOOGLE_CALLBACK_URL=https://topyun.ngrok.app/api/v1/auth/google/callback
FACEBOOK_CALLBACK_URL=https://topyun.ngrok.app/api/v1/auth/facebook/callback
LINE_CALLBACK_URL=https://topyun.ngrok.app/api/v1/auth/line/callback
```

### 3. Next.js 配置 (`frontend/next.config.js`)
```javascript
module.exports = {
  env: {
    API_BASE_URL: process.env.API_BASE_URL || 'https://topyun.ngrok.app',
  },
  
  images: {
    domains: ['localhost', 'topyun.ngrok.app', 'example.com'],
  },
  
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `https://topyun.ngrok.app/api/:path*`,
      },
    ];
  },
};
```

## 🚀 啟動步驟

### 1. 啟動後端服務
```bash
cd backend
npm run dev
# 後端運行在 http://localhost:3000
```

### 2. 啟動前端服務
```bash
cd frontend
npm run dev:ngrok
# 前端運行在 http://0.0.0.0:3001 (允許外部訪問)
```

### 3. 啟動 ngrok 隧道
```bash
# 方法一：直接命令
ngrok http 3001 --hostname=topyun.ngrok.app

# 方法二：使用配置文件
ngrok start frontend --config=ngrok.yml

# 方法三：使用 npm 腳本
cd frontend
npm run ngrok
```

### 4. 驗證配置
- 訪問 https://topyun.ngrok.app
- 檢查所有功能是否正常
- 查看 ngrok 控制台：http://localhost:4040

## 🔧 故障排除

### 常見問題

1. **域名無法訪問**
   ```bash
   # 檢查 ngrok 狀態
   curl -s http://localhost:4040/api/tunnels | jq
   
   # 重新啟動 ngrok
   pkill ngrok
   ngrok http 3001 --hostname=topyun.ngrok.app
   ```

2. **CORS 錯誤**
   - 確保後端 `.env` 文件中的 `CORS_ORIGINS` 包含 ngrok 域名
   - 重新啟動後端服務

3. **API 請求失敗**
   - 檢查前端 `.env.local` 中的 `NEXT_PUBLIC_API_URL`
   - 確保 API 路由配置正確

4. **圖片載入失敗**
   - 檢查 `next.config.js` 中的 `images.domains` 配置
   - 確保包含 `topyun.ngrok.app`

### 調試工具

1. **ngrok 控制台**
   - 訪問：http://localhost:4040
   - 查看請求日誌和隧道狀態

2. **瀏覽器開發者工具**
   - 檢查網路請求
   - 查看控制台錯誤

3. **服務器日誌**
   ```bash
   # 前端日誌
   cd frontend && npm run dev:ngrok
   
   # 後端日誌
   cd backend && npm run dev
   ```

## 🔒 安全注意事項

1. **認證令牌保護**
   - 不要將 ngrok 認證令牌提交到版本控制
   - 使用環境變數或配置文件

2. **域名驗證**
   - 確保使用的是您申請的域名
   - 定期檢查域名狀態

3. **HTTPS 強制**
   - ngrok 自動提供 HTTPS
   - 確保所有請求都使用 HTTPS

## 📊 監控和日誌

### ngrok 日誌
```bash
# 查看 ngrok 日誌
tail -f ngrok.log

# 實時監控隧道狀態
watch -n 5 'curl -s http://localhost:4040/api/tunnels | jq'
```

### 應用日誌
```bash
# 前端日誌
cd frontend && npm run dev:ngrok 2>&1 | tee frontend.log

# 後端日誌
cd backend && npm run dev 2>&1 | tee backend.log
```

## 🎯 最佳實踐

1. **開發環境**
   - 使用 `dev:ngrok` 腳本啟動前端
   - 保持 ngrok 隧道穩定運行

2. **測試環境**
   - 定期測試所有功能
   - 檢查跨域請求

3. **生產準備**
   - 配置正確的環境變數
   - 測試所有 OAuth 回調

## 📞 支援

如果遇到問題：
1. 檢查 ngrok 官方文檔：https://ngrok.com/docs
2. 查看項目 GitHub Issues
3. 聯繫技術支援

---

**更新日期**: 2025-06-26  
**版本**: v1.0.0  
**維護者**: MemberService 開發團隊
