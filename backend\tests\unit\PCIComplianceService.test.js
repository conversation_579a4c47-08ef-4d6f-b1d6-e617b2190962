const PCIComplianceService = require('../../src/services/PCIComplianceService');

describe('PCIComplianceService', () => {
  describe('credit card validation', () => {
    test('should validate valid credit card numbers', () => {
      // Visa test numbers
      expect(PCIComplianceService.validateCreditCardNumber('****************')).toBe(true);
      expect(PCIComplianceService.validateCreditCardNumber('****************')).toBe(true);
      
      // Mastercard test numbers
      expect(PCIComplianceService.validateCreditCardNumber('****************')).toBe(true);
      expect(PCIComplianceService.validateCreditCardNumber('****************')).toBe(true);
      
      // American Express test numbers
      expect(PCIComplianceService.validateCreditCardNumber('***************')).toBe(true);
      expect(PCIComplianceService.validateCreditCardNumber('***************')).toBe(true);
      
      // Discover test numbers
      expect(PCIComplianceService.validateCreditCardNumber('****************')).toBe(true);
      expect(PCIComplianceService.validateCreditCardNumber('****************')).toBe(true);
    });

    test('should reject invalid credit card numbers', () => {
      expect(PCIComplianceService.validateCreditCardNumber('****************')).toBe(false); // Invalid Luhn
      expect(PCIComplianceService.validateCreditCardNumber('1234567890123456')).toBe(false); // Invalid format
      expect(PCIComplianceService.validateCreditCardNumber('***************')).toBe(false);  // Too short
      expect(PCIComplianceService.validateCreditCardNumber('****************1')).toBe(false); // Too long
      expect(PCIComplianceService.validateCreditCardNumber('')).toBe(false);
      expect(PCIComplianceService.validateCreditCardNumber(null)).toBe(false);
    });

    test('should handle formatted card numbers', () => {
      expect(PCIComplianceService.validateCreditCardNumber('4111-1111-1111-1111')).toBe(true);
      expect(PCIComplianceService.validateCreditCardNumber('4111 1111 1111 1111')).toBe(true);
      expect(PCIComplianceService.validateCreditCardNumber('4111.1111.1111.1111')).toBe(true);
    });
  });

  describe('card type identification', () => {
    test('should identify Visa cards', () => {
      expect(PCIComplianceService.identifyCardType('****************')).toBe('visa');
      expect(PCIComplianceService.identifyCardType('****************')).toBe('visa');
      expect(PCIComplianceService.identifyCardType('***************')).toBe('visa'); // 15 digits
    });

    test('should identify Mastercard cards', () => {
      expect(PCIComplianceService.identifyCardType('****************')).toBe('mastercard');
      expect(PCIComplianceService.identifyCardType('****************')).toBe('mastercard');
      expect(PCIComplianceService.identifyCardType('2223003122003222')).toBe('mastercard'); // New range
    });

    test('should identify American Express cards', () => {
      expect(PCIComplianceService.identifyCardType('***************')).toBe('amex');
      expect(PCIComplianceService.identifyCardType('***************')).toBe('amex');
    });

    test('should identify Discover cards', () => {
      expect(PCIComplianceService.identifyCardType('****************')).toBe('discover');
      expect(PCIComplianceService.identifyCardType('****************')).toBe('discover');
    });

    test('should return unknown for unrecognized cards', () => {
      expect(PCIComplianceService.identifyCardType('1234567890123456')).toBe('unknown');
      expect(PCIComplianceService.identifyCardType('9999999999999999')).toBe('unknown');
    });
  });

  describe('CVV validation', () => {
    test('should validate CVV for different card types', () => {
      // 3-digit CVV for most cards
      expect(PCIComplianceService.validateCVV('123', 'visa')).toBe(true);
      expect(PCIComplianceService.validateCVV('456', 'mastercard')).toBe(true);
      expect(PCIComplianceService.validateCVV('789', 'discover')).toBe(true);
      
      // 4-digit CVV for American Express
      expect(PCIComplianceService.validateCVV('1234', 'amex')).toBe(true);
    });

    test('should reject invalid CVV', () => {
      // Wrong length for card type
      expect(PCIComplianceService.validateCVV('12', 'visa')).toBe(false);
      expect(PCIComplianceService.validateCVV('1234', 'visa')).toBe(false);
      expect(PCIComplianceService.validateCVV('123', 'amex')).toBe(false);
      expect(PCIComplianceService.validateCVV('12345', 'amex')).toBe(false);
      
      // Non-numeric
      expect(PCIComplianceService.validateCVV('abc', 'visa')).toBe(false);
      expect(PCIComplianceService.validateCVV('', 'visa')).toBe(false);
      expect(PCIComplianceService.validateCVV(null, 'visa')).toBe(false);
    });
  });

  describe('expiry date validation', () => {
    test('should validate future expiry dates', () => {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      
      // Future month this year
      if (currentMonth < 12) {
        expect(PCIComplianceService.validateExpiryDate(currentMonth + 1, currentYear)).toBe(true);
      }
      
      // Future year
      expect(PCIComplianceService.validateExpiryDate(12, currentYear + 1)).toBe(true);
      expect(PCIComplianceService.validateExpiryDate(1, currentYear + 5)).toBe(true);
    });

    test('should reject past expiry dates', () => {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      
      // Past year
      expect(PCIComplianceService.validateExpiryDate(12, currentYear - 1)).toBe(false);
      
      // Past month this year
      if (currentMonth > 1) {
        expect(PCIComplianceService.validateExpiryDate(currentMonth - 1, currentYear)).toBe(false);
      }
    });

    test('should reject invalid month/year values', () => {
      const currentYear = new Date().getFullYear();
      
      // Invalid months
      expect(PCIComplianceService.validateExpiryDate(0, currentYear + 1)).toBe(false);
      expect(PCIComplianceService.validateExpiryDate(13, currentYear + 1)).toBe(false);
      expect(PCIComplianceService.validateExpiryDate(-1, currentYear + 1)).toBe(false);
      
      // Too far in future
      expect(PCIComplianceService.validateExpiryDate(12, currentYear + 15)).toBe(false);
    });

    test('should handle current month/year', () => {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      
      // Current month should be valid
      expect(PCIComplianceService.validateExpiryDate(currentMonth, currentYear)).toBe(true);
    });
  });

  describe('secure card data storage', () => {
    // Mock database query function
    const mockQuery = jest.fn();
    
    beforeEach(() => {
      mockQuery.mockClear();
      // Mock successful database operations
      mockQuery.mockResolvedValue({
        rows: [{ card_token: 'test_token_123', card_type: 'visa' }]
      });
      
      // Replace the actual query function
      const originalQuery = require('../../src/config/database').query;
      require('../../src/config/database').query = mockQuery;
    });

    test('should store valid card data securely', async () => {
      const userId = 1;
      const cardData = {
        cardNumber: '****************',
        cvv: '123',
        expiryMonth: 12,
        expiryYear: 2025,
        cardholderName: 'John Doe',
        billingAddress: {
          street: '123 Main St',
          city: 'Taipei',
          country: 'TW'
        }
      };

      const result = await PCIComplianceService.secureStoreCardData(userId, cardData);
      
      expect(result).toBeDefined();
      expect(result.cardToken).toBeDefined();
      expect(result.cardType).toBe('visa');
      expect(result.maskedCardNumber).toBe('************1111');
      expect(mockQuery).toHaveBeenCalled();
    });

    test('should reject invalid card data', async () => {
      const userId = 1;
      const invalidCardData = {
        cardNumber: '1234567890123456', // Invalid Luhn
        cvv: '123',
        expiryMonth: 12,
        expiryYear: 2025,
        cardholderName: 'John Doe'
      };

      await expect(PCIComplianceService.secureStoreCardData(userId, invalidCardData))
        .rejects.toThrow('無效的信用卡號碼');
    });

    test('should reject unsupported card types', async () => {
      const userId = 1;
      const unsupportedCardData = {
        cardNumber: '1234567890123452', // Unknown card type but valid Luhn
        cvv: '123',
        expiryMonth: 12,
        expiryYear: 2025,
        cardholderName: 'John Doe'
      };

      // First make it pass Luhn validation
      const validLuhnNumber = '1234567890123456'; // This won't pass Luhn, need a real test number
      // Use a real test number that's not in supported types
      const dinersClubNumber = '30569309025904'; // Diners Club test number
      
      unsupportedCardData.cardNumber = dinersClubNumber;
      
      await expect(PCIComplianceService.secureStoreCardData(userId, unsupportedCardData))
        .rejects.toThrow('不支援的信用卡類型');
    });
  });
});
