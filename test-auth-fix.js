// 測試認證修復
const https = require('https');
const http = require('http');

// 簡單的 fetch 實現
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };
    
    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testAuthFlow() {
  console.log('🔐 測試認證流程...');
  
  try {
    // 1. 登入獲取 token
    console.log('1️⃣ 登入...');
    const loginResponse = await fetch('http://localhost:3000/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '1234'
      })
    });

    const loginResult = await loginResponse.json();
    console.log('登入響應:', JSON.stringify(loginResult, null, 2));

    if (!loginResult.success) {
      console.error('❌ 登入失敗');
      return;
    }

    const token = loginResult.data.token;
    console.log('✅ 登入成功，獲得 token');

    // 2. 解析 token 查看 payload
    const tokenParts = token.split('.');
    const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
    console.log('Token payload:', JSON.stringify(payload, null, 2));

    // 3. 測試訂閱 API
    console.log('\n2️⃣ 測試訂閱 API...');
    const subscriptionResponse = await fetch('http://localhost:3001/api/subscriptions/simple', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const subscriptionResult = await subscriptionResponse.json();
    console.log('訂閱 API 響應:', JSON.stringify(subscriptionResult, null, 2));

    if (subscriptionResult.success) {
      console.log('✅ 訂閱 API 認證成功！');
      
      // 4. 測試訂閱服務
      console.log('\n3️⃣ 測試訂閱服務...');
      const servicesResponse = await fetch('http://localhost:3001/api/services/database');
      const servicesResult = await servicesResponse.json();
      
      if (servicesResult.success && servicesResult.data.length > 0) {
        const firstService = servicesResult.data.find(s => s.status === 'active');
        if (firstService) {
          console.log(`嘗試訂閱服務: ${firstService.name} (ID: ${firstService.id})`);
          
          const subscribeResponse = await fetch('http://localhost:3001/api/subscriptions/simple', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              service_id: firstService.id
            })
          });

          const subscribeResult = await subscribeResponse.json();
          console.log('訂閱服務響應:', JSON.stringify(subscribeResult, null, 2));
          
          if (subscribeResult.success) {
            console.log('✅ 訂閱服務成功！認證問題已修復！');
          } else {
            console.log('❌ 訂閱服務失敗:', subscribeResult.error);
          }
        }
      }
    } else {
      console.log('❌ 訂閱 API 認證失敗:', subscriptionResult.error);
    }

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testAuthFlow();
