import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletApiDebug() {
  const router = useRouter();
  const [debugResults, setDebugResults] = useState([]);
  const [isDebugging, setIsDebugging] = useState(false);
  const [rawResponses, setRawResponses] = useState({});

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const debugWalletApi = async () => {
    setIsDebugging(true);
    setDebugResults([]);
    setRawResponses({});

    try {
      addResult('開始', 'info', '開始調試錢包 API');

      // 檢查認證
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (!token || !userStr) {
        addResult('認證', 'error', '未找到認證數據');
        return;
      }

      let user;
      try {
        user = JSON.parse(userStr);
        addResult('認證', 'success', `用戶: ${user.name} (ID: ${user.userId})`);
      } catch (error) {
        addResult('認證', 'error', '用戶數據解析失敗');
        return;
      }

      // 步驟 1: 直接查詢資料庫中的會員數據
      addResult('步驟1', 'info', '直接查詢資料庫中的會員數據...');
      
      try {
        const dbResponse = await fetch('/api/admin/database-query', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            query: "SELECT id, name, email, wallet_balance FROM members WHERE id = ?",
            params: [user.userId]
          })
        });

        const dbResult = await dbResponse.json();
        setRawResponses(prev => ({ ...prev, database: dbResult }));

        if (dbResponse.ok && dbResult.success) {
          if (dbResult.data.length > 0) {
            const memberData = dbResult.data[0];
            addResult('步驟1', 'success', `資料庫查詢成功`);
            addResult('步驟1', 'info', `會員 ID: ${memberData.id}`);
            addResult('步驟1', 'info', `會員姓名: ${memberData.name}`);
            addResult('步驟1', 'info', `錢包餘額: ${memberData.wallet_balance}`);
            addResult('步驟1', 'info', `餘額類型: ${typeof memberData.wallet_balance}`);
          } else {
            addResult('步驟1', 'error', '資料庫中找不到該會員');
          }
        } else {
          addResult('步驟1', 'error', `資料庫查詢失敗: ${dbResult.error || '未知錯誤'}`);
        }
      } catch (error) {
        addResult('步驟1', 'error', `資料庫查詢異常: ${error.message}`);
      }

      // 步驟 2: 測試錢包餘額 API
      addResult('步驟2', 'info', '測試錢包餘額 API...');
      
      try {
        const walletResponse = await fetch('/api/wallet/balance', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        addResult('步驟2', 'info', `API 響應狀態: ${walletResponse.status}`);
        addResult('步驟2', 'info', `API 響應狀態文字: ${walletResponse.statusText}`);

        const walletResult = await walletResponse.json();
        setRawResponses(prev => ({ ...prev, walletApi: walletResult }));

        if (walletResponse.ok) {
          if (walletResult.success) {
            addResult('步驟2', 'success', `錢包 API 成功`);
            addResult('步驟2', 'info', `返回餘額: ${walletResult.data.balance}`);
            addResult('步驟2', 'info', `格式化餘額: ${walletResult.data.formatted_balance}`);
          } else {
            addResult('步驟2', 'error', `錢包 API 業務失敗: ${walletResult.error}`);
          }
        } else {
          addResult('步驟2', 'error', `錢包 API HTTP 失敗: ${walletResponse.status}`);
        }
      } catch (error) {
        addResult('步驟2', 'error', `錢包 API 異常: ${error.message}`);
      }

      // 步驟 3: 檢查 JWT Token 解析
      addResult('步驟3', 'info', '檢查 JWT Token 解析...');
      
      try {
        // 解析 JWT token（不驗證簽名，只是查看內容）
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          const payload = JSON.parse(atob(tokenParts[1]));
          addResult('步驟3', 'success', 'JWT Token 格式正確');
          addResult('步驟3', 'info', `Token 用戶 ID: ${payload.userId}`);
          addResult('步驟3', 'info', `Token 角色: ${payload.role}`);
          addResult('步驟3', 'info', `Token 過期時間: ${new Date(payload.exp * 1000).toLocaleString()}`);
          
          if (payload.userId !== user.userId) {
            addResult('步驟3', 'warning', 'Token 中的用戶 ID 與 localStorage 不一致');
          }
        } else {
          addResult('步驟3', 'error', 'JWT Token 格式錯誤');
        }
      } catch (error) {
        addResult('步驟3', 'error', `JWT Token 解析失敗: ${error.message}`);
      }

      // 步驟 4: 檢查表結構
      addResult('步驟4', 'info', '檢查 members 表結構...');
      
      try {
        const structureResponse = await fetch('/api/admin/database-query', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            query: "PRAGMA table_info(members)"
          })
        });

        const structureResult = await structureResponse.json();
        setRawResponses(prev => ({ ...prev, tableStructure: structureResult }));

        if (structureResponse.ok && structureResult.success) {
          const hasWalletBalance = structureResult.data.some(col => col.name === 'wallet_balance');
          if (hasWalletBalance) {
            addResult('步驟4', 'success', 'members 表包含 wallet_balance 欄位');
            const walletColumn = structureResult.data.find(col => col.name === 'wallet_balance');
            addResult('步驟4', 'info', `欄位類型: ${walletColumn.type}`);
            addResult('步驟4', 'info', `預設值: ${walletColumn.dflt_value}`);
            addResult('步驟4', 'info', `是否可為空: ${walletColumn.notnull ? '否' : '是'}`);
          } else {
            addResult('步驟4', 'error', 'members 表不包含 wallet_balance 欄位');
          }
        } else {
          addResult('步驟4', 'error', `檢查表結構失敗: ${structureResult.error || '未知錯誤'}`);
        }
      } catch (error) {
        addResult('步驟4', 'error', `檢查表結構異常: ${error.message}`);
      }

      addResult('完成', 'success', '錢包 API 調試完成');

    } catch (error) {
      addResult('錯誤', 'error', '調試過程中出現錯誤: ' + error.message);
    } finally {
      setIsDebugging(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>錢包 API 調試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔍 錢包 API 調試</h1>
                <p className="text-gray-600 mt-2">深度調試錢包 API 載入問題</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={debugWalletApi}
                  disabled={isDebugging}
                  className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 font-medium"
                >
                  {isDebugging ? '調試中...' : '🔍 開始調試'}
                </button>
              </div>
            </div>

            {/* 調試結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 調試結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {debugResults.length > 0 ? (
                  <div className="space-y-3">
                    {debugResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                            {result.data && (
                              <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono max-h-32 overflow-y-auto">
                                <pre className="whitespace-pre-wrap">
                                  {JSON.stringify(result.data, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「開始調試」來深度調試錢包 API
                  </div>
                )}
              </div>
            </div>

            {/* 原始響應數據 */}
            {Object.keys(rawResponses).length > 0 && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📄 原始響應數據</h2>
                
                <div className="space-y-4">
                  {Object.entries(rawResponses).map(([key, response]) => (
                    <div key={key} className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-medium text-gray-900 mb-2 capitalize">{key} 響應:</h3>
                      <div className="bg-white rounded border p-3 text-xs font-mono max-h-64 overflow-y-auto">
                        <pre className="whitespace-pre-wrap">
                          {JSON.stringify(response, null, 2)}
                        </pre>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 調試項目</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>步驟1:</strong> 直接查詢資料庫中的會員數據和錢包餘額</div>
                <div><strong>步驟2:</strong> 測試錢包餘額 API 的完整響應</div>
                <div><strong>步驟3:</strong> 檢查 JWT Token 的內容和有效性</div>
                <div><strong>步驟4:</strong> 驗證 members 表的結構和 wallet_balance 欄位</div>
                <div><strong>目的:</strong> 找出錢包餘額無法載入的具體原因</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
