# 環境變數設置指南

## 📋 概述

本指南說明如何正確設置會員管理系統的環境變數，包含開發環境和生產環境的配置。

## 🔧 快速設置

### 1. 複製範例檔案

```bash
# 後端環境變數
cp .env.example backend/.env

# 前端環境變數
cp .env.example frontend/.env.local
```

### 2. 編輯配置檔案

根據您的環境修改相應的配置值。

## 📁 檔案結構

```
project/
├── .env.example          # 環境變數範例檔案
├── backend/
│   └── .env              # 後端環境變數 (不提交到 Git)
├── frontend/
│   └── .env.local        # 前端環境變數 (不提交到 Git)
└── docs/
    └── ENVIRONMENT_SETUP.md
```

## 🔑 必要配置項目

### 後端 (backend/.env)

#### 基本配置
```env
NODE_ENV=development
PORT=3000
DB_PATH=../frontend/lib/database/database.sqlite
```

#### 安全配置
```env
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
ENCRYPTION_KEY=your-super-secret-encryption-key-32-chars-long
```

### 前端 (frontend/.env.local)

#### API 配置
```env
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_APP_NAME=會員管理系統
```

## 🌍 環境別配置

### 開發環境 (Development)

```env
# 後端
NODE_ENV=development
DEBUG=true
FRONTEND_URL=http://localhost:3001

# 前端
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
```

### 測試環境 (Testing)

```env
# 後端
NODE_ENV=test
DB_PATH=./test-database.sqlite
JWT_SECRET=test-jwt-secret

# 前端
NEXT_PUBLIC_ENABLE_MOCK_DATA=true
NEXT_PUBLIC_DEBUG_MODE=true
```

### 生產環境 (Production)

```env
# 後端
NODE_ENV=production
DEBUG=false
DB_TYPE=postgresql
DB_HOST=your-production-db-host

# 前端
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
```

## 🔐 安全最佳實踐

### 1. 密鑰生成

```bash
# 生成 JWT 密鑰
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# 生成加密密鑰 (32 字符)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### 2. 環境變數驗證

在應用程式啟動時驗證必要的環境變數：

```javascript
// backend/src/config/validateEnv.js
const requiredEnvVars = [
  'JWT_SECRET',
  'ENCRYPTION_KEY',
  'DB_PATH'
];

requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
});
```

### 3. 敏感資料保護

- 使用 `.gitignore` 排除 `.env` 檔案
- 在生產環境使用密鑰管理服務
- 定期更換密鑰和密碼
- 限制環境變數的訪問權限

## 🚀 部署配置

### Docker 環境

```dockerfile
# Dockerfile
ENV NODE_ENV=production
ENV PORT=3000
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    environment:
      - NODE_ENV=production
      - JWT_SECRET=${JWT_SECRET}
      - DB_HOST=${DB_HOST}
```

### Vercel 部署 (前端)

在 Vercel 儀表板中設置環境變數：

```
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
NEXT_PUBLIC_APP_NAME=會員管理系統
```

### Heroku 部署 (後端)

```bash
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your-production-jwt-secret
heroku config:set DATABASE_URL=your-database-url
```

## 🔍 故障排除

### 常見問題

1. **API 連接失敗**
   - 檢查 `NEXT_PUBLIC_API_URL` 是否正確
   - 確認後端服務正在運行

2. **認證失敗**
   - 檢查 `JWT_SECRET` 是否一致
   - 確認密鑰長度和格式

3. **資料庫連接失敗**
   - 檢查 `DB_PATH` 路徑是否正確
   - 確認資料庫檔案存在且有讀寫權限

### 除錯工具

```bash
# 檢查環境變數
node -e "console.log(process.env)"

# 測試 API 連接
curl http://localhost:3000/api/v1/health

# 檢查前端環境變數
npm run dev -- --debug
```

## 📞 支援

如果您在設置環境變數時遇到問題，請：

1. 檢查本指南的故障排除部分
2. 查看應用程式日誌
3. 確認所有必要的環境變數都已設置
4. 聯繫開發團隊獲得支援

---

**注意**: 請勿將包含真實密鑰的環境變數檔案提交到版本控制系統。
