#!/usr/bin/env node

/**
 * 環境變數檢查腳本
 * 檢查必要的環境變數是否已設置
 */

const fs = require('fs');
const path = require('path');

// 顏色輸出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 必要的後端環境變數
const requiredBackendEnvVars = [
  'NODE_ENV',
  'PORT',
  'JWT_SECRET',
  'DB_PATH'
];

// 建議的後端環境變數
const recommendedBackendEnvVars = [
  'ENCRYPTION_KEY',
  'BCRYPT_ROUNDS',
  'FRONTEND_URL',
  'CORS_ORIGINS'
];

// 必要的前端環境變數
const requiredFrontendEnvVars = [
  'NEXT_PUBLIC_API_URL',
  'NEXT_PUBLIC_APP_NAME'
];

// 建議的前端環境變數
const recommendedFrontendEnvVars = [
  'NEXT_PUBLIC_APP_VERSION',
  'NEXT_PUBLIC_DEBUG_MODE',
  'NEXT_PUBLIC_DEFAULT_LANGUAGE'
];

function checkEnvFile(filePath, envVars, type = 'required') {
  if (!fs.existsSync(filePath)) {
    colorLog('red', `❌ 環境變數檔案不存在: ${filePath}`);
    return false;
  }

  const envContent = fs.readFileSync(filePath, 'utf8');
  const envLines = envContent.split('\n').filter(line => 
    line.trim() && !line.trim().startsWith('#')
  );

  const envMap = {};
  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      envMap[key.trim()] = value.trim();
    }
  });

  let allPresent = true;
  const missing = [];
  const present = [];

  envVars.forEach(envVar => {
    if (envMap[envVar]) {
      present.push(envVar);
    } else {
      missing.push(envVar);
      if (type === 'required') {
        allPresent = false;
      }
    }
  });

  return { allPresent, missing, present, envMap };
}

function validateEnvValues(envMap) {
  const warnings = [];

  // 檢查 JWT_SECRET 長度
  if (envMap.JWT_SECRET && envMap.JWT_SECRET.length < 32) {
    warnings.push('JWT_SECRET 應該至少 32 個字符長');
  }

  // 檢查是否使用預設值
  if (envMap.JWT_SECRET && envMap.JWT_SECRET.includes('change-this')) {
    warnings.push('JWT_SECRET 仍使用預設值，請更換為安全的密鑰');
  }

  if (envMap.ENCRYPTION_KEY && envMap.ENCRYPTION_KEY.includes('change-this')) {
    warnings.push('ENCRYPTION_KEY 仍使用預設值，請更換為安全的密鑰');
  }

  // 檢查生產環境配置
  if (envMap.NODE_ENV === 'production') {
    if (envMap.DEBUG === 'true') {
      warnings.push('生產環境不應啟用 DEBUG 模式');
    }
    if (envMap.NEXT_PUBLIC_DEBUG_MODE === 'true') {
      warnings.push('生產環境不應啟用前端 DEBUG 模式');
    }
  }

  return warnings;
}

function main() {
  colorLog('cyan', '🔍 檢查環境變數配置...\n');

  let hasErrors = false;

  // 檢查後端環境變數
  colorLog('blue', '📋 檢查後端環境變數 (backend/.env)');
  const backendEnvPath = path.join(__dirname, '../backend/.env');
  
  const backendRequired = checkEnvFile(backendEnvPath, requiredBackendEnvVars, 'required');
  if (backendRequired) {
    if (backendRequired.allPresent) {
      colorLog('green', '✅ 所有必要的後端環境變數都已設置');
      backendRequired.present.forEach(env => {
        colorLog('green', `  ✓ ${env}`);
      });
    } else {
      colorLog('red', '❌ 缺少必要的後端環境變數:');
      backendRequired.missing.forEach(env => {
        colorLog('red', `  ✗ ${env}`);
      });
      hasErrors = true;
    }

    // 檢查建議的環境變數
    const backendRecommended = checkEnvFile(backendEnvPath, recommendedBackendEnvVars, 'recommended');
    if (backendRecommended.missing.length > 0) {
      colorLog('yellow', '⚠️  建議設置的後端環境變數:');
      backendRecommended.missing.forEach(env => {
        colorLog('yellow', `  ! ${env}`);
      });
    }

    // 驗證環境變數值
    const backendWarnings = validateEnvValues(backendRequired.envMap);
    if (backendWarnings.length > 0) {
      colorLog('yellow', '⚠️  後端環境變數警告:');
      backendWarnings.forEach(warning => {
        colorLog('yellow', `  ! ${warning}`);
      });
    }
  } else {
    hasErrors = true;
  }

  console.log();

  // 檢查前端環境變數
  colorLog('blue', '📋 檢查前端環境變數 (frontend/.env.local)');
  const frontendEnvPath = path.join(__dirname, '../frontend/.env.local');
  
  const frontendRequired = checkEnvFile(frontendEnvPath, requiredFrontendEnvVars, 'required');
  if (frontendRequired) {
    if (frontendRequired.allPresent) {
      colorLog('green', '✅ 所有必要的前端環境變數都已設置');
      frontendRequired.present.forEach(env => {
        colorLog('green', `  ✓ ${env}`);
      });
    } else {
      colorLog('red', '❌ 缺少必要的前端環境變數:');
      frontendRequired.missing.forEach(env => {
        colorLog('red', `  ✗ ${env}`);
      });
      hasErrors = true;
    }

    // 檢查建議的環境變數
    const frontendRecommended = checkEnvFile(frontendEnvPath, recommendedFrontendEnvVars, 'recommended');
    if (frontendRecommended.missing.length > 0) {
      colorLog('yellow', '⚠️  建議設置的前端環境變數:');
      frontendRecommended.missing.forEach(env => {
        colorLog('yellow', `  ! ${env}`);
      });
    }
  } else {
    hasErrors = true;
  }

  console.log();

  // 總結
  if (hasErrors) {
    colorLog('red', '❌ 環境變數檢查失敗');
    colorLog('yellow', '💡 請參考 docs/ENVIRONMENT_SETUP.md 進行設置');
    process.exit(1);
  } else {
    colorLog('green', '✅ 環境變數檢查通過');
    colorLog('cyan', '🚀 系統已準備就緒');
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkEnvFile, validateEnvValues };
