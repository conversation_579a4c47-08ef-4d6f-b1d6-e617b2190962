import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function Withdraw() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [amount, setAmount] = useState('');
  const [selectedAccount, setSelectedAccount] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentBalance, setCurrentBalance] = useState(0);
  const [showAddAccount, setShowAddAccount] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 模擬銀行帳戶
  const [bankAccounts, setBankAccounts] = useState([
    {
      id: 'acc-001',
      bankName: '台灣銀行',
      accountNumber: '****-****-****-1234',
      accountName: '張小明',
      isDefault: true
    },
    {
      id: 'acc-002',
      bankName: '中國信託',
      accountNumber: '****-****-****-5678',
      accountName: '張小明',
      isDefault: false
    }
  ]);

  // 新增帳戶表單
  const [newAccount, setNewAccount] = useState({
    bankName: '',
    accountNumber: '',
    accountName: ''
  });

  // 預設金額選項
  const quickAmounts = [100, 300, 500, 1000, 2000, 5000];

  useEffect(() => {
    checkAuth();
    loadCurrentBalance();

    // 設置預設帳戶
    const defaultAccount = bankAccounts.find(acc => acc.isDefault);
    if (defaultAccount) {
      setSelectedAccount(defaultAccount.id);
    }
  }, []);

  const checkAuth = () => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/login');
      return;
    }

    try {
      setUser(JSON.parse(userData));
    } catch (error) {
      console.error('解析用戶數據失敗:', error);
      router.push('/login');
    }
  };

  const loadCurrentBalance = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:3001/api/wallet/balance', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      if (result.success) {
        setCurrentBalance(result.data.balance);
      } else {
        setError('無法載入錢包餘額');
      }
    } catch (error) {
      console.error('載入餘額失敗:', error);
      setError('載入餘額失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickAmount = (quickAmount) => {
    setAmount(quickAmount.toString());
  };

  const calculateFee = () => {
    const baseAmount = parseFloat(amount) || 0;
    if (baseAmount <= 1000) return 15; // 小額提現手續費
    return Math.max(15, baseAmount * 0.005); // 0.5% 手續費，最低 15 元
  };

  const getActualAmount = () => {
    const baseAmount = parseFloat(amount) || 0;
    const fee = calculateFee();
    return baseAmount - fee;
  };

  const handleAddAccount = () => {
    if (!newAccount.bankName || !newAccount.accountNumber || !newAccount.accountName) {
      alert('請填寫完整的銀行帳戶資訊');
      return;
    }

    const newAcc = {
      id: 'acc-' + Date.now(),
      bankName: newAccount.bankName,
      accountNumber: '****-****-****-' + newAccount.accountNumber.slice(-4),
      accountName: newAccount.accountName,
      isDefault: bankAccounts.length === 0
    };

    setBankAccounts([...bankAccounts, newAcc]);
    setSelectedAccount(newAcc.id);
    setNewAccount({ bankName: '', accountNumber: '', accountName: '' });
    setShowAddAccount(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!amount || parseFloat(amount) <= 0) {
      setError('請輸入有效的提現金額');
      return;
    }

    if (parseFloat(amount) < 100) {
      setError('最低提現金額為 NT$ 100');
      return;
    }

    if (parseFloat(amount) > currentBalance) {
      setError('提現金額不能超過可用餘額');
      return;
    }

    if (!selectedAccount) {
      setError('請選擇提現帳戶');
      return;
    }

    setIsProcessing(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/wallet/withdraw', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
          bank_account_id: selectedAccount,
          description: `提現到 ${bankAccounts.find(acc => acc.id === selectedAccount)?.bankName || '銀行帳戶'}`
        })
      });

      const result = await response.json();

      if (result.success) {
        // 安全地處理 new_balance 數據
        const newBalance = result.data.new_balance;
        let balanceAmount = 0;

        if (newBalance && typeof newBalance === 'object') {
          balanceAmount = parseFloat(newBalance.balance) || 0;
        } else if (typeof newBalance === 'number') {
          balanceAmount = newBalance;
        }

        setCurrentBalance(balanceAmount);

        const selectedAcc = bankAccounts.find(acc => acc.id === selectedAccount);
        const details = result.data.withdraw_details;

        alert(`提現申請已提交！\n提現金額：NT$ ${details.amount.toLocaleString()}\n手續費：NT$ ${details.fee.toFixed(2)}\n實際扣除：NT$ ${details.total.toLocaleString()}\n提現帳戶：${selectedAcc.bankName} ${selectedAcc.accountNumber}\n參考號：${details.withdraw_reference}\n預計到帳：${details.estimated_arrival}`);

        // 跳轉回錢包頁面
        router.push('/wallet');
      } else {
        setError(result.error || '提現申請失敗');
      }

    } catch (error) {
      console.error('提現失敗:', error);
      setError('提現申請失敗，請稍後再試');
    } finally {
      setIsProcessing(false);
    }
  };

  const selectedAcc = bankAccounts.find(acc => acc.id === selectedAccount);

  if (!user || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>錢包提現 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/wallet')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回錢包
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">錢包提現</h1>
                </div>
              </div>
              
              <div className="flex items-center">
                <span className="text-sm text-gray-600">可用餘額: NT$ {(currentBalance || 0).toLocaleString()}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 提現表單 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-xl shadow-lg p-8"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">申請提現</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* 金額輸入 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    提現金額 (NT$)
                  </label>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="請輸入提現金額"
                    min="100"
                    max={currentBalance}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    最低提現金額 NT$ 100，可用餘額 NT$ {(currentBalance || 0).toLocaleString()}
                  </p>
                </div>

                {/* 快速金額選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    快速選擇
                  </label>
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
                    {quickAmounts.filter(amt => amt <= (currentBalance || 0)).map((quickAmount) => (
                      <button
                        key={quickAmount}
                        type="button"
                        onClick={() => handleQuickAmount(quickAmount)}
                        className={`py-3 px-4 rounded-lg border-2 transition-colors ${
                          amount === quickAmount.toString()
                            ? 'border-blue-500 bg-blue-50 text-blue-600'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        NT$ {quickAmount}
                      </button>
                    ))}
                    <button
                      type="button"
                      onClick={() => setAmount((currentBalance || 0).toString())}
                      className={`py-3 px-4 rounded-lg border-2 transition-colors ${
                        amount === (currentBalance || 0).toString()
                          ? 'border-blue-500 bg-blue-50 text-blue-600'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      全部
                    </button>
                  </div>
                </div>

                {/* 銀行帳戶選擇 */}
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      選擇提現帳戶
                    </label>
                    <button
                      type="button"
                      onClick={() => setShowAddAccount(!showAddAccount)}
                      className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                    >
                      + 新增帳戶
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    {bankAccounts.map((account) => (
                      <div
                        key={account.id}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                          selectedAccount === account.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedAccount(account.id)}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-medium text-gray-900">{account.bankName}</h3>
                            <p className="text-sm text-gray-500">{account.accountNumber}</p>
                            <p className="text-sm text-gray-500">{account.accountName}</p>
                          </div>
                          {account.isDefault && (
                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                              預設
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 新增帳戶表單 */}
                  {showAddAccount && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="mt-4 p-4 bg-gray-50 rounded-lg"
                    >
                      <h3 className="font-medium text-gray-900 mb-3">新增銀行帳戶</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <input
                          type="text"
                          placeholder="銀行名稱"
                          value={newAccount.bankName}
                          onChange={(e) => setNewAccount({...newAccount, bankName: e.target.value})}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <input
                          type="text"
                          placeholder="帳戶號碼"
                          value={newAccount.accountNumber}
                          onChange={(e) => setNewAccount({...newAccount, accountNumber: e.target.value})}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <input
                          type="text"
                          placeholder="帳戶名稱"
                          value={newAccount.accountName}
                          onChange={(e) => setNewAccount({...newAccount, accountName: e.target.value})}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div className="flex space-x-3 mt-3">
                        <button
                          type="button"
                          onClick={handleAddAccount}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          確認新增
                        </button>
                        <button
                          type="button"
                          onClick={() => setShowAddAccount(false)}
                          className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                        >
                          取消
                        </button>
                      </div>
                    </motion.div>
                  )}
                </div>

                {/* 費用明細 */}
                {amount && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="bg-gray-50 rounded-lg p-4"
                  >
                    <h3 className="font-medium text-gray-900 mb-3">提現明細</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>提現金額:</span>
                        <span>NT$ {parseFloat(amount).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>手續費:</span>
                        <span>NT$ {calculateFee().toLocaleString()}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-medium text-green-600">
                        <span>實際到帳:</span>
                        <span>NT$ {getActualAmount().toLocaleString()}</span>
                      </div>
                    </div>
                    {selectedAcc && (
                      <div className="mt-3 pt-3 border-t">
                        <p className="text-sm text-gray-600">
                          提現至：{selectedAcc.bankName} {selectedAcc.accountNumber}
                        </p>
                      </div>
                    )}
                  </motion.div>
                )}

                {/* 錯誤訊息 */}
                {error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600 text-sm">{error}</p>
                  </div>
                )}

                {/* 提交按鈕 */}
                <button
                  type="submit"
                  disabled={!amount || !selectedAccount || isProcessing}
                  className={`w-full py-4 px-6 rounded-lg font-medium text-lg transition-colors ${
                    !amount || !selectedAccount || isProcessing
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isProcessing ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      處理中...
                    </div>
                  ) : (
                    `確認提現 NT$ ${amount ? parseFloat(amount).toLocaleString() : '0'}`
                  )}
                </button>
              </form>
            </motion.div>

            {/* 提現說明 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-yellow-50 border border-yellow-200 rounded-lg p-6"
            >
              <h3 className="font-medium text-yellow-900 mb-3">⚠️ 提現說明</h3>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• 提現申請提交後無法取消，請確認金額和帳戶無誤</li>
                <li>• 提現手續費：1000 元以下收取 15 元，超過 1000 元收取 0.5%</li>
                <li>• 工作日 16:00 前申請，當日處理；16:00 後申請，次日處理</li>
                <li>• 預計 1-3 個工作天到帳，實際到帳時間依銀行處理速度而定</li>
                <li>• 如有疑問請聯繫客服：<EMAIL></li>
              </ul>
            </motion.div>
          </div>
        </main>
      </div>
    </>
  );
}
