const { query, transaction } = require('../config/database');
const EncryptionService = require('./EncryptionService');

class SecurityAuditService {
  constructor() {
    // 高風險事件類型
    this.highRiskEvents = [
      'failed_login_attempt',
      'password_change',
      'email_change',
      'phone_change',
      'payment_failure',
      'suspicious_activity',
      'account_lockout',
      'privilege_escalation'
    ];

    // 審計保留期限 (天)
    this.auditRetentionDays = 2555; // 7 年
  }

  // 記錄用戶活動
  async logUserActivity(userId, activityType, description, metadata = {}) {
    try {
      const ipAddress = metadata.ipAddress || 'unknown';
      const userAgent = metadata.userAgent || 'unknown';
      const sessionId = metadata.sessionId || null;
      const riskLevel = this.calculateRiskLevel(activityType, metadata);

      await query(
        `INSERT INTO user_activity_logs (
          user_id, activity_type, description, metadata,
          ip_address, user_agent, session_id, risk_level, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)`,
        [
          userId,
          activityType,
          description,
          JSON.stringify(metadata),
          ipAddress,
          userAgent,
          sessionId,
          riskLevel
        ]
      );

      // 如果是高風險事件，觸發額外的安全檢查
      if (this.highRiskEvents.includes(activityType)) {
        await this.handleHighRiskEvent(userId, activityType, metadata);
      }
    } catch (error) {
      console.error('Log user activity error:', error);
      // 不拋出錯誤，避免影響主要業務流程
    }
  }

  // 記錄系統事件
  async logSystemEvent(eventType, description, metadata = {}) {
    try {
      const severity = this.calculateSeverity(eventType);

      await query(
        `INSERT INTO system_audit_logs (
          event_type, description, metadata, severity,
          server_id, process_id, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)`,
        [
          eventType,
          description,
          JSON.stringify(metadata),
          severity,
          process.env.SERVER_ID || 'unknown',
          process.pid
        ]
      );
    } catch (error) {
      console.error('Log system event error:', error);
    }
  }

  // 記錄安全事件
  async logSecurityEvent(userId, eventType, description, metadata = {}) {
    try {
      const severity = this.calculateSecuritySeverity(eventType);
      const threatLevel = this.calculateThreatLevel(eventType, metadata);

      await query(
        `INSERT INTO security_audit_logs (
          user_id, event_type, description, metadata,
          severity, threat_level, ip_address, user_agent, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)`,
        [
          userId,
          eventType,
          description,
          JSON.stringify(metadata),
          severity,
          threatLevel,
          metadata.ipAddress || 'unknown',
          metadata.userAgent || 'unknown'
        ]
      );

      // 高威脅事件立即通知
      if (threatLevel === 'critical' || threatLevel === 'high') {
        await this.sendSecurityAlert(userId, eventType, description, metadata);
      }
    } catch (error) {
      console.error('Log security event error:', error);
    }
  }

  // 計算風險等級
  calculateRiskLevel(activityType, metadata) {
    // 基礎風險等級
    let riskScore = 0;

    // 根據活動類型評分
    const activityRisks = {
      'login': 1,
      'logout': 0,
      'password_change': 3,
      'email_change': 3,
      'phone_change': 2,
      'payment': 2,
      'failed_login_attempt': 4,
      'account_lockout': 5,
      'suspicious_activity': 5
    };

    riskScore += activityRisks[activityType] || 1;

    // IP 地址風險評估
    if (metadata.isNewIP) riskScore += 2;
    if (metadata.isVPN) riskScore += 1;
    if (metadata.isTor) riskScore += 3;

    // 時間風險評估
    const hour = new Date().getHours();
    if (hour < 6 || hour > 22) riskScore += 1; // 非正常時間

    // 地理位置風險評估
    if (metadata.isNewCountry) riskScore += 2;
    if (metadata.isHighRiskCountry) riskScore += 3;

    // 轉換為風險等級
    if (riskScore >= 8) return 'critical';
    if (riskScore >= 6) return 'high';
    if (riskScore >= 4) return 'medium';
    if (riskScore >= 2) return 'low';
    return 'minimal';
  }

  // 計算系統事件嚴重性
  calculateSeverity(eventType) {
    const severityMap = {
      'server_start': 'info',
      'server_stop': 'warning',
      'database_connection_error': 'error',
      'payment_gateway_error': 'error',
      'memory_usage_high': 'warning',
      'disk_space_low': 'warning',
      'security_breach': 'critical',
      'data_corruption': 'critical'
    };

    return severityMap[eventType] || 'info';
  }

  // 計算安全威脅等級
  calculateSecuritySeverity(eventType) {
    const severityMap = {
      'brute_force_attack': 'critical',
      'sql_injection_attempt': 'critical',
      'xss_attempt': 'high',
      'csrf_attempt': 'high',
      'unauthorized_access': 'high',
      'data_breach': 'critical',
      'privilege_escalation': 'critical',
      'suspicious_file_upload': 'medium',
      'rate_limit_exceeded': 'low'
    };

    return severityMap[eventType] || 'low';
  }
}

module.exports = new SecurityAuditService();