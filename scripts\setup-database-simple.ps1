# 簡化資料庫設置腳本
# 提供多種安裝選項和詳細指導

Write-Host "資料庫設置指南" -ForegroundColor Cyan
Write-Host ""

# 檢查 Docker 狀態
Write-Host "檢查 Docker 狀態..." -ForegroundColor Blue
$dockerAvailable = $false

try {
    $dockerVersion = docker --version 2>$null
    if ($dockerVersion) {
        Write-Host "Docker 已安裝: $dockerVersion" -ForegroundColor Green

        try {
            docker info 2>$null | Out-Null
            Write-Host "Docker 服務正在運行" -ForegroundColor Green
            $dockerAvailable = $true
        } catch {
            Write-Host "Docker 已安裝但未運行" -ForegroundColor Yellow
            $dockerAvailable = $false
        }
    } else {
        Write-Host "Docker 未安裝" -ForegroundColor Red
        $dockerAvailable = $false
    }
} catch {
    Write-Host "Docker 未安裝" -ForegroundColor Red
    $dockerAvailable = $false
}

Write-Host ""
Write-Host "請選擇資料庫安裝方式:" -ForegroundColor Cyan
Write-Host "1. Docker 安裝 (推薦，最簡單)"
Write-Host "2. 本地安裝 PostgreSQL"
Write-Host "3. 使用雲端資料庫"
Write-Host "4. 查看詳細安裝指南"
Write-Host ""

$choice = Read-Host "請選擇 (1-4)"

if ($choice -eq "1") {
    if ($dockerAvailable) {
        Write-Host ""
        Write-Host "使用 Docker 安裝資料庫..." -ForegroundColor Blue
        Write-Host ""

        # 檢查 docker-compose.yml 是否存在
        if (Test-Path "backend/docker-compose.yml") {
            Set-Location backend

            Write-Host "啟動 PostgreSQL 和 Redis 服務..."
            docker-compose up -d postgres redis

            Write-Host "等待服務啟動..."
            Start-Sleep -Seconds 5

            Write-Host ""
            Write-Host "資料庫服務已啟動！" -ForegroundColor Green
            Write-Host ""
            Write-Host "連接資訊:"
            Write-Host "  PostgreSQL: localhost:5432"
            Write-Host "  Redis: localhost:6379"
            Write-Host "  資料庫: member_system"
            Write-Host "  用戶名: postgres"
            Write-Host "  密碼: password"
            Write-Host ""
            Write-Host "下一步:"
            Write-Host "  1. cd backend"
            Write-Host "  2. npm install"
            Write-Host "  3. npm run migrate"
            Write-Host "  4. npm run dev"

        } else {
            Write-Host "找不到 docker-compose.yml 文件" -ForegroundColor Red
        }
    } else {
        Write-Host ""
        Write-Host "Docker 不可用" -ForegroundColor Red
        Write-Host ""
        Write-Host "請先安裝 Docker Desktop:"
        Write-Host "1. 下載: https://www.docker.com/products/docker-desktop"
        Write-Host "2. 安裝並啟動 Docker Desktop"
        Write-Host "3. 重新運行此腳本"
    }
}
elseif ($choice -eq "2") {
    Write-Host ""
    Write-Host "本地安裝 PostgreSQL" -ForegroundColor Blue
    Write-Host ""
    Write-Host "安裝步驟:"
    Write-Host "1. 下載 PostgreSQL 15+"
    Write-Host "   https://www.postgresql.org/download/windows/"
    Write-Host ""
    Write-Host "2. 安裝時設置:"
    Write-Host "   - 超級用戶密碼: password"
    Write-Host "   - 端口: 5432"
    Write-Host "   - 安裝 pgAdmin (管理工具)"
    Write-Host ""
    Write-Host "3. 安裝完成後創建資料庫:"
    Write-Host "   psql -U postgres"
    Write-Host "   CREATE DATABASE member_system;"
    Write-Host "   CREATE DATABASE member_system_test;"
    Write-Host ""
    Write-Host "4. 更新環境變數:"
    Write-Host "   編輯 backend/.env 文件"
    Write-Host "   設置 DB_HOST=localhost"
    Write-Host "   設置 DB_PASSWORD=password"
}
elseif ($choice -eq "3") {
    Write-Host ""
    Write-Host "雲端資料庫選項" -ForegroundColor Blue
    Write-Host ""
    Write-Host "推薦的免費雲端資料庫:"
    Write-Host ""
    Write-Host "PostgreSQL:"
    Write-Host "  Supabase (推薦)"
    Write-Host "     - 網址: https://supabase.com/"
    Write-Host "     - 免費額度: 500MB"
    Write-Host "     - 包含認證和 API"
    Write-Host ""
    Write-Host "  ElephantSQL"
    Write-Host "     - 網址: https://www.elephantsql.com/"
    Write-Host "     - 免費額度: 20MB"
    Write-Host ""
    Write-Host "Redis:"
    Write-Host "  Redis Cloud"
    Write-Host "     - 網址: https://redis.com/redis-enterprise-cloud/"
    Write-Host "     - 免費額度: 30MB"
    Write-Host ""
    Write-Host "設置完成後，請更新 backend/.env 文件中的連接資訊"
}
elseif ($choice -eq "4") {
    Write-Host ""
    Write-Host "詳細安裝指南" -ForegroundColor Blue
    Write-Host ""
    Write-Host "請查看以下文檔:"
    Write-Host "  docs/DATABASE_SETUP.md - 完整安裝指南"
    Write-Host "  backend/docker-compose.yml - Docker 配置"
    Write-Host "  backend/.env.example - 環境變數範例"
    Write-Host ""
    Write-Host "快速開始 (Docker):"
    Write-Host "  1. 安裝 Docker Desktop"
    Write-Host "  2. cd backend"
    Write-Host "  3. docker-compose up -d"
    Write-Host "  4. npm run migrate"
    Write-Host ""
}
else {
    Write-Host "無效選擇" -ForegroundColor Red
}

Write-Host ""
Write-Host "提示:" -ForegroundColor Yellow
Write-Host "如果您是初學者，建議使用 Docker 方式，最簡單快速！"
Write-Host ""
Write-Host "需要幫助？請查看:"
Write-Host "  docs/DATABASE_SETUP.md"
Write-Host "  GitHub Issues"
Write-Host ""
Write-Host "腳本執行完成" -ForegroundColor Green
