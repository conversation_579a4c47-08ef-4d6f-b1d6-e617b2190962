// 密碼驗證工具 API
const bcrypt = require('bcrypt');

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允許'
    });
  }

  try {
    const { password, hash } = req.body;

    if (!password || !hash) {
      return res.status(400).json({
        success: false,
        error: '密碼和哈希值為必填欄位'
      });
    }

    console.log('驗證密碼:', password);
    console.log('對比哈希:', hash);

    // 驗證密碼
    const isValid = await bcrypt.compare(password, hash);

    console.log('驗證結果:', isValid);

    res.status(200).json({
      success: true,
      isValid: isValid,
      password: password,
      hash: hash
    });

  } catch (error) {
    console.error('密碼驗證錯誤:', error);
    
    res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: error.message
    });
  }
}
