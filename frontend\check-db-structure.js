const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const db = new sqlite3.Database(path.join(__dirname, 'lib/database/database.sqlite'));

console.log('檢查當前資料庫結構...');

// 檢查 members 表結構
db.all('PRAGMA table_info(members)', [], (err, columns) => {
  if (err) {
    console.error('檢查 members 表失敗:', err);
  } else {
    console.log('\nmembers 表結構:');
    columns.forEach(col => {
      console.log(`  ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? 'DEFAULT ' + col.dflt_value : ''}`);
    });
  }

  // 檢查所有表
  db.all('SELECT name FROM sqlite_master WHERE type="table"', [], (err2, tables) => {
    if (!err2) {
      console.log('\n所有表:');
      tables.forEach(table => {
        console.log(`  - ${table.name}`);
      });
    }
    
    // 檢查是否有錢包相關欄位
    db.all('SELECT * FROM members LIMIT 1', [], (err3, sample) => {
      if (!err3 && sample.length > 0) {
        console.log('\nmembers 表範例數據欄位:');
        Object.keys(sample[0]).forEach(key => {
          console.log(`  - ${key}: ${sample[0][key]}`);
        });
      }
      db.close();
    });
  });
});
