const bcrypt = require('bcrypt');

async function generateHash() {
  try {
    const password = 'password123';
    const saltRounds = 10;
    const hash = await bcrypt.hash(password, saltRounds);
    
    console.log('Password:', password);
    console.log('Hash:', hash);
    
    // 驗證哈希
    const isValid = await bcrypt.compare(password, hash);
    console.log('Verification:', isValid);
    
    // 測試舊密碼
    const oldHash = '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';
    const isOldValid = await bcrypt.compare('password', oldHash);
    console.log('Old password "password" valid:', isOldValid);
    
    const isOld123Valid = await bcrypt.compare('password123', oldHash);
    console.log('Old password "password123" valid:', isOld123Valid);
    
    const is1234Valid = await bcrypt.compare('1234', oldHash);
    console.log('Old password "1234" valid:', is1234Valid);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

generateHash();
