const EncryptionService = require('./EncryptionService');
const { query } = require('../config/database');

class PCIComplianceService {
  constructor() {
    // PCI DSS 要求的最小密鑰長度
    this.minKeyLength = 256;
    // 敏感資料保留期限 (天)
    this.dataRetentionDays = 365;
    // 允許的支付卡類型
    this.allowedCardTypes = ['visa', 'mastercard', 'amex', 'discover'];
  }

  // 驗證信用卡號碼 (Luhn 算法)
  validateCreditCardNumber(cardNumber) {
    if (!cardNumber) return false;

    // 移除所有非數字字符
    const cleaned = cardNumber.replace(/\D/g, '');

    // 檢查長度 (13-19 位)
    if (cleaned.length < 13 || cleaned.length > 19) {
      return false;
    }

    // Luhn 算法驗證
    let sum = 0;
    let isEven = false;

    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }

  // 識別信用卡類型
  identifyCardType(cardNumber) {
    const cleaned = cardNumber.replace(/\D/g, '');

    // Visa: 4開頭，13或16位
    if (/^4[0-9]{12}$/.test(cleaned) || /^4[0-9]{15}$/.test(cleaned)) {
      return 'visa';
    }

    // Mastercard: 5[1-5]開頭或2221-2720，16位
    if (/^5[1-5][0-9]{14}$/.test(cleaned) ||
        /^2(?:2(?:2[1-9]|[3-9][0-9])|[3-6][0-9][0-9]|7(?:[01][0-9]|20))[0-9]{12}$/.test(cleaned)) {
      return 'mastercard';
    }

    // American Express: 34或37開頭，15位
    if (/^3[47][0-9]{13}$/.test(cleaned)) {
      return 'amex';
    }

    // Discover: 6開頭，16位
    if (/^6(?:011|5[0-9]{2})[0-9]{12}$/.test(cleaned)) {
      return 'discover';
    }

    return 'unknown';
  }

  // 安全儲存支付卡資料 (符合 PCI DSS)
  async secureStoreCardData(userId, cardData) {
    try {
      // 驗證信用卡號碼
      if (!this.validateCreditCardNumber(cardData.cardNumber)) {
        throw new Error('無效的信用卡號碼');
      }

      // 識別卡片類型
      const cardType = this.identifyCardType(cardData.cardNumber);
      if (!this.allowedCardTypes.includes(cardType)) {
        throw new Error('不支援的信用卡類型');
      }

      // 生成卡片 Token (用於後續引用)
      const cardToken = EncryptionService.generateSecureToken(32);

      // 加密敏感資料
      const encryptedCardNumber = EncryptionService.encryptPII(cardData.cardNumber);
      const encryptedCVV = EncryptionService.encryptPII(cardData.cvv);

      // 儲存到安全的支付卡資料表
      const result = await query(
        `INSERT INTO secure_payment_cards (
          user_id, card_token, card_type, encrypted_card_number,
          encrypted_cvv, expiry_month, expiry_year, cardholder_name,
          billing_address, created_at, expires_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP,
                  CURRENT_TIMESTAMP + INTERVAL '${this.dataRetentionDays} days')
        RETURNING card_token, card_type`,
        [
          userId,
          cardToken,
          cardType,
          encryptedCardNumber,
          encryptedCVV,
          cardData.expiryMonth,
          cardData.expiryYear,
          cardData.cardholderName,
          JSON.stringify(cardData.billingAddress)
        ]
      );

      // 記錄安全事件
      await this.logSecurityEvent(userId, 'card_stored', {
        cardToken,
        cardType,
        lastFourDigits: cardData.cardNumber.slice(-4)
      });

      return {
        cardToken: result.rows[0].card_token,
        cardType: result.rows[0].card_type,
        maskedCardNumber: EncryptionService.maskCreditCard(cardData.cardNumber)
      };
    } catch (error) {
      console.error('Secure store card data error:', error);
      throw error;
    }
  }

  // 驗證 CVV
  validateCVV(cvv, cardType) {
    if (!cvv) return false;

    const cleaned = cvv.replace(/\D/g, '');

    // American Express 使用 4 位 CVV，其他使用 3 位
    if (cardType === 'amex') {
      return cleaned.length === 4;
    } else {
      return cleaned.length === 3;
    }
  }

  // 驗證到期日期
  validateExpiryDate(month, year) {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    const expiryMonth = parseInt(month);
    const expiryYear = parseInt(year);

    // 檢查月份範圍
    if (expiryMonth < 1 || expiryMonth > 12) {
      return false;
    }

    // 檢查是否已過期
    if (expiryYear < currentYear ||
        (expiryYear === currentYear && expiryMonth < currentMonth)) {
      return false;
    }

    // 檢查年份不能太遠 (10年內)
    if (expiryYear > currentYear + 10) {
      return false;
    }

    return true;
  }

  // 記錄安全事件
  async logSecurityEvent(userId, eventType, eventData) {
    try {
      await query(
        `INSERT INTO security_audit_logs (
          user_id, event_type, event_data, ip_address, user_agent, created_at
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)`,
        [
          userId,
          eventType,
          JSON.stringify(eventData),
          eventData.ipAddress || 'unknown',
          eventData.userAgent || 'unknown'
        ]
      );
    } catch (error) {
      console.error('Log security event error:', error);
      // 不拋出錯誤，避免影響主要流程
    }
  }
}

module.exports = new PCIComplianceService();