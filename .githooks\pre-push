#!/bin/sh

# Pre-push hook
# 在推送前運行完整測試

echo "🚀 執行 pre-push 檢查..."

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 獲取要推送的分支
protected_branch='main'
current_branch=$(git symbolic-ref HEAD | sed -e 's,.*/\(.*\),\1,')

echo "${BLUE}📋 當前分支: $current_branch${NC}"

# 如果推送到主分支，執行更嚴格的檢查
if [ $protected_branch = $current_branch ]; then
    echo "${YELLOW}⚠️ 推送到保護分支 '$protected_branch'，執行完整檢查...${NC}"
    
    # 進入後端目錄
    cd backend
    
    # 1. 運行所有測試
    echo "🧪 運行完整測試套件..."
    if npm run test:ci; then
        echo "${GREEN}✅ 所有測試通過${NC}"
    else
        echo "${RED}❌ 測試失敗，阻止推送到 $protected_branch${NC}"
        exit 1
    fi
    
    # 2. 檢查測試覆蓋率
    echo "📊 檢查測試覆蓋率..."
    if npm run test:coverage; then
        echo "${GREEN}✅ 測試覆蓋率檢查通過${NC}"
    else
        echo "${RED}❌ 測試覆蓋率不足${NC}"
        exit 1
    fi
    
    # 3. 構建檢查
    echo "🏗️ 檢查構建..."
    if npm run build; then
        echo "${GREEN}✅ 構建成功${NC}"
    else
        echo "${RED}❌ 構建失敗${NC}"
        exit 1
    fi
    
    cd ..
    
    echo "${GREEN}🎉 所有檢查通過，允許推送到 $protected_branch${NC}"
else
    echo "${BLUE}ℹ️ 推送到開發分支，執行基本檢查...${NC}"
    
    cd backend
    
    # 只運行快速測試
    echo "🧪 運行單元測試..."
    if npm run test:unit; then
        echo "${GREEN}✅ 單元測試通過${NC}"
    else
        echo "${RED}❌ 單元測試失敗${NC}"
        exit 1
    fi
    
    cd ..
fi

echo "${GREEN}✅ Pre-push 檢查完成${NC}"
exit 0
