import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function AnalyticsExport() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [exportType, setExportType] = useState('spending');
  const [dateRange, setDateRange] = useState('30d');
  const [format, setFormat] = useState('csv');
  const [isExporting, setIsExporting] = useState(false);
  const [exportHistory, setExportHistory] = useState([]);

  // 模擬導出歷史
  const mockExportHistory = [
    {
      id: 'exp-001',
      type: '消費分析',
      format: 'CSV',
      dateRange: '最近 30 天',
      createdAt: '2025-06-26T10:30:00Z',
      status: 'completed',
      fileSize: '2.3 KB'
    },
    {
      id: 'exp-002',
      type: '訂閱分析',
      format: 'PDF',
      dateRange: '最近 90 天',
      createdAt: '2025-06-25T14:20:00Z',
      status: 'completed',
      fileSize: '156 KB'
    },
    {
      id: 'exp-003',
      type: '錢包分析',
      format: 'Excel',
      dateRange: '最近 7 天',
      createdAt: '2025-06-24T09:15:00Z',
      status: 'completed',
      fileSize: '4.7 KB'
    }
  ];

  useEffect(() => {
    // 檢查認證
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=' + encodeURIComponent('/analytics/export'));
      return;
    }

    // 載入用戶數據
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setExportHistory(mockExportHistory);
  }, [router]);

  const exportTypes = [
    { id: 'spending', name: '消費分析', description: '包含所有消費記錄和統計' },
    { id: 'subscriptions', name: '訂閱分析', description: '訂閱使用量和帳單記錄' },
    { id: 'wallet', name: '錢包分析', description: '錢包交易和餘額變化' },
    { id: 'overview', name: '綜合報告', description: '包含所有數據的完整報告' }
  ];

  const dateRanges = [
    { id: '7d', name: '最近 7 天' },
    { id: '30d', name: '最近 30 天' },
    { id: '90d', name: '最近 90 天' },
    { id: '1y', name: '最近 1 年' },
    { id: 'custom', name: '自定義範圍' }
  ];

  const formats = [
    { id: 'csv', name: 'CSV', description: '逗號分隔值，適合 Excel 開啟' },
    { id: 'excel', name: 'Excel', description: 'Microsoft Excel 格式' },
    { id: 'pdf', name: 'PDF', description: '便攜式文檔格式，適合列印' },
    { id: 'json', name: 'JSON', description: '結構化數據格式，適合開發者' }
  ];

  const handleExport = async () => {
    setIsExporting(true);

    try {
      // 模擬導出處理
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 模擬文件下載
      const selectedType = exportTypes.find(t => t.id === exportType);
      const selectedRange = dateRanges.find(r => r.id === dateRange);
      const selectedFormat = formats.find(f => f.id === format);
      
      // 創建模擬文件內容
      let content = '';
      let mimeType = '';
      let fileName = '';
      
      if (format === 'csv') {
        content = `日期,類型,金額,描述\n2025-06-26,收入,500,信用卡充值\n2025-06-25,支出,299,AI 文字生成服務`;
        mimeType = 'text/csv';
        fileName = `${exportType}_${dateRange}.csv`;
      } else if (format === 'json') {
        content = JSON.stringify({
          exportType: selectedType.name,
          dateRange: selectedRange.name,
          data: [
            { date: '2025-06-26', type: '收入', amount: 500, description: '信用卡充值' },
            { date: '2025-06-25', type: '支出', amount: 299, description: 'AI 文字生成服務' }
          ]
        }, null, 2);
        mimeType = 'application/json';
        fileName = `${exportType}_${dateRange}.json`;
      }
      
      // 創建並下載文件
      const blob = new Blob([content], { type: mimeType });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      // 添加到導出歷史
      const newExport = {
        id: 'exp-' + Date.now(),
        type: selectedType.name,
        format: selectedFormat.name.toUpperCase(),
        dateRange: selectedRange.name,
        createdAt: new Date().toISOString(),
        status: 'completed',
        fileSize: '1.2 KB'
      };
      
      setExportHistory([newExport, ...exportHistory]);
      
      alert(`導出成功！文件已下載：${fileName}`);
      
    } catch (error) {
      alert('導出失敗，請稍後再試');
    } finally {
      setIsExporting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'processing': return '處理中';
      case 'failed': return '失敗';
      default: return '未知';
    }
  };

  return (
    <>
      <Head>
        <title>數據導出 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/analytics')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回數據分析
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">數據導出</h1>
                </div>
              </div>
              
              <div className="flex items-center">
                <span className="text-sm text-gray-600">歡迎，{user?.name}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 導出設置 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-xl shadow-lg p-8"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">導出設置</h2>
              
              <div className="space-y-6">
                {/* 導出類型 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    選擇導出類型
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {exportTypes.map((type) => (
                      <div
                        key={type.id}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                          exportType === type.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setExportType(type.id)}
                      >
                        <h3 className="font-medium text-gray-900">{type.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">{type.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 時間範圍 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    選擇時間範圍
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                    {dateRanges.map((range) => (
                      <button
                        key={range.id}
                        onClick={() => setDateRange(range.id)}
                        className={`py-2 px-4 rounded-lg border-2 transition-colors ${
                          dateRange === range.id
                            ? 'border-blue-500 bg-blue-50 text-blue-600'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        {range.name}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 文件格式 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    選擇文件格式
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {formats.map((fmt) => (
                      <div
                        key={fmt.id}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                          format === fmt.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setFormat(fmt.id)}
                      >
                        <h3 className="font-medium text-gray-900">{fmt.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">{fmt.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 導出按鈕 */}
                <button
                  onClick={handleExport}
                  disabled={isExporting}
                  className={`w-full py-4 px-6 rounded-lg font-medium text-lg transition-colors ${
                    isExporting
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isExporting ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      正在導出...
                    </div>
                  ) : (
                    '開始導出'
                  )}
                </button>
              </div>
            </motion.div>

            {/* 導出歷史 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-6">導出歷史</h3>
              
              <div className="space-y-4">
                {exportHistory.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <svg className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暫無導出記錄</h3>
                    <p className="text-gray-500">您的導出記錄將顯示在這裡</p>
                  </div>
                ) : (
                  exportHistory.map((item, index) => (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="text-2xl">📊</div>
                        <div>
                          <h4 className="font-medium text-gray-900">{item.type}</h4>
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            <span>{item.format}</span>
                            <span>•</span>
                            <span>{item.dateRange}</span>
                            <span>•</span>
                            <span>{formatDate(item.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="flex items-center space-x-3">
                          <span className="text-sm text-gray-500">{item.fileSize}</span>
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(item.status)}`}>
                            {getStatusText(item.status)}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  ))
                )}
              </div>
            </motion.div>

            {/* 使用說明 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-blue-50 border border-blue-200 rounded-lg p-6"
            >
              <h3 className="font-medium text-blue-900 mb-3">📋 使用說明</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 選擇您需要的數據類型和時間範圍</li>
                <li>• CSV 格式適合在 Excel 中分析數據</li>
                <li>• PDF 格式適合列印和分享報告</li>
                <li>• JSON 格式適合程式開發和數據處理</li>
                <li>• 導出的文件會自動下載到您的設備</li>
              </ul>
            </motion.div>
          </div>
        </main>
      </div>
    </>
  );
}
