// 會員註冊 API (簡化版本)
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// 使用內嵌的模擬數據，避免模型載入問題
let mockMembers = [
  {
    id: 1,
    email: '<EMAIL>',
    name: '系統管理員',
    password_hash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: 1234
    role: 'admin',
    status: 'active',
    phone: '0912345678',
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    email: '<EMAIL>',
    name: '一般用戶',
    password_hash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: 1234
    role: 'user',
    status: 'active',
    phone: '0987654321',
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z'
  }
];

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    const { name, email, password, confirmPassword, phone } = req.body;

    console.log('簡化註冊 API: 收到請求', { name, email, phone, password: '***' });

    // 驗證必要欄位
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        error: '請填寫所有必要欄位'
      });
    }

    // 驗證 email 格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: '請輸入有效的電子郵件地址'
      });
    }

    // 驗證密碼長度
    if (password.length < 4) {
      return res.status(400).json({
        success: false,
        error: '密碼長度至少需要 4 個字符'
      });
    }

    // 驗證密碼確認
    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        error: '密碼確認不匹配'
      });
    }

    // 檢查 email 是否已存在
    const existingMember = mockMembers.find(m => m.email.toLowerCase() === email.toLowerCase());
    if (existingMember) {
      return res.status(409).json({
        success: false,
        error: '此電子郵件已被註冊'
      });
    }

    console.log('簡化註冊 API: 驗證通過，開始創建用戶');

    // 生成密碼哈希 (臨時使用簡單方法)
    const saltRounds = 10;
    let passwordHash;
    try {
      passwordHash = await bcrypt.hash(password, saltRounds);
    } catch (hashError) {
      console.error('密碼哈希生成失敗:', hashError);
      // 臨時回退方案
      passwordHash = '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';
    }

    // 創建新會員
    const newMember = {
      id: mockMembers.length + 1,
      email: email.toLowerCase().trim(),
      name: name.trim(),
      password_hash: passwordHash,
      role: 'user',
      status: 'active',
      phone: phone || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // 添加到模擬數據庫
    mockMembers.push(newMember);

    console.log('簡化註冊 API: 用戶創建成功', { id: newMember.id, email: newMember.email });

    // 生成 JWT Token
    const token = jwt.sign(
      { 
        userId: newMember.id, 
        email: newMember.email,
        role: newMember.role
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // 準備用戶數據 (移除敏感信息)
    const userData = {
      id: newMember.id,
      email: newMember.email,
      name: newMember.name,
      role: newMember.role,
      status: newMember.status,
      phone: newMember.phone,
      created_at: newMember.created_at,
      updated_at: newMember.updated_at
    };

    console.log('簡化註冊 API: 註冊完成，返回成功響應');

    // 返回成功響應
    return res.status(201).json({
      success: true,
      data: {
        token,
        user: userData
      },
      message: '註冊成功！歡迎加入我們的服務'
    });

  } catch (error) {
    console.error('簡化註冊 API: 註冊過程出錯:', error);
    
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤，請稍後再試',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
