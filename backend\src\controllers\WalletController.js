const { Pool } = require('pg');

// 資料庫連接
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'member_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

class WalletController {
  // 獲取用戶錢包列表
  static async getUserWallets(req, res) {
    try {
      const userId = req.user.userId;
      const client = await pool.connect();

      try {
        const result = await client.query(
          `SELECT id, currency, balance, frozen_balance, total_deposited, 
                  total_withdrawn, status, created_at, updated_at
           FROM wallets 
           WHERE user_id = $1
           ORDER BY currency ASC`,
          [userId]
        );

        client.release();

        res.json({
          success: true,
          data: {
            wallets: result.rows
          }
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('獲取錢包列表失敗:', error);
      res.status(500).json({
        success: false,
        message: '獲取錢包列表失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 獲取特定錢包詳情
  static async getWalletDetails(req, res) {
    try {
      const userId = req.user.userId;
      const { walletId } = req.params;
      const client = await pool.connect();

      try {
        const result = await client.query(
          `SELECT id, currency, balance, frozen_balance, total_deposited, 
                  total_withdrawn, status, created_at, updated_at
           FROM wallets 
           WHERE id = $1 AND user_id = $2`,
          [walletId, userId]
        );

        if (result.rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: '錢包不存在'
          });
        }

        client.release();

        res.json({
          success: true,
          data: {
            wallet: result.rows[0]
          }
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('獲取錢包詳情失敗:', error);
      res.status(500).json({
        success: false,
        message: '獲取錢包詳情失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 獲取錢包交易記錄
  static async getWalletTransactions(req, res) {
    try {
      const userId = req.user.userId;
      const { walletId } = req.params;
      const { page = 1, limit = 20, type } = req.query;
      const offset = (page - 1) * limit;

      const client = await pool.connect();

      try {
        // 驗證錢包所有權
        const walletResult = await client.query(
          'SELECT id FROM wallets WHERE id = $1 AND user_id = $2',
          [walletId, userId]
        );

        if (walletResult.rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: '錢包不存在'
          });
        }

        // 構建查詢條件
        let whereClause = 'WHERE wallet_id = $1';
        let queryParams = [walletId];
        let paramIndex = 2;

        if (type) {
          whereClause += ` AND type = $${paramIndex}`;
          queryParams.push(type);
          paramIndex++;
        }

        // 獲取交易記錄
        const transactionsResult = await client.query(
          `SELECT id, type, amount, currency, description, reference_id, 
                  status, metadata, created_at, updated_at
           FROM transactions 
           ${whereClause}
           ORDER BY created_at DESC
           LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
          [...queryParams, limit, offset]
        );

        // 獲取總數
        const countResult = await client.query(
          `SELECT COUNT(*) FROM transactions ${whereClause}`,
          queryParams
        );

        client.release();

        res.json({
          success: true,
          data: {
            transactions: transactionsResult.rows,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: parseInt(countResult.rows[0].count),
              totalPages: Math.ceil(countResult.rows[0].count / limit)
            }
          }
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('獲取交易記錄失敗:', error);
      res.status(500).json({
        success: false,
        message: '獲取交易記錄失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 錢包轉帳 (內部轉帳)
  static async transferFunds(req, res) {
    try {
      const userId = req.user.userId;
      const { fromWalletId, toWalletId, amount, description } = req.body;

      if (!fromWalletId || !toWalletId || !amount) {
        return res.status(400).json({
          success: false,
          message: '轉出錢包、轉入錢包和金額為必填項'
        });
      }

      if (amount <= 0) {
        return res.status(400).json({
          success: false,
          message: '轉帳金額必須大於 0'
        });
      }

      const client = await pool.connect();

      try {
        await client.query('BEGIN');

        // 驗證轉出錢包
        const fromWalletResult = await client.query(
          'SELECT id, currency, balance FROM wallets WHERE id = $1 AND user_id = $2',
          [fromWalletId, userId]
        );

        if (fromWalletResult.rows.length === 0) {
          await client.query('ROLLBACK');
          return res.status(404).json({
            success: false,
            message: '轉出錢包不存在'
          });
        }

        const fromWallet = fromWalletResult.rows[0];

        // 檢查餘額
        if (parseFloat(fromWallet.balance) < parseFloat(amount)) {
          await client.query('ROLLBACK');
          return res.status(400).json({
            success: false,
            message: '餘額不足'
          });
        }

        // 驗證轉入錢包
        const toWalletResult = await client.query(
          'SELECT id, currency FROM wallets WHERE id = $1 AND user_id = $2',
          [toWalletId, userId]
        );

        if (toWalletResult.rows.length === 0) {
          await client.query('ROLLBACK');
          return res.status(404).json({
            success: false,
            message: '轉入錢包不存在'
          });
        }

        const toWallet = toWalletResult.rows[0];

        // 檢查幣別是否相同
        if (fromWallet.currency !== toWallet.currency) {
          await client.query('ROLLBACK');
          return res.status(400).json({
            success: false,
            message: '不同幣別之間無法直接轉帳'
          });
        }

        // 更新轉出錢包餘額
        await client.query(
          'UPDATE wallets SET balance = balance - $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
          [amount, fromWalletId]
        );

        // 更新轉入錢包餘額
        await client.query(
          'UPDATE wallets SET balance = balance + $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
          [amount, toWalletId]
        );

        // 記錄轉出交易
        await client.query(
          `INSERT INTO transactions (wallet_id, type, amount, currency, description, status)
           VALUES ($1, 'transfer_out', $2, $3, $4, 'completed')`,
          [fromWalletId, amount, fromWallet.currency, description || '內部轉帳']
        );

        // 記錄轉入交易
        await client.query(
          `INSERT INTO transactions (wallet_id, type, amount, currency, description, status)
           VALUES ($1, 'transfer_in', $2, $3, $4, 'completed')`,
          [toWalletId, amount, fromWallet.currency, description || '內部轉帳']
        );

        await client.query('COMMIT');
        client.release();

        res.json({
          success: true,
          message: '轉帳成功',
          data: {
            amount: parseFloat(amount),
            currency: fromWallet.currency,
            fromWalletId: parseInt(fromWalletId),
            toWalletId: parseInt(toWalletId)
          }
        });

      } catch (error) {
        await client.query('ROLLBACK');
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('錢包轉帳失敗:', error);
      res.status(500).json({
        success: false,
        message: '錢包轉帳失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = WalletController;
