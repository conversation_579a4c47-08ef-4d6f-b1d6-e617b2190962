import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function AuthIntegrationTest() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [subscriptions, setSubscriptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
      id: Date.now(),
      timestamp,
      message,
      type
    };
    setLogs(prev => [logEntry, ...prev]);
    console.log(`[${timestamp}] ${message}`);
  };

  // 檢查登入狀態
  const checkAuthStatus = async () => {
    try {
      addLog('🔍 檢查登入狀態...', 'info');
      
      const token = localStorage.getItem('token');
      if (!token) {
        addLog('❌ 未找到 Token', 'error');
        setUser(null);
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      
      if (result.success) {
        setUser(result.data.user);
        setSubscriptions(result.data.subscriptions || []);
        addLog(`✅ 登入狀態有效 - ${result.data.user.name}`, 'success');
      } else {
        addLog(`❌ Token 無效: ${result.error}`, 'error');
        setUser(null);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    } catch (error) {
      addLog(`❌ 檢查登入狀態失敗: ${error.message}`, 'error');
      setUser(null);
    }
  };

  // 載入可用服務
  const loadAvailableServices = async () => {
    try {
      addLog('🔄 載入可用服務...', 'info');
      
      const token = localStorage.getItem('token');
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
      
      const response = await fetch('/api/services/db', { headers });
      const result = await response.json();
      
      if (result.success) {
        setAvailableServices(result.data);
        addLog(`✅ 載入 ${result.data.length} 個可用服務`, 'success');
      } else {
        addLog(`❌ 載入服務失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 載入服務錯誤: ${error.message}`, 'error');
    }
  };

  // 載入用戶訂閱
  const loadUserSubscriptions = async () => {
    if (!user) {
      addLog('⚠️ 用戶未登入，無法載入訂閱', 'warning');
      return;
    }

    try {
      addLog('🔄 載入用戶訂閱...', 'info');
      
      const token = localStorage.getItem('token');
      const response = await fetch('/api/subscriptions', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      
      if (result.success) {
        setSubscriptions(result.data);
        addLog(`✅ 載入 ${result.data.length} 個訂閱`, 'success');
      } else {
        addLog(`❌ 載入訂閱失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 載入訂閱錯誤: ${error.message}`, 'error');
    }
  };

  // 訂閱服務
  const subscribeService = async (serviceId) => {
    if (!user) {
      addLog('⚠️ 請先登入', 'warning');
      return;
    }

    try {
      addLog(`🔄 訂閱服務 ${serviceId}...`, 'info');
      
      const token = localStorage.getItem('token');
      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          service_id: serviceId
        })
      });

      const result = await response.json();
      
      if (result.success) {
        addLog(`✅ 訂閱成功: ${result.data.service_name}`, 'success');
        await Promise.all([loadUserSubscriptions(), loadAvailableServices()]);
      } else {
        addLog(`❌ 訂閱失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 訂閱錯誤: ${error.message}`, 'error');
    }
  };

  // 取消訂閱
  const cancelSubscription = async (subscriptionId) => {
    try {
      addLog(`🔄 取消訂閱 ${subscriptionId}...`, 'info');
      
      const token = localStorage.getItem('token');
      const response = await fetch('/api/subscriptions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          subscription_id: subscriptionId,
          action: 'cancel'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        addLog(`✅ 取消訂閱成功`, 'success');
        await Promise.all([loadUserSubscriptions(), loadAvailableServices()]);
      } else {
        addLog(`❌ 取消訂閱失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 取消訂閱錯誤: ${error.message}`, 'error');
    }
  };

  // 登出
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('subscriptions');
    document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    setUser(null);
    setSubscriptions([]);
    addLog('👋 已登出', 'info');
  };

  // 初始載入
  useEffect(() => {
    const initLoad = async () => {
      await checkAuthStatus();
      await loadAvailableServices();
    };
    initLoad();
  }, []);

  // 當用戶狀態改變時載入訂閱
  useEffect(() => {
    if (user) {
      loadUserSubscriptions();
    }
  }, [user]);

  return (
    <>
      <Head>
        <title>認證整合測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">認證系統整合測試</h1>
            
            {/* 用戶狀態 */}
            <div className="mb-8 p-6 bg-blue-50 rounded-lg">
              <h2 className="text-lg font-semibold text-blue-900 mb-4">用戶狀態</h2>
              {user ? (
                <div className="space-y-2">
                  <p><strong>姓名:</strong> {user.name}</p>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>角色:</strong> {user.role}</p>
                  <p><strong>狀態:</strong> {user.status}</p>
                  <p><strong>註冊時間:</strong> {new Date(user.created_at).toLocaleString()}</p>
                  <button
                    onClick={logout}
                    className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    登出
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-gray-600">未登入</p>
                  <div className="space-x-4">
                    <a
                      href="/auth/login"
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 inline-block"
                    >
                      登入
                    </a>
                    <a
                      href="/auth/register"
                      className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 inline-block"
                    >
                      註冊
                    </a>
                  </div>
                </div>
              )}
            </div>

            {/* 控制按鈕 */}
            <div className="mb-8 flex flex-wrap gap-4">
              <button
                onClick={checkAuthStatus}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                🔍 檢查登入狀態
              </button>
              
              <button
                onClick={loadAvailableServices}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                🔄 載入可用服務
              </button>
              
              <button
                onClick={loadUserSubscriptions}
                disabled={!user}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
              >
                📋 載入我的訂閱
              </button>
              
              <button
                onClick={() => setLogs([])}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                🧹 清除日誌
              </button>
            </div>

            {/* 統計信息 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-green-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-900 mb-2">可用服務</h3>
                <p className="text-3xl font-bold text-green-600">{availableServices.length}</p>
                <p className="text-sm text-green-700">可以訂閱的服務</p>
              </div>
              
              <div className="bg-orange-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-orange-900 mb-2">我的訂閱</h3>
                <p className="text-3xl font-bold text-orange-600">{subscriptions.length}</p>
                <p className="text-sm text-orange-700">已訂閱的服務</p>
              </div>
              
              <div className="bg-blue-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">登入狀態</h3>
                <p className="text-3xl font-bold text-blue-600">{user ? '✅' : '❌'}</p>
                <p className="text-sm text-blue-700">{user ? '已登入' : '未登入'}</p>
              </div>
            </div>

            {/* 操作日誌 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">操作日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
                {logs.map((log) => (
                  <div key={log.id} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'success' ? 'text-green-400' : 
                    log.type === 'warning' ? 'text-yellow-400' :
                    'text-blue-400'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">尚無操作記錄</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
