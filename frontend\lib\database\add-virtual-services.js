// 將虛擬服務添加到資料庫
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 連接資料庫
const dbPath = path.join(__dirname, 'database.sqlite');

if (!fs.existsSync(dbPath)) {
  console.error('資料庫文件不存在:', dbPath);
  process.exit(1);
}

const db = new sqlite3.Database(dbPath);

// 需要添加的虛擬服務 (資料庫中已有前3個，添加後4個)
const newServices = [
  {
    name: '語音轉文字服務',
    description: '高精度的語音識別和轉換服務',
    price: 149.00,
    billing_cycle: 'monthly',
    status: 'active',
    category: '語音處理',
    features: JSON.stringify(['多語言支援', '即時轉換', '高精度識別', '批量處理'])
  },
  {
    name: '雲端存儲服務',
    description: '安全可靠的雲端文件存儲解決方案',
    price: 99.00,
    billing_cycle: 'monthly',
    status: 'active',
    category: '存儲',
    features: JSON.stringify(['1TB 存儲空間', '自動備份', '多設備同步', '文件分享'])
  },
  {
    name: '企業級 API 服務',
    description: '為企業提供的高性能 API 接口服務',
    price: 999.00,
    billing_cycle: 'monthly',
    status: 'active',
    category: 'API',
    features: JSON.stringify(['無限 API 調用', '專屬技術支援', 'SLA 保證', '自定義功能'])
  },
  {
    name: '視頻處理服務',
    description: '專業的視頻編輯和處理工具',
    price: 399.00,
    billing_cycle: 'monthly',
    status: 'active',
    category: '視頻處理',
    features: JSON.stringify(['4K 視頻處理', '多格式支援', '雲端渲染', '批量轉換'])
  }
];

console.log('開始添加虛擬服務到資料庫...');

// 首先檢查現有服務
db.all('SELECT id, name FROM services ORDER BY id', [], (err, existingServices) => {
  if (err) {
    console.error('查詢現有服務失敗:', err);
    db.close();
    process.exit(1);
  }

  console.log('現有服務:');
  existingServices.forEach(service => {
    console.log(`  ${service.id}: ${service.name}`);
  });

  // 準備插入語句
  const insertQuery = `
    INSERT INTO services (name, description, price, billing_cycle, status, category, features)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `;

  const stmt = db.prepare(insertQuery);
  let insertedCount = 0;

  // 插入新服務
  newServices.forEach((service, index) => {
    stmt.run([
      service.name,
      service.description,
      service.price,
      service.billing_cycle,
      service.status,
      service.category,
      service.features
    ], function(insertErr) {
      if (insertErr) {
        console.error(`插入服務 "${service.name}" 失敗:`, insertErr);
      } else {
        console.log(`✅ 插入服務: ${service.name} (ID: ${this.lastID})`);
        insertedCount++;
      }

      // 如果是最後一個服務
      if (index === newServices.length - 1) {
        stmt.finalize();

        // 查詢更新後的服務列表
        setTimeout(() => {
          db.all('SELECT id, name, price, category FROM services ORDER BY id', [], (selectErr, allServices) => {
            if (selectErr) {
              console.error('查詢更新後的服務失敗:', selectErr);
            } else {
              console.log('\n更新後的服務列表:');
              allServices.forEach(service => {
                console.log(`  ${service.id}: ${service.name} - $${service.price} (${service.category})`);
              });
              console.log(`\n總共 ${allServices.length} 個服務`);
            }

            db.close((closeErr) => {
              if (closeErr) {
                console.error('關閉資料庫失敗:', closeErr);
              } else {
                console.log('\n✅ 虛擬服務添加完成！');
                console.log(`成功添加 ${insertedCount} 個新服務`);
              }
            });
          });
        }, 500);
      }
    });
  });
});
