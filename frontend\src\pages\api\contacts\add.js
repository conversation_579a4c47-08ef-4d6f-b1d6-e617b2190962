// 添加聯絡人 API
import jwt from 'jsonwebtoken';
import sqlite3 from 'sqlite3';
import path from 'path';
import fs from 'fs';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];
    
    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('添加聯絡人 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }
    
    console.log('添加聯絡人 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('添加聯絡人 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    // 驗證 JWT token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('添加聯絡人 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    const userId = decoded.userId;
    const { contact_name, contact_email, contact_phone, avatar_emoji, nickname, notes, is_favorite } = req.body;

    // 驗證必填欄位
    if (!contact_name || !contact_email) {
      return res.status(400).json({
        success: false,
        error: '聯絡人姓名和郵箱為必填項'
      });
    }

    // 驗證郵箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(contact_email)) {
      return res.status(400).json({
        success: false,
        error: '請輸入有效的郵箱地址'
      });
    }

    console.log('添加聯絡人 API: 用戶 ID:', userId);
    console.log('添加聯絡人 API: 聯絡人信息:', { contact_name, contact_email });

    const db = connectDatabase();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    return new Promise((resolve) => {
      // 檢查聯絡人是否已存在
      db.get(
        'SELECT id FROM frequent_contacts WHERE user_id = ? AND contact_email = ?',
        [userId, contact_email],
        (err, existing) => {
          if (err) {
            console.error('添加聯絡人 API: 檢查重複失敗:', err);
            db.close();
            resolve(res.status(500).json({
              success: false,
              error: '檢查聯絡人失敗: ' + err.message
            }));
            return;
          }

          if (existing) {
            db.close();
            resolve(res.status(400).json({
              success: false,
              error: '該聯絡人已存在'
            }));
            return;
          }

          // 插入新聯絡人
          const insertQuery = `
            INSERT INTO frequent_contacts 
            (user_id, contact_name, contact_email, contact_phone, avatar_emoji, nickname, notes, is_favorite)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `;

          db.run(insertQuery, [
            userId,
            contact_name,
            contact_email,
            contact_phone || null,
            avatar_emoji || '👤',
            nickname || null,
            notes || null,
            is_favorite ? 1 : 0
          ], function(err) {
            if (err) {
              console.error('添加聯絡人 API: 插入失敗:', err);
              db.close();
              resolve(res.status(500).json({
                success: false,
                error: '添加聯絡人失敗: ' + err.message
              }));
              return;
            }

            const contactId = this.lastID;
            console.log('添加聯絡人 API: 成功添加聯絡人，ID:', contactId);

            // 查詢新添加的聯絡人完整信息
            db.get(
              'SELECT * FROM frequent_contacts WHERE id = ?',
              [contactId],
              (err2, newContact) => {
                db.close();
                
                if (err2) {
                  console.error('添加聯絡人 API: 查詢新聯絡人失敗:', err2);
                  resolve(res.status(500).json({
                    success: false,
                    error: '查詢新聯絡人失敗: ' + err2.message
                  }));
                  return;
                }

                resolve(res.status(201).json({
                  success: true,
                  data: newContact,
                  message: '聯絡人添加成功'
                }));
              }
            );
          });
        }
      );
    });

  } catch (error) {
    console.error('添加聯絡人 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
