import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function ResubscribeTest() {
  const [subscriptions, setSubscriptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setIsLoading(true);
    addLog('🔄 載入所有數據...', 'info');

    try {
      await Promise.all([
        loadSubscriptions(),
        loadAvailableServices()
      ]);
    } catch (error) {
      addLog(`❌ 載入失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubscriptions = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        addLog('❌ 未登入', 'error');
        return;
      }

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        const formattedSubscriptions = result.data.map(sub => ({
          id: sub.id,
          service_id: sub.service_id,
          service_name: sub.service_name,
          status: sub.status,
          subscribed_at: sub.subscribed_at,
          updated_at: sub.updated_at
        }));
        
        setSubscriptions(formattedSubscriptions);
        addLog(`✅ 載入 ${formattedSubscriptions.length} 個訂閱記錄`, 'success');
        
        // 分析訂閱狀態
        const statusCount = formattedSubscriptions.reduce((acc, sub) => {
          acc[sub.status] = (acc[sub.status] || 0) + 1;
          return acc;
        }, {});
        
        Object.entries(statusCount).forEach(([status, count]) => {
          addLog(`  📊 ${status}: ${count} 個`, 'info');
        });
      } else {
        addLog(`❌ 載入訂閱失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 訂閱請求失敗: ${error.message}`, 'error');
    }
  };

  const loadAvailableServices = async () => {
    try {
      const response = await fetch('/api/services/database');
      const result = await response.json();

      if (result.success) {
        const activeServices = result.data.filter(service => service.status === 'active');
        
        // 計算可用服務（排除已有活躍訂閱的服務）
        const activeSubscribedServiceIds = subscriptions
          .filter(sub => sub.status === 'active')
          .map(sub => sub.service_id);
        
        const available = activeServices.filter(service =>
          !activeSubscribedServiceIds.includes(service.id)
        );

        setAvailableServices(available);
        addLog(`✅ 載入 ${available.length} 個可用服務`, 'success');
      } else {
        addLog(`❌ 載入服務失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 服務請求失敗: ${error.message}`, 'error');
    }
  };

  // 重新計算可用服務當訂閱變化時
  useEffect(() => {
    if (subscriptions.length > 0) {
      loadAvailableServices();
    }
  }, [subscriptions]);

  const testCancelAndResubscribe = async (subscriptionId) => {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    addLog(`🧪 開始測試: 取消並重新訂閱 ${subscription.service_name}`, 'info');

    try {
      // 步驟 1: 取消訂閱
      addLog(`1️⃣ 取消訂閱 ${subscription.service_name}...`, 'info');
      
      const token = localStorage.getItem('token');
      const cancelResponse = await fetch('/api/subscriptions/cancel', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription_id: subscriptionId
        })
      });

      const cancelResult = await cancelResponse.json();

      if (!cancelResult.success) {
        addLog(`❌ 取消失敗: ${cancelResult.error}`, 'error');
        return;
      }

      addLog(`✅ 取消成功: ${cancelResult.message}`, 'success');

      // 步驟 2: 更新本地狀態並重新載入
      addLog(`2️⃣ 更新本地狀態...`, 'info');
      await loadSubscriptions();

      // 步驟 3: 等待一下，然後重新訂閱
      addLog(`3️⃣ 準備重新訂閱 ${subscription.service_name}...`, 'info');
      
      await new Promise(resolve => setTimeout(resolve, 1000));

      const resubscribeResponse = await fetch('/api/subscriptions/simple', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          service_id: subscription.service_id
        })
      });

      const resubscribeResult = await resubscribeResponse.json();

      if (!resubscribeResult.success) {
        addLog(`❌ 重新訂閱失敗: ${resubscribeResult.error}`, 'error');
        return;
      }

      addLog(`✅ 重新訂閱成功: ${resubscribeResult.message}`, 'success');
      addLog(`📋 操作類型: ${resubscribeResult.action || 'unknown'}`, 'info');

      // 檢查是否重用了記錄
      if (resubscribeResult.action === 'reactivated') {
        addLog(`🔄 ✅ 正確：重新激活了現有記錄 ID ${resubscribeResult.data.id}`, 'success');
      } else if (resubscribeResult.action === 'created') {
        addLog(`🆕 ⚠️ 注意：創建了新記錄 ID ${resubscribeResult.data.id}`, 'warning');
      } else {
        addLog(`❓ 未知操作類型`, 'warning');
      }

      // 步驟 4: 重新載入數據驗證
      addLog(`4️⃣ 重新載入數據驗證...`, 'info');
      await loadSubscriptions();

      // 檢查是否有重複記錄
      const serviceSubscriptions = subscriptions.filter(sub => sub.service_id === subscription.service_id);
      if (serviceSubscriptions.length > 1) {
        addLog(`⚠️ 警告: 服務 ${subscription.service_name} 有 ${serviceSubscriptions.length} 個訂閱記錄`, 'warning');
        serviceSubscriptions.forEach(sub => {
          addLog(`  📦 記錄 ${sub.id}: ${sub.status} (${sub.subscribed_at})`, 'info');
        });
      } else {
        addLog(`✅ 正確: 服務 ${subscription.service_name} 只有 1 個訂閱記錄`, 'success');
      }

    } catch (error) {
      addLog(`❌ 測試失敗: ${error.message}`, 'error');
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      <Head>
        <title>重新訂閱測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔄 重新訂閱測試</h1>
              <p className="text-gray-600 mt-2">測試取消後重新訂閱是否重用現有記錄</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* 控制面板 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試控制</h2>
                
                <div className="space-y-4">
                  <button
                    onClick={loadAllData}
                    disabled={isLoading}
                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isLoading ? '載入中...' : '🔄 重新載入數據'}
                  </button>

                  <button
                    onClick={clearLogs}
                    className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    🗑️ 清除日誌
                  </button>
                </div>

                {/* 數據摘要 */}
                <div className="mt-6 space-y-4">
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">📊 數據摘要</h3>
                    <div className="text-sm text-blue-800 space-y-1">
                      <div>總訂閱記錄: {subscriptions.length}</div>
                      <div>活躍訂閱: {subscriptions.filter(s => s.status === 'active').length}</div>
                      <div>已取消訂閱: {subscriptions.filter(s => s.status === 'cancelled').length}</div>
                      <div>可用服務: {availableServices.length}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 訂閱列表 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">我的訂閱</h2>
                
                {subscriptions.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    暫無訂閱數據
                  </div>
                ) : (
                  <div className="space-y-3">
                    {subscriptions.map((subscription) => (
                      <div key={subscription.id} className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {subscription.service_name}
                            </h4>
                            <p className="text-sm text-gray-600">
                              記錄ID: {subscription.id} | 服務ID: {subscription.service_id}
                            </p>
                            <p className="text-xs text-gray-500">
                              創建: {new Date(subscription.subscribed_at).toLocaleString()}
                            </p>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                            {subscription.status}
                          </span>
                        </div>

                        {subscription.status === 'active' && (
                          <button
                            onClick={() => testCancelAndResubscribe(subscription.id)}
                            className="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700"
                          >
                            🧪 測試取消→重新訂閱
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 測試日誌 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試日誌</h2>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                  {logs.map((log) => (
                    <div key={log.id} className={`mb-1 ${
                      log.type === 'error' ? 'text-red-400' : 
                      log.type === 'success' ? 'text-green-400' : 
                      log.type === 'warning' ? 'text-yellow-400' :
                      'text-blue-400'
                    }`}>
                      [{log.timestamp}] {log.message}
                    </div>
                  ))}
                  {logs.length === 0 && (
                    <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                  )}
                </div>
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">🎯 測試目標</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>正確行為:</strong> 重新訂閱時應該重新激活現有的 cancelled 記錄，而不是創建新記錄</p>
                <p><strong>測試步驟:</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>選擇一個活躍訂閱進行測試</li>
                  <li>取消該訂閱（狀態變為 cancelled）</li>
                  <li>重新訂閱該服務</li>
                  <li>檢查是否重用了現有記錄（action: reactivated）</li>
                  <li>確認該服務只有一個訂閱記錄</li>
                </ol>
                <p><strong>預期結果:</strong> 操作類型應該是 "reactivated"，且該服務只有一個訂閱記錄</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
