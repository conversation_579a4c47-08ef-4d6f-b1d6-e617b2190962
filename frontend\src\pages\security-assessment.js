import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function SecurityAssessment() {
  const router = useRouter();
  const [assessmentResults, setAssessmentResults] = useState([]);
  const [isAssessing, setIsAssessing] = useState(false);
  const [securityScore, setSecurityScore] = useState(0);
  const [currentUser, setCurrentUser] = useState(null);

  useEffect(() => {
    checkCurrentUser();
  }, []);

  const checkCurrentUser = () => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const userData = JSON.parse(userStr);
        setCurrentUser(userData);
      } catch (error) {
        console.error('解析用戶數據失敗:', error);
      }
    }
  };

  const addResult = (category, status, message, severity = 'info', recommendation = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setAssessmentResults(prev => [...prev, {
      timestamp,
      category,
      status, // 'pass', 'fail', 'warning', 'info'
      message,
      severity, // 'critical', 'high', 'medium', 'low', 'info'
      recommendation
    }]);
  };

  const runSecurityAssessment = async () => {
    setIsAssessing(true);
    setAssessmentResults([]);
    setSecurityScore(0);

    try {
      addResult('開始', 'info', '開始安全性評估');

      // 1. 認證安全檢查
      await assessAuthentication();

      // 2. 密碼安全檢查
      await assessPasswordSecurity();

      // 3. API 安全檢查
      await assessApiSecurity();

      // 4. 數據保護檢查
      await assessDataProtection();

      // 5. 傳輸安全檢查
      await assessTransportSecurity();

      // 6. 會話管理檢查
      await assessSessionManagement();

      // 計算安全分數
      calculateSecurityScore();

      addResult('完成', 'info', '安全性評估完成');

    } catch (error) {
      addResult('錯誤', 'fail', `評估過程中出現錯誤: ${error.message}`, 'critical');
    } finally {
      setIsAssessing(false);
    }
  };

  const assessAuthentication = async () => {
    addResult('認證安全', 'info', '檢查認證機制...');

    // 檢查 JWT Token
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const parts = token.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]));
          const exp = new Date(payload.exp * 1000);
          const isExpired = exp < new Date();

          if (isExpired) {
            addResult('認證安全', 'fail', 'JWT Token 已過期', 'high', '請重新登入獲取新的 Token');
          } else {
            addResult('認證安全', 'pass', 'JWT Token 有效且未過期');
          }

          // 檢查 Token 過期時間設置
          const expiryHours = (exp - new Date()) / (1000 * 60 * 60);
          if (expiryHours > 24 * 7) {
            addResult('認證安全', 'warning', 'JWT Token 過期時間過長', 'medium', '建議設置較短的過期時間以提高安全性');
          } else {
            addResult('認證安全', 'pass', 'JWT Token 過期時間設置合理');
          }

        } else {
          addResult('認證安全', 'fail', 'JWT Token 格式錯誤', 'critical', '請檢查 Token 生成邏輯');
        }
      } catch (error) {
        addResult('認證安全', 'fail', 'JWT Token 解析失敗', 'critical', '請檢查 Token 格式和簽名');
      }
    } else {
      addResult('認證安全', 'warning', '未找到認證 Token', 'medium', '用戶未登入或 Token 已清除');
    }

    // 檢查多重認證
    addResult('認證安全', 'warning', '未實施多重認證 (MFA)', 'medium', '建議實施 2FA/MFA 以提高帳號安全性');
  };

  const assessPasswordSecurity = async () => {
    addResult('密碼安全', 'info', '檢查密碼安全策略...');

    // 檢查密碼強度要求
    addResult('密碼安全', 'warning', '未檢測到強密碼策略', 'medium', '建議實施密碼複雜度要求：至少8位，包含大小寫字母、數字和特殊字符');

    // 檢查密碼加密
    addResult('密碼安全', 'pass', '使用 bcrypt 進行密碼雜湊');

    // 檢查密碼重置機制
    addResult('密碼安全', 'warning', '密碼重置機制需要改進', 'medium', '建議實施安全的密碼重置流程，包含郵件驗證和時效性限制');

    // 檢查帳號鎖定機制
    addResult('密碼安全', 'warning', '未實施帳號鎖定機制', 'high', '建議在多次登入失敗後暫時鎖定帳號');
  };

  const assessApiSecurity = async () => {
    addResult('API 安全', 'info', '檢查 API 安全措施...');

    const token = localStorage.getItem('token');
    if (!token) {
      addResult('API 安全', 'warning', '無法測試 API 安全性 - 未登入');
      return;
    }

    // 測試 API 限流
    try {
      const startTime = Date.now();
      const requests = [];
      for (let i = 0; i < 5; i++) {
        requests.push(fetch('/api/wallet/balance', {
          headers: { 'Authorization': `Bearer ${token}` }
        }));
      }
      await Promise.all(requests);
      const endTime = Date.now();

      if (endTime - startTime < 1000) {
        addResult('API 安全', 'warning', '未檢測到 API 限流機制', 'medium', '建議實施 API 限流以防止濫用');
      } else {
        addResult('API 安全', 'pass', '檢測到 API 限流機制');
      }
    } catch (error) {
      addResult('API 安全', 'warning', 'API 限流測試失敗', 'low');
    }

    // 檢查 CORS 設置
    addResult('API 安全', 'warning', 'CORS 設置需要檢查', 'medium', '確保 CORS 策略只允許信任的域名');

    // 檢查輸入驗證
    addResult('API 安全', 'warning', '需要加強輸入驗證', 'high', '實施嚴格的輸入驗證和清理機制');
  };

  const assessDataProtection = async () => {
    addResult('數據保護', 'info', '檢查數據保護措施...');

    // 檢查敏感數據加密
    addResult('數據保護', 'warning', '敏感數據加密需要改進', 'high', '實施敏感數據（如個人信息）的加密存儲');

    // 檢查數據備份
    addResult('數據保護', 'warning', '數據備份策略需要確認', 'medium', '確保定期備份重要數據並測試恢復流程');

    // 檢查 GDPR 合規
    addResult('數據保護', 'warning', 'GDPR 合規性需要改進', 'medium', '實施數據主體權利、數據最小化原則和隱私政策');

    // 檢查日誌記錄
    addResult('數據保護', 'pass', '基本日誌記錄已實施');
  };

  const assessTransportSecurity = async () => {
    addResult('傳輸安全', 'info', '檢查傳輸安全措施...');

    // 檢查 HTTPS
    if (window.location.protocol === 'https:') {
      addResult('傳輸安全', 'pass', '使用 HTTPS 加密傳輸');
    } else {
      addResult('傳輸安全', 'fail', '未使用 HTTPS', 'critical', '必須使用 HTTPS 保護數據傳輸');
    }

    // 檢查安全標頭
    addResult('傳輸安全', 'warning', '安全標頭需要配置', 'medium', '配置 HSTS、CSP、X-Frame-Options 等安全標頭');

    // 檢查 Cookie 安全
    const cookies = document.cookie;
    if (cookies.includes('Secure') && cookies.includes('HttpOnly')) {
      addResult('傳輸安全', 'pass', 'Cookie 安全標誌已設置');
    } else {
      addResult('傳輸安全', 'warning', 'Cookie 安全標誌需要改進', 'medium', '設置 Secure 和 HttpOnly 標誌');
    }
  };

  const assessSessionManagement = async () => {
    addResult('會話管理', 'info', '檢查會話管理...');

    // 檢查會話超時
    addResult('會話管理', 'warning', '會話超時機制需要改進', 'medium', '實施自動會話超時和活動檢測');

    // 檢查並發會話控制
    addResult('會話管理', 'warning', '並發會話控制未實施', 'low', '考慮限制同一用戶的並發會話數量');

    // 檢查登出機制
    addResult('會話管理', 'pass', '基本登出機制已實施');
  };

  const calculateSecurityScore = () => {
    const results = assessmentResults.filter(r => r.status !== 'info');
    const totalChecks = results.length;
    const passedChecks = results.filter(r => r.status === 'pass').length;
    const criticalIssues = results.filter(r => r.severity === 'critical').length;
    const highIssues = results.filter(r => r.severity === 'high').length;

    let score = (passedChecks / totalChecks) * 100;
    score -= criticalIssues * 20;
    score -= highIssues * 10;
    score = Math.max(0, Math.min(100, score));

    setSecurityScore(Math.round(score));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pass': return 'text-green-600';
      case 'fail': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pass': return '✅';
      case 'fail': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <>
      <Head>
        <title>安全性評估 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔒 安全性評估</h1>
                <p className="text-gray-600 mt-2">全面評估系統安全性並提供改進建議</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={runSecurityAssessment}
                  disabled={isAssessing}
                  className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 font-medium"
                >
                  {isAssessing ? '評估中...' : '🔒 開始評估'}
                </button>
              </div>
            </div>

            {/* 安全分數 */}
            {securityScore > 0 && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 安全分數</h2>
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="flex items-center justify-center">
                    <div className="text-center">
                      <div className={`text-6xl font-bold ${getScoreColor(securityScore)}`}>
                        {securityScore}
                      </div>
                      <div className="text-lg text-gray-600 mt-2">安全分數 / 100</div>
                      <div className="text-sm text-gray-500 mt-1">
                        {securityScore >= 80 ? '🟢 良好' : 
                         securityScore >= 60 ? '🟡 需要改進' : '🔴 需要立即改進'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 當前用戶信息 */}
            {currentUser && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">👤 評估用戶</h2>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <div className="text-sm font-medium text-blue-700">姓名</div>
                      <div className="text-blue-900">{currentUser.name}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-blue-700">Email</div>
                      <div className="text-blue-900">{currentUser.email}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-blue-700">角色</div>
                      <div className="text-blue-900">{currentUser.role === 'admin' ? '管理員' : '一般用戶'}</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 評估結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 評估結果</h2>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {assessmentResults.length > 0 ? (
                  assessmentResults.map((result, index) => (
                    <div key={index} className="bg-white rounded-lg p-4 border">
                      <div className="flex items-start space-x-3">
                        <span className={getStatusColor(result.status)}>
                          {getStatusIcon(result.status)}
                        </span>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 text-sm mb-2">
                            <span className="text-gray-500">[{result.timestamp}]</span>
                            <span className="font-medium text-gray-700">{result.category}:</span>
                            <span className={getStatusColor(result.status)}>{result.message}</span>
                            {result.severity !== 'info' && (
                              <span className={`px-2 py-1 rounded text-xs ${getSeverityColor(result.severity)}`}>
                                {result.severity}
                              </span>
                            )}
                          </div>
                          {result.recommendation && (
                            <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                              <strong>建議:</strong> {result.recommendation}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「開始評估」來進行安全性評估
                  </div>
                )}
              </div>
            </div>

            {/* 安全建議 */}
            <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-medium text-red-900 mb-3">🔒 安全改進建議</h3>
              <div className="text-sm text-red-800 space-y-2">
                <div><strong>高優先級:</strong> 實施 HTTPS、強化密碼策略、加強 API 安全</div>
                <div><strong>中優先級:</strong> 實施多重認證、改進會話管理、數據加密</div>
                <div><strong>低優先級:</strong> 安全標頭配置、日誌監控、合規性改進</div>
                <div><strong>持續改進:</strong> 定期安全評估、漏洞掃描、安全培訓</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
