import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function SwitchUserRole() {
  const router = useRouter();
  const [switchResults, setSwitchResults] = useState([]);
  const [isSwitching, setIsSwitching] = useState(false);
  const [currentRole, setCurrentRole] = useState(null);

  useEffect(() => {
    checkCurrentRole();
  }, []);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setSwitchResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const checkCurrentRole = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let tokenData = null;
    let userData = null;

    if (token) {
      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          tokenData = JSON.parse(atob(tokenParts[1]));
        }
      } catch (error) {
        console.error('Token 解析失敗:', error);
      }
    }

    if (userStr) {
      try {
        userData = JSON.parse(userStr);
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setCurrentRole({
      hasToken: !!token,
      hasUser: !!userData,
      tokenRole: tokenData?.role,
      userRole: userData?.role,
      userName: userData?.name,
      userEmail: userData?.email,
      isRoleConsistent: tokenData?.role === userData?.role
    });
  };

  const switchToUser = async () => {
    setIsSwitching(true);
    setSwitchResults([]);

    try {
      addResult('開始', 'info', '開始切換到一般會員角色');

      // 步驟 1: 清除舊認證數據
      addResult('步驟1', 'info', '清除舊認證數據...');
      localStorage.clear();
      addResult('步驟1', 'success', '舊認證數據已清除');

      // 步驟 2: 重新登入獲取一般會員角色
      addResult('步驟2', 'info', '重新登入獲取一般會員角色...');
      
      const loginResponse = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      if (!loginResponse.ok) {
        addResult('步驟2', 'error', `登入請求失敗: ${loginResponse.status}`);
        return;
      }

      const loginResult = await loginResponse.json();
      
      if (loginResult.success) {
        addResult('步驟2', 'success', '重新登入成功');
        addResult('步驟2', 'info', `新角色: ${loginResult.user.role}`);
        addResult('步驟2', 'info', `新姓名: ${loginResult.user.name}`);
        
        // 解析新 Token
        try {
          const newTokenParts = loginResult.token.split('.');
          const newTokenData = JSON.parse(atob(newTokenParts[1]));
          addResult('步驟2', 'info', `Token 角色: ${newTokenData.role}`);
        } catch (error) {
          addResult('步驟2', 'warning', `Token 解析失敗: ${error.message}`);
        }
      } else {
        addResult('步驟2', 'error', `登入失敗: ${loginResult.error}`);
        return;
      }

      // 步驟 3: 保存新認證數據
      addResult('步驟3', 'info', '保存新認證數據...');
      
      localStorage.setItem('token', loginResult.token);
      localStorage.setItem('user', JSON.stringify(loginResult.user));
      
      addResult('步驟3', 'success', '新認證數據已保存');

      // 步驟 4: 驗證角色切換
      addResult('步驟4', 'info', '驗證角色切換結果...');
      
      if (loginResult.user.role === 'user') {
        addResult('步驟4', 'success', '角色切換成功：現在是一般會員');
      } else {
        addResult('步驟4', 'error', `角色切換失敗：當前角色仍是 ${loginResult.user.role}`);
      }

      addResult('完成', 'success', '角色切換完成！');
      
      // 更新顯示
      checkCurrentRole();

    } catch (error) {
      addResult('錯誤', 'error', '角色切換過程中出現錯誤: ' + error.message);
    } finally {
      setIsSwitching(false);
    }
  };

  const switchToAdmin = async () => {
    setIsSwitching(true);
    setSwitchResults([]);

    try {
      addResult('開始', 'info', '開始切換到管理員角色');

      // 清除舊數據並使用管理員帳號登入
      localStorage.clear();
      
      const loginResponse = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password'
        })
      });

      const loginResult = await loginResponse.json();
      
      if (loginResult.success) {
        localStorage.setItem('token', loginResult.token);
        localStorage.setItem('user', JSON.stringify(loginResult.user));
        
        addResult('完成', 'success', `角色切換成功：現在是 ${loginResult.user.role}`);
        checkCurrentRole();
      } else {
        addResult('錯誤', 'error', `切換失敗: ${loginResult.error}`);
      }

    } catch (error) {
      addResult('錯誤', 'error', '角色切換錯誤: ' + error.message);
    } finally {
      setIsSwitching(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>切換用戶角色 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">👤 切換用戶角色</h1>
                <p className="text-gray-600 mt-2">在管理員和一般會員角色之間切換</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={switchToUser}
                  disabled={isSwitching}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
                >
                  {isSwitching ? '切換中...' : '👤 切換為一般會員'}
                </button>
                <button
                  onClick={switchToAdmin}
                  disabled={isSwitching}
                  className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 font-medium"
                >
                  {isSwitching ? '切換中...' : '👨‍💼 切換為管理員'}
                </button>
              </div>
            </div>

            {/* 當前角色狀態 */}
            {currentRole && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 當前角色狀態</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm space-y-2">
                        <div>Token 存在: {currentRole.hasToken ? '✅' : '❌'}</div>
                        <div>用戶數據: {currentRole.hasUser ? '✅' : '❌'}</div>
                        <div>角色一致性: {currentRole.isRoleConsistent ? '✅' : '❌'}</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm space-y-2">
                        <div>姓名: {currentRole.userName || 'N/A'}</div>
                        <div>Email: {currentRole.userEmail || 'N/A'}</div>
                        <div>當前角色: <span className={`font-semibold ${
                          currentRole.userRole === 'admin' ? 'text-red-600' : 'text-blue-600'
                        }`}>
                          {currentRole.userRole === 'admin' ? '👨‍💼 管理員' : '👤 一般會員'}
                        </span></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 切換結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 切換結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {switchResults.length > 0 ? (
                  <div className="space-y-3">
                    {switchResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    選擇要切換的角色
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">👤 角色說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>一般會員 (user):</strong> <EMAIL> - 可以使用錢包功能，查看自己的數據</div>
                <div><strong>管理員 (admin):</strong> <EMAIL> - 可以訪問所有管理功能和資料庫</div>
                <div><strong>切換過程:</strong> 清除舊認證數據 → 重新登入 → 獲取新角色 Token</div>
                <div><strong>注意:</strong> 切換角色後需要重新載入頁面以更新權限</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
