import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function InstantAuthFix() {
  const router = useRouter();
  const [status, setStatus] = useState('開始修復...');
  const [isFixed, setIsFixed] = useState(false);
  const [tokenInfo, setTokenInfo] = useState(null);

  useEffect(() => {
    // 自動開始修復
    fixAuthAndTest();
  }, []);

  const fixAuthAndTest = async () => {
    try {
      setStatus('🔍 檢查當前認證狀態...');
      
      // 檢查現有認證
      const existingToken = localStorage.getItem('token');
      const existingUser = localStorage.getItem('user');
      
      if (existingToken && existingUser) {
        setStatus('🧪 測試現有 Token...');
        
        // 測試現有 token
        const testResponse = await fetch('/api/subscriptions/simple', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${existingToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (testResponse.ok) {
          setStatus('✅ 現有 Token 有效！');
          setTokenInfo({
            token: existingToken,
            user: JSON.parse(existingUser),
            isValid: true
          });
          setIsFixed(true);
          
          // 自動跳轉到前端邏輯測試
          setTimeout(() => {
            router.push('/frontend-logic-test');
          }, 2000);
          return;
        }
      }

      setStatus('🗑️ 清除無效的認證數據...');
      localStorage.clear();

      setStatus('🔑 自動登錄獲取新 Token...');
      
      const loginResponse = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const loginResult = await loginResponse.json();
      
      if (loginResult.success) {
        setStatus('💾 保存新的認證數據...');
        
        localStorage.setItem('token', loginResult.token);
        localStorage.setItem('user', JSON.stringify(loginResult.user));
        
        setTokenInfo({
          token: loginResult.token,
          user: loginResult.user,
          isValid: true
        });

        setStatus('🧪 驗證新 Token...');
        
        // 驗證新 token
        const verifyResponse = await fetch('/api/subscriptions/simple', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${loginResult.token}`,
            'Content-Type': 'application/json'
          }
        });

        if (verifyResponse.ok) {
          const verifyData = await verifyResponse.json();
          setStatus(`✅ 認證修復成功！載入了 ${verifyData.data.length} 個訂閱記錄`);
          setIsFixed(true);
          
          // 自動跳轉到前端邏輯測試
          setTimeout(() => {
            setStatus('🚀 正在跳轉到前端邏輯測試...');
            router.push('/frontend-logic-test');
          }, 3000);
        } else {
          setStatus('❌ Token 驗證失敗');
        }
      } else {
        setStatus('❌ 自動登錄失敗: ' + loginResult.error);
      }

    } catch (error) {
      setStatus('❌ 修復失敗: ' + error.message);
    }
  };

  const manualTest = () => {
    router.push('/frontend-logic-test');
  };

  const goToTokenGenerator = () => {
    router.push('/token-generator');
  };

  const goToTestCenter = () => {
    router.push('/test-center');
  };

  return (
    <>
      <Head>
        <title>一鍵認證修復 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-lg w-full mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {isFixed ? (
                  <span className="text-2xl">✅</span>
                ) : (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                )}
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">⚡ 一鍵認證修復</h1>
              <p className="text-gray-600">自動修復認證問題並跳轉測試</p>
            </div>

            {/* 狀態顯示 */}
            <div className="mb-8">
              <div className={`p-4 rounded-lg ${
                isFixed ? 'bg-green-50 border border-green-200' : 'bg-blue-50 border border-blue-200'
              }`}>
                <p className={`font-medium ${
                  isFixed ? 'text-green-800' : 'text-blue-800'
                }`}>
                  {status}
                </p>
              </div>
            </div>

            {/* Token 信息 */}
            {tokenInfo && (
              <div className="mb-8">
                <h3 className="font-medium text-gray-900 mb-3">📋 認證信息</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Token:</span>
                    <span className="text-green-600">✅ {tokenInfo.token.length} 字符</span>
                  </div>
                  <div className="flex justify-between">
                    <span>用戶:</span>
                    <span className="text-green-600">✅ {tokenInfo.user.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>角色:</span>
                    <span className="text-green-600">✅ {tokenInfo.user.role}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Email:</span>
                    <span className="text-green-600">✅ {tokenInfo.user.email}</span>
                  </div>
                </div>
              </div>
            )}

            {/* 操作按鈕 */}
            <div className="space-y-3">
              {isFixed ? (
                <div className="space-y-3">
                  <button
                    onClick={manualTest}
                    className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                  >
                    🧪 立即測試前端邏輯
                  </button>
                  
                  <button
                    onClick={() => router.push('/subscriptions')}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    📦 測試訂閱頁面
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  <button
                    onClick={fixAuthAndTest}
                    className="w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
                  >
                    🔄 重新嘗試修復
                  </button>
                  
                  <button
                    onClick={goToTokenGenerator}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    🔑 手動生成 Token
                  </button>
                </div>
              )}
              
              <button
                onClick={goToTestCenter}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🏥 前往測試中心
              </button>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">🎯 修復流程</h3>
              <div className="text-sm text-yellow-800 space-y-1">
                <div>1. 檢查現有認證數據</div>
                <div>2. 測試 Token 有效性</div>
                <div>3. 如無效，自動重新登錄</div>
                <div>4. 保存新的認證數據</div>
                <div>5. 驗證修復結果</div>
                <div>6. 自動跳轉到測試頁面</div>
              </div>
            </div>

            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2">📋 管理員帳號</h3>
              <div className="text-sm text-green-800">
                <div>Email: <EMAIL></div>
                <div>Password: password123</div>
                <div>Role: admin</div>
              </div>
            </div>

            {/* 進度指示 */}
            {!isFixed && (
              <div className="mt-6">
                <div className="flex justify-center">
                  <div className="animate-pulse flex space-x-1">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  </div>
                </div>
                <p className="text-center text-sm text-gray-500 mt-2">
                  正在自動修復認證問題...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
