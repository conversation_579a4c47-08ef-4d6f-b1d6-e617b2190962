#!/usr/bin/env node

// 資料庫遷移腳本
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 載入環境變數
require('dotenv').config();

console.log('🗄️ 開始資料庫遷移...\n');

// 資料庫連接配置
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'member_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

// SQL 遷移腳本
const migrations = [
  {
    name: '001_create_users_table',
    sql: `
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name <PERSON><PERSON><PERSON><PERSON>(50),
        last_name <PERSON><PERSON><PERSON><PERSON>(50),
        phone VARCHAR(20),
        date_of_birth DATE,
        gender VARCHAR(10),
        avatar_url TEXT,
        timezone VARCHAR(50) DEFAULT 'Asia/Taipei',
        language VARCHAR(10) DEFAULT 'zh-TW',
        status VARCHAR(20) DEFAULT 'active',
        email_verified_at TIMESTAMP,
        phone_verified_at TIMESTAMP,
        requires_verification BOOLEAN DEFAULT false,
        verification_required_at TIMESTAMP,
        processing_restricted BOOLEAN DEFAULT false,
        restriction_type VARCHAR(50),
        locked_at TIMESTAMP,
        lock_reason VARCHAR(100),
        deleted_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `
  },
  {
    name: '002_create_wallets_table',
    sql: `
      CREATE TABLE IF NOT EXISTS wallets (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        currency VARCHAR(10) NOT NULL DEFAULT 'TWD',
        balance DECIMAL(15,2) DEFAULT 0.00,
        frozen_balance DECIMAL(15,2) DEFAULT 0.00,
        total_deposited DECIMAL(15,2) DEFAULT 0.00,
        total_withdrawn DECIMAL(15,2) DEFAULT 0.00,
        status VARCHAR(20) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, currency)
      );
    `
  },
  {
    name: '003_create_transactions_table',
    sql: `
      CREATE TABLE IF NOT EXISTS transactions (
        id SERIAL PRIMARY KEY,
        wallet_id INTEGER REFERENCES wallets(id) ON DELETE CASCADE,
        type VARCHAR(20) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        currency VARCHAR(10) NOT NULL,
        description TEXT,
        reference_id VARCHAR(100),
        status VARCHAR(20) DEFAULT 'pending',
        metadata JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `
  },
  {
    name: '004_create_payments_table',
    sql: `
      CREATE TABLE IF NOT EXISTS payments (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        payment_method VARCHAR(50) NOT NULL,
        provider VARCHAR(50) NOT NULL,
        provider_payment_id VARCHAR(255),
        amount DECIMAL(15,2) NOT NULL,
        currency VARCHAR(10) NOT NULL,
        net_amount DECIMAL(15,2),
        fee_amount DECIMAL(15,2),
        description TEXT,
        status VARCHAR(20) DEFAULT 'pending',
        metadata JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `
  },
  {
    name: '005_create_services_table',
    sql: `
      CREATE TABLE IF NOT EXISTS services (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        category VARCHAR(50),
        status VARCHAR(20) DEFAULT 'active',
        metadata JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `
  },
  {
    name: '006_create_service_plans_table',
    sql: `
      CREATE TABLE IF NOT EXISTS service_plans (
        id SERIAL PRIMARY KEY,
        service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        currency VARCHAR(10) DEFAULT 'TWD',
        billing_period VARCHAR(20) DEFAULT 'monthly',
        features JSONB,
        limits JSONB,
        status VARCHAR(20) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `
  },
  {
    name: '007_create_subscriptions_table',
    sql: `
      CREATE TABLE IF NOT EXISTS subscriptions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
        plan_id INTEGER REFERENCES service_plans(id) ON DELETE CASCADE,
        status VARCHAR(20) DEFAULT 'active',
        price DECIMAL(10,2) NOT NULL,
        currency VARCHAR(10) DEFAULT 'TWD',
        billing_period VARCHAR(20) DEFAULT 'monthly',
        current_period_start TIMESTAMP,
        current_period_end TIMESTAMP,
        next_billing_date TIMESTAMP,
        cancelled_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `
  },
  {
    name: '008_create_audit_tables',
    sql: `
      CREATE TABLE IF NOT EXISTS user_activity_logs (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        activity_type VARCHAR(50) NOT NULL,
        description TEXT,
        metadata JSONB,
        ip_address INET,
        user_agent TEXT,
        session_id VARCHAR(255),
        risk_level VARCHAR(20) DEFAULT 'low',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS security_audit_logs (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
        event_type VARCHAR(50) NOT NULL,
        description TEXT,
        metadata JSONB,
        severity VARCHAR(20) DEFAULT 'info',
        threat_level VARCHAR(20) DEFAULT 'low',
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS system_audit_logs (
        id SERIAL PRIMARY KEY,
        event_type VARCHAR(50) NOT NULL,
        description TEXT,
        metadata JSONB,
        severity VARCHAR(20) DEFAULT 'info',
        server_id VARCHAR(50),
        process_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `
  },
  {
    name: '009_create_gdpr_tables',
    sql: `
      CREATE TABLE IF NOT EXISTS user_consents (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        consent_type VARCHAR(50) NOT NULL,
        version VARCHAR(20) NOT NULL,
        status VARCHAR(20) DEFAULT 'granted',
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        revoked_at TIMESTAMP,
        ip_address INET,
        user_agent TEXT
      );

      CREATE TABLE IF NOT EXISTS data_export_requests (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        export_type VARCHAR(50) DEFAULT 'full_export',
        status VARCHAR(20) DEFAULT 'pending',
        requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP,
        download_url TEXT,
        expires_at TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS data_deletion_requests (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        deletion_type VARCHAR(20) DEFAULT 'soft',
        status VARCHAR(20) DEFAULT 'pending',
        requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP
      );
    `
  },
  {
    name: '010_create_indexes',
    sql: `
      -- 用戶表索引
      CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
      CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

      -- 錢包表索引
      CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON wallets(user_id);
      CREATE INDEX IF NOT EXISTS idx_wallets_currency ON wallets(currency);

      -- 交易表索引
      CREATE INDEX IF NOT EXISTS idx_transactions_wallet_id ON transactions(wallet_id);
      CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
      CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
      CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

      -- 支付表索引
      CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
      CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
      CREATE INDEX IF NOT EXISTS idx_payments_provider ON payments(provider);
      CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

      -- 訂閱表索引
      CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
      CREATE INDEX IF NOT EXISTS idx_subscriptions_service_id ON subscriptions(service_id);
      CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);

      -- 審計日誌索引
      CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);
      CREATE INDEX IF NOT EXISTS idx_user_activity_logs_created_at ON user_activity_logs(created_at);
      CREATE INDEX IF NOT EXISTS idx_security_audit_logs_created_at ON security_audit_logs(created_at);
      CREATE INDEX IF NOT EXISTS idx_system_audit_logs_created_at ON system_audit_logs(created_at);
    `
  }
];

// 執行遷移
async function runMigrations() {
  const client = await pool.connect();
  
  try {
    // 創建遷移記錄表
    await client.query(`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id SERIAL PRIMARY KEY,
        migration_name VARCHAR(255) UNIQUE NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    console.log('📋 檢查已執行的遷移...');
    
    // 獲取已執行的遷移
    const executedResult = await client.query(
      'SELECT migration_name FROM schema_migrations'
    );
    const executedMigrations = executedResult.rows.map(row => row.migration_name);

    console.log(`已執行的遷移: ${executedMigrations.length} 個\n`);

    // 執行未執行的遷移
    for (const migration of migrations) {
      if (!executedMigrations.includes(migration.name)) {
        console.log(`🔄 執行遷移: ${migration.name}`);
        
        await client.query('BEGIN');
        try {
          await client.query(migration.sql);
          await client.query(
            'INSERT INTO schema_migrations (migration_name) VALUES ($1)',
            [migration.name]
          );
          await client.query('COMMIT');
          
          console.log(`✅ 完成: ${migration.name}`);
        } catch (error) {
          await client.query('ROLLBACK');
          throw error;
        }
      } else {
        console.log(`⏭️ 跳過: ${migration.name} (已執行)`);
      }
    }

    console.log('\n🎉 所有遷移執行完成！');
    
  } catch (error) {
    console.error('❌ 遷移失敗:', error.message);
    throw error;
  } finally {
    client.release();
  }
}

// 主函數
async function main() {
  try {
    await runMigrations();
    console.log('\n✅ 資料庫遷移成功完成');
  } catch (error) {
    console.error('\n❌ 資料庫遷移失敗:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// 執行遷移
if (require.main === module) {
  main();
}

module.exports = { runMigrations };
