// 簡化版 Token 驗證 API
const jwt = require('jsonwebtoken');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// 模擬會員數據
const mockMembers = [
  {
    id: 1,
    email: '<EMAIL>',
    name: '系統管理員',
    role: 'admin',
    status: 'active',
    phone: '0912345678',
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    email: '<EMAIL>',
    name: '一般用戶',
    role: 'user',
    status: 'active',
    phone: '0987654321',
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z'
  }
];

// 模擬訂閱數據
const mockSubscriptions = [
  {
    id: 1,
    member_id: 1,
    service_id: 1,
    service_name: 'AI 文字生成服務',
    status: 'active',
    subscribed_at: '2025-01-01T00:00:00.000Z',
    next_billing_date: '2025-02-01',
    price: 299,
    billing_cycle: 'monthly'
  },
  {
    id: 2,
    member_id: 1,
    service_id: 2,
    service_name: '圖片處理服務',
    status: 'active',
    subscribed_at: '2025-01-01T00:00:00.000Z',
    next_billing_date: '2025-02-01',
    price: 199,
    billing_cycle: 'monthly'
  }
];

export default async function handler(req, res) {
  // 只允許 GET 請求
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允許'
    });
  }

  try {
    // 獲取 Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        error: '未提供認證 Token'
      });
    }

    // 提取 token
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Token 格式錯誤'
      });
    }

    console.log('簡化驗證 API: 驗證 Token');

    // 驗證 JWT Token
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('簡化驗證 API: JWT 驗證失敗', jwtError.message);
      
      return res.status(401).json({
        success: false,
        error: 'Token 無效或已過期'
      });
    }

    console.log('簡化驗證 API: Token 解碼成功', decoded.userId);

    // 查找會員
    const member = mockMembers.find(m => m.id === decoded.userId);
    
    if (!member) {
      return res.status(404).json({
        success: false,
        error: '用戶不存在'
      });
    }

    // 檢查帳號狀態
    if (member.status !== 'active') {
      return res.status(403).json({
        success: false,
        error: '帳號已被停用'
      });
    }

    console.log('簡化驗證 API: 用戶驗證成功', member.name);

    // 獲取用戶的訂閱
    const userSubscriptions = mockSubscriptions.filter(sub => sub.member_id === member.id);

    // 準備返回的用戶數據
    const userData = {
      id: member.id,
      email: member.email,
      name: member.name,
      role: member.role,
      status: member.status,
      phone: member.phone,
      avatar_url: member.avatar_url,
      created_at: member.created_at,
      updated_at: member.updated_at
    };

    // 返回成功響應
    res.status(200).json({
      success: true,
      message: 'Token 驗證成功',
      data: {
        user: userData,
        subscriptions: userSubscriptions,
        tokenInfo: {
          userId: decoded.userId,
          email: decoded.email,
          role: decoded.role,
          iat: decoded.iat,
          exp: decoded.exp
        }
      }
    });

  } catch (error) {
    console.error('簡化驗證 API 錯誤:', error);
    
    res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: error.message
    });
  }
}
