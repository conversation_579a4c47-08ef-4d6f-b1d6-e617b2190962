import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import AvatarUpload from '../components/AvatarUpload';

export default function AccountSettings() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [activeTab, setActiveTab] = useState('profile');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // 個人資料表單
  const [profileForm, setProfileForm] = useState({
    name: '',
    email: '',
    phone: '',
    birthday: '',
    gender: '',
    address: '',
    bio: ''
  });

  // 密碼修改表單
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // 通知設定
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    marketingEmails: false,
    subscriptionReminders: true,
    walletNotifications: true,
    securityAlerts: true
  });

  // 隱私設定
  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: 'private',
    dataSharing: false,
    analyticsTracking: true,
    thirdPartyIntegration: false
  });

  useEffect(() => {
    // 檢查認證
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=' + encodeURIComponent('/account-settings'));
      return;
    }

    // 載入用戶數據
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        
        // 初始化表單數據
        setProfileForm({
          name: userData.name || '',
          email: userData.email || '',
          phone: userData.phone || '',
          birthday: userData.birthday || '',
          gender: userData.gender || '',
          address: userData.address || '',
          bio: userData.bio || ''
        });
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setIsLoading(false);
  }, [router]);

  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // 模擬 API 請求
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 更新本地存儲的用戶數據
      const updatedUser = { ...user, ...profileForm };
      localStorage.setItem('user', JSON.stringify(updatedUser));
      setUser(updatedUser);
      
      alert('個人資料更新成功！');
    } catch (error) {
      alert('更新失敗，請稍後再試');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('新密碼和確認密碼不一致');
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      alert('新密碼長度至少需要 6 個字符');
      return;
    }

    setIsSaving(true);

    try {
      // 模擬 API 請求
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      alert('密碼修改成功！');
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      alert('密碼修改失敗，請稍後再試');
    } finally {
      setIsSaving(false);
    }
  };

  const handleNotificationChange = (key) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handlePrivacyChange = (key, value) => {
    setPrivacySettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDeleteAccount = () => {
    const confirmed = window.confirm(
      '確定要刪除帳戶嗎？此操作無法撤回，所有數據將被永久刪除。'
    );
    
    if (confirmed) {
      const doubleConfirmed = window.confirm(
        '請再次確認：您真的要刪除帳戶嗎？'
      );
      
      if (doubleConfirmed) {
        alert('帳戶刪除功能暫未開放，如需刪除請聯繫客服。');
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入帳戶設定中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>帳戶設定 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/minimal-dashboard')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回儀表板
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">帳戶設定</h1>
                </div>
              </div>
              
              <div className="flex items-center">
                <span className="text-sm text-gray-600">歡迎，{user?.name}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* 側邊欄 */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="space-y-2">
                  <button
                    onClick={() => setActiveTab('profile')}
                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors ${
                      activeTab === 'profile'
                        ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    👤 個人資料
                  </button>
                  <button
                    onClick={() => setActiveTab('security')}
                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors ${
                      activeTab === 'security'
                        ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    🔒 安全設定
                  </button>
                  <button
                    onClick={() => setActiveTab('notifications')}
                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors ${
                      activeTab === 'notifications'
                        ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    🔔 通知設定
                  </button>
                  <button
                    onClick={() => setActiveTab('privacy')}
                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors ${
                      activeTab === 'privacy'
                        ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    🛡️ 隱私設定
                  </button>
                  <button
                    onClick={() => setActiveTab('danger')}
                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors ${
                      activeTab === 'danger'
                        ? 'bg-red-50 text-red-600 border-l-4 border-red-600'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    ⚠️ 危險操作
                  </button>
                </div>
              </div>
            </div>

            {/* 主要內容區域 */}
            <div className="lg:col-span-3">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4 }}
                className="space-y-8"
              >
                {/* 個人資料 */}
                {activeTab === 'profile' && (
                  <div className="bg-white rounded-xl shadow-lg p-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">個人資料</h2>

                    <form onSubmit={handleProfileSubmit} className="space-y-6">
                      {/* 頭像上傳 */}
                      <div className="flex justify-center mb-8">
                        <AvatarUpload
                          currentAvatar={user?.avatar}
                          onAvatarChange={(newAvatar) => {
                            const updatedUser = { ...user, avatar: newAvatar };
                            setUser(updatedUser);
                            localStorage.setItem('user', JSON.stringify(updatedUser));
                          }}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* 姓名 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            姓名 *
                          </label>
                          <input
                            type="text"
                            value={profileForm.name}
                            onChange={(e) => setProfileForm({...profileForm, name: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                          />
                        </div>

                        {/* 電子郵件 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            電子郵件 *
                          </label>
                          <input
                            type="email"
                            value={profileForm.email}
                            onChange={(e) => setProfileForm({...profileForm, email: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                          />
                        </div>

                        {/* 手機號碼 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            手機號碼
                          </label>
                          <input
                            type="tel"
                            value={profileForm.phone}
                            onChange={(e) => setProfileForm({...profileForm, phone: e.target.value})}
                            placeholder="+886 912 345 678"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>

                        {/* 生日 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            生日
                          </label>
                          <input
                            type="date"
                            value={profileForm.birthday}
                            onChange={(e) => setProfileForm({...profileForm, birthday: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>

                        {/* 性別 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            性別
                          </label>
                          <select
                            value={profileForm.gender}
                            onChange={(e) => setProfileForm({...profileForm, gender: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="">請選擇</option>
                            <option value="male">男性</option>
                            <option value="female">女性</option>
                            <option value="other">其他</option>
                            <option value="prefer-not-to-say">不願透露</option>
                          </select>
                        </div>

                        {/* 地址 */}
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            地址
                          </label>
                          <input
                            type="text"
                            value={profileForm.address}
                            onChange={(e) => setProfileForm({...profileForm, address: e.target.value})}
                            placeholder="台北市信義區信義路五段7號"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>

                        {/* 個人簡介 */}
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            個人簡介
                          </label>
                          <textarea
                            value={profileForm.bio}
                            onChange={(e) => setProfileForm({...profileForm, bio: e.target.value})}
                            placeholder="簡單介紹一下自己..."
                            rows={4}
                            maxLength={200}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            {profileForm.bio.length}/200 字符
                          </p>
                        </div>
                      </div>

                      {/* 提交按鈕 */}
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={isSaving}
                          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                            isSaving
                              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              : 'bg-blue-600 text-white hover:bg-blue-700'
                          }`}
                        >
                          {isSaving ? (
                            <div className="flex items-center">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              保存中...
                            </div>
                          ) : (
                            '保存更改'
                          )}
                        </button>
                      </div>
                    </form>
                  </div>
                )}

                {/* 安全設定 */}
                {activeTab === 'security' && (
                  <div className="space-y-8">
                    {/* 修改密碼 */}
                    <div className="bg-white rounded-xl shadow-lg p-8">
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">修改密碼</h2>

                      <form onSubmit={handlePasswordSubmit} className="space-y-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            當前密碼 *
                          </label>
                          <input
                            type="password"
                            value={passwordForm.currentPassword}
                            onChange={(e) => setPasswordForm({...passwordForm, currentPassword: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            新密碼 *
                          </label>
                          <input
                            type="password"
                            value={passwordForm.newPassword}
                            onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            minLength={6}
                            required
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            密碼長度至少需要 6 個字符
                          </p>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            確認新密碼 *
                          </label>
                          <input
                            type="password"
                            value={passwordForm.confirmPassword}
                            onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                          />
                        </div>

                        <div className="flex justify-end">
                          <button
                            type="submit"
                            disabled={isSaving}
                            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                              isSaving
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                            }`}
                          >
                            {isSaving ? '更新中...' : '更新密碼'}
                          </button>
                        </div>
                      </form>
                    </div>

                    {/* 兩步驟驗證 */}
                    <div className="bg-white rounded-xl shadow-lg p-8">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">兩步驟驗證</h3>
                      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="font-medium text-gray-900">SMS 驗證</h4>
                          <p className="text-sm text-gray-500">透過手機簡訊接收驗證碼</p>
                        </div>
                        <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                          啟用
                        </button>
                      </div>
                    </div>

                    {/* 登入記錄 */}
                    <div className="bg-white rounded-xl shadow-lg p-8">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">最近登入記錄</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">Windows Chrome</p>
                            <p className="text-sm text-gray-500">台北市 • 2025-06-26 10:30</p>
                          </div>
                          <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            當前會話
                          </span>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">iPhone Safari</p>
                            <p className="text-sm text-gray-500">台北市 • 2025-06-25 18:45</p>
                          </div>
                          <button className="text-red-600 hover:text-red-700 text-sm">
                            登出
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 通知設定 */}
                {activeTab === 'notifications' && (
                  <div className="bg-white rounded-xl shadow-lg p-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">通知設定</h2>

                    <div className="space-y-6">
                      {/* 電子郵件通知 */}
                      <div className="border-b border-gray-200 pb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">電子郵件通知</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">一般通知</h4>
                              <p className="text-sm text-gray-500">帳戶活動和重要更新</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={notificationSettings.emailNotifications}
                                onChange={() => handleNotificationChange('emailNotifications')}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">行銷郵件</h4>
                              <p className="text-sm text-gray-500">產品更新和促銷活動</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={notificationSettings.marketingEmails}
                                onChange={() => handleNotificationChange('marketingEmails')}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>
                        </div>
                      </div>

                      {/* 手機通知 */}
                      <div className="border-b border-gray-200 pb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">手機通知</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">簡訊通知</h4>
                              <p className="text-sm text-gray-500">重要帳戶活動的簡訊提醒</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={notificationSettings.smsNotifications}
                                onChange={() => handleNotificationChange('smsNotifications')}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">推播通知</h4>
                              <p className="text-sm text-gray-500">瀏覽器推播通知</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={notificationSettings.pushNotifications}
                                onChange={() => handleNotificationChange('pushNotifications')}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>
                        </div>
                      </div>

                      {/* 服務通知 */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">服務通知</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">訂閱提醒</h4>
                              <p className="text-sm text-gray-500">訂閱到期和續費提醒</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={notificationSettings.subscriptionReminders}
                                onChange={() => handleNotificationChange('subscriptionReminders')}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">錢包通知</h4>
                              <p className="text-sm text-gray-500">充值、提現和轉帳通知</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={notificationSettings.walletNotifications}
                                onChange={() => handleNotificationChange('walletNotifications')}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">安全警報</h4>
                              <p className="text-sm text-gray-500">可疑活動和安全事件</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={notificationSettings.securityAlerts}
                                onChange={() => handleNotificationChange('securityAlerts')}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 隱私設定 */}
                {activeTab === 'privacy' && (
                  <div className="bg-white rounded-xl shadow-lg p-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">隱私設定</h2>

                    <div className="space-y-6">
                      {/* 個人資料可見性 */}
                      <div className="border-b border-gray-200 pb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">個人資料可見性</h3>
                        <div className="space-y-3">
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name="profileVisibility"
                              value="public"
                              checked={privacySettings.profileVisibility === 'public'}
                              onChange={(e) => handlePrivacyChange('profileVisibility', e.target.value)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <span className="ml-3">
                              <span className="font-medium text-gray-900">公開</span>
                              <span className="block text-sm text-gray-500">任何人都可以查看您的基本資料</span>
                            </span>
                          </label>
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name="profileVisibility"
                              value="friends"
                              checked={privacySettings.profileVisibility === 'friends'}
                              onChange={(e) => handlePrivacyChange('profileVisibility', e.target.value)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <span className="ml-3">
                              <span className="font-medium text-gray-900">朋友</span>
                              <span className="block text-sm text-gray-500">只有您的朋友可以查看</span>
                            </span>
                          </label>
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name="profileVisibility"
                              value="private"
                              checked={privacySettings.profileVisibility === 'private'}
                              onChange={(e) => handlePrivacyChange('profileVisibility', e.target.value)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <span className="ml-3">
                              <span className="font-medium text-gray-900">私人</span>
                              <span className="block text-sm text-gray-500">只有您自己可以查看</span>
                            </span>
                          </label>
                        </div>
                      </div>

                      {/* 數據使用 */}
                      <div className="border-b border-gray-200 pb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">數據使用</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">數據分享</h4>
                              <p className="text-sm text-gray-500">允許與合作夥伴分享匿名數據</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={privacySettings.dataSharing}
                                onChange={() => handlePrivacyChange('dataSharing', !privacySettings.dataSharing)}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">分析追蹤</h4>
                              <p className="text-sm text-gray-500">允許收集使用數據以改善服務</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={privacySettings.analyticsTracking}
                                onChange={() => handlePrivacyChange('analyticsTracking', !privacySettings.analyticsTracking)}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">第三方整合</h4>
                              <p className="text-sm text-gray-500">允許第三方應用存取您的資料</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={privacySettings.thirdPartyIntegration}
                                onChange={() => handlePrivacyChange('thirdPartyIntegration', !privacySettings.thirdPartyIntegration)}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                          </div>
                        </div>
                      </div>

                      {/* 數據下載 */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">數據管理</h3>
                        <div className="space-y-4">
                          <div className="p-4 bg-blue-50 rounded-lg">
                            <h4 className="font-medium text-blue-900 mb-2">下載您的數據</h4>
                            <p className="text-sm text-blue-700 mb-3">
                              您可以下載我們收集的所有關於您的數據副本
                            </p>
                            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                              請求數據下載
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 危險操作 */}
                {activeTab === 'danger' && (
                  <div className="bg-white rounded-xl shadow-lg p-8">
                    <h2 className="text-2xl font-bold text-red-600 mb-6">危險操作</h2>

                    <div className="space-y-6">
                      {/* 登出所有設備 */}
                      <div className="border border-yellow-200 bg-yellow-50 rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-yellow-800 mb-2">登出所有設備</h3>
                        <p className="text-yellow-700 mb-4">
                          這將會登出您在所有設備上的會話，您需要重新登入。
                        </p>
                        <button className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                          登出所有設備
                        </button>
                      </div>

                      {/* 停用帳戶 */}
                      <div className="border border-orange-200 bg-orange-50 rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-orange-800 mb-2">停用帳戶</h3>
                        <p className="text-orange-700 mb-4">
                          暫時停用您的帳戶。您可以隨時重新啟用，所有數據將會保留。
                        </p>
                        <button className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                          停用帳戶
                        </button>
                      </div>

                      {/* 刪除帳戶 */}
                      <div className="border border-red-200 bg-red-50 rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-red-800 mb-2">刪除帳戶</h3>
                        <p className="text-red-700 mb-4">
                          永久刪除您的帳戶和所有相關數據。此操作無法撤回！
                        </p>
                        <div className="space-y-3">
                          <div className="text-sm text-red-600">
                            <p>刪除帳戶將會：</p>
                            <ul className="list-disc list-inside mt-2 space-y-1">
                              <li>永久刪除您的個人資料</li>
                              <li>取消所有訂閱服務</li>
                              <li>清空錢包餘額（請先提現）</li>
                              <li>刪除所有交易記錄</li>
                              <li>移除所有數據分析記錄</li>
                            </ul>
                          </div>
                          <button
                            onClick={handleDeleteAccount}
                            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                          >
                            刪除帳戶
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
