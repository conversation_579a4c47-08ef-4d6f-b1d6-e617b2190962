import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletQuickDiagnosis() {
  const router = useRouter();
  const [diagnosisResults, setDiagnosisResults] = useState([]);
  const [isDiagnosing, setIsDiagnosing] = useState(false);
  const [summary, setSummary] = useState(null);

  const addResult = (test, status, message, solution = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setDiagnosisResults(prev => [...prev, {
      timestamp,
      test,
      status, // 'pass', 'fail', 'warning'
      message,
      solution
    }]);
  };

  const runQuickDiagnosis = async () => {
    setIsDiagnosing(true);
    setDiagnosisResults([]);
    setSummary(null);

    try {
      addResult('開始', 'pass', '開始快速診斷錢包功能');

      // 檢查認證
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (!token || !userStr) {
        addResult('認證', 'fail', '未找到認證數據', '請先登入或使用錢包登入修復工具');
        return;
      }

      let user;
      try {
        user = JSON.parse(userStr);
        addResult('認證', 'pass', `用戶: ${user.name} (ID: ${user.userId})`);
      } catch (error) {
        addResult('認證', 'fail', '用戶數據解析失敗', '請重新登入');
        return;
      }

      // 測試 1: 資料庫查詢 API
      addResult('API-1', 'pass', '測試資料庫查詢 API...');
      
      try {
        const dbResponse = await fetch('/api/admin/database-query', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            query: "SELECT name FROM sqlite_master WHERE type='table' LIMIT 1"
          })
        });

        if (dbResponse.ok) {
          const dbResult = await dbResponse.json();
          if (dbResult.success) {
            addResult('API-1', 'pass', '資料庫查詢 API 正常');
          } else {
            addResult('API-1', 'fail', `資料庫查詢失敗: ${dbResult.error}`, '檢查資料庫連接');
          }
        } else {
          addResult('API-1', 'fail', '資料庫查詢 API 無法訪問', '檢查 API 是否存在');
        }
      } catch (error) {
        addResult('API-1', 'fail', `資料庫查詢 API 錯誤: ${error.message}`, '檢查網路連接');
      }

      // 測試 2: Members 表結構
      addResult('DB-1', 'pass', '檢查 members 表結構...');
      
      try {
        const columnsResponse = await fetch('/api/admin/database-query', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            query: "PRAGMA table_info(members)"
          })
        });

        if (columnsResponse.ok) {
          const columnsResult = await columnsResponse.json();
          if (columnsResult.success) {
            const hasWalletBalance = columnsResult.data.some(col => col.name === 'wallet_balance');
            if (hasWalletBalance) {
              addResult('DB-1', 'pass', 'members 表包含 wallet_balance 欄位');
            } else {
              addResult('DB-1', 'fail', 'members 表缺少 wallet_balance 欄位', '使用 Members 表修復工具');
            }
          } else {
            addResult('DB-1', 'fail', `檢查表結構失敗: ${columnsResult.error}`, '檢查資料庫權限');
          }
        } else {
          addResult('DB-1', 'fail', '無法檢查表結構', '檢查 API 權限');
        }
      } catch (error) {
        addResult('DB-1', 'fail', `表結構檢查錯誤: ${error.message}`, '檢查網路連接');
      }

      // 測試 3: 錢包交易記錄表
      addResult('DB-2', 'pass', '檢查 wallet_transactions 表...');
      
      try {
        const transactionsResponse = await fetch('/api/admin/database-query', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            query: "SELECT COUNT(*) as count FROM wallet_transactions"
          })
        });

        if (transactionsResponse.ok) {
          const transactionsResult = await transactionsResponse.json();
          if (transactionsResult.success) {
            addResult('DB-2', 'pass', `wallet_transactions 表存在，有 ${transactionsResult.data[0].count} 筆記錄`);
          } else {
            addResult('DB-2', 'fail', 'wallet_transactions 表不存在', '使用資料庫重新初始化工具');
          }
        } else {
          addResult('DB-2', 'fail', '無法查詢 wallet_transactions 表', '檢查表是否存在');
        }
      } catch (error) {
        addResult('DB-2', 'fail', `交易表檢查錯誤: ${error.message}`, '檢查資料庫結構');
      }

      // 測試 4: 支付方式表
      addResult('DB-3', 'pass', '檢查 payment_methods 表...');
      
      try {
        const paymentResponse = await fetch('/api/admin/database-query', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            query: "SELECT COUNT(*) as count FROM payment_methods WHERE is_active = 1"
          })
        });

        if (paymentResponse.ok) {
          const paymentResult = await paymentResponse.json();
          if (paymentResult.success) {
            addResult('DB-3', 'pass', `payment_methods 表存在，有 ${paymentResult.data[0].count} 種可用支付方式`);
          } else {
            addResult('DB-3', 'fail', 'payment_methods 表不存在', '使用資料庫重新初始化工具');
          }
        } else {
          addResult('DB-3', 'fail', '無法查詢 payment_methods 表', '檢查表是否存在');
        }
      } catch (error) {
        addResult('DB-3', 'fail', `支付方式表檢查錯誤: ${error.message}`, '檢查資料庫結構');
      }

      // 測試 5: 錢包 API
      addResult('API-2', 'pass', '測試錢包餘額 API...');
      
      try {
        const walletResponse = await fetch('/api/wallet/balance', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (walletResponse.ok) {
          const walletResult = await walletResponse.json();
          if (walletResult.success) {
            addResult('API-2', 'pass', `錢包 API 正常，餘額: $${walletResult.data.balance}`);
          } else {
            addResult('API-2', 'fail', `錢包 API 失敗: ${walletResult.error}`, '檢查 members 表的 wallet_balance 欄位');
          }
        } else {
          addResult('API-2', 'fail', '錢包 API 無法訪問', '檢查 API 是否存在');
        }
      } catch (error) {
        addResult('API-2', 'fail', `錢包 API 錯誤: ${error.message}`, '檢查網路連接');
      }

      // 生成總結
      const results = diagnosisResults;
      const passCount = results.filter(r => r.status === 'pass').length;
      const failCount = results.filter(r => r.status === 'fail').length;
      const warningCount = results.filter(r => r.status === 'warning').length;

      let overallStatus = 'healthy';
      let recommendation = '錢包功能完全正常';

      if (failCount > 0) {
        overallStatus = 'critical';
        recommendation = '需要立即修復';
      } else if (warningCount > 0) {
        overallStatus = 'warning';
        recommendation = '建議檢查和優化';
      }

      setSummary({
        total: results.length,
        pass: passCount,
        fail: failCount,
        warning: warningCount,
        status: overallStatus,
        recommendation: recommendation
      });

      addResult('完成', 'pass', `診斷完成 - 通過: ${passCount}, 失敗: ${failCount}, 警告: ${warningCount}`);

    } catch (error) {
      addResult('錯誤', 'fail', '診斷過程中出現錯誤: ' + error.message);
    } finally {
      setIsDiagnosing(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pass': return 'text-green-600';
      case 'fail': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pass': return '✅';
      case 'fail': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  const getBgColor = (status) => {
    switch (status) {
      case 'pass': return 'bg-green-50 border-green-200';
      case 'fail': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      default: return 'bg-blue-50 border-blue-200';
    }
  };

  return (
    <>
      <Head>
        <title>錢包快速診斷 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🩺 錢包快速診斷</h1>
                <p className="text-gray-600 mt-2">快速檢查錢包功能的健康狀態</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/fix-members-table')}
                  className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
                >
                  🔧 修復 Members
                </button>
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={runQuickDiagnosis}
                  disabled={isDiagnosing}
                  className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 font-medium"
                >
                  {isDiagnosing ? '診斷中...' : '🩺 開始診斷'}
                </button>
              </div>
            </div>

            {/* 診斷總結 */}
            {summary && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 診斷總結</h2>
                <div className={`rounded-lg p-6 border ${
                  summary.status === 'healthy' ? 'bg-green-50 border-green-200' :
                  summary.status === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                  'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className={`text-lg font-medium ${
                      summary.status === 'healthy' ? 'text-green-800' :
                      summary.status === 'warning' ? 'text-yellow-800' :
                      'text-red-800'
                    }`}>
                      {summary.status === 'healthy' ? '🟢 健康' :
                       summary.status === 'warning' ? '🟡 警告' :
                       '🔴 嚴重'}
                    </h3>
                    <span className={`text-sm ${
                      summary.status === 'healthy' ? 'text-green-600' :
                      summary.status === 'warning' ? 'text-yellow-600' :
                      'text-red-600'
                    }`}>
                      {summary.recommendation}
                    </span>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{summary.pass}</div>
                      <div className="text-sm text-green-800">通過</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{summary.fail}</div>
                      <div className="text-sm text-red-800">失敗</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{summary.warning}</div>
                      <div className="text-sm text-yellow-800">警告</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 診斷結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 詳細結果</h2>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {diagnosisResults.length > 0 ? (
                  diagnosisResults.map((result, index) => (
                    <div key={index} className={`rounded-lg p-4 border ${getBgColor(result.status)}`}>
                      <div className="flex items-start space-x-3">
                        <span className={getStatusColor(result.status)}>
                          {getStatusIcon(result.status)}
                        </span>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 text-sm">
                            <span className="text-gray-500">[{result.timestamp}]</span>
                            <span className="font-medium text-gray-700">{result.test}:</span>
                            <span className={getStatusColor(result.status)}>{result.message}</span>
                          </div>
                          {result.solution && (
                            <div className="mt-2 text-sm text-gray-600">
                              <strong>建議:</strong> {result.solution}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「開始診斷」來檢查錢包功能狀態
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 診斷項目</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>認證檢查:</strong> 驗證用戶登入狀態和權限</div>
                <div><strong>API 測試:</strong> 測試資料庫查詢和錢包 API</div>
                <div><strong>資料庫結構:</strong> 檢查錢包相關表和欄位</div>
                <div><strong>數據完整性:</strong> 驗證數據是否正確</div>
                <div><strong>功能可用性:</strong> 確認錢包功能是否正常</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
