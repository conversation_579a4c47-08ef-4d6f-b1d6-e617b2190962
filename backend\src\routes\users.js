const express = require('express');
const UserController = require('../controllers/UserController');
const { authenticateToken } = require('../middleware/authMiddleware');
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: 用戶管理相關 API
 */

/**
 * @swagger
 * /api/v1/users/profile:
 *   get:
 *     summary: 獲取用戶個人資料
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 獲取成功
 *       401:
 *         description: 未授權
 *       404:
 *         description: 用戶不存在
 */
router.get('/profile', authenticateToken, UserController.getProfile);

/**
 * @swagger
 * /api/v1/users/profile:
 *   put:
 *     summary: 更新用戶個人資料
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phone:
 *                 type: string
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *               gender:
 *                 type: string
 *               timezone:
 *                 type: string
 *               language:
 *                 type: string
 *     responses:
 *       200:
 *         description: 更新成功
 *       401:
 *         description: 未授權
 *       404:
 *         description: 用戶不存在
 */
router.put('/profile', authenticateToken, UserController.updateProfile);

/**
 * @swagger
 * /api/v1/users/change-password:
 *   post:
 *     summary: 修改密碼
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *     responses:
 *       200:
 *         description: 修改成功
 *       400:
 *         description: 密碼錯誤
 *       401:
 *         description: 未授權
 */
router.post('/change-password', authenticateToken, UserController.changePassword);

/**
 * @swagger
 * /api/v1/users/activity-logs:
 *   get:
 *     summary: 獲取用戶活動記錄
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每頁數量
 *     responses:
 *       200:
 *         description: 獲取成功
 *       401:
 *         description: 未授權
 */
router.get('/activity-logs', authenticateToken, UserController.getActivityLogs);

/**
 * @swagger
 * /api/v1/users/delete-account:
 *   delete:
 *     summary: 刪除帳號
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: 刪除成功
 *       400:
 *         description: 密碼錯誤
 *       401:
 *         description: 未授權
 */
router.delete('/delete-account', authenticateToken, UserController.deleteAccount);

module.exports = router;
