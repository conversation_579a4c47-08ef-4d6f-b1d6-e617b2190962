// 測試訂閱 API
const https = require('https');
const http = require('http');

// 簡單的 fetch 實現
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: { raw: () => res.headers },
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function testSubscriptionAPI() {
  try {
    console.log('🔐 測試訂閱 API...');
    
    // 1. 先登入獲取 token
    console.log('1️⃣ 登入...');
    const loginResponse = await fetch('http://localhost:3000/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '1234'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      console.error('❌ 登入失敗:', loginResult);
      return;
    }

    const token = loginResult.data.token;
    console.log('✅ 登入成功，獲得 token');
    console.log('Token:', token.substring(0, 50) + '...');

    // 2. 測試訂閱 API
    console.log('\n2️⃣ 測試訂閱 API...');
    console.log('請求 URL: http://localhost:3001/api/subscriptions/simple');
    console.log('Authorization: Bearer', token.substring(0, 20) + '...');
    
    const subscriptionResponse = await fetch('http://localhost:3001/api/subscriptions/simple', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('響應狀態:', subscriptionResponse.status);
    console.log('響應 headers:', subscriptionResponse.headers.raw());

    const subscriptionResult = await subscriptionResponse.json();
    console.log('訂閱 API 響應:', JSON.stringify(subscriptionResult, null, 2));

    if (subscriptionResult.success) {
      console.log('✅ 訂閱 API 認證成功！');
    } else {
      console.log('❌ 訂閱 API 認證失敗:', subscriptionResult.error);
    }

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testSubscriptionAPI();
