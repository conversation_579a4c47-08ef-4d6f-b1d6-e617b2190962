// 修復所有數據庫問題：視圖和交易類型約束
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'frontend', 'lib', 'database', 'database.sqlite');

function fixAllDatabaseIssues() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('❌ 連接數據庫失敗:', err);
        reject(err);
        return;
      }
      console.log('✅ 數據庫連接成功');
    });

    console.log('🔧 修復所有數據庫問題...');

    // 1. 修復所有有問題的視圖
    console.log('\n1️⃣ 修復所有視圖...');
    
    // 刪除所有有問題的視圖
    db.run('DROP VIEW IF EXISTS member_service_details', (err) => {
      if (err) {
        console.error('❌ 刪除 member_service_details 視圖失敗:', err);
        reject(err);
        return;
      }

      console.log('✅ member_service_details 視圖已刪除');

      db.run('DROP VIEW IF EXISTS service_summary', (err) => {
        if (err) {
          console.error('❌ 刪除 service_summary 視圖失敗:', err);
          reject(err);
          return;
        }

        console.log('✅ service_summary 視圖已刪除');

        // 重新創建修復後的視圖
        db.run(`
          CREATE VIEW member_service_details AS
          SELECT 
              ms.id as subscription_id,
              m.id as member_id,
              m.name as member_name,
              m.email as member_email,
              s.id as service_id,
              s.name as service_name,
              s.description as service_description,
              s.price as service_price,
              s.billing_cycle,
              s.category,
              ms.status as subscription_status,
              ms.subscribed_at,
              ms.next_billing_date
          FROM member_services ms
          JOIN members m ON ms.member_id = m.id
          JOIN services s ON ms.service_id = s.id
        `, (err) => {
          if (err) {
            console.error('❌ 重新創建 member_service_details 視圖失敗:', err);
            reject(err);
            return;
          }

          console.log('✅ member_service_details 視圖重新創建成功');

          db.run(`
            CREATE VIEW service_summary AS
            SELECT
                s.id,
                s.name,
                s.description,
                s.price,
                s.category,
                s.status,
                COALESCE(ss.total_subscribers, 0) as total_subscribers,
                COALESCE(ss.active_subscribers, 0) as active_subscribers,
                COALESCE(ss.total_revenue, 0) as total_revenue,
                COALESCE(ss.monthly_revenue, 0) as monthly_revenue,
                s.created_at,
                s.updated_at
            FROM services s
            LEFT JOIN service_stats ss ON s.id = ss.service_id
          `, (err) => {
            if (err) {
              console.error('❌ 重新創建 service_summary 視圖失敗:', err);
              reject(err);
              return;
            }

            console.log('✅ service_summary 視圖重新創建成功');

            // 2. 現在修復交易類型約束
            console.log('\n2️⃣ 修復錢包交易類型約束...');

            // 檢查當前表結構
            db.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='wallet_transactions'", (err, row) => {
              if (err) {
                console.error('❌ 檢查表結構失敗:', err);
                reject(err);
                return;
              }

              if (!row) {
                console.log('❌ wallet_transactions 表不存在');
                reject(new Error('wallet_transactions 表不存在'));
                return;
              }

              console.log('📋 當前表結構:', row.sql);

              // 檢查是否需要修復
              if (row.sql.includes("'penalty'")) {
                console.log('✅ 約束已經包含 penalty 類型，無需修復');
                db.close();
                resolve();
                return;
              }

              console.log('🔄 需要修復約束...');

              // 關閉外鍵約束
              db.run('PRAGMA foreign_keys = OFF', (err) => {
                if (err) {
                  console.error('❌ 關閉外鍵約束失敗:', err);
                  reject(err);
                  return;
                }

                console.log('✅ 外鍵約束已關閉');

                // 開始事務
                db.run('BEGIN TRANSACTION', (err) => {
                  if (err) {
                    console.error('❌ 開始事務失敗:', err);
                    reject(err);
                    return;
                  }

                  // 創建新表
                  db.run(`
                    CREATE TABLE wallet_transactions_new (
                      id INTEGER PRIMARY KEY AUTOINCREMENT,
                      member_id INTEGER NOT NULL,
                      transaction_type TEXT NOT NULL CHECK(transaction_type IN ('recharge', 'withdraw', 'payment', 'refund', 'bonus', 'penalty')),
                      amount DECIMAL(15,2) NOT NULL,
                      balance_before DECIMAL(15,2) NOT NULL,
                      balance_after DECIMAL(15,2) NOT NULL,
                      description TEXT,
                      reference_id TEXT,
                      reference_type TEXT,
                      status TEXT DEFAULT 'completed' CHECK(status IN ('pending', 'completed', 'failed', 'cancelled')),
                      payment_method TEXT,
                      payment_reference TEXT,
                      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                      FOREIGN KEY (member_id) REFERENCES members(id)
                    )
                  `, (err) => {
                    if (err) {
                      console.error('❌ 創建新表失敗:', err);
                      db.run('ROLLBACK');
                      reject(err);
                      return;
                    }

                    console.log('✅ 新表創建成功');

                    // 複製數據
                    db.run(`
                      INSERT INTO wallet_transactions_new 
                      SELECT * FROM wallet_transactions
                    `, (err) => {
                      if (err) {
                        console.error('❌ 複製數據失敗:', err);
                        db.run('ROLLBACK');
                        reject(err);
                        return;
                      }

                      console.log('✅ 數據複製成功');

                      // 刪除舊表
                      db.run('DROP TABLE wallet_transactions', (err) => {
                        if (err) {
                          console.error('❌ 刪除舊表失敗:', err);
                          db.run('ROLLBACK');
                          reject(err);
                          return;
                        }

                        console.log('✅ 舊表刪除成功');

                        // 重命名新表
                        db.run('ALTER TABLE wallet_transactions_new RENAME TO wallet_transactions', (err) => {
                          if (err) {
                            console.error('❌ 重命名表失敗:', err);
                            db.run('ROLLBACK');
                            reject(err);
                            return;
                          }

                          console.log('✅ 表重命名成功');

                          // 重建索引
                          const indexes = [
                            "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_member ON wallet_transactions(member_id)",
                            "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_type ON wallet_transactions(transaction_type)",
                            "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_status ON wallet_transactions(status)",
                            "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_date ON wallet_transactions(created_at)"
                          ];

                          let indexCompleted = 0;
                          indexes.forEach((indexSql, i) => {
                            db.run(indexSql, (err) => {
                              if (err) {
                                console.error(`❌ 創建索引 ${i + 1} 失敗:`, err);
                              } else {
                                console.log(`✅ 索引 ${i + 1} 創建成功`);
                              }
                              
                              indexCompleted++;
                              if (indexCompleted === indexes.length) {
                                // 提交事務
                                db.run('COMMIT', (err) => {
                                  if (err) {
                                    console.error('❌ 提交事務失敗:', err);
                                    reject(err);
                                    return;
                                  }

                                  console.log('✅ 事務提交成功');

                                  // 重新啟用外鍵約束
                                  db.run('PRAGMA foreign_keys = ON', (err) => {
                                    if (err) {
                                      console.error('❌ 啟用外鍵約束失敗:', err);
                                    } else {
                                      console.log('✅ 外鍵約束已啟用');
                                    }

                                    // 驗證修復結果
                                    db.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='wallet_transactions'", (err, row) => {
                                      if (err) {
                                        console.error('❌ 驗證失敗:', err);
                                        reject(err);
                                        return;
                                      }

                                      console.log('\n📋 修復後表結構:', row.sql);

                                      if (row.sql.includes("'penalty'")) {
                                        console.log('\n🎉 所有數據庫問題修復成功！');
                                        console.log('✅ 視圖 member_service_details 已修復');
                                        console.log('✅ 視圖 service_summary 已修復');
                                        console.log('✅ 錢包交易類型約束已修復，現在支持:');
                                        console.log('   - recharge (充值)');
                                        console.log('   - withdraw (提現)');
                                        console.log('   - payment (支付)');
                                        console.log('   - refund (退款)');
                                        console.log('   - bonus (獎勵)');
                                        console.log('   - penalty (手續費/罰金)');
                                      } else {
                                        console.log('❌ 約束修復失敗');
                                      }

                                      db.close();
                                      resolve();
                                    });
                                  });
                                });
                              }
                            });
                          });
                        });
                      });
                    });
                  });
                });
              });
            });
          });
        });
      });
    });
  });
}

// 執行修復
fixAllDatabaseIssues()
  .then(() => {
    console.log('\n✅ 所有數據庫問題修復完成');
  })
  .catch((error) => {
    console.error('\n❌ 修復失敗:', error);
  });
