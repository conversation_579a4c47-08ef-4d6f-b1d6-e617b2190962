// 服務管理 API - 資料庫版本
const path = require('path');
// 動態導入認證中間件
const getAuth = () => {
  try {
    const authPaths = [
      path.join(process.cwd(), 'lib', 'middleware', 'auth'),
      path.join(process.cwd(), 'lib', 'middleware', 'auth.js'),
      path.join(__dirname, '..', '..', '..', 'lib', 'middleware', 'auth'),
      path.join(__dirname, '..', '..', '..', 'lib', 'middleware', 'auth.js')
    ];

    for (const authPath of authPaths) {
      try {
        return require(authPath);
      } catch (err) {
        continue;
      }
    }

    throw new Error('無法找到認證中間件');
  } catch (error) {
    console.error('認證中間件載入失敗:', error);
    throw error;
  }
};

// 使用 frontend 內的資料庫模型
const getModels = async () => {
  try {
    // 嘗試載入 Service 模型
    const servicePaths = [
      path.join(process.cwd(), 'lib', 'database', 'models', 'Service'),
      path.join(process.cwd(), 'lib', 'database', 'models', 'Service.js'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'Service'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'Service.js')
    ];

    const memberServicePaths = [
      path.join(process.cwd(), 'lib', 'database', 'models', 'MemberService'),
      path.join(process.cwd(), 'lib', 'database', 'models', 'MemberService.js'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'MemberService'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'MemberService.js')
    ];

    let Service, MemberService;

    // 載入 Service 模型
    for (const servicePath of servicePaths) {
      try {
        Service = require(servicePath);
        break;
      } catch (err) {
        continue;
      }
    }

    // 載入 MemberService 模型
    for (const memberServicePath of memberServicePaths) {
      try {
        MemberService = require(memberServicePath);
        break;
      } catch (err) {
        continue;
      }
    }

    if (!Service || !MemberService) {
      throw new Error('無法載入必要的模型');
    }

    return { Service, MemberService };
  } catch (error) {
    console.error('模型載入失敗:', error);
    throw error;
  }
};

async function handler(req, res) {
  try {
    const { Service, MemberService } = await getModels();
    const { method } = req;

    switch (method) {
      case 'GET':
        // 獲取所有服務
        const { status, category, search, page = 1, limit = 50, member_id } = req.query;
        
        if (member_id || req.userId) {
          // 獲取會員可用的服務 - 優先使用登入用戶的 ID
          const targetMemberId = req.userId || parseInt(member_id);
          const availableServices = await MemberService.getAvailableServices(targetMemberId);
          return res.status(200).json({
            success: true,
            data: availableServices,
            message: `找到 ${availableServices.length} 個可用服務`
          });
        } else {
          // 獲取所有服務
          const options = {
            status,
            category,
            search,
            limit: parseInt(limit),
            offset: (parseInt(page) - 1) * parseInt(limit)
          };

          const services = await Service.findAll(options);
          
          return res.status(200).json({
            success: true,
            data: services.map(service => service.toJSON()),
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: services.length
            }
          });
        }
        
      case 'POST':
        // 創建新服務
        const {
          name,
          description,
          price,
          billing_cycle = 'monthly',
          category: serviceCategory,
          features = [],
          status: serviceStatus = 'active',
          popular = false,
          max_users = 1,
          api_limit = 0,
          storage_limit,
          support_level = 'basic'
        } = req.body;
        
        // 驗證必要欄位
        if (!name || !description || price === undefined) {
          return res.status(400).json({
            success: false,
            error: '缺少必要欄位：name, description, price'
          });
        }

        // 驗證價格
        if (isNaN(parseFloat(price)) || parseFloat(price) < 0) {
          return res.status(400).json({
            success: false,
            error: '價格必須是有效的數字'
          });
        }

        const serviceData = {
          name: name.trim(),
          description: description.trim(),
          price: parseFloat(price),
          billing_cycle,
          category: serviceCategory?.trim(),
          features: Array.isArray(features) ? features : [],
          status: serviceStatus,
          popular: Boolean(popular),
          max_users: parseInt(max_users) || 1,
          api_limit: parseInt(api_limit) || 0,
          storage_limit: storage_limit?.trim(),
          support_level
        };

        const newService = await Service.create(serviceData);

        return res.status(201).json({
          success: true,
          data: newService.toJSON(),
          message: '服務創建成功'
        });
        
      case 'PUT':
        // 批量更新服務
        const { serviceIds, updates } = req.body;
        
        if (!serviceIds || !Array.isArray(serviceIds)) {
          return res.status(400).json({
            success: false,
            error: '無效的服務 ID 列表'
          });
        }

        const updatedServices = [];
        
        for (const serviceId of serviceIds) {
          const service = await Service.findById(serviceId);
          if (service) {
            await service.update(updates);
            updatedServices.push(service.toJSON());
          }
        }

        return res.status(200).json({
          success: true,
          data: updatedServices,
          message: `${updatedServices.length} 個服務已更新`
        });
        
      case 'DELETE':
        // 批量刪除服務
        const { ids } = req.body;
        
        if (!ids || !Array.isArray(ids)) {
          return res.status(400).json({
            success: false,
            error: '無效的服務 ID 列表'
          });
        }

        let deletedCount = 0;
        const errors = [];
        
        for (const serviceId of ids) {
          try {
            const service = await Service.findById(serviceId);
            if (service) {
              await service.delete();
              deletedCount++;
            }
          } catch (error) {
            errors.push(`服務 ${serviceId}: ${error.message}`);
          }
        }

        return res.status(200).json({
          success: true,
          message: `${deletedCount} 個服務已刪除`,
          errors: errors.length > 0 ? errors : undefined
        });

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).json({
          success: false,
          error: `方法 ${method} 不被允許`
        });
    }
  } catch (error) {
    console.error('服務 API 錯誤:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

// 對於服務 API，GET 請求使用可選認證，POST/PUT/DELETE 需要管理員權限
export default async function wrappedHandler(req, res) {
  const { withAuth } = getAuth();

  if (req.method === 'GET') {
    // GET 請求使用可選認證
    return withAuth(handler, { required: false })(req, res);
  } else {
    // POST/PUT/DELETE 需要管理員權限
    return withAuth(handler, { admin: true })(req, res);
  }
};
