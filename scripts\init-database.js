#!/usr/bin/env node

// 資料庫初始化腳本
const path = require('path');
const fs = require('fs');

// 設置項目根目錄
process.chdir(path.join(__dirname, '..'));

async function initDatabase() {
  try {
    console.log('🚀 開始初始化資料庫...');
    
    // 檢查 sqlite3 是否已安裝
    try {
      require('sqlite3');
      console.log('✅ sqlite3 已安裝');
    } catch (error) {
      console.log('📦 正在安裝 sqlite3...');
      const { execSync } = require('child_process');
      execSync('npm install sqlite3', { stdio: 'inherit' });
      console.log('✅ sqlite3 安裝完成');
    }

    // 導入資料庫初始化模組
    const { initializeDatabase, testConnection, resetDatabase } = require('../database/init');
    
    // 檢查是否需要重置資料庫
    const shouldReset = process.argv.includes('--reset');
    
    if (shouldReset) {
      console.log('🔄 重置資料庫...');
      await resetDatabase();
    } else {
      console.log('🔧 初始化資料庫...');
      await initializeDatabase();
    }
    
    // 測試連接
    console.log('🧪 測試資料庫連接...');
    const testResult = await testConnection();
    
    if (testResult) {
      console.log('🎉 資料庫初始化完成！');
      console.log('');
      console.log('📋 可用的 API 端點：');
      console.log('  GET    /api/services/db - 獲取所有服務');
      console.log('  POST   /api/services/db - 創建新服務');
      console.log('  GET    /api/subscriptions - 獲取會員訂閱');
      console.log('  POST   /api/subscriptions - 創建新訂閱');
      console.log('');
      console.log('💡 使用方法：');
      console.log('  1. 啟動開發服務器：npm run dev');
      console.log('  2. 訪問測試頁面：http://localhost:3000/database-test');
      console.log('  3. 測試 API：http://localhost:3000/api/services/db');
    } else {
      console.error('❌ 資料庫測試失敗');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 初始化失敗:', error.message);
    console.error('詳細錯誤:', error);
    process.exit(1);
  }
}

// 創建測試用戶的函數
async function createTestUser() {
  try {
    const { getDatabase, dbRun, dbGet } = require('../database/init');
    const db = await getDatabase();
    
    // 檢查測試用戶是否已存在
    const existingUser = await dbGet(db, 'SELECT id FROM members WHERE email = ?', ['<EMAIL>']);
    
    if (!existingUser) {
      console.log('👤 創建測試用戶...');
      const result = await dbRun(db, `
        INSERT INTO members (email, name, password_hash, role) 
        VALUES (?, ?, ?, ?)
      `, ['<EMAIL>', '測試用戶', '$2b$10$test_hash', 'user']);
      
      console.log(`✅ 測試用戶創建成功，ID: ${result.id}`);
      return result.id;
    } else {
      console.log('✅ 測試用戶已存在，ID:', existingUser.id);
      return existingUser.id;
    }
  } catch (error) {
    console.error('❌ 創建測試用戶失敗:', error);
    throw error;
  }
}

// 創建測試訂閱的函數
async function createTestSubscriptions() {
  try {
    console.log('📋 創建測試訂閱...');
    
    const userId = await createTestUser();
    const MemberService = require('../database/models/MemberService');
    
    // 訂閱第一個服務
    try {
      const subscription1 = await MemberService.subscribe(userId, 1);
      console.log(`✅ 測試訂閱 1 創建成功: ${subscription1.service_name}`);
    } catch (error) {
      if (error.message.includes('已經訂閱')) {
        console.log('ℹ️ 測試訂閱 1 已存在');
      } else {
        throw error;
      }
    }
    
    // 訂閱第二個服務
    try {
      const subscription2 = await MemberService.subscribe(userId, 5);
      console.log(`✅ 測試訂閱 2 創建成功: ${subscription2.service_name}`);
    } catch (error) {
      if (error.message.includes('已經訂閱')) {
        console.log('ℹ️ 測試訂閱 2 已存在');
      } else {
        throw error;
      }
    }
    
    console.log('✅ 測試訂閱創建完成');
    
  } catch (error) {
    console.error('❌ 創建測試訂閱失敗:', error);
    throw error;
  }
}

// 主函數
async function main() {
  await initDatabase();
  
  // 如果指定了 --with-test-data 參數，創建測試數據
  if (process.argv.includes('--with-test-data')) {
    console.log('');
    console.log('🧪 創建測試數據...');
    await createTestSubscriptions();
  }
  
  console.log('');
  console.log('🎯 下一步：');
  console.log('  1. 運行 npm run dev 啟動開發服務器');
  console.log('  2. 訪問 http://localhost:3000/database-test 測試資料庫功能');
  console.log('  3. 查看 database/memberservice.db 文件確認資料庫已創建');
  
  process.exit(0);
}

// 如果直接運行此腳本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 腳本執行失敗:', error);
    process.exit(1);
  });
}

module.exports = {
  initDatabase,
  createTestUser,
  createTestSubscriptions
};
