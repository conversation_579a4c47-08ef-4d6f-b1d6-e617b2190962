// 錢包轉帳 API
const jwt = require('jsonwebtoken');
const Wallet = require('../../../../lib/database/models/Wallet');
const WalletTransaction = require('../../../../lib/database/models/WalletTransaction');
const Member = require('../../../../lib/database/models/Member');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

export default async function handler(req, res) {
  try {
    console.log('錢包轉帳 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    // 驗證認證
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('錢包轉帳 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    const fromUserId = decoded.userId;
    console.log('錢包轉帳 API: 發送方用戶 ID:', fromUserId);

    // 獲取請求數據
    const { recipient_email, amount, description } = req.body;

    // 驗證轉帳金額
    if (!amount || isNaN(amount) || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: '請輸入有效的轉帳金額'
      });
    }

    const amountNum = parseFloat(amount);

    // 檢查最小轉帳金額
    if (amountNum < 1) {
      return res.status(400).json({
        success: false,
        error: '最小轉帳金額為 NT$ 1'
      });
    }

    // 檢查最大轉帳金額
    if (amountNum > 100000) {
      return res.status(400).json({
        success: false,
        error: '單次轉帳金額不能超過 NT$ 100,000'
      });
    }

    // 驗證收款人郵箱
    if (!recipient_email || !recipient_email.includes('@')) {
      return res.status(400).json({
        success: false,
        error: '請輸入有效的收款人郵箱'
      });
    }

    // 查找收款人
    const recipient = await Member.findByEmail(recipient_email);
    if (!recipient) {
      return res.status(404).json({
        success: false,
        error: '收款人不存在'
      });
    }

    const toUserId = recipient.id;

    // 檢查是否轉帳給自己
    if (fromUserId === toUserId) {
      return res.status(400).json({
        success: false,
        error: '不能轉帳給自己'
      });
    }

    // 獲取發送方當前餘額
    const fromBalance = await Wallet.getBalance(fromUserId);
    
    if (amountNum > fromBalance) {
      return res.status(400).json({
        success: false,
        error: '餘額不足'
      });
    }

    // 獲取接收方當前餘額
    const toBalance = await Wallet.getBalance(toUserId);

    // 生成轉帳參考號
    const transferReference = `TF_${Date.now()}_${fromUserId}_${toUserId}`;

    // 創建發送方交易記錄
    const fromTransaction = await WalletTransaction.create({
      member_id: fromUserId,
      transaction_type: 'payment',
      amount: amountNum,
      balance_before: fromBalance,
      balance_after: fromBalance - amountNum,
      description: description || `轉帳給 ${recipient_email}`,
      status: 'completed',
      payment_method: '錢包轉帳',
      payment_reference: transferReference
    });

    // 創建接收方交易記錄
    const toTransaction = await WalletTransaction.create({
      member_id: toUserId,
      transaction_type: 'recharge',
      amount: amountNum,
      balance_before: toBalance,
      balance_after: toBalance + amountNum,
      description: description || `收到轉帳來自 ${decoded.email || '用戶'}`,
      status: 'completed',
      payment_method: '錢包轉帳',
      payment_reference: transferReference
    });

    // 更新雙方錢包餘額
    await Wallet.updateBalance(fromUserId, fromBalance - amountNum);
    await Wallet.updateBalance(toUserId, toBalance + amountNum);

    // 獲取更新後的餘額
    const newFromBalanceAmount = await Wallet.getBalance(fromUserId);
    const newFromBalance = {
      balance: newFromBalanceAmount,
      formatted_balance: `$${newFromBalanceAmount.toFixed(2)}`
    };

    console.log('錢包轉帳 API: 轉帳成功', {
      fromTransactionId: fromTransaction.id,
      toTransactionId: toTransaction.id,
      amount: amountNum,
      fromUser: fromUserId,
      toUser: toUserId,
      newFromBalance: newFromBalanceAmount
    });

    return res.status(200).json({
      success: true,
      data: {
        from_transaction: fromTransaction.toJSON(),
        to_transaction: toTransaction.toJSON(),
        new_balance: newFromBalance,
        transfer_details: {
          amount: amountNum,
          recipient_email: recipient_email,
          recipient_name: recipient.name,
          transfer_reference: transferReference,
          description: description || `轉帳給 ${recipient_email}`
        }
      },
      message: '轉帳成功'
    });

  } catch (error) {
    console.error('錢包轉帳 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
