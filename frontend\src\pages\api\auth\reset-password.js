// 密碼重置 API
const bcrypt = require('bcrypt');
const path = require('path');
const fs = require('fs');

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();
    
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];
    
    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('密碼重置 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }
    
    console.log('密碼重置 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('密碼重置 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    const { email, newPassword } = req.body;

    console.log('密碼重置 API: 收到請求', { email, newPassword: '***' });

    // 基本驗證
    if (!email || !newPassword) {
      return res.status(400).json({
        success: false,
        error: '請提供 email 和新密碼'
      });
    }

    // 密碼長度驗證
    if (newPassword.length < 4) {
      return res.status(400).json({
        success: false,
        error: '密碼長度至少需要 4 個字符'
      });
    }

    // 連接資料庫
    const db = connectDatabase();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    // 使用 Promise 包裝資料庫操作
    return new Promise(async (resolve) => {
      try {
        // 檢查用戶是否存在
        db.get('SELECT id, email, name FROM members WHERE email = ?', [email.toLowerCase()], async (err, user) => {
          if (err) {
            console.error('密碼重置 API: 查詢用戶失敗:', err);
            db.close();
            resolve(res.status(500).json({
              success: false,
              error: '資料庫查詢失敗'
            }));
            return;
          }

          if (!user) {
            console.log('密碼重置 API: 用戶不存在');
            db.close();
            resolve(res.status(404).json({
              success: false,
              error: '用戶不存在'
            }));
            return;
          }

          console.log('密碼重置 API: 找到用戶', { id: user.id, email: user.email });

          try {
            // 生成新密碼哈希
            console.log('密碼重置 API: 生成新密碼哈希...');
            const saltRounds = 10;
            const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
            console.log('密碼重置 API: 新密碼哈希生成成功');

            // 更新密碼
            db.run('UPDATE members SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', 
              [newPasswordHash, user.id], function(updateErr) {
              
              db.close();

              if (updateErr) {
                console.error('密碼重置 API: 更新密碼失敗:', updateErr);
                resolve(res.status(500).json({
                  success: false,
                  error: '更新密碼失敗'
                }));
                return;
              }

              console.log('密碼重置 API: 密碼更新成功');

              resolve(res.status(200).json({
                success: true,
                message: `用戶 ${user.email} 的密碼已成功重置`,
                data: {
                  userId: user.id,
                  email: user.email,
                  name: user.name
                }
              }));
            });

          } catch (hashError) {
            console.error('密碼重置 API: 密碼哈希生成失敗:', hashError);
            db.close();
            resolve(res.status(500).json({
              success: false,
              error: '密碼處理失敗'
            }));
          }
        });

      } catch (error) {
        console.error('密碼重置 API: 未預期的錯誤:', error);
        db.close();
        resolve(res.status(500).json({
          success: false,
          error: '服務器內部錯誤'
        }));
      }
    });

  } catch (error) {
    console.error('密碼重置 API: 外部錯誤:', error);
    
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
