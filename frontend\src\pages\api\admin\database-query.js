// 管理員資料庫查詢 API
const jwt = require('jsonwebtoken');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export default async function handler(req, res) {
  try {
    console.log('資料庫查詢 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    // 驗證認證
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('資料庫查詢 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    // 檢查管理員權限
    if (decoded.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: '需要管理員權限'
      });
    }

    const { query, params = [] } = req.body;

    if (!query) {
      return res.status(400).json({
        success: false,
        error: '請提供查詢語句'
      });
    }

    console.log('資料庫查詢 API: 執行查詢', { query, params });

    // 使用資料庫連接
    const { getDatabase, dbAll, dbGet, dbRun } = require('../../../../lib/database/init');
    const db = await getDatabase();

    // 安全檢查：只允許 SELECT 查詢和特定的 UPDATE/INSERT
    const queryLower = query.toLowerCase().trim();
    const allowedOperations = ['select', 'update members set wallet_balance', 'insert into wallet_transactions'];
    
    const isAllowed = allowedOperations.some(op => queryLower.startsWith(op));
    
    if (!isAllowed) {
      return res.status(400).json({
        success: false,
        error: '不允許的查詢操作'
      });
    }

    let result;
    
    if (queryLower.startsWith('select')) {
      // SELECT 查詢
      if (query.includes('LIMIT') || params.length > 0) {
        // 有參數或限制的查詢
        result = await dbAll(db, query, params);
      } else {
        // 簡單查詢
        result = await dbAll(db, query);
      }
    } else {
      // UPDATE 或 INSERT 查詢
      result = await dbRun(db, query, params);
      result = { changes: result.changes, lastID: result.lastID };
    }

    console.log('資料庫查詢 API: 查詢成功', { 
      rowCount: Array.isArray(result) ? result.length : 'N/A',
      result: Array.isArray(result) ? result.slice(0, 3) : result // 只顯示前3筆
    });

    return res.status(200).json({
      success: true,
      data: result,
      query: query,
      params: params,
      message: `查詢成功，返回 ${Array.isArray(result) ? result.length : 1} 筆數據`
    });

  } catch (error) {
    console.error('資料庫查詢 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '資料庫查詢失敗: ' + error.message,
      details: error.message
    });
  }
}
