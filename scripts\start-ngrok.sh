#!/bin/bash

# ngrok 啟動腳本
# 使用方法: ./scripts/start-ngrok.sh

echo "🚀 啟動 ngrok 隧道..."

# 檢查 ngrok 是否已安裝
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok 未安裝，請先安裝 ngrok"
    echo "📥 下載地址: https://ngrok.com/download"
    exit 1
fi

# 檢查配置文件是否存在
if [ ! -f "ngrok.yml" ]; then
    echo "❌ ngrok.yml 配置文件不存在"
    echo "📝 請先配置 ngrok.yml 文件"
    exit 1
fi

# 啟動前端隧道
echo "🌐 啟動前端隧道 (https://topyun.ngrok.app)"
ngrok start frontend --config=ngrok.yml

echo "✅ ngrok 隧道已啟動"
echo "🔗 前端地址: https://topyun.ngrok.app"
echo "📊 ngrok 控制台: http://localhost:4040"
