const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

// Swagger 配置
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '會員管理系統 API',
      version: process.env.API_VERSION || '1.0.0',
      description: '完整的會員管理系統 RESTful API 文檔',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: process.env.API_URL || 'http://localhost:3000',
        description: '開發環境'
      },
      {
        url: 'https://api.example.com',
        description: '生產環境'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: '用戶唯一識別碼'
            },
            username: {
              type: 'string',
              description: '用戶名'
            },
            email: {
              type: 'string',
              format: 'email',
              description: '郵件地址'
            },
            firstName: {
              type: 'string',
              description: '名字'
            },
            lastName: {
              type: 'string',
              description: '姓氏'
            },
            phone: {
              type: 'string',
              description: '手機號碼'
            },
            isEmailVerified: {
              type: 'boolean',
              description: '郵件是否已驗證'
            },
            isPhoneVerified: {
              type: 'boolean',
              description: '手機是否已驗證'
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'suspended', 'deleted'],
              description: '用戶狀態'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: '創建時間'
            }
          }
        },
        ApiResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              description: '請求是否成功'
            },
            message: {
              type: 'string',
              description: '響應訊息'
            },
            data: {
              type: 'object',
              description: '響應資料'
            },
            errors: {
              type: 'object',
              description: '錯誤詳情'
            }
          }
        }
      }
    },
    tags: [
      {
        name: 'Authentication',
        description: '認證相關 API'
      },
      {
        name: 'Users',
        description: '用戶管理 API'
      },
      {
        name: 'Payments',
        description: '支付相關 API'
      },
      {
        name: 'Subscriptions',
        description: '訂閱管理 API'
      },
      {
        name: 'Wallets',
        description: '錢包管理 API'
      }
    ]
  },
  apis: [
    './src/routes/*.js',
    './src/controllers/*.js'
  ]
};

// 生成 Swagger 規範
const specs = swaggerJsdoc(swaggerOptions);

// Swagger UI 配置
const swaggerUiOptions = {
  explorer: true,
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    showExtensions: true,
    showCommonExtensions: true
  },
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info { margin: 20px 0 }
    .swagger-ui .scheme-container { margin: 20px 0 }
  `,
  customSiteTitle: '會員管理系統 API 文檔'
};

module.exports = {
  specs,
  swaggerUi,
  swaggerUiOptions
};