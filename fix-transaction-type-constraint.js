// 修復錢包交易類型約束
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'frontend', 'lib', 'database', 'database.sqlite');

function fixTransactionTypeConstraint() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('❌ 連接數據庫失敗:', err);
        reject(err);
        return;
      }
      console.log('✅ 數據庫連接成功');
    });

    console.log('🔧 修復錢包交易類型約束...');

    // 1. 檢查當前表結構
    db.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='wallet_transactions'", (err, row) => {
      if (err) {
        console.error('❌ 檢查表結構失敗:', err);
        reject(err);
        return;
      }

      if (!row) {
        console.log('❌ wallet_transactions 表不存在');
        reject(new Error('wallet_transactions 表不存在'));
        return;
      }

      console.log('📋 當前表結構:', row.sql);

      // 2. 檢查是否需要修復
      if (row.sql.includes("'penalty'")) {
        console.log('✅ 約束已經包含 penalty 類型，無需修復');
        db.close();
        resolve();
        return;
      }

      console.log('🔄 需要修復約束，開始重建表...');

      // 3. 開始事務
      db.run('BEGIN TRANSACTION', (err) => {
        if (err) {
          console.error('❌ 開始事務失敗:', err);
          reject(err);
          return;
        }

        // 4. 創建新表
        db.run(`
          CREATE TABLE wallet_transactions_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            member_id INTEGER NOT NULL,
            transaction_type TEXT NOT NULL CHECK(transaction_type IN ('recharge', 'withdraw', 'payment', 'refund', 'bonus', 'penalty')),
            amount DECIMAL(15,2) NOT NULL,
            balance_before DECIMAL(15,2) NOT NULL,
            balance_after DECIMAL(15,2) NOT NULL,
            description TEXT,
            reference_id TEXT,
            reference_type TEXT,
            status TEXT DEFAULT 'completed' CHECK(status IN ('pending', 'completed', 'failed', 'cancelled')),
            payment_method TEXT,
            payment_reference TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members(id)
          )
        `, (err) => {
          if (err) {
            console.error('❌ 創建新表失敗:', err);
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          console.log('✅ 新表創建成功');

          // 5. 複製數據
          db.run(`
            INSERT INTO wallet_transactions_new 
            SELECT * FROM wallet_transactions
          `, (err) => {
            if (err) {
              console.error('❌ 複製數據失敗:', err);
              db.run('ROLLBACK');
              reject(err);
              return;
            }

            console.log('✅ 數據複製成功');

            // 6. 刪除舊表
            db.run('DROP TABLE wallet_transactions', (err) => {
              if (err) {
                console.error('❌ 刪除舊表失敗:', err);
                db.run('ROLLBACK');
                reject(err);
                return;
              }

              console.log('✅ 舊表刪除成功');

              // 7. 重命名新表
              db.run('ALTER TABLE wallet_transactions_new RENAME TO wallet_transactions', (err) => {
                if (err) {
                  console.error('❌ 重命名表失敗:', err);
                  db.run('ROLLBACK');
                  reject(err);
                  return;
                }

                console.log('✅ 表重命名成功');

                // 8. 重建索引
                const indexes = [
                  "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_member ON wallet_transactions(member_id)",
                  "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_type ON wallet_transactions(transaction_type)",
                  "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_status ON wallet_transactions(status)",
                  "CREATE INDEX IF NOT EXISTS idx_wallet_transactions_date ON wallet_transactions(created_at)"
                ];

                let indexCompleted = 0;
                indexes.forEach((indexSql, i) => {
                  db.run(indexSql, (err) => {
                    if (err) {
                      console.error(`❌ 創建索引 ${i + 1} 失敗:`, err);
                    } else {
                      console.log(`✅ 索引 ${i + 1} 創建成功`);
                    }
                    
                    indexCompleted++;
                    if (indexCompleted === indexes.length) {
                      // 9. 提交事務
                      db.run('COMMIT', (err) => {
                        if (err) {
                          console.error('❌ 提交事務失敗:', err);
                          reject(err);
                          return;
                        }

                        console.log('✅ 事務提交成功');

                        // 10. 驗證修復結果
                        db.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='wallet_transactions'", (err, row) => {
                          if (err) {
                            console.error('❌ 驗證失敗:', err);
                            reject(err);
                            return;
                          }

                          console.log('📋 修復後表結構:', row.sql);

                          if (row.sql.includes("'penalty'")) {
                            console.log('🎉 約束修復成功！現在支持以下交易類型:');
                            console.log('   - recharge (充值)');
                            console.log('   - withdraw (提現)');
                            console.log('   - payment (支付)');
                            console.log('   - refund (退款)');
                            console.log('   - bonus (獎勵)');
                            console.log('   - penalty (手續費/罰金)');
                          } else {
                            console.log('❌ 約束修復失敗');
                          }

                          db.close();
                          resolve();
                        });
                      });
                    }
                  });
                });
              });
            });
          });
        });
      });
    });
  });
}

// 執行修復
fixTransactionTypeConstraint()
  .then(() => {
    console.log('\n✅ 錢包交易類型約束修復完成');
  })
  .catch((error) => {
    console.error('\n❌ 修復失敗:', error);
  });
