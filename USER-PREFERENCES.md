# 用戶偏好與開發規則

本文檔記錄用戶的設計偏好、功能開發偏好和系統架構要求，作為持續開發的指導原則。

## 🎨 UI/UX 設計偏好

### 極簡設計原則
- **移除大圖標** - 移除所有不必要的裝飾性圖標、圓形圖標、橢圓形圖標
- **保留功能圖標** - 只保留必要的功能性小圖標
- **無多餘裝飾** - 避免使用大型裝飾元素和視覺噪音
- **純淨界面** - 通過顏色、間距和字體大小建立視覺層次
- **專業外觀** - 商業級的簡潔設計風格

### 具體UI改進要求
- **儀表板快速操作區域** - 移除大圖標，使用文字或小圖標
- **訂閱程序界面** - 移除641-650行附近的大圖標
- **會員管理界面** - 優化佈局，移除不必要的視覺元素
- **檢查標記** - 使用小型或無檢查標記圖標
- **按鈕設計** - 簡潔的按鈕樣式，避免過度裝飾

### 路由偏好
- **會員儀表板** - 使用 `/minimal-dashboard` 而非 `/dashboard`
- **簡潔URL結構** - 偏好語義化的簡潔路由命名

## 🔧 功能開發偏好

### 數據存儲優先級
- **數據庫優先** - 所有數據必須存儲在實際數據庫中，而非內存或模擬數據
- **真實數據源** - 會員註冊、訂閱、服務、錢包數據都必須從數據庫讀取
- **避免模擬數據** - 不使用 mock 或 in-memory 數據源作為主要數據來源
- **數據持久化** - 確保所有用戶操作的數據都能持久化保存

### 會員系統功能
- **錢包管理** - 繼續開發充值和提現功能
- **服務管理** - 管理員可以添加和修改服務項目
- **數據分析** - 開發使用統計和數據分析功能
- **數據庫管理界面** - 管理員可以查看服務表、會員表、訂閱關係表

### 會員儀表板內容
- **移除管理鏈接** - 移除"管理您的服務訂閱"、"系統測試"、"資料庫測試"等鏈接
- **服務列表顯示** - 登入後以列表格式顯示已訂閱的服務
- **錢包頁面鏈接** - 從會員儀表板提供錢包頁面的訪問鏈接

## 📊 數據管理規則

### 訂閱管理邏輯
- **取消訂閱** - 將狀態從 'active' 改為 'cancelled'，不刪除記錄
- **重新訂閱** - 更新現有記錄狀態從 'cancelled' 到 'active'，不創建新記錄
- **唯一性約束** - 同一會員對同一服務只能有一條記錄（member-service組合唯一）
- **服務可見性** - 取消的服務重新出現在可用服務列表中供重新訂閱

### 服務列表邏輯
- **活躍訂閱** - 已訂閱的服務不出現在可用服務列表中
- **可用服務** - 未訂閱和已取消的服務出現在可用服務列表中
- **即時更新** - 訂閱服務後立即從可用列表中移除
- **數據同步** - 內存服務和新增服務同時顯示在服務列表中

## 🔐 用戶角色管理

### 角色偏好
- **用戶角色** - 用戶偏好設置為"一般會員"而非管理員
- **角色管理工具** - 管理員工具應先顯示當前用戶信息，再允許選擇要修改的用戶角色

### 權限控制
- **管理員功能** - 服務管理、會員管理、數據庫查看等功能
- **一般會員功能** - 服務訂閱、錢包管理、個人資料管理

## 🗄️ 數據庫設計原則

### 數據完整性
- **保留歷史記錄** - 不刪除訂閱記錄，使用狀態管理
- **審計追蹤** - 保留所有操作的歷史記錄
- **關係完整性** - 維護表之間的外鍵關係

### 表結構要求
- **會員表** - 存儲用戶基本信息和角色
- **服務表** - 存儲可用服務信息
- **會員服務關係表** - 存儲訂閱關係和狀態
- **錢包表** - 存儲用戶錢包和交易記錄

## 📝 文檔化要求

### 功能說明
- **README.MD** - 項目功能和特性說明應記錄在文檔中
- **技術文檔** - 重要的技術決策和架構說明需要文檔化
- **用戶指南** - 功能使用方法和操作流程說明

### 代碼註釋
- **關鍵邏輯** - 複雜的業務邏輯需要詳細註釋
- **API文檔** - API端點的參數和返回值說明
- **配置說明** - 重要配置項的用途和設置方法

## 🚀 開發流程偏好

### 測試優先
- **功能測試** - 新功能開發後建議編寫和運行測試
- **集成測試** - 確保前後端集成正常工作
- **用戶測試** - 重要功能需要用戶實際測試驗證

### 迭代開發
- **小步快跑** - 偏好小幅度的功能改進和修復
- **即時反饋** - 每次修改後及時測試和驗證
- **問題修復** - 發現問題立即修復，避免累積技術債務

---

**更新日期**: 2025-07-07  
**版本**: v2.0.0  
**維護者**: 會員管理系統開發團隊

> 本文檔應定期更新，反映最新的用戶偏好和開發要求。所有開發工作都應遵循本文檔中的原則和偏好。
