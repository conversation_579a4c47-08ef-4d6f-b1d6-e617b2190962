# Git 設置腳本 (PowerShell 版本)
# 設置 Git 倉庫、遠端連接和 hooks

param(
    [string]$RepoUrl = "",
    [string]$UserName = "",
    [string]$UserEmail = ""
)

# 顏色函數
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-Host "🔧 設置 Git 倉庫和自動部署..." -ForegroundColor Cyan

# 1. 檢查 Git 是否已安裝
try {
    $gitVersion = git --version
    Write-Info "Git 已安裝: $gitVersion"
} catch {
    Write-Error "Git 未安裝，請先安裝 Git"
    exit 1
}

# 2. 檢查 Git 是否已初始化
if (-not (Test-Path ".git")) {
    Write-Info "初始化 Git 倉庫..."
    git init
    Write-Success "Git 倉庫初始化完成"
} else {
    Write-Info "Git 倉庫已存在"
}

# 3. 設置 Git 配置
Write-Info "設置 Git 配置..."

# 檢查用戶名
$currentUserName = git config user.name 2>$null
if (-not $currentUserName -and -not $UserName) {
    $UserName = Read-Host "請輸入您的 Git 用戶名"
}
if ($UserName) {
    git config user.name $UserName
}

# 檢查郵箱
$currentUserEmail = git config user.email 2>$null
if (-not $currentUserEmail -and -not $UserEmail) {
    $UserEmail = Read-Host "請輸入您的 Git 郵箱"
}
if ($UserEmail) {
    git config user.email $UserEmail
}

Write-Success "Git 配置完成"
Write-Host "   用戶名: $(git config user.name)"
Write-Host "   郵箱: $(git config user.email)"

# 4. 設置 Git Hooks
Write-Info "設置 Git Hooks..."

# 設置 hooks 目錄
git config core.hooksPath .githooks

Write-Success "Git Hooks 設置完成"

# 5. 設置遠端倉庫
$currentOrigin = git remote get-url origin 2>$null
if (-not $currentOrigin) {
    Write-Warning "未設置遠端倉庫"
    
    if (-not $RepoUrl) {
        Write-Host ""
        Write-Host "請選擇設置方式:"
        Write-Host "1. 手動輸入 GitHub 倉庫 URL"
        Write-Host "2. 稍後手動設置"
        Write-Host ""
        $choice = Read-Host "請選擇 (1/2)"
        
        switch ($choice) {
            "1" {
                $RepoUrl = Read-Host "請輸入 GitHub 倉庫 URL"
                git remote add origin $RepoUrl
                Write-Success "遠端倉庫設置完成: $RepoUrl"
            }
            "2" {
                Write-Info "請稍後使用以下命令設置遠端倉庫:"
                Write-Host "git remote add origin https://github.com/your-username/member-management-system.git"
            }
            default {
                Write-Warning "無效選擇，請稍後手動設置遠端倉庫"
            }
        }
    } else {
        git remote add origin $RepoUrl
        Write-Success "遠端倉庫設置完成: $RepoUrl"
    }
} else {
    Write-Info "遠端倉庫已設置: $currentOrigin"
}

# 6. 檢查是否有提交記錄
$hasCommits = git log --oneline 2>$null
if (-not $hasCommits) {
    Write-Info "創建初始提交..."
    
    git add .
    git commit -m "🎉 初始提交: 會員管理系統

✨ 功能特色:
- 👤 會員註冊與管理
- 💰 多元支付系統 (Stripe, PayPal, ECPay)
- 🏦 錢包系統
- 📱 服務訂閱
- 🔐 安全認證與合規 (PCI DSS, GDPR)

🏗️ 技術架構:
- Node.js + Express.js 後端
- React + Next.js 前端
- PostgreSQL + Redis
- Docker 容器化
- 完整測試覆蓋

🚀 CI/CD:
- GitHub Actions 自動化
- 多環境部署
- 安全檢查和測試"
    
    Write-Success "初始提交完成"
} else {
    Write-Info "已存在提交記錄"
}

# 7. 創建開發分支
$developExists = git show-ref --verify --quiet refs/heads/develop 2>$null
if (-not $developExists) {
    Write-Info "創建 develop 分支..."
    git checkout -b develop
    git checkout main
    Write-Success "develop 分支創建完成"
}

# 8. 顯示設置摘要
Write-Host ""
Write-Success "🎉 Git 設置完成！"
Write-Host ""
Write-Host "📋 設置摘要:"
Write-Host "   Git 用戶: $(git config user.name) <$(git config user.email)>"
Write-Host "   當前分支: $(git branch --show-current)"
$remoteUrl = git remote get-url origin 2>$null
Write-Host "   遠端倉庫: $(if ($remoteUrl) { $remoteUrl } else { '未設置' })"
Write-Host "   Git Hooks: 已啟用"
Write-Host ""

# 9. 顯示下一步
Write-Host "🚀 下一步:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 如果還未設置 GitHub 倉庫，請:"
Write-Host "   - 在 GitHub 創建新倉庫"
Write-Host "   - 運行: git remote add origin <倉庫URL>"
Write-Host ""
Write-Host "2. 推送代碼到 GitHub:"
Write-Host "   - 推送到 main: git push -u origin main"
Write-Host "   - 推送到 develop: git push -u origin develop"
Write-Host ""
Write-Host "3. 設置 GitHub Actions:"
Write-Host "   - GitHub Actions 配置已包含在 .github/workflows/"
Write-Host "   - 推送後會自動觸發 CI/CD 流程"
Write-Host ""
Write-Host "4. 配置環境變數:"
Write-Host "   - 在 GitHub 倉庫設置中添加 Secrets"
Write-Host "   - 包含資料庫、支付等敏感配置"
Write-Host ""

Write-Success "設置腳本執行完成"
