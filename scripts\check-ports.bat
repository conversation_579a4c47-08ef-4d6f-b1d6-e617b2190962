@echo off
echo ========================================
echo 會員管理系統 - 端口檢查工具
echo ========================================
echo.

echo 檢查端口 3000...
netstat -ano | findstr ":3000" > nul
if %errorlevel% == 0 (
    echo ✅ 端口 3000 正在使用
    netstat -ano | findstr ":3000"
) else (
    echo ❌ 端口 3000 未使用
)
echo.

echo 檢查端口 3001...
netstat -ano | findstr ":3001" > nul
if %errorlevel% == 0 (
    echo ✅ 端口 3001 正在使用
    netstat -ano | findstr ":3001"
) else (
    echo ❌ 端口 3001 未使用
)
echo.

echo 檢查 Node.js 進程...
tasklist | findstr "node.exe" > nul
if %errorlevel% == 0 (
    echo ✅ 找到 Node.js 進程
    tasklist | findstr "node.exe"
) else (
    echo ❌ 未找到 Node.js 進程
)
echo.

echo 測試本地連接...
echo 測試 localhost:3000...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000' -Method Head -TimeoutSec 5; Write-Host '✅ localhost:3000 可訪問' } catch { Write-Host '❌ localhost:3000 無法訪問' }"

echo 測試 localhost:3001...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3001' -Method Head -TimeoutSec 5; Write-Host '✅ localhost:3001 可訪問' } catch { Write-Host '❌ localhost:3001 無法訪問' }"

echo.
echo 測試 ngrok 隧道...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'https://topyun.ngrok.app' -Method Head -TimeoutSec 10; Write-Host '✅ ngrok 隧道可訪問' } catch { Write-Host '❌ ngrok 隧道無法訪問' }"

echo.
echo ========================================
echo 檢查完成
echo ========================================
pause
