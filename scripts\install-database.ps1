# 資料庫安裝腳本 (PowerShell 版本)
# 自動安裝和配置 PostgreSQL 和 Redis

param(
    [string]$InstallMethod = "docker",  # docker, local, cloud
    [string]$PostgresPassword = "password",
    [switch]$SkipRedis = $false
)

# 顏色函數
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-Host "🗄️ 安裝資料庫服務..." -ForegroundColor Cyan

# 檢查安裝方法
switch ($InstallMethod.ToLower()) {
    "docker" {
        Write-Info "使用 Docker 安裝資料庫服務"
        
        # 檢查 Docker 是否安裝
        try {
            $dockerVersion = docker --version
            Write-Info "Docker 已安裝: $dockerVersion"
        } catch {
            Write-Error "Docker 未安裝，請先安裝 Docker Desktop"
            Write-Host "下載地址: https://www.docker.com/products/docker-desktop"
            exit 1
        }
        
        # 檢查 Docker 是否運行
        try {
            docker info | Out-Null
            Write-Info "Docker 服務正在運行"
        } catch {
            Write-Error "Docker 服務未運行，請啟動 Docker Desktop"
            exit 1
        }
        
        # 進入後端目錄
        if (-not (Test-Path "backend/docker-compose.yml")) {
            Write-Error "找不到 docker-compose.yml 文件"
            exit 1
        }
        
        Set-Location backend
        
        # 啟動資料庫服務
        Write-Info "啟動 PostgreSQL 和 Redis 服務..."
        docker-compose up -d postgres redis
        
        # 等待服務啟動
        Write-Info "等待服務啟動..."
        Start-Sleep -Seconds 10
        
        # 檢查服務狀態
        $services = docker-compose ps --services --filter "status=running"
        if ($services -contains "postgres") {
            Write-Success "PostgreSQL 服務已啟動"
        } else {
            Write-Error "PostgreSQL 服務啟動失敗"
        }
        
        if ($services -contains "redis") {
            Write-Success "Redis 服務已啟動"
        } else {
            Write-Warning "Redis 服務啟動失敗"
        }
        
        # 創建資料庫
        Write-Info "創建應用程式資料庫..."
        docker-compose exec -T postgres psql -U postgres -c "CREATE DATABASE IF NOT EXISTS member_system;"
        docker-compose exec -T postgres psql -U postgres -c "CREATE DATABASE IF NOT EXISTS member_system_test;"
        
        Write-Success "Docker 資料庫服務安裝完成"
        Write-Host "PostgreSQL: localhost:5432"
        Write-Host "Redis: localhost:6379"
        Write-Host "用戶名: postgres"
        Write-Host "密碼: $PostgresPassword"
    }
    
    "local" {
        Write-Info "本地安裝指南"
        Write-Host ""
        Write-Host "請手動安裝以下軟體:"
        Write-Host ""
        Write-Host "1. PostgreSQL 15+"
        Write-Host "   下載: https://www.postgresql.org/download/windows/"
        Write-Host "   安裝時請設置密碼為: $PostgresPassword"
        Write-Host ""
        Write-Host "2. Redis (可選)"
        Write-Host "   選項 A: 使用 WSL"
        Write-Host "   選項 B: 使用 Docker"
        Write-Host "   選項 C: 使用雲端服務"
        Write-Host ""
        Write-Host "安裝完成後，請運行:"
        Write-Host "   .\scripts\setup-database.ps1"
        Write-Host ""
    }
    
    "cloud" {
        Write-Info "雲端資料庫選項"
        Write-Host ""
        Write-Host "推薦的雲端資料庫服務:"
        Write-Host ""
        Write-Host "PostgreSQL:"
        Write-Host "  • Supabase (免費): https://supabase.com/"
        Write-Host "  • ElephantSQL (免費): https://www.elephantsql.com/"
        Write-Host "  • AWS RDS: https://aws.amazon.com/rds/"
        Write-Host ""
        Write-Host "Redis:"
        Write-Host "  • Redis Cloud (免費): https://redis.com/redis-enterprise-cloud/"
        Write-Host "  • AWS ElastiCache: https://aws.amazon.com/elasticache/"
        Write-Host ""
        Write-Host "設置完成後，請更新 .env 文件中的連接資訊"
        Write-Host ""
    }
    
    default {
        Write-Error "無效的安裝方法: $InstallMethod"
        Write-Host "可用選項: docker, local, cloud"
        exit 1
    }
}

# 創建環境變數文件
if (-not (Test-Path "backend/.env")) {
    Write-Info "創建環境變數文件..."
    
    if (Test-Path "backend/.env.example") {
        Copy-Item "backend/.env.example" "backend/.env"
        Write-Success "已複製 .env.example 到 .env"
        Write-Warning "請編輯 backend/.env 文件並更新資料庫連接資訊"
    } else {
        Write-Warning "找不到 .env.example 文件"
    }
}

# 顯示下一步
Write-Host ""
Write-Host "🚀 下一步:" -ForegroundColor Cyan
Write-Host ""

if ($InstallMethod -eq "docker") {
    Write-Host "1. 檢查服務狀態:"
    Write-Host "   cd backend && docker-compose ps"
    Write-Host ""
    Write-Host "2. 運行資料庫遷移:"
    Write-Host "   cd backend && npm run migrate"
    Write-Host ""
    Write-Host "3. 插入種子資料:"
    Write-Host "   cd backend && npm run seed"
    Write-Host ""
    Write-Host "4. 啟動應用程式:"
    Write-Host "   cd backend && npm run dev"
    Write-Host ""
    Write-Host "5. 測試連接:"
    Write-Host "   curl http://localhost:3000/health"
} else {
    Write-Host "1. 完成資料庫軟體安裝"
    Write-Host "2. 更新 backend/.env 文件"
    Write-Host "3. 運行資料庫設置腳本:"
    Write-Host "   .\scripts\setup-database.ps1"
}

Write-Host ""
Write-Success "資料庫安裝腳本執行完成"
