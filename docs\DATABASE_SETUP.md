# 資料庫安裝與設置指南

本指南將幫助您安裝和配置 PostgreSQL 資料庫以及 Redis 快取。

## 📋 資料庫需求

- **PostgreSQL 15+** - 主要資料庫
- **Redis 7+** - 快取和會話管理

## 🚀 安裝選項

### 選項 1: Docker 安裝 (推薦)

這是最簡單的方式，使用 Docker Compose 一鍵安裝所有服務。

#### 前置要求
- 安裝 Docker Desktop for Windows

#### 安裝步驟
```bash
# 1. 確保 Docker 正在運行
docker --version

# 2. 啟動所有服務 (包含資料庫)
cd backend
docker-compose up -d postgres redis

# 3. 檢查服務狀態
docker-compose ps

# 4. 查看日誌
docker-compose logs postgres redis
```

#### 連接資訊
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **資料庫名稱**: member_system
- **用戶名**: postgres
- **密碼**: password

### 選項 2: Windows 本地安裝

#### PostgreSQL 安裝

1. **下載 PostgreSQL**
   - 訪問: https://www.postgresql.org/download/windows/
   - 下載 PostgreSQL 15+ 安裝程式

2. **安裝 PostgreSQL**
   - 運行安裝程式
   - 設置超級用戶密碼 (建議: `password`)
   - 記住端口號 (預設: 5432)
   - 安裝 pgAdmin (資料庫管理工具)

3. **驗證安裝**
   ```cmd
   psql --version
   ```

#### Redis 安裝

1. **使用 Windows Subsystem for Linux (WSL)**
   ```bash
   # 在 WSL 中安裝 Redis
   sudo apt update
   sudo apt install redis-server
   sudo service redis-server start
   ```

2. **或使用 Docker**
   ```bash
   docker run -d --name redis -p 6379:6379 redis:7-alpine
   ```

### 選項 3: 雲端資料庫

#### PostgreSQL 雲端服務
- **Supabase** (免費額度): https://supabase.com/
- **ElephantSQL** (免費額度): https://www.elephantsql.com/
- **AWS RDS**: https://aws.amazon.com/rds/
- **Google Cloud SQL**: https://cloud.google.com/sql

#### Redis 雲端服務
- **Redis Cloud** (免費額度): https://redis.com/redis-enterprise-cloud/
- **AWS ElastiCache**: https://aws.amazon.com/elasticache/
- **Google Cloud Memorystore**: https://cloud.google.com/memorystore

## 🔧 資料庫設置

### 1. 創建資料庫

#### 使用 psql 命令行
```sql
-- 連接到 PostgreSQL
psql -U postgres -h localhost

-- 創建資料庫
CREATE DATABASE member_system;
CREATE DATABASE member_system_test;

-- 創建用戶 (可選)
CREATE USER member_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE member_system TO member_user;
GRANT ALL PRIVILEGES ON DATABASE member_system_test TO member_user;

-- 退出
\q
```

#### 使用 pgAdmin
1. 打開 pgAdmin
2. 連接到本地 PostgreSQL 服務器
3. 右鍵點擊 "Databases" → "Create" → "Database"
4. 輸入資料庫名稱: `member_system`
5. 重複步驟創建測試資料庫: `member_system_test`

### 2. 運行資料庫遷移

```bash
cd backend

# 安裝依賴
npm install

# 運行資料庫遷移
npm run migrate

# 插入種子資料
npm run seed
```

### 3. 環境變數配置

複製並編輯環境變數文件：

```bash
# 複製環境變數範例
cp .env.example .env

# 編輯環境變數
notepad .env
```

更新資料庫連接資訊：
```env
# 資料庫配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=member_system
DB_USER=postgres
DB_PASSWORD=password

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 測試資料庫
TEST_DB_NAME=member_system_test
```

## 🧪 測試資料庫連接

### 1. 使用測試腳本

```bash
cd backend

# 測試資料庫連接
node -e "
const { Pool } = require('pg');
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'member_system',
  user: 'postgres',
  password: 'password'
});

pool.query('SELECT NOW()', (err, res) => {
  if (err) {
    console.error('❌ 資料庫連接失敗:', err.message);
  } else {
    console.log('✅ 資料庫連接成功:', res.rows[0].now);
  }
  pool.end();
});
"
```

### 2. 使用應用程式測試

```bash
# 啟動應用程式
npm run dev

# 檢查健康狀態
curl http://localhost:3000/health
```

## 🐳 Docker Compose 完整設置

如果選擇 Docker 方式，使用以下命令：

```bash
cd backend

# 啟動所有服務
docker-compose up -d

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f

# 進入 PostgreSQL 容器
docker-compose exec postgres psql -U postgres -d member_system

# 進入 Redis 容器
docker-compose exec redis redis-cli

# 停止服務
docker-compose down

# 停止並刪除數據
docker-compose down -v
```

## 📊 資料庫管理工具

### pgAdmin (PostgreSQL)
- **Web 界面**: http://localhost:5050 (如果使用 Docker)
- **桌面版本**: 隨 PostgreSQL 安裝

### Redis 管理工具
- **Redis CLI**: `redis-cli`
- **RedisInsight**: https://redis.com/redis-enterprise/redis-insight/
- **Redis Desktop Manager**: https://resp.app/

## 🔒 安全設置

### 1. 資料庫安全
```sql
-- 更改預設密碼
ALTER USER postgres PASSWORD 'strong_password_here';

-- 創建應用程式專用用戶
CREATE USER app_user WITH PASSWORD 'app_password';
GRANT CONNECT ON DATABASE member_system TO app_user;
GRANT USAGE ON SCHEMA public TO app_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO app_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO app_user;
```

### 2. 網路安全
- 限制資料庫只接受本地連接
- 使用防火牆規則
- 定期更新資料庫軟體

### 3. 備份策略
```bash
# PostgreSQL 備份
pg_dump -U postgres -h localhost member_system > backup.sql

# Redis 備份
redis-cli BGSAVE
```

## 🆘 故障排除

### 常見問題

1. **連接被拒絕**
   - 檢查服務是否運行: `docker-compose ps`
   - 檢查端口是否被占用: `netstat -an | findstr 5432`

2. **權限錯誤**
   - 檢查用戶權限
   - 確認密碼正確

3. **Docker 問題**
   - 重啟 Docker Desktop
   - 清理 Docker 容器: `docker system prune`

4. **資料庫不存在**
   - 手動創建資料庫
   - 運行遷移腳本

### 聯絡支援
如果遇到問題，請檢查：
1. 服務狀態和日誌
2. 網路連接
3. 環境變數配置
4. 防火牆設置

---

🎉 **完成！** 資料庫已準備就緒，可以開始使用會員管理系統了！
