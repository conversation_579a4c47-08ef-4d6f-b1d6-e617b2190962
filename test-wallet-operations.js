// 測試錢包操作功能 (提現和轉帳)
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testWalletOperations() {
  try {
    console.log('💰 測試錢包操作功能...');
    
    // 1. 登入管理員
    console.log('\n1️⃣ 登入管理員帳戶...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const adminLoginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: '1234'
    });

    if (adminLoginResult.status !== 200 || !adminLoginResult.data.success) {
      console.error('❌ 管理員登入失敗:', adminLoginResult.data);
      return;
    }

    const adminToken = adminLoginResult.data.data.token;
    const adminUser = adminLoginResult.data.data.user;
    console.log('✅ 管理員登入成功 - 用戶:', adminUser.name);

    // 2. 登入測試用戶
    console.log('\n2️⃣ 登入測試用戶帳戶...');
    const userLoginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (userLoginResult.status !== 200 || !userLoginResult.data.success) {
      console.error('❌ 測試用戶登入失敗:', userLoginResult.data);
      return;
    }

    const userToken = userLoginResult.data.data.token;
    const testUser = userLoginResult.data.data.user;
    console.log('✅ 測試用戶登入成功 - 用戶:', testUser.name);

    // 3. 檢查初始餘額
    console.log('\n3️⃣ 檢查初始餘額...');
    const balanceOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/balance',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    // 管理員餘額
    const adminBalanceResult = await makeRequest({
      ...balanceOptions,
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });

    // 測試用戶餘額
    const userBalanceResult = await makeRequest({
      ...balanceOptions,
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (adminBalanceResult.status === 200 && adminBalanceResult.data.success) {
      const adminBalance = adminBalanceResult.data.data.balance;
      console.log(`✅ 管理員初始餘額: $${adminBalance}`);
    }

    if (userBalanceResult.status === 200 && userBalanceResult.data.success) {
      const userBalance = userBalanceResult.data.data.balance;
      console.log(`✅ 測試用戶初始餘額: $${userBalance}`);
    }

    // 4. 測試提現功能
    console.log('\n4️⃣ 測試提現功能...');
    const withdrawOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/withdraw',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    };

    const withdrawResult = await makeRequest(withdrawOptions, {
      amount: 500,
      bank_account_id: 'bank_001',
      description: '測試提現'
    });

    if (withdrawResult.status === 200 && withdrawResult.data.success) {
      console.log('✅ 提現成功!');
      console.log(`   - 提現金額: $${withdrawResult.data.data.withdraw_details.amount}`);
      console.log(`   - 手續費: $${withdrawResult.data.data.withdraw_details.fee.toFixed(2)}`);
      console.log(`   - 總扣除: $${withdrawResult.data.data.withdraw_details.total}`);
      console.log(`   - 新餘額: $${withdrawResult.data.data.new_balance.balance}`);
      console.log(`   - 參考號: ${withdrawResult.data.data.withdraw_details.withdraw_reference}`);
    } else {
      console.log('❌ 提現失敗:', withdrawResult.data);
    }

    // 5. 測試轉帳功能
    console.log('\n5️⃣ 測試轉帳功能...');
    const transferOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/transfer',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    };

    const transferResult = await makeRequest(transferOptions, {
      recipient_email: '<EMAIL>',
      amount: 300,
      description: '測試轉帳'
    });

    if (transferResult.status === 200 && transferResult.data.success) {
      console.log('✅ 轉帳成功!');
      console.log(`   - 轉帳金額: $${transferResult.data.data.transfer_details.amount}`);
      console.log(`   - 收款人: ${transferResult.data.data.transfer_details.recipient_name} (${transferResult.data.data.transfer_details.recipient_email})`);
      console.log(`   - 發送方新餘額: $${transferResult.data.data.new_balance.balance}`);
      console.log(`   - 參考號: ${transferResult.data.data.transfer_details.transfer_reference}`);
    } else {
      console.log('❌ 轉帳失敗:', transferResult.data);
    }

    // 6. 檢查最終餘額
    console.log('\n6️⃣ 檢查最終餘額...');
    
    // 管理員最終餘額
    const adminFinalBalanceResult = await makeRequest({
      ...balanceOptions,
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });

    // 測試用戶最終餘額
    const userFinalBalanceResult = await makeRequest({
      ...balanceOptions,
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (adminFinalBalanceResult.status === 200 && adminFinalBalanceResult.data.success) {
      const adminFinalBalance = adminFinalBalanceResult.data.data.balance;
      console.log(`✅ 管理員最終餘額: $${adminFinalBalance}`);
    }

    if (userFinalBalanceResult.status === 200 && userFinalBalanceResult.data.success) {
      const userFinalBalance = userFinalBalanceResult.data.data.balance;
      console.log(`✅ 測試用戶最終餘額: $${userFinalBalance}`);
    }

    // 7. 測試錯誤情況
    console.log('\n7️⃣ 測試錯誤情況...');
    
    // 測試餘額不足的提現
    const invalidWithdrawResult = await makeRequest(withdrawOptions, {
      amount: 999999,
      bank_account_id: 'bank_001',
      description: '測試餘額不足'
    });

    if (invalidWithdrawResult.status === 400) {
      console.log('✅ 餘額不足提現測試通過:', invalidWithdrawResult.data.error);
    } else {
      console.log('❌ 餘額不足提現測試失敗');
    }

    // 測試轉帳給不存在的用戶
    const invalidTransferResult = await makeRequest(transferOptions, {
      recipient_email: '<EMAIL>',
      amount: 100,
      description: '測試不存在用戶'
    });

    if (invalidTransferResult.status === 404) {
      console.log('✅ 不存在用戶轉帳測試通過:', invalidTransferResult.data.error);
    } else {
      console.log('❌ 不存在用戶轉帳測試失敗');
    }

    console.log('\n📋 測試總結:');
    console.log('✅ 提現功能: 正常運作');
    console.log('✅ 轉帳功能: 正常運作');
    console.log('✅ 餘額更新: 正常運作');
    console.log('✅ 錯誤處理: 正常運作');

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testWalletOperations();
