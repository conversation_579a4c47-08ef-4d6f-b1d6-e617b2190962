# API 文檔

MemberService 會員管理系統的 API 接口說明文檔。

## 🔗 基本信息

- **基礎 URL**: `http://localhost:3000/api`
- **認證方式**: JWT Token
- **數據格式**: JSON
- **字符編碼**: UTF-8

## 🔐 認證相關

### 用戶登入
```http
POST /auth/login
```

**請求參數**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**響應示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_123",
      "name": "張小明",
      "email": "<EMAIL>",
      "memberSince": "2025-06-26T03:36:40.532Z"
    }
  }
}
```

### 用戶註冊
```http
POST /auth/register
```

**請求參數**:
```json
{
  "name": "張小明",
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 登出
```http
POST /auth/logout
```

**請求頭**:
```
Authorization: Bearer <token>
```

## 💰 錢包相關

### 獲取錢包信息
```http
GET /wallet
```

**請求頭**:
```
Authorization: Bearer <token>
```

**響應示例**:
```json
{
  "success": true,
  "data": {
    "balance": 1250.50,
    "frozenAmount": 150.00,
    "availableBalance": 1100.50,
    "totalIncome": 5680.00,
    "totalExpense": 4429.50,
    "monthlyIncome": 890.00,
    "monthlyExpense": 645.50
  }
}
```

### 獲取交易記錄
```http
GET /wallet/transactions
```

**查詢參數**:
- `type`: 交易類型 (`income`, `expense`, `all`)
- `page`: 頁碼 (預設: 1)
- `limit`: 每頁數量 (預設: 20)

**響應示例**:
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": "TXN-2025-001",
        "type": "income",
        "category": "recharge",
        "amount": 500.00,
        "description": "信用卡充值",
        "status": "completed",
        "date": "2025-06-26T10:30:00Z",
        "paymentMethod": "credit_card",
        "reference": "CC-4532****1234"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 6,
      "pages": 1
    }
  }
}
```

### 錢包充值
```http
POST /wallet/recharge
```

**請求參數**:
```json
{
  "amount": 500.00,
  "paymentMethod": "credit_card"
}
```

**響應示例**:
```json
{
  "success": true,
  "data": {
    "transactionId": "TXN-2025-007",
    "amount": 500.00,
    "fee": 12.50,
    "totalAmount": 512.50,
    "status": "processing",
    "paymentUrl": "https://payment.example.com/pay/xxx"
  }
}
```

### 錢包提現
```http
POST /wallet/withdraw
```

**請求參數**:
```json
{
  "amount": 1000.00,
  "bankAccountId": "acc-001"
}
```

**響應示例**:
```json
{
  "success": true,
  "data": {
    "transactionId": "WD-2025-001",
    "amount": 1000.00,
    "fee": 15.00,
    "actualAmount": 985.00,
    "status": "processing",
    "estimatedArrival": "2025-06-29"
  }
}
```

### 錢包轉帳
```http
POST /wallet/transfer
```

**請求參數**:
```json
{
  "recipientEmail": "<EMAIL>",
  "amount": 200.00,
  "note": "轉帳備註"
}
```

## 🏦 銀行帳戶管理

### 獲取銀行帳戶列表
```http
GET /wallet/bank-accounts
```

**響應示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "acc-001",
      "bankName": "台灣銀行",
      "accountNumber": "****-****-****-1234",
      "accountName": "張小明",
      "isDefault": true,
      "createdAt": "2025-06-01T00:00:00Z"
    }
  ]
}
```

### 新增銀行帳戶
```http
POST /wallet/bank-accounts
```

**請求參數**:
```json
{
  "bankName": "中國信託",
  "accountNumber": "****************",
  "accountName": "張小明"
}
```

## 📱 服務訂閱

### 獲取訂閱列表
```http
GET /subscriptions
```

**查詢參數**:
- `status`: 訂閱狀態 (`active`, `expired`, `cancelled`)

**響應示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "AI 文字生成服務",
      "description": "強大的 AI 文字生成工具",
      "price": 299,
      "billingCycle": "monthly",
      "status": "active",
      "nextBilling": "2025-07-26",
      "features": ["無限文字生成", "多語言支援"],
      "usage": {
        "used": 15000,
        "limit": 50000,
        "unit": "字符"
      }
    }
  ]
}
```

### 獲取可用服務
```http
GET /services/available
```

**響應示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 4,
      "name": "語音轉文字服務",
      "description": "高精度語音識別和轉換服務",
      "price": 149,
      "billingCycle": "monthly",
      "features": ["多語言識別", "實時轉換"],
      "popular": false
    }
  ]
}
```

### 訂閱服務
```http
POST /subscriptions
```

**請求參數**:
```json
{
  "serviceId": 4,
  "billingCycle": "monthly"
}
```

### 取消訂閱
```http
DELETE /subscriptions/:id
```

**響應示例**:
```json
{
  "success": true,
  "message": "訂閱已成功取消"
}
```

### 獲取訂閱詳情
```http
GET /subscriptions/:id
```

**響應示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "AI 文字生成服務",
    "billingHistory": [
      {
        "date": "2025-06-26",
        "amount": 299,
        "status": "paid",
        "invoice": "INV-2025-001"
      }
    ],
    "usageHistory": [
      {
        "date": "2025-06-25",
        "usage": 2500,
        "type": "文章生成"
      }
    ]
  }
}
```

## 👤 用戶管理

### 獲取用戶資料
```http
GET /user/profile
```

**響應示例**:
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "name": "張小明",
    "email": "<EMAIL>",
    "avatar": null,
    "memberSince": "2025-06-26T03:36:40.532Z",
    "lastLogin": "2025-06-26T10:30:00Z"
  }
}
```

### 更新用戶資料
```http
PUT /user/profile
```

**請求參數**:
```json
{
  "name": "張小明",
  "phone": "+886912345678"
}
```

## 📊 統計數據

### 獲取儀表板統計
```http
GET /dashboard/stats
```

**響應示例**:
```json
{
  "success": true,
  "data": {
    "walletBalance": 1250.50,
    "totalTransactions": 24,
    "activeSubscriptions": 3,
    "monthlySpending": 899.00
  }
}
```

## ❌ 錯誤處理

### 錯誤響應格式
```json
{
  "success": false,
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "郵件或密碼錯誤",
    "details": {}
  }
}
```

### 常見錯誤碼
- `INVALID_CREDENTIALS`: 認證失敗
- `INSUFFICIENT_BALANCE`: 餘額不足
- `INVALID_AMOUNT`: 金額無效
- `SERVICE_NOT_FOUND`: 服務不存在
- `SUBSCRIPTION_NOT_FOUND`: 訂閱不存在
- `BANK_ACCOUNT_NOT_FOUND`: 銀行帳戶不存在

## 🔒 安全說明

### 認證
- 所有需要認證的 API 都需要在請求頭中包含 `Authorization: Bearer <token>`
- Token 有效期為 24 小時
- Token 過期後需要重新登入

### 限制
- API 請求頻率限制：每分鐘 100 次
- 單次充值限額：NT$ 50,000
- 單次提現限額：NT$ 100,000
- 單次轉帳限額：NT$ 10,000

### 數據加密
- 所有敏感數據使用 HTTPS 傳輸
- 密碼使用 bcrypt 加密存儲
- 銀行帳戶號碼部分遮罩顯示

---

**更新日期**: 2025-06-26  
**API 版本**: v1.0.0  
**維護者**: MemberService 開發團隊
