// 密碼安全工具類
class PasswordSecurity {
  constructor() {
    // 密碼強度要求配置
    this.requirements = {
      minLength: 8,
      maxLength: 128,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      maxRepeatingChars: 3,
      preventCommonPasswords: true
    };

    // 常見弱密碼列表（部分）
    this.commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      '1234567890', 'password1', '123123', 'qwerty123'
    ];

    // 特殊字符集合
    this.specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  }

  // 驗證密碼強度
  validatePassword(password) {
    const errors = [];
    const warnings = [];

    // 基本長度檢查
    if (!password || password.length < this.requirements.minLength) {
      errors.push(`密碼長度至少需要 ${this.requirements.minLength} 個字符`);
    }

    if (password && password.length > this.requirements.maxLength) {
      errors.push(`密碼長度不能超過 ${this.requirements.maxLength} 個字符`);
    }

    if (!password) {
      return { isValid: false, errors, warnings, strength: 0 };
    }

    // 大寫字母檢查
    if (this.requirements.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密碼必須包含至少一個大寫字母');
    }

    // 小寫字母檢查
    if (this.requirements.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密碼必須包含至少一個小寫字母');
    }

    // 數字檢查
    if (this.requirements.requireNumbers && !/\d/.test(password)) {
      errors.push('密碼必須包含至少一個數字');
    }

    // 特殊字符檢查
    if (this.requirements.requireSpecialChars) {
      const hasSpecialChar = this.specialChars.split('').some(char => password.includes(char));
      if (!hasSpecialChar) {
        errors.push(`密碼必須包含至少一個特殊字符 (${this.specialChars})`);
      }
    }

    // 重複字符檢查
    if (this.hasExcessiveRepeatingChars(password)) {
      warnings.push(`避免連續重複超過 ${this.requirements.maxRepeatingChars} 個相同字符`);
    }

    // 常見密碼檢查
    if (this.requirements.preventCommonPasswords && this.isCommonPassword(password)) {
      errors.push('請避免使用常見的弱密碼');
    }

    // 順序字符檢查
    if (this.hasSequentialChars(password)) {
      warnings.push('避免使用連續的字符序列（如 123、abc）');
    }

    // 計算密碼強度
    const strength = this.calculatePasswordStrength(password);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      strength,
      strengthText: this.getStrengthText(strength)
    };
  }

  // 檢查重複字符
  hasExcessiveRepeatingChars(password) {
    let count = 1;
    for (let i = 1; i < password.length; i++) {
      if (password[i] === password[i - 1]) {
        count++;
        if (count > this.requirements.maxRepeatingChars) {
          return true;
        }
      } else {
        count = 1;
      }
    }
    return false;
  }

  // 檢查是否為常見密碼
  isCommonPassword(password) {
    const lowerPassword = password.toLowerCase();
    return this.commonPasswords.some(common => 
      lowerPassword.includes(common) || common.includes(lowerPassword)
    );
  }

  // 檢查順序字符
  hasSequentialChars(password) {
    const sequences = [
      '0123456789', 'abcdefghijklmnopqrstuvwxyz', 'qwertyuiop', 'asdfghjkl', 'zxcvbnm'
    ];

    for (const sequence of sequences) {
      for (let i = 0; i <= sequence.length - 3; i++) {
        const subseq = sequence.substring(i, i + 3);
        if (password.toLowerCase().includes(subseq) || 
            password.toLowerCase().includes(subseq.split('').reverse().join(''))) {
          return true;
        }
      }
    }
    return false;
  }

  // 計算密碼強度 (0-100)
  calculatePasswordStrength(password) {
    let score = 0;

    // 長度分數 (最多30分)
    score += Math.min(30, password.length * 2);

    // 字符類型分數
    if (/[a-z]/.test(password)) score += 10;
    if (/[A-Z]/.test(password)) score += 10;
    if (/\d/.test(password)) score += 10;
    if (new RegExp(`[${this.specialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`).test(password)) score += 15;

    // 複雜度獎勵
    const uniqueChars = new Set(password).size;
    score += Math.min(15, uniqueChars);

    // 懲罰項
    if (this.hasExcessiveRepeatingChars(password)) score -= 10;
    if (this.isCommonPassword(password)) score -= 20;
    if (this.hasSequentialChars(password)) score -= 10;

    return Math.max(0, Math.min(100, score));
  }

  // 獲取強度文字描述
  getStrengthText(strength) {
    if (strength >= 80) return '非常強';
    if (strength >= 60) return '強';
    if (strength >= 40) return '中等';
    if (strength >= 20) return '弱';
    return '非常弱';
  }

  // 獲取強度顏色
  getStrengthColor(strength) {
    if (strength >= 80) return 'text-green-600';
    if (strength >= 60) return 'text-blue-600';
    if (strength >= 40) return 'text-yellow-600';
    if (strength >= 20) return 'text-orange-600';
    return 'text-red-600';
  }

  // 生成安全密碼建議
  generatePasswordSuggestions() {
    const suggestions = [
      '使用密碼短語：將多個不相關的詞組合，如 "Coffee#Mountain9Sky"',
      '使用首字母縮寫：取一句話的首字母，如 "I love to eat pizza!" → "Iltep!"',
      '替換字符：用數字或符號替換字母，如 "Password" → "P@ssw0rd"',
      '添加個人元素：結合個人信息（但不要太明顯），如生日、寵物名等',
      '使用密碼管理器：讓工具生成和管理複雜密碼'
    ];

    return suggestions;
  }

  // 生成隨機安全密碼
  generateSecurePassword(length = 12) {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const specials = this.specialChars;

    let charset = '';
    let password = '';

    // 確保包含每種字符類型
    if (this.requirements.requireLowercase) {
      charset += lowercase;
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
    }
    if (this.requirements.requireUppercase) {
      charset += uppercase;
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
    }
    if (this.requirements.requireNumbers) {
      charset += numbers;
      password += numbers[Math.floor(Math.random() * numbers.length)];
    }
    if (this.requirements.requireSpecialChars) {
      charset += specials;
      password += specials[Math.floor(Math.random() * specials.length)];
    }

    // 填充剩餘長度
    for (let i = password.length; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }

    // 打亂密碼字符順序
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  // 檢查密碼是否已被洩露（模擬 - 實際應該調用 HaveIBeenPwned API）
  async checkPasswordBreach(password) {
    // 這裡應該實際調用 HaveIBeenPwned API
    // 為了演示，我們只檢查常見密碼
    return {
      isBreached: this.isCommonPassword(password),
      breachCount: this.isCommonPassword(password) ? Math.floor(Math.random() * 1000000) : 0
    };
  }

  // 密碼歷史檢查（防止重複使用最近的密碼）
  checkPasswordHistory(newPassword, passwordHistory = []) {
    // 檢查新密碼是否與最近使用的密碼相同
    const recentPasswords = passwordHistory.slice(-5); // 檢查最近5個密碼
    
    for (const oldPassword of recentPasswords) {
      if (newPassword === oldPassword) {
        return {
          isReused: true,
          message: '不能使用最近使用過的密碼'
        };
      }
      
      // 檢查相似度（簡單的編輯距離）
      if (this.calculateSimilarity(newPassword, oldPassword) > 0.8) {
        return {
          isReused: true,
          message: '新密碼與最近使用的密碼過於相似'
        };
      }
    }

    return { isReused: false };
  }

  // 計算兩個字符串的相似度
  calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  // 計算編輯距離
  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }
}

// 創建全局實例
const passwordSecurity = new PasswordSecurity();

// 導出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { PasswordSecurity, passwordSecurity };
} else if (typeof window !== 'undefined') {
  window.PasswordSecurity = PasswordSecurity;
  window.passwordSecurity = passwordSecurity;
}
