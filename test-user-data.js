// 檢查用戶數據
const sqlite3 = require('sqlite3').verbose();

const dbPath = 'C:\\Users\\<USER>\\member\\frontend\\lib\\database\\database.sqlite';

console.log('檢查用戶數據...');

const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
  if (err) {
    console.error('❌ 資料庫連接失敗:', err.message);
    process.exit(1);
  } else {
    console.log('✅ 資料庫連接成功');

    // 查找管理員用戶的完整信息
    db.get('SELECT * FROM members WHERE email = ?', ['<EMAIL>'], (err, user) => {
      if (err) {
        console.error('❌ 查找用戶失敗:', err.message);
        process.exit(1);
      } else if (user) {
        console.log('✅ 找到管理員用戶:');
        console.log('ID:', user.id);
        console.log('Email:', user.email);
        console.log('Name:', user.name);
        console.log('Role:', user.role);
        console.log('Password Hash:', user.password ? user.password.substring(0, 20) + '...' : 'NULL');
        console.log('Password Hash Length:', user.password ? user.password.length : 0);
        console.log('Password Hash Type:', typeof user.password);
        console.log('Created At:', user.created_at);

        // 檢查所有字段
        console.log('\n所有字段:');
        Object.keys(user).forEach(key => {
          console.log(`${key}:`, user[key]);
        });
      } else {
        console.log('❌ 未找到管理員用戶');
      }

      db.close();
      process.exit(0);
    });
  }
});
