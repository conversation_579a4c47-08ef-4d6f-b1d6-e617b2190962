import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function Transfer() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [amount, setAmount] = useState('');
  const [recipientEmail, setRecipientEmail] = useState('');
  const [transferNote, setTransferNote] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentBalance, setCurrentBalance] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 預設金額選項
  const quickAmounts = [50, 100, 200, 500, 1000, 2000];

  // 常用聯絡人
  const [frequentContacts] = useState([
    {
      id: 'contact-001',
      name: '王小華',
      email: '<EMAIL>',
      avatar: '👨'
    },
    {
      id: 'contact-002',
      name: '李小美',
      email: '<EMAIL>',
      avatar: '👩'
    },
    {
      id: 'contact-003',
      name: '陳大明',
      email: '<EMAIL>',
      avatar: '👨‍💼'
    }
  ]);

  useEffect(() => {
    checkAuth();
    loadCurrentBalance();
  }, []);

  const checkAuth = () => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/login');
      return;
    }

    try {
      setUser(JSON.parse(userData));
    } catch (error) {
      console.error('解析用戶數據失敗:', error);
      router.push('/login');
    }
  };

  const loadCurrentBalance = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:3001/api/wallet/balance', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      if (result.success) {
        setCurrentBalance(result.data.balance);
      } else {
        setError('無法載入錢包餘額');
      }
    } catch (error) {
      console.error('載入餘額失敗:', error);
      setError('載入餘額失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickAmount = (quickAmount) => {
    setAmount(quickAmount.toString());
  };

  const handleContactSelect = (contact) => {
    setRecipientEmail(contact.email);
  };

  const calculateFee = () => {
    const baseAmount = parseFloat(amount) || 0;
    if (baseAmount <= 100) return 0; // 小額轉帳免手續費
    return Math.max(5, baseAmount * 0.001); // 0.1% 手續費，最低 5 元
  };

  const getTotalAmount = () => {
    const baseAmount = parseFloat(amount) || 0;
    const fee = calculateFee();
    return baseAmount + fee;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!amount || parseFloat(amount) <= 0) {
      setError('請輸入有效的轉帳金額');
      return;
    }

    if (parseFloat(amount) < 1) {
      setError('最低轉帳金額為 NT$ 1');
      return;
    }

    if (parseFloat(amount) > currentBalance) {
      setError('轉帳金額不能超過可用餘額');
      return;
    }

    if (!recipientEmail) {
      setError('請輸入收款人電子郵件');
      return;
    }

    // 簡單的郵件格式驗證
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      setError('請輸入有效的電子郵件地址');
      return;
    }

    if (recipientEmail === user?.email) {
      setError('不能轉帳給自己');
      return;
    }

    setIsProcessing(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/wallet/transfer', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          recipient_email: recipientEmail,
          amount: parseFloat(amount),
          description: transferNote || `轉帳給 ${recipientEmail}`
        })
      });

      const result = await response.json();

      if (result.success) {
        // 安全地處理 new_balance 數據
        const newBalance = result.data.new_balance;
        let balanceAmount = 0;

        if (newBalance && typeof newBalance === 'object') {
          balanceAmount = parseFloat(newBalance.balance) || 0;
        } else if (typeof newBalance === 'number') {
          balanceAmount = newBalance;
        }

        setCurrentBalance(balanceAmount);

        const details = result.data.transfer_details;

        alert(`轉帳成功！\n轉帳金額：NT$ ${details.amount.toLocaleString()}\n收款人：${details.recipient_name} (${details.recipient_email})\n參考號：${details.transfer_reference}\n當前餘額：NT$ ${balanceAmount.toLocaleString()}`);

        // 跳轉回錢包頁面
        router.push('/wallet');
      } else {
        setError(result.error || '轉帳失敗');
      }

    } catch (error) {
      console.error('轉帳失敗:', error);
      setError('轉帳失敗，請稍後再試');
    } finally {
      setIsProcessing(false);
    }
  };

  if (!user || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>錢包轉帳 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/wallet')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回錢包
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">錢包轉帳</h1>
                </div>
              </div>
              
              <div className="flex items-center">
                <span className="text-sm text-gray-600">可用餘額: NT$ {(currentBalance || 0).toLocaleString()}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 轉帳表單 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-xl shadow-lg p-8"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">發起轉帳</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* 收款人選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    選擇收款人
                  </label>
                  
                  {/* 常用聯絡人 */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-2">常用聯絡人</p>
                    <div className="flex space-x-3">
                      {frequentContacts.map((contact) => (
                        <button
                          key={contact.id}
                          type="button"
                          onClick={() => handleContactSelect(contact)}
                          className={`p-3 border-2 rounded-lg transition-colors ${
                            recipientEmail === contact.email
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="text-center">
                            <div className="text-2xl mb-1">{contact.avatar}</div>
                            <div className="text-xs font-medium">{contact.name}</div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 手動輸入郵件 */}
                  <input
                    type="email"
                    value={recipientEmail}
                    onChange={(e) => setRecipientEmail(e.target.value)}
                    placeholder="或輸入收款人電子郵件"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* 金額輸入 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    轉帳金額 (NT$)
                  </label>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="請輸入轉帳金額"
                    min="1"
                    max={currentBalance}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    最低轉帳金額 NT$ 1，可用餘額 NT$ {(currentBalance || 0).toLocaleString()}
                  </p>
                </div>

                {/* 快速金額選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    快速選擇
                  </label>
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
                    {quickAmounts.filter(amt => amt <= (currentBalance || 0)).map((quickAmount) => (
                      <button
                        key={quickAmount}
                        type="button"
                        onClick={() => handleQuickAmount(quickAmount)}
                        className={`py-3 px-4 rounded-lg border-2 transition-colors ${
                          amount === quickAmount.toString()
                            ? 'border-blue-500 bg-blue-50 text-blue-600'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        NT$ {quickAmount}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 轉帳備註 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    轉帳備註 (選填)
                  </label>
                  <textarea
                    value={transferNote}
                    onChange={(e) => setTransferNote(e.target.value)}
                    placeholder="請輸入轉帳備註..."
                    rows={3}
                    maxLength={100}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    {transferNote.length}/100 字符
                  </p>
                </div>

                {/* 費用明細 */}
                {amount && recipientEmail && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="bg-gray-50 rounded-lg p-4"
                  >
                    <h3 className="font-medium text-gray-900 mb-3">轉帳明細</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>收款人:</span>
                        <span>{recipientEmail}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>轉帳金額:</span>
                        <span>NT$ {parseFloat(amount).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>手續費:</span>
                        <span>NT$ {calculateFee().toLocaleString()}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-medium">
                        <span>總計:</span>
                        <span>NT$ {getTotalAmount().toLocaleString()}</span>
                      </div>
                      {transferNote && (
                        <div className="border-t pt-2">
                          <span className="text-gray-600">備註: {transferNote}</span>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}

                {/* 錯誤訊息 */}
                {error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600 text-sm">{error}</p>
                  </div>
                )}

                {/* 提交按鈕 */}
                <button
                  type="submit"
                  disabled={!amount || !recipientEmail || isProcessing}
                  className={`w-full py-4 px-6 rounded-lg font-medium text-lg transition-colors ${
                    !amount || !recipientEmail || isProcessing
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isProcessing ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      處理中...
                    </div>
                  ) : (
                    `確認轉帳 NT$ ${amount ? getTotalAmount().toLocaleString() : '0'}`
                  )}
                </button>
              </form>
            </motion.div>

            {/* 轉帳說明 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-blue-50 border border-blue-200 rounded-lg p-6"
            >
              <h3 className="font-medium text-blue-900 mb-3">💸 轉帳說明</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 轉帳金額 100 元以下免手續費，超過 100 元收取 0.1% 手續費</li>
                <li>• 轉帳成功後資金將立即到達收款人帳戶</li>
                <li>• 請確認收款人電子郵件地址正確，轉帳後無法撤回</li>
                <li>• 系統會發送轉帳通知給收款人</li>
                <li>• 如有疑問請聯繫客服：<EMAIL></li>
              </ul>
            </motion.div>
          </div>
        </main>
      </div>
    </>
  );
}
