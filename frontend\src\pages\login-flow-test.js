import { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function LoginFlowTest() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);

  const testCompleteFlow = async () => {
    setIsLoading(true);
    setStep(1);
    setResult(null);

    try {
      // 步驟 1: 測試登入
      setStep(1);
      console.log('步驟 1: 測試登入...');
      
      const loginResponse = await fetch('/api/auth/login-db', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: '123456'
        })
      });

      const loginData = await loginResponse.json();
      
      if (!loginResponse.ok || !loginData.success) {
        throw new Error(`登入失敗: ${loginData.error}`);
      }

      console.log('步驟 1: 登入成功');

      // 步驟 2: 保存登入信息
      setStep(2);
      console.log('步驟 2: 保存登入信息...');
      
      localStorage.setItem('token', loginData.data.token);
      localStorage.setItem('user', JSON.stringify(loginData.data.user));
      
      console.log('步驟 2: 登入信息已保存');

      // 步驟 3: 測試 dashboard 頁面
      setStep(3);
      console.log('步驟 3: 測試 dashboard 頁面...');
      
      // 檢查 dashboard 頁面是否存在
      const dashboardResponse = await fetch('/dashboard');
      console.log('Dashboard 頁面狀態:', dashboardResponse.status);

      // 步驟 4: 測試 dashboard-simple 頁面
      setStep(4);
      console.log('步驟 4: 測試 dashboard-simple 頁面...');
      
      const dashboardSimpleResponse = await fetch('/dashboard-simple');
      console.log('Dashboard-simple 頁面狀態:', dashboardSimpleResponse.status);

      // 步驟 5: 完成
      setStep(5);
      setResult({
        type: 'success',
        message: '完整流程測試成功！',
        data: {
          user: loginData.data.user,
          dashboardStatus: dashboardResponse.status,
          dashboardSimpleStatus: dashboardSimpleResponse.status
        }
      });

    } catch (error) {
      console.error('測試失敗:', error);
      setResult({
        type: 'error',
        message: error.message,
        step: step
      });
    } finally {
      setIsLoading(false);
    }
  };

  const goToDashboard = () => {
    router.push('/dashboard');
  };

  const goToDashboardSimple = () => {
    router.push('/dashboard-simple');
  };

  const goToLogin = () => {
    router.push('/auth/login');
  };

  const resetTest = () => {
    setStep(1);
    setResult(null);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  };

  return (
    <>
      <Head>
        <title>登入流程測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔄 登入流程測試</h1>
              <p className="text-gray-600 mt-2">測試完整的登入和跳轉流程</p>
            </div>

            {/* 測試步驟 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">測試步驟</h2>
              <div className="space-y-3">
                {[
                  { num: 1, title: '測試登入 API', desc: '使用 <EMAIL> + 123456' },
                  { num: 2, title: '保存登入信息', desc: '存儲 token 和用戶數據到 localStorage' },
                  { num: 3, title: '檢查 dashboard 頁面', desc: '驗證 /dashboard 是否存在' },
                  { num: 4, title: '檢查 dashboard-simple 頁面', desc: '驗證 /dashboard-simple 是否存在' },
                  { num: 5, title: '完成測試', desc: '所有步驟成功完成' }
                ].map((stepInfo) => (
                  <div key={stepInfo.num} className={`flex items-center p-3 rounded-lg ${
                    step > stepInfo.num ? 'bg-green-50 border border-green-200' :
                    step === stepInfo.num ? 'bg-blue-50 border border-blue-200' :
                    'bg-gray-50 border border-gray-200'
                  }`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium mr-3 ${
                      step > stepInfo.num ? 'bg-green-600 text-white' :
                      step === stepInfo.num ? 'bg-blue-600 text-white' :
                      'bg-gray-300 text-gray-600'
                    }`}>
                      {step > stepInfo.num ? '✓' : stepInfo.num}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{stepInfo.title}</div>
                      <div className="text-sm text-gray-600">{stepInfo.desc}</div>
                    </div>
                    {step === stepInfo.num && isLoading && (
                      <div className="ml-auto">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 測試按鈕 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <button
                onClick={testCompleteFlow}
                disabled={isLoading}
                className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {isLoading ? '測試中...' : '🧪 開始完整測試'}
              </button>

              <button
                onClick={goToDashboard}
                className="px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                🏠 前往 Dashboard
              </button>

              <button
                onClick={goToDashboardSimple}
                className="px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                📊 前往 Dashboard Simple
              </button>

              <button
                onClick={resetTest}
                className="px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                🔄 重置測試
              </button>
            </div>

            {/* 結果顯示 */}
            {result && (
              <div className={`p-6 rounded-lg ${
                result.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
              }`}>
                <div className={`flex items-center mb-4 ${
                  result.type === 'success' ? 'text-green-900' : 'text-red-900'
                }`}>
                  <span className="mr-2 text-2xl">
                    {result.type === 'success' ? '✅' : '❌'}
                  </span>
                  <h3 className="text-lg font-semibold">
                    {result.type === 'success' ? '測試成功！' : '測試失敗'}
                  </h3>
                </div>
                
                <div className={`text-sm ${
                  result.type === 'success' ? 'text-green-700' : 'text-red-700'
                }`}>
                  <div className="mb-2">{result.message}</div>
                  
                  {result.type === 'success' && result.data && (
                    <div className="space-y-2">
                      <div><strong>用戶:</strong> {result.data.user.name} ({result.data.user.email})</div>
                      <div><strong>Dashboard 狀態:</strong> {result.data.dashboardStatus}</div>
                      <div><strong>Dashboard Simple 狀態:</strong> {result.data.dashboardSimpleStatus}</div>
                    </div>
                  )}
                  
                  {result.type === 'error' && (
                    <div><strong>失敗步驟:</strong> {result.step}</div>
                  )}
                </div>

                {result.type === 'success' && (
                  <div className="mt-4 space-x-3">
                    <button
                      onClick={goToDashboard}
                      className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      🚀 前往 Dashboard
                    </button>
                    <button
                      onClick={goToLogin}
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      📝 測試登入頁面
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* 說明 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">📋 測試說明</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>目的:</strong> 確保登入成功後能正確跳轉到儀表板頁面</p>
                <p><strong>測試帳號:</strong> <EMAIL> / 123456</p>
                <p><strong>預期結果:</strong> 登入成功 → 保存 token → 跳轉到 dashboard-simple</p>
                <p><strong>解決 404:</strong> 確保所有相關頁面都存在且可訪問</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
