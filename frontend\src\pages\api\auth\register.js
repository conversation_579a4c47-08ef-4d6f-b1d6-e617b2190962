// 會員註冊 API (資料庫版本)
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const path = require('path');
const fs = require('fs');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();

    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];

    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('註冊 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }

    console.log('註冊 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('註冊 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    const { name, email, password, confirmPassword, phone } = req.body;

    // 驗證必要欄位
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        error: '請提供姓名、email 和密碼'
      });
    }

    // 驗證 email 格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: 'email 格式不正確'
      });
    }

    // 驗證密碼長度
    if (password.length < 4) {
      return res.status(400).json({
        success: false,
        error: '密碼長度至少需要 4 個字符'
      });
    }

    // 驗證密碼確認
    if (confirmPassword && password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        error: '密碼確認不匹配'
      });
    }

    console.log('註冊 API: 收到請求', { name, email, phone, password: '***' });

    // 連接資料庫
    const db = connectDatabase();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    // 使用 Promise 包裝資料庫操作
    return new Promise(async (resolve) => {
      try {
        // 檢查 email 是否已存在
        db.get('SELECT id FROM members WHERE email = ?', [email.toLowerCase()], async (err, row) => {
          if (err) {
            console.error('註冊 API: 查詢用戶失敗:', err);
            db.close();
            resolve(res.status(500).json({
              success: false,
              error: '資料庫查詢失敗'
            }));
            return;
          }

          if (row) {
            console.log('註冊 API: Email 已存在');
            db.close();
            resolve(res.status(409).json({
              success: false,
              error: '此電子郵件已被註冊'
            }));
            return;
          }

          console.log('註冊 API: Email 可用，開始創建用戶');

          // 生成密碼哈希
          const saltRounds = 10;
          let passwordHash;
          try {
            passwordHash = await bcrypt.hash(password, saltRounds);
            console.log('註冊 API: 密碼哈希生成成功');
          } catch (hashError) {
            console.error('註冊 API: 密碼哈希生成失敗:', hashError);
            db.close();
            resolve(res.status(500).json({
              success: false,
              error: '密碼處理失敗'
            }));
            return;
          }

          // 插入新會員到資料庫
          const insertQuery = `
            INSERT INTO members (email, name, password_hash, role, status, phone, email_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `;

          db.run(insertQuery, [
            email.toLowerCase().trim(),
            name.trim(),
            passwordHash,
            'user',
            'active',
            phone?.trim() || null,
            0 // email_verified = false
          ], function(insertErr) {
            if (insertErr) {
              console.error('註冊 API: 插入用戶失敗:', insertErr);
              db.close();
              resolve(res.status(500).json({
                success: false,
                error: '創建用戶失敗'
              }));
              return;
            }

            const newUserId = this.lastID;
            console.log('註冊 API: 用戶創建成功', { id: newUserId, email: email });

            // 查詢剛創建的用戶完整信息
            db.get('SELECT id, email, name, role, status, phone, email_verified, created_at FROM members WHERE id = ?',
              [newUserId], (selectErr, member) => {
              db.close();

              if (selectErr || !member) {
                console.error('註冊 API: 查詢新用戶失敗:', selectErr);
                resolve(res.status(500).json({
                  success: false,
                  error: '用戶創建後查詢失敗'
                }));
                return;
              }

              // 生成 JWT Token
              const token = jwt.sign(
                {
                  userId: member.id,
                  email: member.email,
                  role: member.role
                },
                JWT_SECRET,
                { expiresIn: JWT_EXPIRES_IN }
              );

              console.log('註冊 API: JWT Token 生成成功');

              // 準備用戶數據 (移除敏感信息)
              const userData = {
                id: member.id,
                email: member.email,
                name: member.name,
                role: member.role,
                status: member.status,
                phone: member.phone,
                email_verified: member.email_verified,
                created_at: member.created_at
              };

              console.log('註冊 API: 註冊完成，返回成功響應');

              // 返回成功響應
              resolve(res.status(201).json({
                success: true,
                data: {
                  token,
                  user: userData,
                  subscriptions: []
                },
                message: '註冊成功！歡迎加入我們的服務，數據已保存到資料庫'
              }));
            });
          });
        });
      } catch (error) {
        console.error('註冊 API: 未預期的錯誤:', error);
        db.close();
        resolve(res.status(500).json({
          success: false,
          error: '註冊過程發生錯誤'
        }));
      }
    });

  } catch (error) {
    console.error('註冊 API: 註冊過程出錯:', error);

    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤，請稍後再試',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
