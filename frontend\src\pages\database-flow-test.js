import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function DatabaseFlowTest() {
  const router = useRouter();
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, { 
      id: Date.now(), 
      timestamp, 
      message, 
      type 
    }]);
    console.log(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    // 檢查當前登入狀態
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (token && userData) {
      try {
        const user = JSON.parse(userData);
        setCurrentUser(user);
        addLog(`✅ 當前已登入: ${user.name} (${user.email})`, 'success');
      } catch (error) {
        addLog('⚠️ 用戶數據解析失敗', 'warning');
      }
    } else {
      addLog('ℹ️ 當前未登入', 'info');
    }
  }, []);

  // 測試完整流程
  const testCompleteFlow = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    addLog('🚀 開始完整資料庫流程測試...', 'info');

    try {
      // 1. 測試資料庫連接
      addLog('📊 步驟 1: 測試資料庫連接', 'info');
      const dbResponse = await fetch('/api/test-database');
      const dbResult = await dbResponse.json();
      
      if (dbResult.success) {
        addLog(`✅ 資料庫連接成功 - 會員: ${dbResult.data.memberCount}, 服務: ${dbResult.data.serviceCount}`, 'success');
      } else {
        addLog(`❌ 資料庫連接失敗: ${dbResult.error}`, 'error');
        return;
      }

      // 2. 註冊新用戶
      addLog('👤 步驟 2: 註冊新用戶到資料庫', 'info');
      const testEmail = `dbtest${Date.now()}@example.com`;
      const registerResponse = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: '資料庫測試用戶',
          email: testEmail,
          password: '1234',
          confirmPassword: '1234',
          phone: '0912345678'
        })
      });

      const registerResult = await registerResponse.json();
      if (registerResult.success) {
        addLog(`✅ 註冊成功: ${testEmail} (ID: ${registerResult.data.user.id})`, 'success');
        
        // 保存登入信息
        localStorage.setItem('token', registerResult.data.token);
        localStorage.setItem('user', JSON.stringify(registerResult.data.user));
        setCurrentUser(registerResult.data.user);
      } else {
        addLog(`❌ 註冊失敗: ${registerResult.error}`, 'error');
        return;
      }

      // 3. 測試登入 (使用資料庫版本)
      addLog('🔑 步驟 3: 測試資料庫登入', 'info');
      const loginResponse = await fetch('/api/auth/login-db', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testEmail,
          password: '1234'
        })
      });

      const loginResult = await loginResponse.json();
      if (loginResult.success) {
        addLog(`✅ 登入成功，訂閱數量: ${loginResult.data.subscriptions.length}`, 'success');
      } else {
        addLog(`❌ 登入失敗: ${loginResult.error}`, 'error');
      }

      // 4. 查詢資料庫中的服務
      addLog('🛍️ 步驟 4: 查詢資料庫中的可用服務', 'info');
      const servicesResponse = await fetch('/api/services/database');
      const servicesResult = await servicesResponse.json();
      
      if (servicesResult.success) {
        addLog(`✅ 查詢到 ${servicesResult.data.length} 個服務 (來源: ${servicesResult.source})`, 'success');
        servicesResult.data.forEach((service, index) => {
          addLog(`   ${index + 1}. ${service.name} - $${service.price}/${service.billing_cycle}`, 'info');
        });

        // 5. 訂閱第一個服務
        if (servicesResult.data.length > 0) {
          const firstService = servicesResult.data[0];
          addLog(`📦 步驟 5: 訂閱服務 "${firstService.name}"`, 'info');
          
          const subscribeResponse = await fetch('/api/subscriptions/simple', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
              service_id: firstService.id
            })
          });

          const subscribeResult = await subscribeResponse.json();
          if (subscribeResult.success) {
            addLog(`✅ 訂閱成功: ${subscribeResult.data.service_name} (來源: ${subscribeResult.source})`, 'success');
            addLog(`   下次計費: ${subscribeResult.data.next_billing_date}`, 'info');
          } else {
            addLog(`❌ 訂閱失敗: ${subscribeResult.error}`, 'error');
          }

          // 6. 查詢用戶訂閱
          addLog('📋 步驟 6: 查詢用戶訂閱列表', 'info');
          const userSubsResponse = await fetch('/api/subscriptions/simple', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          const userSubsResult = await userSubsResponse.json();
          if (userSubsResult.success) {
            addLog(`✅ 查詢到 ${userSubsResult.data.length} 個訂閱 (來源: ${userSubsResult.source})`, 'success');
            userSubsResult.data.forEach((sub, index) => {
              addLog(`   ${index + 1}. ${sub.service_name} - 狀態: ${sub.status}`, 'info');
            });
          } else {
            addLog(`❌ 查詢訂閱失敗: ${userSubsResult.error}`, 'error');
          }
        }
      } else {
        addLog(`❌ 查詢服務失敗: ${servicesResult.error}`, 'error');
      }

      // 7. 驗證資料庫中的會員數據
      addLog('👥 步驟 7: 驗證資料庫中的會員數據', 'info');
      const membersResponse = await fetch('/api/members/list');
      const membersResult = await membersResponse.json();
      
      if (membersResult.success) {
        addLog(`✅ 資料庫中共有 ${membersResult.data.length} 個會員 (來源: ${membersResult.source})`, 'success');
        const newMember = membersResult.data.find(m => m.email === testEmail);
        if (newMember) {
          addLog(`✅ 找到新註冊的會員: ${newMember.name} (ID: ${newMember.id})`, 'success');
        } else {
          addLog(`⚠️ 未找到新註冊的會員`, 'warning');
        }
      } else {
        addLog(`❌ 查詢會員失敗: ${membersResult.error}`, 'error');
      }

      addLog('🎉 完整資料庫流程測試完成！', 'success');

    } catch (error) {
      addLog(`❌ 測試過程發生錯誤: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = () => {
    setTestResults([]);
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setCurrentUser(null);
    addLog('👋 已登出', 'info');
  };

  return (
    <>
      <Head>
        <title>資料庫流程測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🗄️ 資料庫流程測試</h1>
                <p className="text-gray-600 mt-2">測試註冊、登入、訂閱的完整資料庫流程</p>
              </div>
              <div className="text-right">
                {currentUser ? (
                  <div className="text-sm">
                    <div className="text-green-600 font-medium">✅ 已登入</div>
                    <div className="text-gray-600">{currentUser.name}</div>
                    <div className="text-gray-500">{currentUser.email}</div>
                    <button
                      onClick={logout}
                      className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700"
                    >
                      登出
                    </button>
                  </div>
                ) : (
                  <div className="text-sm text-gray-500">未登入</div>
                )}
              </div>
            </div>

            {/* 測試按鈕 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <button
                onClick={testCompleteFlow}
                disabled={isLoading}
                className="p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                <div className="text-lg font-medium">🚀 完整流程測試</div>
                <div className="text-sm opacity-75">註冊→登入→訂閱→查詢</div>
              </button>

              <button
                onClick={clearLogs}
                className="p-4 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <div className="text-lg font-medium">🗑️ 清除日誌</div>
                <div className="text-sm opacity-75">清空測試結果</div>
              </button>

              <a
                href="/database-viewer"
                className="p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center"
              >
                <div className="text-lg font-medium">👀 查看資料庫</div>
                <div className="text-sm opacity-75">直接查看資料庫內容</div>
              </a>
            </div>

            {/* 測試流程說明 */}
            <div className="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h2 className="text-lg font-semibold text-blue-900 mb-3">📋 測試流程</h2>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>1. 資料庫連接測試</strong> - 驗證 SQLite 資料庫是否可用</p>
                <p><strong>2. 用戶註冊</strong> - 創建新用戶並寫入資料庫</p>
                <p><strong>3. 用戶登入</strong> - 從資料庫驗證用戶身份</p>
                <p><strong>4. 服務查詢</strong> - 從資料庫讀取可用服務</p>
                <p><strong>5. 服務訂閱</strong> - 創建訂閱關係並寫入資料庫</p>
                <p><strong>6. 訂閱查詢</strong> - 從資料庫讀取用戶訂閱</p>
                <p><strong>7. 會員驗證</strong> - 確認新會員已存在於資料庫</p>
              </div>
            </div>

            {/* 測試日誌 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 測試日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                {testResults.map((log) => (
                  <div key={log.id} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'success' ? 'text-green-400' : 
                    log.type === 'warning' ? 'text-yellow-400' :
                    'text-blue-400'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))}
                {testResults.length === 0 && (
                  <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
