import '../styles/globals.css';
import React, { useState, useEffect } from 'react';
import Head from 'next/head';

// 全局狀態管理 (簡單版本)
export const AppContext = React.createContext();

function MyApp({ Component, pageProps }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState([]);

  // 初始化應用
  useEffect(() => {
    // 檢查用戶登入狀態
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('authToken');
        if (token) {
          // 驗證 token 並獲取用戶資訊
          const response = await fetch('/api/auth/verify', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (response.ok) {
            const userData = await response.json();
            setUser(userData.user);
          } else {
            localStorage.removeItem('authToken');
          }
        }
      } catch (error) {
        console.error('認證檢查失敗:', error);
        localStorage.removeItem('authToken');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // 添加通知
  const addNotification = (notification) => {
    const id = Date.now();
    const newNotification = { id, ...notification };
    setNotifications(prev => [...prev, newNotification]);

    // 自動移除通知
    setTimeout(() => {
      removeNotification(id);
    }, notification.duration || 5000);
  };

  // 移除通知
  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // 登入函數
  const login = (userData, token) => {
    setUser(userData);
    localStorage.setItem('authToken', token);
    addNotification({
      type: 'success',
      title: '登入成功',
      message: `歡迎回來，${userData.username}！`
    });
  };

  // 登出函數
  const logout = () => {
    setUser(null);
    localStorage.removeItem('authToken');
    addNotification({
      type: 'info',
      title: '已登出',
      message: '您已成功登出系統'
    });
  };

  // 全局狀態值
  const contextValue = {
    user,
    setUser,
    login,
    logout,
    loading,
    notifications,
    addNotification,
    removeNotification
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <AppContext.Provider value={contextValue}>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      
      {/* 通知系統 */}
      <NotificationContainer 
        notifications={notifications} 
        onRemove={removeNotification} 
      />
      
      <Component {...pageProps} />
    </AppContext.Provider>
  );
}

// 通知組件
function NotificationContainer({ notifications, onRemove }) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <Notification
          key={notification.id}
          notification={notification}
          onRemove={() => onRemove(notification.id)}
        />
      ))}
    </div>
  );
}

function Notification({ notification, onRemove }) {
  const { type, title, message } = notification;
  
  const typeStyles = {
    success: 'bg-green-500 text-white',
    error: 'bg-red-500 text-white',
    warning: 'bg-yellow-500 text-white',
    info: 'bg-blue-500 text-white'
  };

  const icons = {
    success: '✓',
    error: '✕',
    warning: '⚠',
    info: 'ℹ'
  };

  return (
    <div className={`max-w-sm w-full ${typeStyles[type]} p-4 rounded-lg shadow-lg animate-fade-in`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <span className="text-lg">{icons[type]}</span>
        </div>
        <div className="ml-3 flex-1">
          {title && (
            <p className="text-sm font-medium">{title}</p>
          )}
          {message && (
            <p className="text-sm mt-1 opacity-90">{message}</p>
          )}
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={onRemove}
            className="text-white hover:text-gray-200 focus:outline-none"
          >
            <span className="sr-only">關閉</span>
            <span className="text-lg">×</span>
          </button>
        </div>
      </div>
    </div>
  );
}

export default MyApp;
