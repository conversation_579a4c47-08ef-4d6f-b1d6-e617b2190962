import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function AddWalletBalanceColumn() {
  const router = useRouter();
  const [fixResults, setFixResults] = useState([]);
  const [isFixing, setIsFixing] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setFixResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const addWalletBalanceColumn = async () => {
    setIsFixing(true);
    setFixResults([]);
    setIsCompleted(false);

    try {
      addResult('開始', 'info', '開始添加 wallet_balance 欄位');

      // 檢查認證
      const token = localStorage.getItem('token');
      if (!token) {
        addResult('認證', 'error', '未找到認證 token，請先登入');
        return;
      }

      // 步驟 1: 檢查當前表結構
      addResult('步驟1', 'info', '檢查 members 表當前結構...');
      
      const columnsResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "PRAGMA table_info(members)"
        })
      });

      if (columnsResponse.ok) {
        const columnsResult = await columnsResponse.json();
        if (columnsResult.success) {
          addResult('步驟1', 'success', `members 表有 ${columnsResult.data.length} 個欄位`);
          
          const columns = columnsResult.data.map(col => col.name);
          addResult('步驟1', 'info', `現有欄位: ${columns.join(', ')}`);
          
          const hasWalletBalance = columns.includes('wallet_balance');
          if (hasWalletBalance) {
            addResult('步驟1', 'warning', 'wallet_balance 欄位已存在');
          } else {
            addResult('步驟1', 'info', 'wallet_balance 欄位不存在，需要添加');
          }
        } else {
          addResult('步驟1', 'error', `檢查表結構失敗: ${columnsResult.error}`);
          return;
        }
      } else {
        addResult('步驟1', 'error', '無法檢查表結構');
        return;
      }

      // 步驟 2: 添加 wallet_balance 欄位
      addResult('步驟2', 'info', '添加 wallet_balance 欄位...');
      
      const alterResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "ALTER TABLE members ADD COLUMN wallet_balance DECIMAL(15,2) DEFAULT 0.00"
        })
      });

      if (alterResponse.ok) {
        const alterResult = await alterResponse.json();
        if (alterResult.success) {
          addResult('步驟2', 'success', 'wallet_balance 欄位添加成功');
        } else {
          if (alterResult.error.includes('duplicate column name')) {
            addResult('步驟2', 'warning', 'wallet_balance 欄位已存在');
          } else {
            addResult('步驟2', 'error', `添加欄位失敗: ${alterResult.error}`);
            return;
          }
        }
      } else {
        addResult('步驟2', 'error', '添加欄位請求失敗');
        return;
      }

      // 步驟 3: 驗證欄位添加成功
      addResult('步驟3', 'info', '驗證欄位添加結果...');
      
      const verifyResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "PRAGMA table_info(members)"
        })
      });

      if (verifyResponse.ok) {
        const verifyResult = await verifyResponse.json();
        if (verifyResult.success) {
          const hasWalletBalance = verifyResult.data.some(col => col.name === 'wallet_balance');
          if (hasWalletBalance) {
            addResult('步驟3', 'success', 'wallet_balance 欄位驗證成功');
            
            const walletColumn = verifyResult.data.find(col => col.name === 'wallet_balance');
            addResult('步驟3', 'info', `欄位類型: ${walletColumn.type}, 預設值: ${walletColumn.dflt_value}`);
          } else {
            addResult('步驟3', 'error', 'wallet_balance 欄位驗證失敗');
            return;
          }
        }
      }

      // 步驟 4: 初始化管理員錢包餘額
      addResult('步驟4', 'info', '初始化管理員錢包餘額...');
      
      const updateResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "UPDATE members SET wallet_balance = ? WHERE id = ?",
          params: [1000.00, 1]
        })
      });

      if (updateResponse.ok) {
        const updateResult = await updateResponse.json();
        if (updateResult.success) {
          addResult('步驟4', 'success', '管理員錢包餘額設置為 $1000.00');
        } else {
          addResult('步驟4', 'error', `更新餘額失敗: ${updateResult.error}`);
        }
      }

      // 步驟 5: 查詢管理員數據驗證
      addResult('步驟5', 'info', '驗證管理員數據...');
      
      const queryResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "SELECT id, name, email, wallet_balance FROM members WHERE id = ?",
          params: [1]
        })
      });

      if (queryResponse.ok) {
        const queryResult = await queryResponse.json();
        if (queryResult.success && queryResult.data.length > 0) {
          const admin = queryResult.data[0];
          addResult('步驟5', 'success', `管理員數據: ${admin.name} - 餘額: $${admin.wallet_balance}`);
        } else {
          addResult('步驟5', 'error', '無法查詢管理員數據');
        }
      }

      // 步驟 6: 測試錢包 API
      addResult('步驟6', 'info', '測試錢包 API...');
      
      const walletResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (walletResponse.ok) {
        const walletResult = await walletResponse.json();
        if (walletResult.success) {
          addResult('步驟6', 'success', `錢包 API 正常！餘額: $${walletResult.data.balance}`);
        } else {
          addResult('步驟6', 'error', `錢包 API 失敗: ${walletResult.error}`);
        }
      } else {
        addResult('步驟6', 'error', '錢包 API 請求失敗');
      }

      addResult('完成', 'success', 'wallet_balance 欄位添加完成！');
      setIsCompleted(true);

    } catch (error) {
      addResult('錯誤', 'error', '修復過程中出現錯誤: ' + error.message);
    } finally {
      setIsFixing(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>添加錢包餘額欄位 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">💰 添加錢包餘額欄位</h1>
                <p className="text-gray-600 mt-2">修復 SQLITE_ERROR: no such column: wallet_balance</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/wallet-login-fix')}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  🔧 錢包登入修復
                </button>
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={addWalletBalanceColumn}
                  disabled={isFixing}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-medium"
                >
                  {isFixing ? '修復中...' : '🔧 添加欄位'}
                </button>
              </div>
            </div>

            {/* 錯誤說明 */}
            <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-medium text-red-900 mb-2">❌ 錯誤原因</h3>
              <div className="text-sm text-red-800 space-y-1">
                <div><strong>錯誤信息:</strong> SQLITE_ERROR: no such column: wallet_balance</div>
                <div><strong>問題原因:</strong> members 表中沒有 wallet_balance 欄位</div>
                <div><strong>解決方案:</strong> 使用 ALTER TABLE 添加 wallet_balance 欄位</div>
              </div>
            </div>

            {/* 修復結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 修復結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {fixResults.length > 0 ? (
                  <div className="space-y-3">
                    {fixResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                            {result.data && (
                              <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono max-h-32 overflow-y-auto">
                                <pre className="whitespace-pre-wrap">
                                  {JSON.stringify(result.data, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「添加欄位」來修復 wallet_balance 欄位問題
                  </div>
                )}
              </div>
            </div>

            {/* 完成後的操作 */}
            {isCompleted && (
              <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-medium text-green-900 mb-3">🎉 修復完成</h3>
                <div className="text-sm text-green-800 mb-4">
                  wallet_balance 欄位已成功添加，錢包 API 現在應該可以正常工作了！
                </div>
                <div className="flex space-x-4">
                  <button
                    onClick={() => router.push('/wallet-login-fix')}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    🔧 重新測試錢包登入
                  </button>
                  <button
                    onClick={() => router.push('/wallet')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    💰 測試錢包頁面
                  </button>
                </div>
              </div>
            )}

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 修復步驟</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>步驟1:</strong> 檢查 members 表當前結構</div>
                <div><strong>步驟2:</strong> 執行 ALTER TABLE 添加 wallet_balance 欄位</div>
                <div><strong>步驟3:</strong> 驗證欄位添加成功</div>
                <div><strong>步驟4:</strong> 初始化管理員錢包餘額為 $1000.00</div>
                <div><strong>步驟5:</strong> 查詢驗證管理員數據</div>
                <div><strong>步驟6:</strong> 測試錢包 API 是否正常工作</div>
              </div>
            </div>

            {/* SQL 命令 */}
            <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">📝 執行的 SQL 命令</h3>
              <div className="text-sm font-mono text-gray-700 space-y-1">
                <div>PRAGMA table_info(members);</div>
                <div>ALTER TABLE members ADD COLUMN wallet_balance DECIMAL(15,2) DEFAULT 0.00;</div>
                <div>UPDATE members SET wallet_balance = 1000.00 WHERE id = 1;</div>
                <div>SELECT id, name, email, wallet_balance FROM members WHERE id = 1;</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
