import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletDbCheck() {
  const router = useRouter();
  const [checkResults, setCheckResults] = useState([]);
  const [isChecking, setIsChecking] = useState(false);
  const [dbData, setDbData] = useState(null);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setCheckResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const checkDatabaseData = async () => {
    setIsChecking(true);
    setCheckResults([]);
    setDbData(null);

    try {
      addResult('開始', 'info', '開始檢查資料庫錢包數據');

      // 檢查認證
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (!token || !userStr) {
        addResult('認證', 'error', '未找到認證數據');
        return;
      }

      let user;
      try {
        user = JSON.parse(userStr);
        addResult('認證', 'success', `用戶: ${user.name} (ID: ${user.userId})`);
      } catch (error) {
        addResult('認證', 'error', '用戶數據解析失敗');
        return;
      }

      // 檢查會員表中的錢包餘額
      addResult('步驟1', 'info', '檢查會員表中的錢包餘額...');
      
      const memberResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: 'SELECT id, name, email, wallet_balance FROM members WHERE id = ?',
          params: [user.userId]
        })
      });

      if (memberResponse.ok) {
        const memberResult = await memberResponse.json();
        if (memberResult.success && memberResult.data.length > 0) {
          const memberData = memberResult.data[0];
          addResult('步驟1', 'success', `會員錢包餘額: $${memberData.wallet_balance || 0}`);
          setDbData(prev => ({ ...prev, member: memberData }));
        } else {
          addResult('步驟1', 'warning', '會員數據不存在或查詢失敗');
        }
      } else {
        addResult('步驟1', 'error', '無法查詢會員數據');
      }

      // 檢查錢包交易記錄表
      addResult('步驟2', 'info', '檢查錢包交易記錄...');
      
      const transactionResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: 'SELECT * FROM wallet_transactions WHERE member_id = ? ORDER BY created_at DESC LIMIT 10',
          params: [user.userId]
        })
      });

      if (transactionResponse.ok) {
        const transactionResult = await transactionResponse.json();
        if (transactionResult.success) {
          addResult('步驟2', 'success', `找到 ${transactionResult.data.length} 筆交易記錄`);
          setDbData(prev => ({ ...prev, transactions: transactionResult.data }));
        } else {
          addResult('步驟2', 'warning', '交易記錄查詢失敗');
        }
      } else {
        addResult('步驟2', 'error', '無法查詢交易記錄');
      }

      // 檢查支付方式表
      addResult('步驟3', 'info', '檢查支付方式數據...');
      
      const paymentResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: 'SELECT * FROM payment_methods WHERE is_active = 1',
          params: []
        })
      });

      if (paymentResponse.ok) {
        const paymentResult = await paymentResponse.json();
        if (paymentResult.success) {
          addResult('步驟3', 'success', `找到 ${paymentResult.data.length} 種支付方式`);
          setDbData(prev => ({ ...prev, paymentMethods: paymentResult.data }));
        } else {
          addResult('步驟3', 'warning', '支付方式查詢失敗');
        }
      } else {
        addResult('步驟3', 'error', '無法查詢支付方式');
      }

      // 測試錢包 API
      addResult('步驟4', 'info', '測試錢包 API...');
      
      const walletResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (walletResponse.ok) {
        const walletResult = await walletResponse.json();
        if (walletResult.success) {
          addResult('步驟4', 'success', `錢包 API 正常，餘額: $${walletResult.data.balance}`);
          setDbData(prev => ({ ...prev, apiBalance: walletResult.data.balance }));
        } else {
          addResult('步驟4', 'error', `錢包 API 失敗: ${walletResult.error}`);
        }
      } else {
        addResult('步驟4', 'error', '錢包 API 無法訪問');
      }

      addResult('完成', 'success', '資料庫錢包數據檢查完成');

    } catch (error) {
      addResult('錯誤', 'error', '檢查過程中出現錯誤: ' + error.message);
    } finally {
      setIsChecking(false);
    }
  };

  const initializeWalletBalance = async () => {
    try {
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (!token || !userStr) {
        alert('請先登入');
        return;
      }

      const user = JSON.parse(userStr);
      
      // 初始化會員錢包餘額
      const response = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: 'UPDATE members SET wallet_balance = ? WHERE id = ?',
          params: [1000.00, user.userId]
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert('錢包餘額初始化成功！設置為 $1000.00');
          checkDatabaseData(); // 重新檢查
        } else {
          alert('初始化失敗: ' + result.error);
        }
      } else {
        alert('初始化請求失敗');
      }
    } catch (error) {
      alert('初始化失敗: ' + error.message);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>錢包資料庫檢查 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🗄️ 錢包資料庫檢查</h1>
                <p className="text-gray-600 mt-2">檢查資料庫中的錢包相關數據</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={initializeWalletBalance}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  🚀 初始化餘額
                </button>
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={checkDatabaseData}
                  disabled={isChecking}
                  className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 font-medium"
                >
                  {isChecking ? '檢查中...' : '🔍 開始檢查'}
                </button>
              </div>
            </div>

            {/* 資料庫數據顯示 */}
            {dbData && (
              <div className="mb-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* 會員數據 */}
                {dbData.member && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="font-medium text-blue-900 mb-3">👤 會員數據</h3>
                    <div className="text-sm text-blue-800 space-y-1">
                      <div>ID: {dbData.member.id}</div>
                      <div>姓名: {dbData.member.name}</div>
                      <div>Email: {dbData.member.email}</div>
                      <div className="font-semibold">錢包餘額: ${dbData.member.wallet_balance || 0}</div>
                    </div>
                  </div>
                )}

                {/* 交易記錄 */}
                {dbData.transactions && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="font-medium text-green-900 mb-3">📋 交易記錄</h3>
                    <div className="text-sm text-green-800">
                      <div>總記錄數: {dbData.transactions.length}</div>
                      {dbData.transactions.length > 0 && (
                        <div className="mt-2 space-y-1">
                          <div>最新交易:</div>
                          <div className="pl-2">
                            <div>類型: {dbData.transactions[0].transaction_type}</div>
                            <div>金額: ${dbData.transactions[0].amount}</div>
                            <div>狀態: {dbData.transactions[0].status}</div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* 支付方式 */}
                {dbData.paymentMethods && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 className="font-medium text-yellow-900 mb-3">💳 支付方式</h3>
                    <div className="text-sm text-yellow-800">
                      <div>可用方式: {dbData.paymentMethods.length}</div>
                      {dbData.paymentMethods.length > 0 && (
                        <div className="mt-2 space-y-1">
                          {dbData.paymentMethods.slice(0, 3).map((method, index) => (
                            <div key={index}>• {method.name}</div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 檢查結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 檢查結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {checkResults.length > 0 ? (
                  <div className="space-y-3">
                    {checkResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                            {result.data && (
                              <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono max-h-32 overflow-y-auto">
                                <pre className="whitespace-pre-wrap">
                                  {JSON.stringify(result.data, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「開始檢查」來檢查資料庫錢包數據
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 檢查內容</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>步驟1:</strong> 檢查 members 表中的 wallet_balance 欄位</div>
                <div><strong>步驟2:</strong> 檢查 wallet_transactions 表中的交易記錄</div>
                <div><strong>步驟3:</strong> 檢查 payment_methods 表中的支付方式</div>
                <div><strong>步驟4:</strong> 測試錢包 API 是否正常工作</div>
                <div><strong>目的:</strong> 確認錢包功能使用真實資料庫數據而非虛擬數據</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
