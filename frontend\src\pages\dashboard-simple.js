import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function DashboardSimple() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [mounted, setMounted] = useState(false);
  const [subscriptions, setSubscriptions] = useState([]);
  const [isLoadingSubscriptions, setIsLoadingSubscriptions] = useState(false);
  const [walletBalance, setWalletBalance] = useState(null);
  const [isLoadingWallet, setIsLoadingWallet] = useState(false);

  // 確保組件已掛載
  useEffect(() => {
    setMounted(true);
  }, []);

  // 載入用戶訂閱數據
  const loadSubscriptions = async () => {
    setIsLoadingSubscriptions(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('http://localhost:3000/api/v1/subscriptions', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      if (result.success) {
        const activeSubscriptions = result.data.filter(sub => sub.status === 'active');
        setSubscriptions(activeSubscriptions);
      }
    } catch (error) {
      console.error('載入訂閱失敗:', error);
    } finally {
      setIsLoadingSubscriptions(false);
    }
  };

  // 載入錢包餘額
  const loadWalletBalance = async () => {
    setIsLoadingWallet(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      if (result.success) {
        setWalletBalance(result.data.balance);
      } else {
        // 如果是認證錯誤，不顯示錯誤，只是不顯示餘額
        if (result.error && (result.error.includes('認證失效') || result.error.includes('請先登入'))) {
          console.log('錢包餘額載入需要重新認證');
          setWalletBalance(null);
        } else {
          console.error('載入錢包餘額失敗:', result.error);
        }
      }
    } catch (error) {
      console.error('載入錢包餘額失敗:', error);
    } finally {
      setIsLoadingWallet(false);
    }
  };

  // 簡單的認證檢查
  useEffect(() => {
    if (!mounted) return;

    console.log('Dashboard: 開始認證檢查');

    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');

    console.log('Dashboard: Token 存在:', !!token);
    console.log('Dashboard: User 數據存在:', !!userStr);

    if (token && userStr) {
      try {
        const userData = JSON.parse(userStr);
        console.log('Dashboard: 用戶數據解析成功:', userData);
        setUser(userData);
        // 載入訂閱數據和錢包餘額
        loadSubscriptions();
        loadWalletBalance();
      } catch (error) {
        console.error('Dashboard: 用戶數據解析錯誤:', error);
        console.log('Dashboard: 原始用戶數據:', userStr);
        localStorage.clear();
        router.push('/auth/login');
      }
    } else {
      console.log('Dashboard: 缺少認證數據，重定向到登入頁面');
      router.push('/auth/login');
    }
  }, [mounted, router]);

  const handleLogout = () => {
    localStorage.clear();
    document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    router.push('/');
  };

  // 如果還沒掛載或沒有用戶，顯示載入
  if (!mounted || !user) {
    console.log('Dashboard: 顯示載入狀態 - mounted:', mounted, 'user:', !!user);

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
          <div className="mt-4 text-xs text-gray-500">
            <div>Mounted: {mounted ? '✅' : '❌'}</div>
            <div>User: {user ? '✅' : '❌'}</div>
          </div>
          <div className="mt-4 space-y-2">
            <button
              onClick={() => window.location.href = '/instant-fix'}
              className="w-full px-4 py-3 bg-red-600 text-white rounded-lg text-sm hover:bg-red-700 font-medium"
            >
              ⚡ 一鍵修復 (推薦)
            </button>
            <button
              onClick={() => window.location.href = '/fix-auth'}
              className="w-full px-4 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              🔧 自動修復認證
            </button>
            <button
              onClick={() => window.location.href = '/quick-login'}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
            >
              🔑 快速登入
            </button>
            <button
              onClick={() => window.location.href = '/debug-auth'}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
            >
              🔍 調試認證
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>會員儀表板 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 頂部導航 */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">會員儀表板</h1>
                <p className="text-sm text-gray-600">歡迎回來，{user.name}</p>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-500">{user.email}</span>
                <button
                  onClick={handleLogout}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  登出
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* 主要內容 */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            
            {/* 歡迎卡片 */}
            <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
              <div className="px-4 py-5 sm:p-6">
                <h2 className="text-lg leading-6 font-medium text-gray-900 mb-2">
                  歡迎使用會員管理系統
                </h2>
                <p className="text-sm text-gray-600">
                  您已成功登入系統。您可以在這裡查看您的服務訂閱和帳戶設置。
                </p>
                <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">會員等級：</span>
                    <span className="text-gray-600">{user.role === 'admin' ? '管理員' : '一般會員'}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">帳戶狀態：</span>
                    <span className="text-green-600">{user.status || '正常'}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">錢包餘額：</span>
                    {isLoadingWallet ? (
                      <span className="text-gray-500">載入中...</span>
                    ) : walletBalance !== null ? (
                      <span className="text-blue-600 font-semibold">${walletBalance.toFixed(2)}</span>
                    ) : (
                      <span className="text-gray-500">無法載入</span>
                    )}
                  </div>
                  {user.created_at && (
                    <div className="sm:col-span-2">
                      <span className="font-medium text-gray-700">註冊時間：</span>
                      <span className="text-gray-600">{new Date(user.created_at).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  快速操作
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">

                  <a
                    href="/subscriptions"
                    className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        <span className="absolute inset-0" aria-hidden="true" />
                        📦 服務訂閱
                      </h3>
                    </div>
                  </a>

                  <a
                    href="/wallet"
                    className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        <span className="absolute inset-0" aria-hidden="true" />
                        💰 我的錢包
                      </h3>
                    </div>
                  </a>

                  <a
                    href="/wallet/recharge"
                    className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-green-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        <span className="absolute inset-0" aria-hidden="true" />
                        💳 錢包充值
                      </h3>
                    </div>
                  </a>

                  <a
                    href="/wallet/transfer"
                    className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-purple-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        <span className="absolute inset-0" aria-hidden="true" />
                        💸 轉帳功能
                      </h3>
                    </div>
                  </a>

                  <a
                    href="/wallet/withdraw"
                    className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-orange-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        <span className="absolute inset-0" aria-hidden="true" />
                        🏧 錢包提現
                      </h3>
                    </div>
                  </a>

                  <a
                    href="/services"
                    className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-cyan-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        <span className="absolute inset-0" aria-hidden="true" />
                        🛍️ 服務商店
                      </h3>
                    </div>
                  </a>



                  {user.role === 'admin' && (
                    <>
                      <a
                        href="/admin"
                        className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-red-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            <span className="absolute inset-0" aria-hidden="true" />
                            ⚙️ 管理後台
                          </h3>
                        </div>
                      </a>

                      <a
                        href="/admin/members"
                        className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            <span className="absolute inset-0" aria-hidden="true" />
                            👥 會員管理
                          </h3>
                        </div>
                      </a>

                      <a
                        href="/admin/services"
                        className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-green-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            <span className="absolute inset-0" aria-hidden="true" />
                            🛍️ 服務管理
                          </h3>
                        </div>
                      </a>

                      <a
                        href="/admin/database"
                        className="relative group bg-white p-4 focus-within:ring-2 focus-within:ring-inset focus-within:ring-purple-500 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            <span className="absolute inset-0" aria-hidden="true" />
                            🗄️ 資料庫管理
                          </h3>
                        </div>
                      </a>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* 已訂閱服務 */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  📋 我的訂閱服務
                </h3>

                {isLoadingSubscriptions ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-gray-600">載入中...</span>
                  </div>
                ) : subscriptions.length > 0 ? (
                  <div className="space-y-3">
                    {subscriptions.map((subscription, index) => (
                      <div key={subscription.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-blue-600 font-medium text-sm">{index + 1}</span>
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">
                              {subscription.service_name || subscription.name}
                            </h4>
                            <p className="text-xs text-gray-500">
                              訂閱時間: {new Date(subscription.subscribed_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            使用中
                          </span>
                          {subscription.service_price && (
                            <span className="text-sm text-gray-600">
                              ${subscription.service_price}/{subscription.service_billing_cycle === 'monthly' ? '月' : '年'}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-400 mb-2 text-4xl">
                      📦
                    </div>
                    <h4 className="text-sm font-medium text-gray-900 mb-1">暫無訂閱服務</h4>
                    <p className="text-sm text-gray-500 mb-4">您還沒有訂閱任何服務</p>
                    <a
                      href="/subscriptions"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      瀏覽可用服務
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* 登出按鈕 */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <button
                  onClick={handleLogout}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  登出
                </button>
              </div>
            </div>

          </div>
        </main>
      </div>
    </>
  );
}
