// 服務介紹頁面
import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function Services() {
  const [services, setServices] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadServicesData();
  }, []);

  const loadServicesData = async () => {
    try {
      const response = await fetch('/api/stats/dashboard');
      const result = await response.json();
      
      if (result.success) {
        setServices(result.data.services || []);
        setStats(result.data);
      }
    } catch (error) {
      console.error('載入服務數據失敗:', error);
    } finally {
      setLoading(false);
    }
  };

  const defaultServices = [
    {
      name: 'AI 文字生成服務',
      description: '使用先進的 AI 技術生成高品質文字內容，支援多種文體和語言',
      price: 'NT$299/月起',
      features: ['文章生成', '摘要製作', '翻譯服務', '內容優化'],
      icon: '🤖'
    },
    {
      name: '圖片處理服務',
      description: '專業的圖片編輯和處理服務，支援批量處理和 AI 增強',
      price: 'NT$199/月起',
      features: ['圖片壓縮', 'AI 修復', '批量處理', '格式轉換'],
      icon: '🖼️'
    },
    {
      name: '數據分析服務',
      description: '深度數據分析和可視化服務，幫助您洞察業務趨勢',
      price: 'NT$499/月起',
      features: ['數據可視化', '趨勢分析', '報告生成', 'API 整合'],
      icon: '📊'
    },
    {
      name: '雲端存儲服務',
      description: '安全可靠的雲端存儲解決方案，支援多設備同步',
      price: 'NT$99/月起',
      features: ['100GB 存儲', '多設備同步', '版本控制', '分享功能'],
      icon: '☁️'
    }
  ];

  const displayServices = services.length > 0 ? services : defaultServices;

  return (
    <>
      <Head>
        <title>服務介紹 - 會員管理系統</title>
        <meta name="description" content="探索我們提供的各種專業服務，滿足您的不同需求" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 頁面標題 */}
        <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.h1 
              className="text-4xl md:text-6xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              我們的服務
            </motion.h1>
            <motion.p 
              className="text-xl md:text-2xl mb-8 opacity-90"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              專業、可靠、創新的解決方案
            </motion.p>
            
            {/* 統計數據 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-12">
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.totalServices || 0}</div>
                <div className="text-blue-200">可用服務</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.totalUsers || 0}</div>
                <div className="text-blue-200">滿意用戶</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.additionalStats?.activeServices || 0}</div>
                <div className="text-blue-200">活躍訂閱</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.systemUptime || '99.9%'}</div>
                <div className="text-blue-200">服務穩定性</div>
              </div>
            </div>
          </div>
        </section>

        {/* 服務列表 */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">服務方案</h2>
              <p className="text-xl text-gray-600">選擇最適合您需求的服務方案</p>
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="mt-4 text-gray-600">載入服務資料中...</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                {displayServices.map((service, index) => (
                  <motion.div
                    key={index}
                    className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <div className="text-4xl mb-4">{service.icon || '🔧'}</div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{service.name}</h3>
                    <p className="text-gray-600 mb-4">{service.description}</p>
                    
                    {service.price && (
                      <div className="text-2xl font-bold text-blue-600 mb-4">{service.price}</div>
                    )}
                    
                    {service.features && (
                      <ul className="space-y-2 mb-6">
                        {service.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                            <span className="text-green-500 mr-2">✓</span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    )}
                    
                    <Link 
                      href="/register"
                      className="block w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200"
                    >
                      立即訂閱
                    </Link>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* 為什麼選擇我們 */}
        <section className="bg-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">為什麼選擇我們</h2>
              <p className="text-xl text-gray-600">我們致力於提供最優質的服務體驗</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl mb-4">🚀</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">快速部署</h3>
                <p className="text-gray-600">幾分鐘內即可開始使用我們的服務</p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">🔒</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">安全可靠</h3>
                <p className="text-gray-600">企業級安全保障，保護您的數據安全</p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">💬</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">24/7 支援</h3>
                <p className="text-gray-600">全天候技術支援，隨時為您解決問題</p>
              </div>
            </div>
          </div>
        </section>

        {/* 行動呼籲 */}
        <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-4">準備開始了嗎？</h2>
            <p className="text-xl mb-8 opacity-90">立即註冊，體驗我們的專業服務</p>
            <div className="space-x-4">
              <Link 
                href="/register"
                className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
              >
                免費註冊
              </Link>
              <Link 
                href="/login"
                className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors duration-200"
              >
                立即登入
              </Link>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
