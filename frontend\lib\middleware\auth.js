// 認證中間件
const jwt = require('jsonwebtoken');
const path = require('path');

// 動態導入 Member 模型
const getMember = () => {
  try {
    // 嘗試多個可能的路徑
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'models', 'Member'),
      path.join(process.cwd(), 'lib', 'database', 'models', 'Member.js'),
      path.join(__dirname, '..', 'database', 'models', 'Member'),
      path.join(__dirname, '..', 'database', 'models', 'Member.js')
    ];

    for (const memberPath of possiblePaths) {
      try {
        return require(memberPath);
      } catch (err) {
        continue;
      }
    }

    throw new Error('無法找到 Member 模型');
  } catch (error) {
    console.error('Member 模型載入失敗:', error);
    throw error;
  }
};

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// 從請求中提取 token
const extractToken = (req) => {
  // 從 Authorization header 中獲取
  let token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    // 嘗試從 cookie 中獲取
    const cookies = req.headers.cookie;
    if (cookies) {
      const tokenMatch = cookies.match(/access_token=([^;]+)/);
      if (tokenMatch) {
        token = tokenMatch[1];
      }
    }
  }
  
  return token;
};

// 必須登入的中間件
const requireAuth = async (req, res, next) => {
  try {
    const token = extractToken(req);

    if (!token) {
      return res.status(401).json({
        success: false,
        error: '需要登入才能訪問此資源'
      });
    }

    // 驗證 JWT Token
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // 查找會員
    const Member = getMember();
    const member = await Member.findById(decoded.userId);
    if (!member) {
      return res.status(401).json({
        success: false,
        error: '會員不存在'
      });
    }

    // 檢查帳號狀態
    if (member.status !== 'active') {
      return res.status(403).json({
        success: false,
        error: '帳號已被停用'
      });
    }

    // 將會員信息添加到請求對象
    req.user = member;
    req.userId = member.id;

    if (next) {
      next();
    } else {
      return { user: member, userId: member.id };
    }

  } catch (error) {
    console.error('認證中間件錯誤:', error);
    
    // JWT 相關錯誤
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: '無效的 Token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token 已過期，請重新登入'
      });
    }

    return res.status(500).json({
      success: false,
      error: '認證失敗'
    });
  }
};

// 可選登入的中間件
const optionalAuth = async (req, res, next) => {
  try {
    const token = extractToken(req);

    if (!token) {
      req.user = null;
      req.userId = null;
      if (next) next();
      return { user: null, userId: null };
    }

    // 驗證 JWT Token
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // 查找會員
    const Member = getMember();
    const member = await Member.findById(decoded.userId);
    if (!member || member.status !== 'active') {
      req.user = null;
      req.userId = null;
    } else {
      req.user = member;
      req.userId = member.id;
    }

    if (next) {
      next();
    } else {
      return { user: req.user, userId: req.userId };
    }

  } catch (error) {
    // 可選認證失敗時不返回錯誤，只是設置為未登入狀態
    req.user = null;
    req.userId = null;
    if (next) {
      next();
    } else {
      return { user: null, userId: null };
    }
  }
};

// 管理員權限檢查
const requireAdmin = async (req, res, next) => {
  try {
    // 先進行基本認證
    const authResult = await requireAuth(req, res);
    if (!authResult || !authResult.user) {
      return; // requireAuth 已經處理了錯誤響應
    }

    // 檢查管理員權限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: '需要管理員權限'
      });
    }

    if (next) {
      next();
    } else {
      return authResult;
    }

  } catch (error) {
    console.error('管理員權限檢查錯誤:', error);
    return res.status(500).json({
      success: false,
      error: '權限檢查失敗'
    });
  }
};

// 用於 API 路由的認證包裝器
const withAuth = (handler, options = {}) => {
  return async (req, res) => {
    try {
      let authResult;
      
      if (options.required === false) {
        authResult = await optionalAuth(req, res);
      } else if (options.admin === true) {
        authResult = await requireAdmin(req, res);
        if (!authResult) return; // 認證失敗
      } else {
        authResult = await requireAuth(req, res);
        if (!authResult) return; // 認證失敗
      }

      // 執行原始處理器
      return await handler(req, res);
      
    } catch (error) {
      console.error('認證包裝器錯誤:', error);
      return res.status(500).json({
        success: false,
        error: '服務器內部錯誤'
      });
    }
  };
};

module.exports = {
  requireAuth,
  optionalAuth,
  requireAdmin,
  withAuth,
  extractToken
};
