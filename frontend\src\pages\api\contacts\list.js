// 聯絡人列表 API
import jwt from 'jsonwebtoken';
import sqlite3 from 'sqlite3';
import path from 'path';
import fs from 'fs';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];
    
    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('聯絡人 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }
    
    console.log('聯絡人 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('聯絡人 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    // 驗證 JWT token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('聯絡人 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    const userId = decoded.userId;
    console.log('聯絡人 API: 用戶 ID:', userId);

    const db = connectDatabase();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    return new Promise((resolve) => {
      // 查詢用戶的聯絡人，包含轉帳統計
      const query = `
        SELECT
          fc.id,
          fc.contact_name,
          fc.contact_email,
          fc.contact_phone,
          fc.avatar_emoji,
          fc.nickname,
          fc.notes,
          fc.is_favorite,
          fc.transfer_count,
          fc.last_transfer_at,
          fc.created_at,
          COALESCE(
            (SELECT COUNT(*) FROM wallet_transactions wt
             WHERE wt.member_id = fc.user_id
               AND wt.transaction_type = 'transfer_out'
               AND wt.description LIKE '%' || fc.contact_email || '%'),
            0
          ) as actual_transfer_count,
          COALESCE(
            (SELECT SUM(ABS(wt.amount)) FROM wallet_transactions wt
             WHERE wt.member_id = fc.user_id
               AND wt.transaction_type = 'transfer_out'
               AND wt.description LIKE '%' || fc.contact_email || '%'),
            0
          ) as total_transfer_amount,
          COALESCE(
            (SELECT MAX(wt.created_at) FROM wallet_transactions wt
             WHERE wt.member_id = fc.user_id
               AND wt.transaction_type = 'transfer_out'
               AND wt.description LIKE '%' || fc.contact_email || '%'),
            fc.last_transfer_at
          ) as latest_transfer_at
        FROM frequent_contacts fc
        WHERE fc.user_id = ?
        ORDER BY fc.is_favorite DESC, fc.transfer_count DESC, fc.created_at DESC
      `;
      
      db.all(query, [userId], (err, contacts) => {
        if (err) {
          console.error('聯絡人 API: 查詢失敗:', err);
          db.close();
          resolve(res.status(500).json({
            success: false,
            error: '查詢聯絡人失敗: ' + err.message
          }));
          return;
        }

        console.log(`聯絡人 API: 找到 ${contacts.length} 個聯絡人`);

        // 查詢聯絡人群組
        const groupQuery = `
          SELECT 
            cg.id,
            cg.group_name,
            cg.group_color,
            cg.group_icon,
            cg.description,
            COUNT(cgm.contact_id) as member_count
          FROM contact_groups cg
          LEFT JOIN contact_group_members cgm ON cg.id = cgm.group_id
          WHERE cg.user_id = ?
          GROUP BY cg.id, cg.group_name, cg.group_color, cg.group_icon, cg.description
          ORDER BY cg.group_name
        `;

        db.all(groupQuery, [userId], (err2, groups) => {
          db.close();
          
          if (err2) {
            console.error('聯絡人 API: 查詢群組失敗:', err2);
            resolve(res.status(500).json({
              success: false,
              error: '查詢聯絡人群組失敗: ' + err2.message
            }));
            return;
          }

          console.log(`聯絡人 API: 找到 ${groups.length} 個群組`);

          resolve(res.status(200).json({
            success: true,
            data: {
              contacts: contacts,
              groups: groups,
              summary: {
                total_contacts: contacts.length,
                favorite_contacts: contacts.filter(c => c.is_favorite).length,
                total_groups: groups.length
              }
            },
            message: '成功獲取聯絡人列表'
          }));
        });
      });
    });

  } catch (error) {
    console.error('聯絡人 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
