const { Pool } = require('pg');

// 全域測試設置
let testDb;

beforeAll(async () => {
  // 設置測試資料庫連接
  testDb = new Pool({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
  });

  // 等待資料庫連接
  try {
    await testDb.query('SELECT 1');
    console.log('✅ 測試資料庫連接成功');
  } catch (error) {
    console.warn('⚠️ 測試資料庫連接失敗，將使用模擬資料庫');
    // 如果資料庫連接失敗，可以使用記憶體資料庫或模擬
  }
});

afterAll(async () => {
  // 清理測試資料庫連接
  if (testDb) {
    await testDb.end();
  }
});

// 每個測試前清理資料
beforeEach(async () => {
  if (testDb) {
    try {
      // 清理測試資料 (保留結構)
      await testDb.query('TRUNCATE TABLE user_activity_logs, security_audit_logs, system_audit_logs RESTART IDENTITY CASCADE');
      await testDb.query('TRUNCATE TABLE subscription_usage, subscriptions RESTART IDENTITY CASCADE');
      await testDb.query('TRUNCATE TABLE transactions, payments RESTART IDENTITY CASCADE');
      await testDb.query('TRUNCATE TABLE wallets RESTART IDENTITY CASCADE');
      await testDb.query('TRUNCATE TABLE user_consents, user_social_accounts RESTART IDENTITY CASCADE');
      await testDb.query('TRUNCATE TABLE users RESTART IDENTITY CASCADE');
    } catch (error) {
      // 如果清理失敗，繼續執行測試
      console.warn('⚠️ 測試資料清理失敗:', error.message);
    }
  }
});

// 全域測試工具函數
global.createTestUser = async (userData = {}) => {
  const defaultUser = {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'Test123456',
    first_name: 'Test',
    last_name: 'User',
    phone: '+************',
    status: 'active'
  };

  return { ...defaultUser, ...userData };
};

global.createTestWallet = async (userId, currency = 'TWD') => {
  return {
    user_id: userId,
    currency: currency,
    balance: 0,
    frozen_balance: 0,
    total_deposited: 0,
    total_withdrawn: 0,
    status: 'active'
  };
};

global.createTestPayment = async (userId, amount = 1000) => {
  return {
    user_id: userId,
    payment_method: 'credit_card',
    provider: 'stripe',
    amount: amount,
    currency: 'TWD',
    status: 'pending'
  };
};

// 模擬外部服務
global.mockStripe = {
  paymentIntents: {
    create: jest.fn().mockResolvedValue({
      id: 'pi_test_123',
      client_secret: 'pi_test_123_secret',
      status: 'requires_payment_method'
    }),
    confirm: jest.fn().mockResolvedValue({
      id: 'pi_test_123',
      status: 'succeeded'
    })
  }
};

global.mockPayPal = {
  orders: {
    create: jest.fn().mockResolvedValue({
      id: 'ORDER_123',
      status: 'CREATED',
      links: [
        {
          rel: 'approve',
          href: 'https://www.sandbox.paypal.com/checkoutnow?token=ORDER_123'
        }
      ]
    }),
    capture: jest.fn().mockResolvedValue({
      id: 'ORDER_123',
      status: 'COMPLETED'
    })
  }
};

// 測試資料庫工具
global.testDb = testDb;
