import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function LoginRedirectDemo() {
  const router = useRouter();
  const [currentUrl, setCurrentUrl] = useState('');
  const [redirectParam, setRedirectParam] = useState('');
  const [testResults, setTestResults] = useState([]);

  useEffect(() => {
    // 獲取當前 URL 信息
    if (typeof window !== 'undefined') {
      setCurrentUrl(window.location.href);
      setRedirectParam(router.query.redirect || '無');
    }
  }, [router.query]);

  // 實際測試登錄功能
  const testActualLogin = async (apiUrl, description) => {
    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: '1234'
        })
      });

      const result = await response.json();
      
      setTestResults(prev => [...prev, {
        id: Date.now(),
        description,
        success: response.ok && result.success,
        status: response.status,
        message: result.success ? '登錄成功' : result.error,
        timestamp: new Date().toLocaleTimeString()
      }]);

      return { success: response.ok && result.success, result };
    } catch (error) {
      setTestResults(prev => [...prev, {
        id: Date.now(),
        description,
        success: false,
        error: error.message,
        timestamp: new Date().toLocaleTimeString()
      }]);
      return { success: false, error };
    }
  };

  // 測試本地登錄 API
  const testLocalLogin = () => {
    testActualLogin('/api/auth/login', '本地登錄 API');
  };

  // 測試簡化登錄 API
  const testSimpleLogin = () => {
    testActualLogin('/api/auth/login-simple', '簡化登錄 API');
  };

  // 清除測試結果
  const clearResults = () => {
    setTestResults([]);
  };

  // 模擬登錄重定向流程
  const simulateLoginFlow = () => {
    const redirectUrl = router.query.redirect || '/dashboard';
    alert(`模擬登錄成功！\n即將跳轉到: ${redirectUrl}`);
    
    // 實際執行跳轉（註釋掉以避免真的跳轉）
    // router.push(redirectUrl);
    
    setTestResults(prev => [...prev, {
      id: Date.now(),
      description: '模擬登錄流程',
      success: true,
      message: `模擬跳轉到: ${redirectUrl}`,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  return (
    <>
      <Head>
        <title>登錄重定向演示 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">登錄重定向演示</h1>
              <p className="text-gray-600">實際測試登錄功能和重定向邏輯</p>
            </div>

            {/* 當前頁面信息 */}
            <div className="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h2 className="text-lg font-semibold text-blue-900 mb-3">當前頁面信息</h2>
              <div className="space-y-2 text-sm">
                <div><strong>當前 URL:</strong> <code className="bg-blue-100 px-2 py-1 rounded">{currentUrl}</code></div>
                <div><strong>重定向參數:</strong> <code className="bg-blue-100 px-2 py-1 rounded">{redirectParam}</code></div>
                <div><strong>解析結果:</strong> 登錄成功後會跳轉到 <code className="bg-blue-100 px-2 py-1 rounded">{router.query.redirect || '/dashboard'}</code></div>
              </div>
            </div>

            {/* 測試 URL 鏈接 */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">測試不同的登錄 URL</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h3 className="font-medium text-gray-900 mb-2">本地登錄 (localhost)</h3>
                  <div className="space-y-2">
                    <a
                      href="http://localhost:3001/auth/login?redirect=%2Fdashboard"
                      className="block px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 text-center"
                    >
                      🔗 localhost:3001/auth/login?redirect=/dashboard
                    </a>
                    <a
                      href="http://localhost:3001/auth/login-test?redirect=%2Fdashboard"
                      className="block px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700 text-center"
                    >
                      🧪 localhost:3001/auth/login-test?redirect=/dashboard
                    </a>
                  </div>
                </div>

                <div className="p-4 border border-gray-200 rounded-lg">
                  <h3 className="font-medium text-gray-900 mb-2">ngrok 登錄 (公網)</h3>
                  <div className="space-y-2">
                    <a
                      href="https://topyun.ngrok.app/auth/login?redirect=%2Fdashboard"
                      className="block px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 text-center"
                    >
                      🔗 topyun.ngrok.app/auth/login?redirect=/dashboard
                    </a>
                    <a
                      href="https://topyun.ngrok.app/auth/login-test?redirect=%2Fdashboard"
                      className="block px-3 py-2 bg-orange-600 text-white rounded text-sm hover:bg-orange-700 text-center"
                    >
                      🧪 topyun.ngrok.app/auth/login-test?redirect=/dashboard
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* 功能測試按鈕 */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">API 功能測試</h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <button
                  onClick={testLocalLogin}
                  className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <div className="font-medium">🔑 測試登錄 API</div>
                  <div className="text-sm opacity-75">/api/auth/login</div>
                </button>

                <button
                  onClick={testSimpleLogin}
                  className="px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <div className="font-medium">🧪 測試簡化 API</div>
                  <div className="text-sm opacity-75">/api/auth/login-simple</div>
                </button>

                <button
                  onClick={simulateLoginFlow}
                  className="px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <div className="font-medium">🚀 模擬登錄流程</div>
                  <div className="text-sm opacity-75">測試重定向邏輯</div>
                </button>

                <button
                  onClick={clearResults}
                  className="px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <div className="font-medium">🗑️ 清除結果</div>
                  <div className="text-sm opacity-75">清空測試日誌</div>
                </button>
              </div>
            </div>

            {/* 結論說明 */}
            <div className="mb-8 p-6 bg-green-50 border border-green-200 rounded-lg">
              <h2 className="text-lg font-semibold text-green-900 mb-3">結論</h2>
              <div className="space-y-2 text-sm text-green-800">
                <p><strong>✅ 功能完全相同:</strong> localhost:3001 和 topyun.ngrok.app 的登錄功能完全一致</p>
                <p><strong>✅ 重定向邏輯相同:</strong> 兩者都使用 router.query.redirect 解析重定向參數</p>
                <p><strong>✅ API 調用相同:</strong> 兩者都調用相同的後端 API 端點</p>
                <p><strong>✅ 數據處理相同:</strong> 兩者都使用相同的 localStorage 和 cookie 存儲</p>
                <p><strong>🌐 唯一差異:</strong> 訪問域名不同 (本地 vs 公網)</p>
              </div>
            </div>

            {/* 測試結果 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">測試結果</h2>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {testResults.map((result) => (
                  <div key={result.id} className={`p-3 border rounded-lg ${
                    result.success ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800'
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span>{result.success ? '✅' : '❌'}</span>
                        <span className="font-medium">{result.description}</span>
                      </div>
                      <span className="text-sm opacity-75">{result.timestamp}</span>
                    </div>
                    <div className="mt-1 text-sm">
                      {result.message && <div>消息: {result.message}</div>}
                      {result.error && <div>錯誤: {result.error}</div>}
                      {result.status && <div>狀態碼: {result.status}</div>}
                    </div>
                  </div>
                ))}
                {testResults.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    點擊上方按鈕開始測試
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
