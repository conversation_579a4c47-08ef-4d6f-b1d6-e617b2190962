import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletTest() {
  const router = useRouter();
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const runWalletTest = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      addResult('開始', 'info', '開始測試錢包 API 訪問');

      // 檢查認證
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (!token) {
        addResult('認證', 'error', '未找到認證 token');
        addResult('建議', 'warning', '請使用錢包認證修復工具');
        return;
      }

      if (!userStr) {
        addResult('認證', 'error', '未找到用戶數據');
        addResult('建議', 'warning', '請使用錢包認證修復工具');
        return;
      }

      let user;
      try {
        user = JSON.parse(userStr);
        addResult('認證', 'success', `用戶: ${user.name} (${user.email})`);
      } catch (error) {
        addResult('認證', 'error', '用戶數據解析失敗');
        return;
      }

      // 測試錢包餘額 API
      addResult('步驟1', 'info', '測試錢包餘額 API...');
      const balanceResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      addResult('步驟1', 'info', `API 響應狀態: ${balanceResponse.status}`);

      const balanceResult = await balanceResponse.json();
      addResult('步驟1', 'info', `API 響應內容: ${JSON.stringify(balanceResult, null, 2)}`);

      if (balanceResult.success) {
        addResult('步驟1', 'success', `錢包餘額: $${balanceResult.data.balance.toFixed(2)}`);
      } else {
        addResult('步驟1', 'error', `錢包餘額 API 失敗: ${balanceResult.error}`);
        if (balanceResult.error.includes('認證失效') || balanceResult.error.includes('請先登入')) {
          addResult('建議', 'warning', '需要重新認證，請使用錢包認證修復工具');
        }
        return;
      }

      // 測試交易記錄 API
      addResult('步驟2', 'info', '測試交易記錄 API...');
      const transactionsResponse = await fetch('/api/wallet/transactions?limit=5', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      addResult('步驟2', 'info', `API 響應狀態: ${transactionsResponse.status}`);

      const transactionsResult = await transactionsResponse.json();
      
      if (transactionsResult.success) {
        addResult('步驟2', 'success', `交易記錄: ${transactionsResult.data.transactions.length} 筆`);
        addResult('步驟2', 'info', `統計數據: 總交易 ${transactionsResult.data.stats.total_transactions} 筆`);
      } else {
        addResult('步驟2', 'error', `交易記錄 API 失敗: ${transactionsResult.error}`);
        return;
      }

      // 測試支付方式 API
      addResult('步驟3', 'info', '測試支付方式 API...');
      const paymentMethodsResponse = await fetch('/api/wallet/payment-methods');
      const paymentMethodsResult = await paymentMethodsResponse.json();

      if (paymentMethodsResult.success) {
        addResult('步驟3', 'success', `支付方式: ${paymentMethodsResult.data.payment_methods.length} 種`);
      } else {
        addResult('步驟3', 'error', `支付方式 API 失敗: ${paymentMethodsResult.error}`);
      }

      addResult('完成', 'success', '所有錢包 API 測試通過！');

    } catch (error) {
      addResult('錯誤', 'error', '測試過程中出現錯誤: ' + error.message);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>錢包測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">💰 錢包 API 測試</h1>
                <p className="text-gray-600 mt-2">測試錢包相關 API 的訪問權限</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/wallet-auth-fix')}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  🔧 修復認證
                </button>
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={runWalletTest}
                  disabled={isRunning}
                  className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 font-medium"
                >
                  {isRunning ? '測試中...' : '🧪 開始測試'}
                </button>
              </div>
            </div>

            {/* 測試結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 測試結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {testResults.length > 0 ? (
                  <div className="space-y-3">
                    {testResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                            {result.data && (
                              <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono max-h-32 overflow-y-auto">
                                <pre className="whitespace-pre-wrap">
                                  {JSON.stringify(result.data, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「開始測試」來測試錢包 API 訪問
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 測試內容</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>認證檢查:</strong> 驗證 localStorage 中的 token 和用戶數據</div>
                <div><strong>步驟1:</strong> 測試錢包餘額 API (/api/wallet/balance)</div>
                <div><strong>步驟2:</strong> 測試交易記錄 API (/api/wallet/transactions)</div>
                <div><strong>步驟3:</strong> 測試支付方式 API (/api/wallet/payment-methods)</div>
                <div><strong>錯誤處理:</strong> 檢測認證失效並提供修復建議</div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="mt-8 flex flex-wrap gap-4 justify-center">
              <button
                onClick={() => router.push('/wallet')}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
              >
                💰 前往錢包頁面
              </button>
              
              <button
                onClick={() => router.push('/wallet-auth-fix')}
                className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
              >
                🔧 修復錢包認證
              </button>
              
              <button
                onClick={() => router.push('/token-generator')}
                className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 font-medium"
              >
                🔑 生成新 Token
              </button>
              
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 font-medium"
              >
                🔄 重新載入
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
