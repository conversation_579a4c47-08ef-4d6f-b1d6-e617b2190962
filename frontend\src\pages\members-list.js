import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function MembersList() {
  const [members, setMembers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [dataSource, setDataSource] = useState('');

  // 模擬會員數據（包含所有可能的會員）
  const allMembers = [
    {
      id: 1,
      email: '<EMAIL>',
      name: '系統管理員',
      role: 'admin',
      status: 'active',
      phone: '0912345678',
      email_verified: true,
      last_login_at: '2025-01-26T10:00:00.000Z',
      created_at: '2025-01-01T00:00:00.000Z',
      updated_at: '2025-01-26T10:00:00.000Z'
    },
    {
      id: 2,
      email: '<EMAIL>',
      name: '一般用戶',
      role: 'user',
      status: 'active',
      phone: '0987654321',
      email_verified: true,
      last_login_at: '2025-01-26T09:30:00.000Z',
      created_at: '2025-01-01T00:00:00.000Z',
      updated_at: '2025-01-26T09:30:00.000Z'
    },
    {
      id: 3,
      email: '<EMAIL>',
      name: '測試用戶123',
      role: 'user',
      status: 'active',
      phone: '0912345678',
      email_verified: false,
      last_login_at: null,
      created_at: '2025-01-26T09:33:00.000Z',
      updated_at: '2025-01-26T09:33:00.000Z'
    },
    {
      id: 4,
      email: '<EMAIL>',
      name: '測試用戶456',
      role: 'user',
      status: 'active',
      phone: '0912345678',
      email_verified: false,
      last_login_at: null,
      created_at: '2025-01-26T09:35:00.000Z',
      updated_at: '2025-01-26T09:35:00.000Z'
    }
  ];

  useEffect(() => {
    loadMembersFromDatabase();
  }, []);

  const loadMembersFromDatabase = async () => {
    setIsLoading(true);

    try {
      console.log('嘗試從資料庫載入會員數據...');

      // 首先嘗試通過公開 API 獲取真實數據
      const response = await fetch('/api/members/list', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          console.log('成功從 API 載入會員數據:', result.data.length, '個會員');
          setMembers(result.data);
          setDataSource(result.source || 'api');
          setIsLoading(false);
          return;
        }
      }

      console.log('API 調用失敗，使用模擬數據');

    } catch (error) {
      console.error('載入會員數據失敗:', error);
    }

    // 如果 API 調用失敗，使用模擬數據
    setTimeout(() => {
      setMembers(allMembers);
      setDataSource('mock');
      setIsLoading(false);
    }, 500);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '從未';
    try {
      return new Date(dateString).toLocaleString('zh-TW');
    } catch {
      return dateString;
    }
  };

  const getStatusBadge = (status) => {
    const styles = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      suspended: 'bg-red-100 text-red-800'
    };
    
    const labels = {
      active: '活躍',
      inactive: '非活躍',
      suspended: '已停用'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${styles[status] || styles.inactive}`}>
        {labels[status] || status}
      </span>
    );
  };

  const getRoleBadge = (role) => {
    const styles = {
      admin: 'bg-purple-100 text-purple-800',
      user: 'bg-blue-100 text-blue-800'
    };
    
    const labels = {
      admin: '管理員',
      user: '一般用戶'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${styles[role] || styles.user}`}>
        {labels[role] || role}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入會員列表中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>會員列表 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">📋 會員列表</h1>
                <p className="text-gray-600 mt-2">系統中所有註冊會員的詳細信息</p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-500">
                  數據來源: {
                    dataSource === 'database' ? '🗄️ SQLite 資料庫' :
                    dataSource === 'in-memory' ? '💾 內嵌數據 (包含註冊會員)' :
                    dataSource === 'fallback' ? '🔄 備用數據' :
                    dataSource === 'mock' ? '🧪 前端模擬數據' :
                    '📡 API 數據'
                  }
                </div>
                <button
                  onClick={loadMembersFromDatabase}
                  disabled={isLoading}
                  className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                >
                  🔄 重新載入
                </button>
              </div>
            </div>

            {/* 統計信息 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                <div className="text-3xl font-bold text-blue-600 mb-2">{members.length}</div>
                <div className="text-blue-800 font-medium">總會員數</div>
                <div className="text-blue-600 text-sm mt-1">所有註冊用戶</div>
              </div>
              <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {members.filter(m => m.status === 'active').length}
                </div>
                <div className="text-green-800 font-medium">活躍會員</div>
                <div className="text-green-600 text-sm mt-1">可正常使用系統</div>
              </div>
              <div className="bg-purple-50 p-6 rounded-lg border border-purple-200">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {members.filter(m => m.role === 'admin').length}
                </div>
                <div className="text-purple-800 font-medium">管理員</div>
                <div className="text-purple-600 text-sm mt-1">系統管理權限</div>
              </div>
              <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                <div className="text-3xl font-bold text-orange-600 mb-2">
                  {members.filter(m => m.email_verified).length}
                </div>
                <div className="text-orange-800 font-medium">已驗證 Email</div>
                <div className="text-orange-600 text-sm mt-1">完成 Email 驗證</div>
              </div>
            </div>

            {/* 會員詳細列表 */}
            <div className="bg-gray-50 p-6 rounded-lg mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 會員詳細信息</h2>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse bg-white rounded-lg overflow-hidden shadow">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">ID</th>
                      <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">姓名</th>
                      <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">Email</th>
                      <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">角色</th>
                      <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">狀態</th>
                      <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">電話</th>
                      <th className="border border-gray-300 px-4 py-3 text-center font-semibold text-gray-900">Email驗證</th>
                      <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">最後登入</th>
                      <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">註冊時間</th>
                    </tr>
                  </thead>
                  <tbody>
                    {members.map((member, index) => (
                      <tr key={member.id} className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="border border-gray-300 px-4 py-3 font-bold text-blue-600">{member.id}</td>
                        <td className="border border-gray-300 px-4 py-3 font-medium">{member.name}</td>
                        <td className="border border-gray-300 px-4 py-3">
                          <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">{member.email}</div>
                        </td>
                        <td className="border border-gray-300 px-4 py-3">
                          {getRoleBadge(member.role)}
                        </td>
                        <td className="border border-gray-300 px-4 py-3">
                          {getStatusBadge(member.status)}
                        </td>
                        <td className="border border-gray-300 px-4 py-3">
                          {member.phone ? (
                            <span className="font-mono text-sm">{member.phone}</span>
                          ) : (
                            <span className="text-gray-400">未提供</span>
                          )}
                        </td>
                        <td className="border border-gray-300 px-4 py-3 text-center">
                          {member.email_verified ? (
                            <span className="text-green-600 text-lg">✅</span>
                          ) : (
                            <span className="text-red-600 text-lg">❌</span>
                          )}
                        </td>
                        <td className="border border-gray-300 px-4 py-3 text-sm">
                          <div className={member.last_login_at ? 'text-gray-700' : 'text-gray-400'}>
                            {formatDate(member.last_login_at)}
                          </div>
                        </td>
                        <td className="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                          {formatDate(member.created_at)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* 會員摘要信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                <h3 className="font-semibold text-blue-900 mb-3">👥 會員組成</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>管理員:</span>
                    <span className="font-medium">{members.filter(m => m.role === 'admin').length} 人</span>
                  </div>
                  <div className="flex justify-between">
                    <span>一般用戶:</span>
                    <span className="font-medium">{members.filter(m => m.role === 'user').length} 人</span>
                  </div>
                  <div className="flex justify-between">
                    <span>活躍用戶:</span>
                    <span className="font-medium">{members.filter(m => m.status === 'active').length} 人</span>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                <h3 className="font-semibold text-green-900 mb-3">📧 Email 驗證狀態</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>已驗證:</span>
                    <span className="font-medium text-green-600">{members.filter(m => m.email_verified).length} 人</span>
                  </div>
                  <div className="flex justify-between">
                    <span>未驗證:</span>
                    <span className="font-medium text-red-600">{members.filter(m => !m.email_verified).length} 人</span>
                  </div>
                  <div className="flex justify-between">
                    <span>驗證率:</span>
                    <span className="font-medium">
                      {Math.round((members.filter(m => m.email_verified).length / members.length) * 100)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* 說明信息 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">📝 說明</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>數據來源:</strong>
                  {dataSource === 'database' ? '從 SQLite 資料庫讀取真實會員數據' :
                   dataSource === 'in-memory' ? '使用內嵌數據，包含通過註冊功能添加的會員' :
                   '使用模擬數據進行展示'}
                </p>
                <p><strong>會員類型:</strong> 包括系統管理員和一般用戶，每種角色有不同的權限</p>
                <p><strong>狀態說明:</strong> 活躍用戶可以正常使用系統，非活躍或已停用用戶無法登入</p>
                <p><strong>安全性:</strong> 密碼等敏感信息已加密存儲，不會在此頁面顯示</p>
                <p><strong>實時性:</strong> 點擊重新載入按鈕可獲取最新的會員數據</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
