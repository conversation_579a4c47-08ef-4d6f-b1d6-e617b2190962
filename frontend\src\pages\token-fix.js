import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function TokenFix() {
  const router = useRouter();
  const [status, setStatus] = useState('檢查中...');
  const [tokenInfo, setTokenInfo] = useState(null);
  const [isFixed, setIsFixed] = useState(false);

  useEffect(() => {
    checkAndFixToken();
  }, []);

  const checkAndFixToken = async () => {
    try {
      setStatus('🔍 檢查認證狀態...');
      
      // 檢查當前 token
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      let user = null;
      if (userStr) {
        try {
          user = JSON.parse(userStr);
        } catch (error) {
          console.error('用戶數據解析失敗:', error);
        }
      }

      setTokenInfo({
        hasToken: !!token,
        tokenLength: token ? token.length : 0,
        hasUser: !!user,
        user: user,
        isValid: !!(token && user)
      });

      if (token && user) {
        setStatus('✅ 認證狀態正常');
        setIsFixed(true);
        
        // 測試 token 是否真的有效
        await testTokenValidity(token);
      } else {
        setStatus('❌ 認證數據缺失，開始修復...');
        await autoLogin();
      }

    } catch (error) {
      setStatus('❌ 檢查失敗: ' + error.message);
    }
  };

  const testTokenValidity = async (token) => {
    try {
      setStatus('🧪 測試 Token 有效性...');
      
      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setStatus('✅ Token 有效，認證正常');
        setIsFixed(true);
      } else {
        setStatus('❌ Token 無效，重新登錄...');
        await autoLogin();
      }
    } catch (error) {
      setStatus('❌ Token 測試失敗，重新登錄...');
      await autoLogin();
    }
  };

  const autoLogin = async () => {
    try {
      setStatus('🔑 自動登錄中...');
      
      // 清除舊數據
      localStorage.clear();
      
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setStatus('💾 保存認證數據...');
        
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        
        // 更新 token 信息
        setTokenInfo({
          hasToken: true,
          tokenLength: result.token.length,
          hasUser: true,
          user: result.user,
          isValid: true
        });
        
        setStatus('✅ 登錄成功！認證已修復');
        setIsFixed(true);
        
        // 測試新 token
        await testTokenValidity(result.token);
      } else {
        setStatus('❌ 自動登錄失敗: ' + result.error);
      }
    } catch (error) {
      setStatus('❌ 自動登錄失敗: ' + error.message);
    }
  };

  const manualLogin = () => {
    router.push('/super-simple-login');
  };

  const testSubscriptions = () => {
    router.push('/subscriptions');
  };

  const testFrontendLogic = () => {
    router.push('/frontend-logic-test');
  };

  const goToDashboard = () => {
    router.push('/dashboard-simple');
  };

  return (
    <>
      <Head>
        <title>Token 修復 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-lg w-full mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {isFixed ? (
                  <span className="text-2xl">✅</span>
                ) : (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                )}
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">🔧 Token 修復</h1>
              <p className="text-gray-600">自動檢查和修復認證問題</p>
            </div>

            {/* 狀態顯示 */}
            <div className="mb-8">
              <div className={`p-4 rounded-lg ${
                isFixed ? 'bg-green-50 border border-green-200' : 'bg-blue-50 border border-blue-200'
              }`}>
                <p className={`font-medium ${
                  isFixed ? 'text-green-800' : 'text-blue-800'
                }`}>
                  {status}
                </p>
              </div>
            </div>

            {/* Token 信息 */}
            {tokenInfo && (
              <div className="mb-8">
                <h3 className="font-medium text-gray-900 mb-3">📋 認證信息</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Token:</span>
                    <span className={tokenInfo.hasToken ? 'text-green-600' : 'text-red-600'}>
                      {tokenInfo.hasToken ? `✅ 存在 (${tokenInfo.tokenLength} 字符)` : '❌ 缺失'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>User:</span>
                    <span className={tokenInfo.hasUser ? 'text-green-600' : 'text-red-600'}>
                      {tokenInfo.hasUser ? '✅ 存在' : '❌ 缺失'}
                    </span>
                  </div>
                  {tokenInfo.user && (
                    <div className="mt-3 p-3 bg-white rounded border">
                      <div className="text-xs space-y-1">
                        <div><strong>姓名:</strong> {tokenInfo.user.name}</div>
                        <div><strong>Email:</strong> {tokenInfo.user.email}</div>
                        <div><strong>角色:</strong> {tokenInfo.user.role}</div>
                        <div><strong>ID:</strong> {tokenInfo.user.userId}</div>
                      </div>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span>狀態:</span>
                    <span className={tokenInfo.isValid ? 'text-green-600' : 'text-red-600'}>
                      {tokenInfo.isValid ? '✅ 有效' : '❌ 無效'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* 操作按鈕 */}
            <div className="space-y-3">
              {isFixed ? (
                <div className="space-y-3">
                  <button
                    onClick={testSubscriptions}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    📦 測試訂閱頁面
                  </button>
                  
                  <button
                    onClick={testFrontendLogic}
                    className="w-full px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium"
                  >
                    🧪 測試前端邏輯
                  </button>
                  
                  <button
                    onClick={goToDashboard}
                    className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                  >
                    🏠 前往儀表板
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  <button
                    onClick={checkAndFixToken}
                    className="w-full px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
                  >
                    🔄 重新檢查修復
                  </button>
                  
                  <button
                    onClick={manualLogin}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    🔑 手動登錄
                  </button>
                </div>
              )}
              
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🔄 刷新頁面
              </button>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">🎯 修復流程</h3>
              <div className="text-sm text-yellow-800 space-y-1">
                <div>1. 檢查 localStorage 中的 token 和 user 數據</div>
                <div>2. 測試 token 是否有效（調用 API）</div>
                <div>3. 如果無效，自動重新登錄</div>
                <div>4. 保存新的認證數據</div>
                <div>5. 驗證修復結果</div>
              </div>
            </div>

            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2">📋 測試帳號</h3>
              <div className="text-sm text-green-800">
                <div>Email: <EMAIL></div>
                <div>Password: password123</div>
                <div>Role: admin</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
