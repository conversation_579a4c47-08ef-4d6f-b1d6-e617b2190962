import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function SubscriptionDetail() {
  const router = useRouter();
  const { id } = router.query;
  const [subscription, setSubscription] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // 模擬訂閱詳情數據
  const mockSubscriptionDetail = {
    id: 1,
    name: 'AI 文字生成服務',
    description: '強大的 AI 文字生成工具，支援多種語言和格式',
    price: 299,
    billingCycle: 'monthly',
    status: 'active',
    nextBilling: '2025-07-26',
    startDate: '2025-06-26',
    features: ['無限文字生成', '多語言支援', '模板庫', '24/7 客服'],
    usage: { used: 15000, limit: 50000, unit: '字符' },
    billingHistory: [
      { date: '2025-06-26', amount: 299, status: 'paid', invoice: 'INV-2025-001' },
      { date: '2025-05-26', amount: 299, status: 'paid', invoice: 'INV-2025-002' },
      { date: '2025-04-26', amount: 299, status: 'paid', invoice: 'INV-2025-003' }
    ],
    usageHistory: [
      { date: '2025-06-25', usage: 2500, type: '文章生成' },
      { date: '2025-06-24', usage: 1800, type: '摘要生成' },
      { date: '2025-06-23', usage: 3200, type: '翻譯服務' },
      { date: '2025-06-22', usage: 1500, type: '文章生成' },
      { date: '2025-06-21', usage: 2800, type: '創意寫作' }
    ]
  };

  useEffect(() => {
    // 檢查認證
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=' + encodeURIComponent('/subscription-detail'));
      return;
    }

    // 模擬載入訂閱詳情
    setTimeout(() => {
      setSubscription(mockSubscriptionDetail);
      setIsLoading(false);
    }, 1000);
  }, [router, id]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'expired': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return '使用中';
      case 'expired': return '已過期';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入訂閱詳情中...</p>
        </div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">找不到訂閱</h2>
          <p className="text-gray-600 mb-4">請檢查訂閱 ID 是否正確</p>
          <button
            onClick={() => router.push('/subscriptions')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回訂閱列表
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>{subscription.name} - 訂閱詳情</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/subscriptions')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回訂閱列表
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">訂閱詳情</h1>
                </div>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 訂閱概覽 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">{subscription.name}</h2>
                  <p className="text-gray-600 mb-4">{subscription.description}</p>
                  <div className="flex items-center space-x-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                      {getStatusText(subscription.status)}
                    </span>
                    <span className="text-2xl font-bold text-gray-900">
                      NT$ {subscription.price}/月
                    </span>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="mb-2">
                    <p className="text-sm text-gray-500">下次計費</p>
                    <p className="font-medium text-gray-900">{subscription.nextBilling}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">開始時間</p>
                    <p className="font-medium text-gray-900">{subscription.startDate}</p>
                  </div>
                </div>
              </div>

              {/* 使用量進度條 */}
              {subscription.usage && (
                <div className="mb-6">
                  <div className="flex justify-between text-sm text-gray-600 mb-2">
                    <span>本月使用量</span>
                    <span>{subscription.usage.used.toLocaleString()} / {subscription.usage.limit.toLocaleString()} {subscription.usage.unit}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min((subscription.usage.used / subscription.usage.limit) * 100, 100)}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>已使用 {((subscription.usage.used / subscription.usage.limit) * 100).toFixed(1)}%</span>
                    <span>剩餘 {(subscription.usage.limit - subscription.usage.used).toLocaleString()} {subscription.usage.unit}</span>
                  </div>
                </div>
              )}

              {/* 操作按鈕 */}
              <div className="flex space-x-3">
                <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  升級方案
                </button>
                <button className="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
                  暫停訂閱
                </button>
                <button className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                  取消訂閱
                </button>
              </div>
            </motion.div>

            {/* 標籤切換 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'overview'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  功能概覽
                </button>
                <button
                  onClick={() => setActiveTab('usage')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'usage'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  使用記錄
                </button>
                <button
                  onClick={() => setActiveTab('billing')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'billing'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  帳單記錄
                </button>
              </div>
            </div>

            {/* 標籤內容 */}
            {activeTab === 'overview' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-white rounded-xl shadow-lg p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-4">包含功能</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {subscription.features.map((feature, index) => (
                    <div key={index} className="flex items-center p-4 bg-gray-50 rounded-lg">
                      <div className="h-2 w-2 bg-green-500 rounded-full mr-3"></div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'usage' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-white rounded-xl shadow-lg p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-4">使用記錄</h3>
                <div className="space-y-3">
                  {subscription.usageHistory.map((record, index) => (
                    <div key={index} className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{record.type}</p>
                        <p className="text-sm text-gray-500">{record.date}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">{record.usage.toLocaleString()} 字符</p>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'billing' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-white rounded-xl shadow-lg p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-4">帳單記錄</h3>
                <div className="space-y-3">
                  {subscription.billingHistory.map((bill, index) => (
                    <div key={index} className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">發票號碼: {bill.invoice}</p>
                        <p className="text-sm text-gray-500">{bill.date}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">NT$ {bill.amount}</p>
                        <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full">
                          已付款
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}
          </div>
        </main>
      </div>
    </>
  );
}
