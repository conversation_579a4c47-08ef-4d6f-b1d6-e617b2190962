import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function NgrokStatus() {
  const [status, setStatus] = useState({
    localServer: 'checking',
    ngrokTunnel: 'checking',
    connectivity: 'checking',
    ports: {
      3000: 'checking',
      3001: 'checking'
    },
    ngrokInfo: null,
    errors: []
  });

  const addLog = (message, type = 'info') => {
    console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
    setStatus(prev => ({
      ...prev,
      errors: [...prev.errors, {
        id: Date.now(),
        timestamp: new Date().toLocaleTimeString(),
        message,
        type
      }].slice(-10)
    }));
  };

  // 檢查本地端口
  const checkLocalPort = async (port) => {
    try {
      const response = await fetch(`http://localhost:${port}`, {
        method: 'HEAD',
        timeout: 5000
      });
      return response.ok ? 'online' : 'error';
    } catch (error) {
      return 'offline';
    }
  };

  // 檢查 ngrok 狀態
  const checkNgrokStatus = async () => {
    try {
      // 嘗試訪問 ngrok 本地 API (通常在 4040 端口)
      const response = await fetch('http://localhost:4040/api/tunnels');
      if (response.ok) {
        const data = await response.json();
        return {
          status: 'online',
          tunnels: data.tunnels || []
        };
      }
    } catch (error) {
      // ngrok API 不可用
    }
    
    // 嘗試直接訪問 ngrok 域名
    try {
      const response = await fetch('https://topyun.ngrok.app', {
        method: 'HEAD',
        timeout: 10000
      });
      return {
        status: response.ok ? 'online' : 'error',
        tunnels: []
      };
    } catch (error) {
      return {
        status: 'offline',
        error: error.message,
        tunnels: []
      };
    }
  };

  // 執行完整檢查
  const runFullCheck = async () => {
    addLog('🚀 開始 ngrok 狀態檢查...', 'info');

    // 檢查本地端口
    addLog('檢查本地端口...', 'info');
    const port3000Status = await checkLocalPort(3000);
    const port3001Status = await checkLocalPort(3001);
    
    addLog(`端口 3000: ${port3000Status === 'online' ? '✅ 在線' : '❌ 離線'}`, 
           port3000Status === 'online' ? 'success' : 'error');
    addLog(`端口 3001: ${port3001Status === 'online' ? '✅ 在線' : '❌ 離線'}`, 
           port3001Status === 'online' ? 'success' : 'error');

    // 檢查 ngrok 狀態
    addLog('檢查 ngrok 隧道...', 'info');
    const ngrokResult = await checkNgrokStatus();
    
    if (ngrokResult.status === 'online') {
      addLog('✅ ngrok 隧道正常', 'success');
      if (ngrokResult.tunnels.length > 0) {
        ngrokResult.tunnels.forEach(tunnel => {
          addLog(`隧道: ${tunnel.public_url} -> ${tunnel.config.addr}`, 'info');
        });
      }
    } else {
      addLog(`❌ ngrok 隧道異常: ${ngrokResult.error || '連接失敗'}`, 'error');
    }

    setStatus(prev => ({
      ...prev,
      localServer: port3000Status === 'online' || port3001Status === 'online' ? 'online' : 'offline',
      ngrokTunnel: ngrokResult.status,
      connectivity: ngrokResult.status === 'online' && (port3000Status === 'online' || port3001Status === 'online') ? 'online' : 'error',
      ports: {
        3000: port3000Status,
        3001: port3001Status
      },
      ngrokInfo: ngrokResult
    }));

    addLog('🎉 檢查完成', 'success');
  };

  useEffect(() => {
    runFullCheck();
    
    // 每30秒自動檢查一次
    const interval = setInterval(runFullCheck, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'text-green-600 bg-green-50 border-green-200';
      case 'offline': return 'text-red-600 bg-red-50 border-red-200';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'checking': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'online': return '✅';
      case 'offline': return '❌';
      case 'error': return '⚠️';
      case 'checking': return '🔄';
      default: return '❓';
    }
  };

  return (
    <>
      <Head>
        <title>ngrok 狀態檢查 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">ngrok 隧道狀態</h1>
                <p className="text-gray-600 mt-2">檢查本地服務器和 ngrok 隧道連接狀態</p>
              </div>
              <button
                onClick={runFullCheck}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
              >
                🔄 重新檢查
              </button>
            </div>

            {/* 狀態概覽 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className={`p-6 rounded-lg border-2 ${getStatusColor(status.localServer)}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">本地服務器</h3>
                    <p className="text-sm opacity-75">Next.js 開發服務器</p>
                  </div>
                  <span className="text-2xl">{getStatusIcon(status.localServer)}</span>
                </div>
              </div>

              <div className={`p-6 rounded-lg border-2 ${getStatusColor(status.ngrokTunnel)}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">ngrok 隧道</h3>
                    <p className="text-sm opacity-75">公網訪問隧道</p>
                  </div>
                  <span className="text-2xl">{getStatusIcon(status.ngrokTunnel)}</span>
                </div>
              </div>

              <div className={`p-6 rounded-lg border-2 ${getStatusColor(status.connectivity)}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">整體連接</h3>
                    <p className="text-sm opacity-75">端到端連接狀態</p>
                  </div>
                  <span className="text-2xl">{getStatusIcon(status.connectivity)}</span>
                </div>
              </div>
            </div>

            {/* 端口詳情 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">本地端口狀態</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(status.ports).map(([port, portStatus]) => (
                  <div key={port} className={`p-4 rounded-lg border ${getStatusColor(portStatus)}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">端口 {port}</h3>
                        <p className="text-sm opacity-75">http://localhost:{port}</p>
                      </div>
                      <div className="text-right">
                        <span className="text-xl">{getStatusIcon(portStatus)}</span>
                        <div className="text-sm mt-1">{portStatus}</div>
                      </div>
                    </div>
                    <div className="mt-2">
                      <a
                        href={`http://localhost:${port}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm underline hover:no-underline"
                      >
                        測試本地連接
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* ngrok 隧道信息 */}
            {status.ngrokInfo && status.ngrokInfo.tunnels && status.ngrokInfo.tunnels.length > 0 && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">ngrok 隧道詳情</h2>
                <div className="space-y-4">
                  {status.ngrokInfo.tunnels.map((tunnel, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{tunnel.name}</h3>
                        <span className="text-sm text-gray-500">{tunnel.proto}</span>
                      </div>
                      <div className="text-sm space-y-1">
                        <div><strong>公網地址:</strong> <a href={tunnel.public_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">{tunnel.public_url}</a></div>
                        <div><strong>本地地址:</strong> {tunnel.config.addr}</div>
                        <div><strong>區域:</strong> {tunnel.config.region || 'default'}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 快速操作 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">快速操作</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a
                  href="http://localhost:3000"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors text-center"
                >
                  <div className="text-2xl mb-2">🌐</div>
                  <div className="font-medium">本地 :3000</div>
                </a>

                <a
                  href="http://localhost:3001"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors text-center"
                >
                  <div className="text-2xl mb-2">🌐</div>
                  <div className="font-medium">本地 :3001</div>
                </a>

                <a
                  href="https://topyun.ngrok.app"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors text-center"
                >
                  <div className="text-2xl mb-2">🚀</div>
                  <div className="font-medium">ngrok 隧道</div>
                </a>

                <a
                  href="http://localhost:4040"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors text-center"
                >
                  <div className="text-2xl mb-2">⚙️</div>
                  <div className="font-medium">ngrok 控制台</div>
                </a>
              </div>
            </div>

            {/* 故障排除指南 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">故障排除指南</h2>
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="font-medium text-yellow-800 mb-2">ERR_NGROK_8012 錯誤</h3>
                  <p className="text-yellow-700 text-sm mb-2">
                    如果遇到 "target machine actively refused it" 錯誤：
                  </p>
                  <ul className="text-yellow-700 text-sm space-y-1 ml-4">
                    <li>• 確認本地服務器正在運行 (檢查上方端口狀態)</li>
                    <li>• 確認 ngrok 指向正確的端口</li>
                    <li>• 重新啟動 Next.js 開發服務器</li>
                    <li>• 檢查防火牆設置</li>
                  </ul>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-800 mb-2">重新啟動步驟</h3>
                  <ol className="text-blue-700 text-sm space-y-1 ml-4">
                    <li>1. 停止當前的 ngrok 進程</li>
                    <li>2. 確認 Next.js 服務器在運行</li>
                    <li>3. 重新啟動 ngrok: <code className="bg-blue-100 px-1 rounded">ngrok http 3001 --domain=topyun.ngrok.app</code></li>
                  </ol>
                </div>
              </div>
            </div>

            {/* 檢查日誌 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">檢查日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
                {status.errors.map((log) => (
                  <div key={log.id} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'success' ? 'text-green-400' : 
                    log.type === 'warning' ? 'text-yellow-400' :
                    'text-blue-400'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))}
                {status.errors.length === 0 && (
                  <div className="text-gray-500">檢查中...</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
