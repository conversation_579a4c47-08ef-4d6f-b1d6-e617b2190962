const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { Pool } = require('pg');

// 資料庫連接
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'member_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

class AuthController {
  // 用戶註冊
  static async register(req, res) {
    try {
      const { username, email, password, firstName, lastName, phone } = req.body;

      // 驗證必填欄位
      if (!username || !email || !password) {
        return res.status(400).json({
          success: false,
          message: '用戶名、郵箱和密碼為必填項'
        });
      }

      // 驗證密碼強度
      if (password.length < 8) {
        return res.status(400).json({
          success: false,
          message: '密碼長度至少需要 8 個字符'
        });
      }

      const client = await pool.connect();

      try {
        // 檢查用戶是否已存在
        const existingUser = await client.query(
          'SELECT id FROM users WHERE email = $1 OR username = $2',
          [email, username]
        );

        if (existingUser.rows.length > 0) {
          return res.status(409).json({
            success: false,
            message: '用戶名或郵箱已存在'
          });
        }

        // 加密密碼
        const hashedPassword = await bcrypt.hash(password, 12);

        // 創建用戶
        const result = await client.query(
          `INSERT INTO users (username, email, password, first_name, last_name, phone, status)
           VALUES ($1, $2, $3, $4, $5, $6, 'active')
           RETURNING id, username, email, first_name, last_name, phone, status, created_at`,
          [username, email, hashedPassword, firstName, lastName, phone]
        );

        const user = result.rows[0];

        // 為用戶創建預設錢包
        await client.query(
          `INSERT INTO wallets (user_id, currency, balance)
           VALUES ($1, 'TWD', 0.00), ($1, 'USD', 0.00)`,
          [user.id]
        );

        // 生成 JWT Token
        const token = jwt.sign(
          { userId: user.id, username: user.username },
          process.env.JWT_SECRET,
          { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
        );

        // 記錄用戶活動
        await client.query(
          `INSERT INTO user_activity_logs (user_id, activity_type, description, ip_address, user_agent)
           VALUES ($1, 'registration', '用戶註冊', $2, $3)`,
          [user.id, req.ip, req.get('User-Agent')]
        );

        client.release();

        res.status(201).json({
          success: true,
          message: '註冊成功',
          data: {
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              firstName: user.first_name,
              lastName: user.last_name,
              phone: user.phone,
              status: user.status,
              createdAt: user.created_at
            },
            token
          }
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('註冊失敗:', error);
      res.status(500).json({
        success: false,
        message: '註冊失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 用戶登入
  static async login(req, res) {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({
          success: false,
          message: '郵箱和密碼為必填項'
        });
      }

      const client = await pool.connect();

      try {
        // 查找用戶
        const result = await client.query(
          `SELECT id, username, email, password, first_name, last_name, phone, status, 
                  email_verified_at, locked_at, lock_reason
           FROM users 
           WHERE email = $1 AND deleted_at IS NULL`,
          [email]
        );

        if (result.rows.length === 0) {
          return res.status(401).json({
            success: false,
            message: '郵箱或密碼錯誤'
          });
        }

        const user = result.rows[0];

        // 檢查帳號狀態
        if (user.status !== 'active') {
          return res.status(403).json({
            success: false,
            message: '帳號已被停用'
          });
        }

        if (user.locked_at) {
          return res.status(423).json({
            success: false,
            message: `帳號已被鎖定: ${user.lock_reason || '未知原因'}`
          });
        }

        // 驗證密碼
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
          // 記錄登入失敗
          await client.query(
            `INSERT INTO security_audit_logs (user_id, event_type, description, severity, ip_address, user_agent)
             VALUES ($1, 'login_failed', '密碼錯誤', 'medium', $2, $3)`,
            [user.id, req.ip, req.get('User-Agent')]
          );

          return res.status(401).json({
            success: false,
            message: '郵箱或密碼錯誤'
          });
        }

        // 生成 JWT Token
        const token = jwt.sign(
          { userId: user.id, username: user.username },
          process.env.JWT_SECRET,
          { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
        );

        // 記錄登入成功
        await client.query(
          `INSERT INTO user_activity_logs (user_id, activity_type, description, ip_address, user_agent)
           VALUES ($1, 'login', '用戶登入', $2, $3)`,
          [user.id, req.ip, req.get('User-Agent')]
        );

        client.release();

        res.json({
          success: true,
          message: '登入成功',
          data: {
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              firstName: user.first_name,
              lastName: user.last_name,
              phone: user.phone,
              status: user.status,
              emailVerified: !!user.email_verified_at
            },
            token
          }
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      console.error('登入失敗:', error);
      res.status(500).json({
        success: false,
        message: '登入失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 驗證 Token
  static async verify(req, res) {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        return res.status(401).json({
          success: false,
          message: '未提供認證 Token'
        });
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const client = await pool.connect();

      try {
        const result = await client.query(
          `SELECT id, username, email, first_name, last_name, phone, status, email_verified_at
           FROM users 
           WHERE id = $1 AND deleted_at IS NULL`,
          [decoded.userId]
        );

        if (result.rows.length === 0) {
          return res.status(401).json({
            success: false,
            message: '用戶不存在'
          });
        }

        const user = result.rows[0];

        if (user.status !== 'active') {
          return res.status(403).json({
            success: false,
            message: '帳號已被停用'
          });
        }

        client.release();

        res.json({
          success: true,
          data: {
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              firstName: user.first_name,
              lastName: user.last_name,
              phone: user.phone,
              status: user.status,
              emailVerified: !!user.email_verified_at
            }
          }
        });

      } catch (error) {
        client.release();
        throw error;
      }

    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: 'Token 無效'
        });
      }

      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Token 已過期'
        });
      }

      console.error('Token 驗證失敗:', error);
      res.status(500).json({
        success: false,
        message: 'Token 驗證失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // 登出
  static async logout(req, res) {
    try {
      // 在實際應用中，可以將 Token 加入黑名單
      // 這裡簡單返回成功訊息
      res.json({
        success: true,
        message: '登出成功'
      });
    } catch (error) {
      console.error('登出失敗:', error);
      res.status(500).json({
        success: false,
        message: '登出失敗',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = AuthController;
