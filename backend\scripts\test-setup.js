#!/usr/bin/env node

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 測試資料庫設置腳本
async function setupTestDatabase() {
  console.log('🔧 設置測試資料庫...');

  const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: 'postgres', // 連接到預設資料庫
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });

  try {
    // 檢查測試資料庫是否存在
    const dbCheckResult = await pool.query(
      "SELECT 1 FROM pg_database WHERE datname = 'member_system_test'"
    );

    if (dbCheckResult.rows.length === 0) {
      // 創建測試資料庫
      await pool.query('CREATE DATABASE member_system_test');
      console.log('✅ 測試資料庫創建成功');
    } else {
      console.log('ℹ️ 測試資料庫已存在');
    }

    await pool.end();

    // 連接到測試資料庫並創建表格
    const testPool = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: 'member_system_test',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
    });

    // 讀取並執行初始化 SQL
    const initSqlPath = path.join(__dirname, '../database/init.sql');
    if (fs.existsSync(initSqlPath)) {
      const initSql = fs.readFileSync(initSqlPath, 'utf8');
      await testPool.query(initSql);
      console.log('✅ 測試資料庫表格創建成功');
    } else {
      console.log('⚠️ 找不到初始化 SQL 文件');
    }

    await testPool.end();
    console.log('🎉 測試資料庫設置完成');

  } catch (error) {
    console.error('❌ 測試資料庫設置失敗:', error.message);
    process.exit(1);
  }
}

// 清理測試資料庫
async function cleanupTestDatabase() {
  console.log('🧹 清理測試資料庫...');

  const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: 'member_system_test',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });

  try {
    // 清理所有表格數據
    const tables = [
      'user_activity_logs',
      'security_audit_logs',
      'system_audit_logs',
      'subscription_usage',
      'subscriptions',
      'transactions',
      'payments',
      'wallets',
      'user_consents',
      'user_social_accounts',
      'users',
      'service_plans',
      'services'
    ];

    for (const table of tables) {
      try {
        await pool.query(`TRUNCATE TABLE ${table} RESTART IDENTITY CASCADE`);
      } catch (error) {
        // 忽略表格不存在的錯誤
        if (!error.message.includes('does not exist')) {
          console.warn(`⚠️ 清理表格 ${table} 失敗:`, error.message);
        }
      }
    }

    await pool.end();
    console.log('✅ 測試資料庫清理完成');

  } catch (error) {
    console.error('❌ 測試資料庫清理失敗:', error.message);
    process.exit(1);
  }
}

// 運行測試
async function runTests() {
  console.log('🧪 開始運行測試...');

  const { spawn } = require('child_process');

  return new Promise((resolve, reject) => {
    const testProcess = spawn('npm', ['run', 'test:ci'], {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'test'
      }
    });

    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 所有測試通過');
        resolve();
      } else {
        console.error('❌ 測試失敗');
        reject(new Error(`測試進程退出碼: ${code}`));
      }
    });

    testProcess.on('error', (error) => {
      console.error('❌ 測試進程錯誤:', error);
      reject(error);
    });
  });
}

// 主函數
async function main() {
  const command = process.argv[2];

  switch (command) {
    case 'setup':
      await setupTestDatabase();
      break;
    case 'cleanup':
      await cleanupTestDatabase();
      break;
    case 'run':
      await setupTestDatabase();
      await runTests();
      await cleanupTestDatabase();
      break;
    default:
      console.log('使用方法:');
      console.log('  node scripts/test-setup.js setup   - 設置測試資料庫');
      console.log('  node scripts/test-setup.js cleanup - 清理測試資料庫');
      console.log('  node scripts/test-setup.js run     - 運行完整測試流程');
      process.exit(1);
  }
}

// 如果直接運行此腳本
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 腳本執行失敗:', error);
    process.exit(1);
  });
}

module.exports = {
  setupTestDatabase,
  cleanupTestDatabase,
  runTests
};
