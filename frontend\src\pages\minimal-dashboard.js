import { useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function MinimalDashboard() {
  const router = useRouter();

  useEffect(() => {
    // 直接重定向到新的簡單儀表板
    router.replace('/dashboard-simple');
  }, [router]);

  return (
    <>
      <Head>
        <title>重定向中... - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在重定向到新的儀表板...</p>
          <p className="text-sm text-gray-500 mt-2">
            如果沒有自動跳轉，請點擊 
            <a href="/dashboard-simple" className="text-blue-600 hover:text-blue-500 underline ml-1">
              這裡
            </a>
          </p>
        </div>
      </div>
    </>
  );
}
