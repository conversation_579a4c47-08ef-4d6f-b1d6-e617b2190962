// 簡化的登錄測試 API
export default function handler(req, res) {
  console.log('Test Login API called');
  console.log('Method:', req.method);
  console.log('Body:', req.body);

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { email, password } = req.body;

    console.log('Email:', email);
    console.log('Password:', password);

    // 簡單的硬編碼驗證
    const validCredentials = [
      { email: '<EMAIL>', password: 'password123', name: '系統管理員', role: 'admin' },
      { email: '<EMAIL>', password: 'password', name: '系統管理員', role: 'admin' },
      { email: '<EMAIL>', password: 'password', name: '一般用戶', role: 'user' }
    ];

    const user = validCredentials.find(cred => 
      cred.email === email && cred.password === password
    );

    if (user) {
      // 簡單的 token (不使用 JWT)
      const token = `simple_token_${Date.now()}_${user.email}`;

      const userData = {
        userId: 1,
        name: user.name,
        email: user.email,
        role: user.role
      };

      console.log('Login successful:', userData);

      return res.status(200).json({
        success: true,
        message: '登錄成功',
        token: token,
        user: userData
      });
    } else {
      console.log('Invalid credentials');
      return res.status(401).json({
        success: false,
        error: '帳號或密碼錯誤'
      });
    }

  } catch (error) {
    console.error('Login API error:', error);
    return res.status(500).json({
      success: false,
      error: '服務器錯誤: ' + error.message
    });
  }
}
