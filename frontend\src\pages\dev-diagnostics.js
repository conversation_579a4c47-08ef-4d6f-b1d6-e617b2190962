import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function DevDiagnostics() {
  const [diagnostics, setDiagnostics] = useState({
    nextjs: {},
    react: {},
    browser: {},
    network: {},
    errors: []
  });

  useEffect(() => {
    // 收集診斷信息
    const collectDiagnostics = () => {
      const nextjsInfo = {
        version: process.env.NEXT_PUBLIC_VERCEL_ENV || 'development',
        buildId: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA || 'local',
        runtime: typeof window !== 'undefined' ? 'client' : 'server',
        turbopack: process.env.TURBOPACK ? 'enabled' : 'disabled'
      };

      const reactInfo = {
        version: React.version || 'unknown',
        strictMode: true, // 從 next.config.js 讀取
        devMode: process.env.NODE_ENV === 'development'
      };

      const browserInfo = typeof window !== 'undefined' ? {
        userAgent: navigator.userAgent,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        platform: navigator.platform,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        localStorage: typeof localStorage !== 'undefined',
        sessionStorage: typeof sessionStorage !== 'undefined'
      } : {};

      const networkInfo = {
        protocol: typeof window !== 'undefined' ? window.location.protocol : 'unknown',
        host: typeof window !== 'undefined' ? window.location.host : 'unknown',
        origin: typeof window !== 'undefined' ? window.location.origin : 'unknown'
      };

      setDiagnostics({
        nextjs: nextjsInfo,
        react: reactInfo,
        browser: browserInfo,
        network: networkInfo,
        errors: []
      });
    };

    collectDiagnostics();

    // 監聽錯誤
    const handleError = (event) => {
      setDiagnostics(prev => ({
        ...prev,
        errors: [...prev.errors, {
          type: 'javascript',
          message: event.error?.message || event.message,
          stack: event.error?.stack,
          timestamp: new Date().toISOString()
        }].slice(-10) // 只保留最近10個錯誤
      }));
    };

    const handleUnhandledRejection = (event) => {
      setDiagnostics(prev => ({
        ...prev,
        errors: [...prev.errors, {
          type: 'promise',
          message: event.reason?.message || String(event.reason),
          timestamp: new Date().toISOString()
        }].slice(-10)
      }));
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('error', handleError);
      window.addEventListener('unhandledrejection', handleUnhandledRejection);

      return () => {
        window.removeEventListener('error', handleError);
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      };
    }
  }, []);

  const testHMR = () => {
    console.log('HMR Test:', new Date().toISOString());
    alert('HMR 測試 - 檢查控制台是否有 HMR 連接信息');
  };

  const testAPI = async () => {
    try {
      const response = await fetch('/api/services/simple');
      const result = await response.json();
      alert(`API 測試成功: ${result.success ? '✅' : '❌'}`);
    } catch (error) {
      alert(`API 測試失敗: ${error.message}`);
    }
  };

  const clearErrors = () => {
    setDiagnostics(prev => ({ ...prev, errors: [] }));
  };

  return (
    <>
      <Head>
        <title>開發環境診斷 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">開發環境診斷</h1>
                <p className="text-gray-600 mt-2">檢查開發環境配置和常見問題</p>
              </div>
              <div className="space-x-4">
                <button
                  onClick={testHMR}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  測試 HMR
                </button>
                <button
                  onClick={testAPI}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  測試 API
                </button>
              </div>
            </div>

            {/* Next.js 信息 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Next.js 環境</h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">版本:</span> {diagnostics.nextjs.version}
                  </div>
                  <div>
                    <span className="font-medium">Build ID:</span> {diagnostics.nextjs.buildId}
                  </div>
                  <div>
                    <span className="font-medium">運行時:</span> {diagnostics.nextjs.runtime}
                  </div>
                  <div>
                    <span className="font-medium">Turbopack:</span> {diagnostics.nextjs.turbopack}
                  </div>
                </div>
              </div>
            </div>

            {/* React 信息 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">React 環境</h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <span className="font-medium">版本:</span> {diagnostics.react.version}
                  </div>
                  <div>
                    <span className="font-medium">嚴格模式:</span> {diagnostics.react.strictMode ? '✅' : '❌'}
                  </div>
                  <div>
                    <span className="font-medium">開發模式:</span> {diagnostics.react.devMode ? '✅' : '❌'}
                  </div>
                </div>
              </div>
            </div>

            {/* 瀏覽器信息 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">瀏覽器環境</h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">User Agent:</span> 
                    <div className="text-sm text-gray-600 mt-1 break-all">
                      {diagnostics.browser.userAgent}
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
                    <div>
                      <span className="font-medium">語言:</span> {diagnostics.browser.language}
                    </div>
                    <div>
                      <span className="font-medium">Cookie:</span> {diagnostics.browser.cookieEnabled ? '✅' : '❌'}
                    </div>
                    <div>
                      <span className="font-medium">在線:</span> {diagnostics.browser.onLine ? '✅' : '❌'}
                    </div>
                    <div>
                      <span className="font-medium">平台:</span> {diagnostics.browser.platform}
                    </div>
                  </div>
                  {diagnostics.browser.viewport && (
                    <div>
                      <span className="font-medium">視窗大小:</span> {diagnostics.browser.viewport.width} × {diagnostics.browser.viewport.height}
                    </div>
                  )}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="font-medium">localStorage:</span> {diagnostics.browser.localStorage ? '✅' : '❌'}
                    </div>
                    <div>
                      <span className="font-medium">sessionStorage:</span> {diagnostics.browser.sessionStorage ? '✅' : '❌'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 網絡信息 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">網絡環境</h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <span className="font-medium">協議:</span> {diagnostics.network.protocol}
                  </div>
                  <div>
                    <span className="font-medium">主機:</span> {diagnostics.network.host}
                  </div>
                  <div>
                    <span className="font-medium">源:</span> {diagnostics.network.origin}
                  </div>
                </div>
              </div>
            </div>

            {/* 錯誤日誌 */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900">錯誤日誌</h2>
                <button
                  onClick={clearErrors}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm"
                >
                  清除錯誤
                </button>
              </div>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
                {diagnostics.errors.length > 0 ? (
                  diagnostics.errors.map((error, index) => (
                    <div key={index} className="mb-2 text-red-400">
                      <div>[{new Date(error.timestamp).toLocaleTimeString()}] {error.type.toUpperCase()}: {error.message}</div>
                      {error.stack && (
                        <div className="text-xs text-gray-500 ml-4 mt-1">
                          {error.stack.split('\n').slice(0, 3).join('\n')}
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500">暫無錯誤記錄</div>
                )}
              </div>
            </div>

            {/* 常見問題解決方案 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">常見問題解決方案</h2>
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="font-medium text-yellow-800 mb-2">Turbopack 相關錯誤</h3>
                  <p className="text-yellow-700 text-sm mb-2">
                    如果遇到 "__turbopack_load_page_chunks__ is not defined" 錯誤：
                  </p>
                  <ul className="text-yellow-700 text-sm space-y-1 ml-4">
                    <li>• 使用 <code className="bg-yellow-100 px-1 rounded">npm run dev</code> 而不是 <code className="bg-yellow-100 px-1 rounded">npm run dev:turbo</code></li>
                    <li>• 清除 .next 目錄: <code className="bg-yellow-100 px-1 rounded">rm -rf .next</code></li>
                    <li>• 重新安裝依賴: <code className="bg-yellow-100 px-1 rounded">npm install</code></li>
                  </ul>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-800 mb-2">HMR 連接問題</h3>
                  <p className="text-blue-700 text-sm mb-2">
                    如果熱重載不工作：
                  </p>
                  <ul className="text-blue-700 text-sm space-y-1 ml-4">
                    <li>• 檢查防火牆設置</li>
                    <li>• 確認 WebSocket 連接正常</li>
                    <li>• 嘗試重新啟動開發服務器</li>
                  </ul>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-800 mb-2">React DevTools</h3>
                  <p className="text-green-700 text-sm">
                    建議安裝 React DevTools 瀏覽器擴展以獲得更好的開發體驗：
                    <a href="https://react.dev/link/react-devtools" target="_blank" rel="noopener noreferrer" className="underline ml-1">
                      安裝 React DevTools
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
