const express = require('express');
const homeRoutes = require('./home');
const authRoutes = require('./auth');
const userRoutes = require('./users');
const paymentRoutes = require('./payments');
const subscriptionRoutes = require('./subscriptions');
const walletRoutes = require('./wallets');

const router = express.Router();

// API 版本和健康檢查
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: process.env.API_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// API 資訊
router.get('/info', (req, res) => {
  res.json({
    name: 'Member Management System API',
    version: process.env.API_VERSION || '1.0.0',
    description: '會員管理系統 RESTful API',
    documentation: '/api/docs',
    endpoints: {
      home: '/api/v1/home',
      auth: '/api/v1/auth',
      users: '/api/v1/users',
      payments: '/api/v1/payments',
      subscriptions: '/api/v1/subscriptions',
      wallets: '/api/v1/wallets'
    }
  });
});

// 路由掛載
router.use('/home', homeRoutes);
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/payments', paymentRoutes);
router.use('/subscriptions', subscriptionRoutes);
router.use('/wallets', walletRoutes);

// 404 處理
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API 端點不存在',
    error: `路徑 ${req.originalUrl} 未找到`,
    availableEndpoints: [
      '/api/v1/home',
      '/api/v1/auth',
      '/api/v1/users',
      '/api/v1/payments',
      '/api/v1/subscriptions',
      '/api/v1/wallets'
    ]
  });
});

module.exports = router;