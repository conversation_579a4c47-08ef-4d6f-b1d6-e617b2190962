import { useState } from 'react';
import Head from 'next/head';

export default function PasswordHashTool() {
  const [password, setPassword] = useState('1234');
  const [hash, setHash] = useState('');
  const [verifyPassword, setVerifyPassword] = useState('1234');
  const [verifyHash, setVerifyHash] = useState('');
  const [verifyResult, setVerifyResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const generateHash = async () => {
    if (!password) {
      alert('請輸入密碼');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/tools/hash-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password })
      });

      const result = await response.json();
      
      if (result.success) {
        setHash(result.hash);
      } else {
        alert('生成哈希失敗: ' + result.error);
      }
    } catch (error) {
      alert('請求失敗: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const verifyPasswordHash = async () => {
    if (!verifyPassword || !verifyHash) {
      alert('請輸入密碼和哈希值');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/tools/verify-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          password: verifyPassword,
          hash: verifyHash
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setVerifyResult(result.isValid ? '✅ 密碼匹配' : '❌ 密碼不匹配');
      } else {
        setVerifyResult('❌ 驗證失敗: ' + result.error);
      }
    } catch (error) {
      setVerifyResult('❌ 請求失敗: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>密碼哈希工具 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">密碼哈希工具</h1>
            
            {/* 生成哈希 */}
            <div className="mb-8 p-6 bg-blue-50 rounded-lg">
              <h2 className="text-lg font-semibold text-blue-900 mb-4">生成密碼哈希</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    密碼
                  </label>
                  <input
                    type="text"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="輸入要生成哈希的密碼"
                  />
                </div>
                
                <button
                  onClick={generateHash}
                  disabled={isLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {isLoading ? '生成中...' : '生成哈希'}
                </button>
                
                {hash && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      生成的哈希值
                    </label>
                    <textarea
                      value={hash}
                      readOnly
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm"
                    />
                    <button
                      onClick={() => navigator.clipboard.writeText(hash)}
                      className="mt-2 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                    >
                      複製哈希值
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* 驗證哈希 */}
            <div className="mb-8 p-6 bg-green-50 rounded-lg">
              <h2 className="text-lg font-semibold text-green-900 mb-4">驗證密碼哈希</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    密碼
                  </label>
                  <input
                    type="text"
                    value={verifyPassword}
                    onChange={(e) => setVerifyPassword(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="輸入要驗證的密碼"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    哈希值
                  </label>
                  <textarea
                    value={verifyHash}
                    onChange={(e) => setVerifyHash(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 font-mono text-sm"
                    placeholder="輸入要驗證的哈希值"
                  />
                </div>
                
                <button
                  onClick={verifyPasswordHash}
                  disabled={isLoading}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                >
                  {isLoading ? '驗證中...' : '驗證密碼'}
                </button>
                
                {verifyResult && (
                  <div className={`p-3 rounded-md ${
                    verifyResult.includes('✅') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {verifyResult}
                  </div>
                )}
              </div>
            </div>

            {/* 預設哈希值 */}
            <div className="p-6 bg-yellow-50 rounded-lg">
              <h2 className="text-lg font-semibold text-yellow-900 mb-4">常用密碼哈希值</h2>
              
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-white rounded border">
                  <div className="font-medium text-gray-900">密碼: 1234</div>
                  <div className="font-mono text-gray-600 break-all mt-1">
                    {hash || '點擊上方生成按鈕生成哈希值'}
                  </div>
                </div>
                
                <div className="p-3 bg-white rounded border">
                  <div className="font-medium text-gray-900">密碼: admin</div>
                  <div className="font-mono text-gray-600 break-all mt-1">
                    $2b$10$N9qo8uLOickgx2ZMRZoMye.IjPeVXqmyHn7Q.vDSO8qS8QHdOmjO2
                  </div>
                </div>
                
                <div className="p-3 bg-white rounded border">
                  <div className="font-medium text-gray-900">密碼: password</div>
                  <div className="font-mono text-gray-600 break-all mt-1">
                    $2b$10$Yy7aBSuWlNzJgNKjhF.sJOuiKZwGqS8qS8QHdOmjO2N9qo8uLOick
                  </div>
                </div>
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-3">使用說明</h3>
              <div className="text-sm text-gray-700 space-y-2">
                <p><strong>生成哈希：</strong>輸入明文密碼，生成對應的 bcrypt 哈希值</p>
                <p><strong>驗證哈希：</strong>輸入密碼和哈希值，驗證是否匹配</p>
                <p><strong>安全性：</strong>bcrypt 使用 salt 和多輪加密，每次生成的哈希值都不同</p>
                <p><strong>用途：</strong>用於修復登入 API 中的密碼哈希問題</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
