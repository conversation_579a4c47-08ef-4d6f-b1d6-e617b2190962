import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletCompleteFix() {
  const router = useRouter();
  const [fixResults, setFixResults] = useState([]);
  const [isFixing, setIsFixing] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setFixResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const completeWalletFix = async () => {
    setIsFixing(true);
    setFixResults([]);
    setIsCompleted(false);

    try {
      addResult('開始', 'info', '開始完整修復錢包功能');

      // 檢查認證
      const token = localStorage.getItem('token');
      if (!token) {
        addResult('認證', 'error', '未找到認證 token，請先登入');
        return;
      }

      // 步驟 1: 檢查並修復 wallet_balance 欄位
      addResult('步驟1', 'info', '檢查 members 表結構...');
      
      const columnsResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "PRAGMA table_info(members)"
        })
      });

      if (columnsResponse.ok) {
        const columnsResult = await columnsResponse.json();
        if (columnsResult.success) {
          const hasWalletBalance = columnsResult.data.some(col => col.name === 'wallet_balance');
          if (hasWalletBalance) {
            addResult('步驟1', 'success', 'wallet_balance 欄位已存在');
          } else {
            addResult('步驟1', 'warning', 'wallet_balance 欄位不存在，正在添加...');
            
            const alterResponse = await fetch('/api/admin/database-query', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                query: "ALTER TABLE members ADD COLUMN wallet_balance DECIMAL(15,2) DEFAULT 0.00"
              })
            });

            if (alterResponse.ok) {
              const alterResult = await alterResponse.json();
              if (alterResult.success) {
                addResult('步驟1', 'success', 'wallet_balance 欄位添加成功');
              } else {
                if (alterResult.error.includes('duplicate column name')) {
                  addResult('步驟1', 'success', 'wallet_balance 欄位已存在');
                } else {
                  addResult('步驟1', 'error', `添加欄位失敗: ${alterResult.error}`);
                  return;
                }
              }
            }
          }
        }
      }

      // 步驟 2: 初始化管理員錢包餘額
      addResult('步驟2', 'info', '初始化管理員錢包餘額...');
      
      const updateResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "UPDATE members SET wallet_balance = ? WHERE id = ? AND (wallet_balance IS NULL OR wallet_balance = 0)",
          params: [1000.00, 1]
        })
      });

      if (updateResponse.ok) {
        const updateResult = await updateResponse.json();
        if (updateResult.success) {
          addResult('步驟2', 'success', '管理員錢包餘額設置為 $1000.00');
        }
      }

      // 步驟 3: 同步用戶數據
      addResult('步驟3', 'info', '同步用戶認證數據...');
      
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const tokenParts = token.split('.');
          const tokenData = JSON.parse(atob(tokenParts[1]));
          const userData = JSON.parse(userStr);
          
          const syncedUserData = {
            userId: tokenData.userId,
            name: userData.name || '系統管理員',
            email: tokenData.email,
            role: tokenData.role
          };
          
          localStorage.setItem('user', JSON.stringify(syncedUserData));
          addResult('步驟3', 'success', '用戶數據同步完成');
        } catch (error) {
          addResult('步驟3', 'error', `用戶數據同步失敗: ${error.message}`);
        }
      }

      // 步驟 4: 測試錢包 API
      addResult('步驟4', 'info', '測試錢包 API...');
      
      const walletResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (walletResponse.ok) {
        const walletResult = await walletResponse.json();
        if (walletResult.success) {
          addResult('步驟4', 'success', `錢包 API 正常！餘額: $${walletResult.data.balance}`);
        } else {
          addResult('步驟4', 'error', `錢包 API 失敗: ${walletResult.error}`);
        }
      } else {
        addResult('步驟4', 'error', `錢包 API HTTP 失敗: ${walletResponse.status}`);
      }

      // 步驟 5: 測試錢包頁面
      addResult('步驟5', 'info', '準備測試錢包頁面...');
      addResult('步驟5', 'success', '所有修復完成，可以測試錢包頁面');

      addResult('完成', 'success', '錢包功能完整修復完成！');
      setIsCompleted(true);

    } catch (error) {
      addResult('錯誤', 'error', '修復過程中出現錯誤: ' + error.message);
    } finally {
      setIsFixing(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>錢包完整修復 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔧 錢包完整修復</h1>
                <p className="text-gray-600 mt-2">一鍵修復所有錢包相關問題</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={completeWalletFix}
                  disabled={isFixing}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-medium"
                >
                  {isFixing ? '修復中...' : '🔧 一鍵修復'}
                </button>
              </div>
            </div>

            {/* 問題說明 */}
            <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-medium text-red-900 mb-2">❌ 當前問題</h3>
              <div className="text-sm text-red-800 space-y-1">
                <div><strong>錯誤:</strong> SQLITE_ERROR: no such column: wallet_balance</div>
                <div><strong>位置:</strong> src\pages\wallet.js (108:15)</div>
                <div><strong>原因:</strong> members 表中缺少 wallet_balance 欄位</div>
                <div><strong>影響:</strong> 錢包頁面無法載入，顯示運行時錯誤</div>
              </div>
            </div>

            {/* 修復結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 修復結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {fixResults.length > 0 ? (
                  <div className="space-y-3">
                    {fixResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「一鍵修復」來修復所有錢包問題
                  </div>
                )}
              </div>
            </div>

            {/* 完成後的操作 */}
            {isCompleted && (
              <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-medium text-green-900 mb-3">🎉 修復完成</h3>
                <div className="text-sm text-green-800 mb-4">
                  所有錢包相關問題已修復，錢包功能現在應該完全正常！
                </div>
                <div className="flex space-x-4">
                  <button
                    onClick={() => router.push('/wallet')}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    💰 測試錢包頁面
                  </button>
                  <button
                    onClick={() => router.push('/dashboard-simple')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    🏠 回到儀表板
                  </button>
                </div>
              </div>
            )}

            {/* 修復說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 修復內容</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>步驟1:</strong> 檢查並添加 wallet_balance 欄位到 members 表</div>
                <div><strong>步驟2:</strong> 初始化管理員錢包餘額為 $1000.00</div>
                <div><strong>步驟3:</strong> 同步用戶認證數據（userId, email 等）</div>
                <div><strong>步驟4:</strong> 測試錢包 API 是否正常工作</div>
                <div><strong>步驟5:</strong> 準備測試錢包頁面功能</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
