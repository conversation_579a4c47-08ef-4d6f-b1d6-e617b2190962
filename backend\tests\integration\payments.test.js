const request = require('supertest');
const app = require('../../src/app');

describe('Payments API', () => {
  let accessToken;
  let userId;

  beforeEach(async () => {
    // Create and login a test user
    const userData = {
      username: 'paymenttest',
      email: '<EMAIL>',
      password: 'Test123456',
      first_name: 'Payment',
      last_name: 'Test'
    };

    const registerResponse = await request(app)
      .post('/api/auth/register')
      .send(userData);

    accessToken = registerResponse.body.data.tokens.accessToken;
    userId = registerResponse.body.data.user.id;
  });

  describe('POST /api/payments/stripe/create-intent', () => {
    test('should create Stripe payment intent successfully', async () => {
      const paymentData = {
        amount: 1000,
        currency: 'TWD',
        description: 'Test payment'
      };

      const response = await request(app)
        .post('/api/payments/stripe/create-intent')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(paymentData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.clientSecret).toBeDefined();
      expect(response.body.data.paymentIntentId).toBeDefined();
    });

    test('should reject invalid amount', async () => {
      const paymentData = {
        amount: -100, // Invalid negative amount
        currency: 'TWD',
        description: 'Test payment'
      };

      const response = await request(app)
        .post('/api/payments/stripe/create-intent')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    test('should require authentication', async () => {
      const paymentData = {
        amount: 1000,
        currency: 'TWD',
        description: 'Test payment'
      };

      const response = await request(app)
        .post('/api/payments/stripe/create-intent')
        .send(paymentData)
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/payments/paypal/create-order', () => {
    test('should create PayPal order successfully', async () => {
      const orderData = {
        amount: 1000,
        currency: 'TWD',
        description: 'Test PayPal payment'
      };

      const response = await request(app)
        .post('/api/payments/paypal/create-order')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(orderData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.orderId).toBeDefined();
      expect(response.body.data.approvalUrl).toBeDefined();
    });

    test('should reject invalid amount for PayPal', async () => {
      const orderData = {
        amount: 0, // Invalid zero amount
        currency: 'TWD',
        description: 'Test PayPal payment'
      };

      const response = await request(app)
        .post('/api/payments/paypal/create-order')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(orderData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/payments/ecpay/create', () => {
    test('should create ECPay payment successfully', async () => {
      const paymentData = {
        amount: 1000,
        description: 'Test ECPay payment',
        paymentMethod: 'Credit'
      };

      const response = await request(app)
        .post('/api/payments/ecpay/create')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(paymentData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.paymentUrl).toBeDefined();
      expect(response.body.data.merchantTradeNo).toBeDefined();
    });

    test('should reject invalid payment method for ECPay', async () => {
      const paymentData = {
        amount: 1000,
        description: 'Test ECPay payment',
        paymentMethod: 'InvalidMethod'
      };

      const response = await request(app)
        .post('/api/payments/ecpay/create')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/payments/history', () => {
    test('should get payment history successfully', async () => {
      const response = await request(app)
        .get('/api/payments/history')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.payments).toBeDefined();
      expect(Array.isArray(response.body.data.payments)).toBe(true);
    });

    test('should support pagination', async () => {
      const response = await request(app)
        .get('/api/payments/history?page=1&limit=10')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(10);
    });

    test('should require authentication', async () => {
      const response = await request(app)
        .get('/api/payments/history')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });
});
