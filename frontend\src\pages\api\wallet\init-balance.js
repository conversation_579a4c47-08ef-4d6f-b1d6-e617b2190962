// 初始化會員錢包餘額 API
const jwt = require('jsonwebtoken');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export default async function handler(req, res) {
  try {
    console.log('初始化錢包餘額 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    // 驗證認證
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('初始化錢包餘額 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    const userId = decoded.userId;
    console.log('初始化錢包餘額 API: 用戶 ID:', userId);

    // 獲取初始化金額
    const { initial_balance = 1000.00 } = req.body;
    const initialAmount = parseFloat(initial_balance);

    if (isNaN(initialAmount) || initialAmount < 0) {
      return res.status(400).json({
        success: false,
        error: '初始化金額必須是有效的正數'
      });
    }

    // 使用資料庫連接
    const { getDatabase, dbRun, dbGet } = require('../../../../lib/database/init');
    const db = await getDatabase();

    // 檢查會員是否存在
    const member = await dbGet(db, 'SELECT id, name, email, wallet_balance FROM members WHERE id = ?', [userId]);
    
    if (!member) {
      return res.status(404).json({
        success: false,
        error: '會員不存在'
      });
    }

    console.log('初始化錢包餘額 API: 會員數據:', member);

    // 更新會員錢包餘額
    await dbRun(db, 'UPDATE members SET wallet_balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [initialAmount, userId]);

    // 記錄初始化交易（如果有交易記錄表）
    try {
      await dbRun(db, `
        INSERT INTO wallet_transactions (
          member_id, transaction_type, amount, balance_before, balance_after,
          description, status, payment_method, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `, [
        userId,
        'bonus',
        initialAmount,
        member.wallet_balance || 0,
        initialAmount,
        '錢包餘額初始化',
        'completed',
        'system'
      ]);
      console.log('初始化錢包餘額 API: 交易記錄已創建');
    } catch (transactionError) {
      console.log('初始化錢包餘額 API: 交易記錄創建失敗（可能表不存在）:', transactionError.message);
    }

    console.log('初始化錢包餘額 API: 初始化成功');

    return res.status(200).json({
      success: true,
      data: {
        member_id: userId,
        member_name: member.name,
        previous_balance: member.wallet_balance || 0,
        new_balance: initialAmount,
        initialized_amount: initialAmount
      },
      message: `錢包餘額初始化成功，設置為 $${initialAmount.toFixed(2)}`
    });

  } catch (error) {
    console.error('初始化錢包餘額 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
