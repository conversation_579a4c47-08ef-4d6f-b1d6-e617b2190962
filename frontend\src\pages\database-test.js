import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function DatabaseTest() {
  const [services, setServices] = useState([]);
  const [subscriptions, setSubscriptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);
  const [testUserId] = useState(1); // 使用測試用戶 ID

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
      id: Date.now(),
      timestamp,
      message,
      type
    };
    setLogs(prev => [logEntry, ...prev]);
    console.log(`[${timestamp}] ${message}`);
  };

  // 載入所有服務（資料庫版本）
  const loadServices = async () => {
    try {
      setIsLoading(true);
      addLog('🔄 從資料庫載入服務...', 'info');
      
      const response = await fetch('/api/services/db');
      const result = await response.json();
      
      if (result.success) {
        setServices(result.data);
        addLog(`✅ 成功載入 ${result.data.length} 個服務`, 'success');
      } else {
        addLog(`❌ 載入失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 載入錯誤: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 載入會員訂閱
  const loadSubscriptions = async () => {
    try {
      addLog(`🔄 載入會員 ${testUserId} 的訂閱...`, 'info');
      
      const response = await fetch(`/api/subscriptions?member_id=${testUserId}`);
      const result = await response.json();
      
      if (result.success) {
        setSubscriptions(result.data);
        addLog(`✅ 成功載入 ${result.data.length} 個訂閱`, 'success');
      } else {
        addLog(`❌ 載入訂閱失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 載入訂閱錯誤: ${error.message}`, 'error');
    }
  };

  // 載入可用服務
  const loadAvailableServices = async () => {
    try {
      addLog(`🔄 載入會員 ${testUserId} 的可用服務...`, 'info');
      
      const response = await fetch(`/api/services/db?member_id=${testUserId}`);
      const result = await response.json();
      
      if (result.success) {
        setAvailableServices(result.data);
        addLog(`✅ 成功載入 ${result.data.length} 個可用服務`, 'success');
      } else {
        addLog(`❌ 載入可用服務失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 載入可用服務錯誤: ${error.message}`, 'error');
    }
  };

  // 訂閱服務
  const subscribeService = async (serviceId) => {
    try {
      addLog(`🔄 訂閱服務 ${serviceId}...`, 'info');
      
      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          member_id: testUserId,
          service_id: serviceId
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        addLog(`✅ 訂閱成功: ${result.data.service_name}`, 'success');
        // 重新載入數據
        await Promise.all([loadSubscriptions(), loadAvailableServices()]);
      } else {
        addLog(`❌ 訂閱失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 訂閱錯誤: ${error.message}`, 'error');
    }
  };

  // 取消訂閱
  const cancelSubscription = async (subscriptionId) => {
    try {
      addLog(`🔄 取消訂閱 ${subscriptionId}...`, 'info');
      
      const response = await fetch('/api/subscriptions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription_id: subscriptionId,
          action: 'cancel'
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        addLog(`✅ 取消訂閱成功`, 'success');
        // 重新載入數據
        await Promise.all([loadSubscriptions(), loadAvailableServices()]);
      } else {
        addLog(`❌ 取消訂閱失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 取消訂閱錯誤: ${error.message}`, 'error');
    }
  };

  // 創建新服務
  const createTestService = async () => {
    try {
      addLog('🔄 創建測試服務...', 'info');
      
      const testService = {
        name: `測試服務 ${Date.now()}`,
        description: '這是一個測試服務',
        price: 99.99,
        category: '測試類別',
        features: ['功能1', '功能2', '功能3'],
        status: 'active'
      };
      
      const response = await fetch('/api/services/db', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testService)
      });
      
      const result = await response.json();
      
      if (result.success) {
        addLog(`✅ 服務創建成功: ${result.data.name}`, 'success');
        await loadServices();
        await loadAvailableServices();
      } else {
        addLog(`❌ 服務創建失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 服務創建錯誤: ${error.message}`, 'error');
    }
  };

  // 初始載入
  useEffect(() => {
    const initLoad = async () => {
      await Promise.all([
        loadServices(),
        loadSubscriptions(),
        loadAvailableServices()
      ]);
    };
    initLoad();
  }, []);

  return (
    <>
      <Head>
        <title>資料庫測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">資料庫功能測試</h1>
            
            {/* 控制按鈕 */}
            <div className="mb-8 flex flex-wrap gap-4">
              <button
                onClick={loadServices}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                🔄 重新載入服務
              </button>
              
              <button
                onClick={loadSubscriptions}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                📋 載入訂閱
              </button>
              
              <button
                onClick={loadAvailableServices}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                🎯 載入可用服務
              </button>
              
              <button
                onClick={createTestService}
                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                ➕ 創建測試服務
              </button>
              
              <button
                onClick={() => setLogs([])}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                🧹 清除日誌
              </button>
            </div>

            {/* 統計信息 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-blue-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">所有服務</h3>
                <p className="text-3xl font-bold text-blue-600">{services.length}</p>
                <p className="text-sm text-blue-700">資料庫中的服務</p>
              </div>
              
              <div className="bg-green-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-900 mb-2">可用服務</h3>
                <p className="text-3xl font-bold text-green-600">{availableServices.length}</p>
                <p className="text-sm text-green-700">可以訂閱的服務</p>
              </div>
              
              <div className="bg-orange-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-orange-900 mb-2">已訂閱</h3>
                <p className="text-3xl font-bold text-orange-600">{subscriptions.length}</p>
                <p className="text-sm text-orange-700">當前訂閱數量</p>
              </div>
              
              <div className="bg-purple-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-purple-900 mb-2">測試用戶</h3>
                <p className="text-3xl font-bold text-purple-600">{testUserId}</p>
                <p className="text-sm text-purple-700">當前測試用戶 ID</p>
              </div>
            </div>

            {/* 服務列表 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* 可用服務 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  可用服務 ({availableServices.length})
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                  {availableServices.map((service) => (
                    <div key={service.id} className="mb-2 p-3 bg-white rounded border">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">{service.name}</div>
                          <div className="text-sm text-gray-500">
                            ID: {service.id} | 價格: NT$ {service.price}
                          </div>
                        </div>
                        <button
                          onClick={() => subscribeService(service.id)}
                          className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                        >
                          訂閱
                        </button>
                      </div>
                    </div>
                  ))}
                  {availableServices.length === 0 && (
                    <div className="text-gray-500 text-center py-4">沒有可用服務</div>
                  )}
                </div>
              </div>

              {/* 已訂閱服務 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  已訂閱服務 ({subscriptions.length})
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                  {subscriptions.map((subscription) => (
                    <div key={subscription.id} className="mb-2 p-3 bg-white rounded border">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">{subscription.service_name}</div>
                          <div className="text-sm text-gray-500">
                            狀態: {subscription.status} | 下次計費: {subscription.next_billing_date}
                          </div>
                        </div>
                        {subscription.status === 'active' && (
                          <button
                            onClick={() => cancelSubscription(subscription.id)}
                            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
                          >
                            取消
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                  {subscriptions.length === 0 && (
                    <div className="text-gray-500 text-center py-4">沒有訂閱服務</div>
                  )}
                </div>
              </div>
            </div>

            {/* 操作日誌 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">操作日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
                {logs.map((log) => (
                  <div key={log.id} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'success' ? 'text-green-400' : 
                    'text-blue-400'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">尚無操作記錄</div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-3">資料庫測試說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>測試目標：</strong>驗證資料庫版本的服務和訂閱管理功能。</p>
                <p><strong>功能測試：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>✅ 從 SQLite 資料庫載入服務數據</li>
                  <li>✅ 會員訂閱服務的關聯管理</li>
                  <li>✅ 動態過濾可用服務（排除已訂閱）</li>
                  <li>✅ 訂閱和取消訂閱的即時更新</li>
                  <li>✅ 服務創建和管理</li>
                </ul>
                <p><strong>API 端點：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li><code>GET /api/services/db</code> - 獲取所有服務</li>
                  <li><code>GET /api/services/db?member_id=1</code> - 獲取可用服務</li>
                  <li><code>GET /api/subscriptions?member_id=1</code> - 獲取會員訂閱</li>
                  <li><code>POST /api/subscriptions</code> - 創建新訂閱</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
