const jwt = require('jsonwebtoken');
const { Pool } = require('pg');

// 資料庫連接
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'member_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

// 認證中間件
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供認證 Token'
      });
    }

    // 驗證 JWT Token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 檢查用戶是否存在且狀態正常
    const client = await pool.connect();
    
    try {
      const result = await client.query(
        `SELECT id, username, email, status, locked_at
         FROM users 
         WHERE id = $1 AND deleted_at IS NULL`,
        [decoded.userId]
      );

      if (result.rows.length === 0) {
        return res.status(401).json({
          success: false,
          message: '用戶不存在'
        });
      }

      const user = result.rows[0];

      if (user.status !== 'active') {
        return res.status(403).json({
          success: false,
          message: '帳號已被停用'
        });
      }

      if (user.locked_at) {
        return res.status(423).json({
          success: false,
          message: '帳號已被鎖定'
        });
      }

      // 將用戶資訊添加到請求對象
      req.user = {
        userId: user.id,
        username: user.username,
        email: user.email,
        status: user.status
      };

      client.release();
      next();

    } catch (error) {
      client.release();
      throw error;
    }

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token 無效'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token 已過期'
      });
    }

    console.error('認證中間件錯誤:', error);
    res.status(500).json({
      success: false,
      message: '認證失敗',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 可選認證中間件 (不強制要求登入)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const client = await pool.connect();

    try {
      const result = await client.query(
        `SELECT id, username, email, status
         FROM users 
         WHERE id = $1 AND deleted_at IS NULL AND status = 'active'`,
        [decoded.userId]
      );

      if (result.rows.length > 0) {
        const user = result.rows[0];
        req.user = {
          userId: user.id,
          username: user.username,
          email: user.email,
          status: user.status
        };
      } else {
        req.user = null;
      }

      client.release();
      next();

    } catch (error) {
      client.release();
      req.user = null;
      next();
    }

  } catch (error) {
    req.user = null;
    next();
  }
};

// 管理員權限檢查中間件
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '需要登入'
      });
    }

    // 這裡可以添加管理員權限檢查邏輯
    // 例如檢查用戶角色或權限表
    
    next();
  } catch (error) {
    console.error('管理員權限檢查錯誤:', error);
    res.status(500).json({
      success: false,
      message: '權限檢查失敗'
    });
  }
};

module.exports = {
  authenticateToken,
  optionalAuth,
  requireAdmin
};
