# ===========================================
# 會員管理系統 - 環境變數範例檔案
# ===========================================
# 複製此檔案為 .env (後端) 和 .env.local (前端)
# 並填入實際的配置值

# ===========================================
# 後端環境變數 (backend/.env)
# ===========================================

# 應用程式基本配置
NODE_ENV=development
PORT=3000
APP_NAME=會員管理系統
APP_VERSION=1.0.0

# 資料庫配置
DB_TYPE=sqlite
DB_PATH=../frontend/lib/database/database.sqlite
# PostgreSQL 配置 (生產環境)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=memberservice
DB_USER=postgres
DB_PASSWORD=your-database-password

# JWT 認證配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
ENCRYPTION_KEY=your-super-secret-encryption-key-32-chars-long-change-this
ENCRYPTION_SALT=your-encryption-salt-change-this
HMAC_SECRET=your-hmac-secret-for-signing-change-this

# 郵件服務配置
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=Member System

# OAuth 配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
LINE_CHANNEL_ID=your-line-channel-id
LINE_CHANNEL_SECRET=your-line-channel-secret

# 支付服務配置
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# 前端配置
FRONTEND_URL=http://localhost:3001
CORS_ORIGINS=http://localhost:3001,http://localhost:3000

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===========================================
# 前端環境變數 (frontend/.env.local)
# ===========================================

# API 配置
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000
NEXT_PUBLIC_BACKEND_URL=http://localhost:3000

# 應用配置
NEXT_PUBLIC_APP_NAME=會員管理系統
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_DESCRIPTION=專業的會員管理與訂閱服務系統

# 認證配置
NEXT_PUBLIC_JWT_STORAGE_KEY=memberservice_token
NEXT_PUBLIC_REFRESH_TOKEN_KEY=memberservice_refresh_token
NEXT_PUBLIC_USER_STORAGE_KEY=memberservice_user

# OAuth 配置
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
NEXT_PUBLIC_FACEBOOK_APP_ID=your-facebook-app-id
NEXT_PUBLIC_LINE_CHANNEL_ID=your-line-channel-id

# 支付服務配置
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your-paypal-client-id

# reCAPTCHA 配置
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your-recaptcha-site-key

# 功能開關
NEXT_PUBLIC_ENABLE_REGISTRATION=true
NEXT_PUBLIC_ENABLE_SOCIAL_LOGIN=true
NEXT_PUBLIC_ENABLE_EMAIL_VERIFICATION=true
NEXT_PUBLIC_ENABLE_SUBSCRIPTION=true
NEXT_PUBLIC_ENABLE_PAYMENT=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_SENTRY=false

# UI 配置
NEXT_PUBLIC_DEFAULT_LANGUAGE=zh-TW
NEXT_PUBLIC_DEFAULT_THEME=light
NEXT_PUBLIC_ENABLE_DARK_MODE=true

# 開發模式配置
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_SHOW_DEV_TOOLS=true
NEXT_PUBLIC_ENABLE_MOCK_DATA=false

# ===========================================
# 安全注意事項
# ===========================================
# 1. 絕對不要將包含真實密鑰的 .env 檔案提交到版本控制
# 2. 在生產環境中使用強密碼和隨機生成的密鑰
# 3. 定期更換 JWT 密鑰和加密密鑰
# 4. 使用環境變數管理服務 (如 AWS Secrets Manager)
# 5. 限制環境變數的訪問權限

# ===========================================
# 生產環境建議
# ===========================================
# NODE_ENV=production
# DEBUG=false
# JWT_SECRET=使用強隨機密鑰
# ENCRYPTION_KEY=使用32字符的強隨機密鑰
# DB_PASSWORD=使用強資料庫密碼
# CORS_ORIGINS=僅包含實際的前端域名
