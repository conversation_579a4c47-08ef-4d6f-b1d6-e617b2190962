// 錢包餘額 API
const jwt = require('jsonwebtoken');
const Wallet = require('../../../../lib/database/models/Wallet');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export default async function handler(req, res) {
  try {
    console.log('錢包餘額 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'GET') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    // 驗證認證
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('錢包餘額 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    const userId = decoded.userId;
    console.log('錢包餘額 API: 用戶 ID:', userId);

    // 獲取錢包餘額
    const balance = await Wallet.getBalance(userId);

    console.log('錢包餘額 API: 成功獲取餘額:', balance);

    return res.status(200).json({
      success: true,
      data: {
        balance: balance,
        formatted_balance: `$${balance.toFixed(2)}`
      },
      message: '成功獲取錢包餘額'
    });

  } catch (error) {
    console.error('錢包餘額 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
