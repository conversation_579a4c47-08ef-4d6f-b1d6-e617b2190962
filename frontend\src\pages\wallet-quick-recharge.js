import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletQuickRecharge() {
  const router = useRouter();
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRecharging, setIsRecharging] = useState(false);

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      const response = await fetch('/api/wallet/payment-methods');
      const result = await response.json();
      
      if (result.success) {
        setPaymentMethods(result.data.payment_methods);
      }
    } catch (error) {
      console.error('載入支付方式失敗:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickRecharge = async (amount, paymentMethodId) => {
    setIsRecharging(true);
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/wallet/recharge', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount: amount,
          payment_method_id: paymentMethodId,
          description: '快速充值'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        alert(`充值成功！已充值 $${amount}，新餘額: $${result.data.new_balance.toFixed(2)}`);
        router.push('/dashboard-simple');
      } else {
        alert('充值失敗: ' + result.error);
      }
    } catch (error) {
      console.error('充值失敗:', error);
      alert('充值失敗: ' + error.message);
    } finally {
      setIsRecharging(false);
    }
  };

  const quickAmounts = [10, 50, 100, 200, 500];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>快速充值 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">💳 快速充值</h1>
                <p className="text-gray-600 mt-2">選擇金額和支付方式快速充值</p>
              </div>
              <button
                onClick={() => router.push('/dashboard-simple')}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                返回
              </button>
            </div>

            {/* 快速金額選擇 */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">選擇充值金額</h3>
              <div className="grid grid-cols-3 md:grid-cols-5 gap-4">
                {quickAmounts.map((amount) => (
                  <QuickAmountCard
                    key={amount}
                    amount={amount}
                    paymentMethods={paymentMethods}
                    onRecharge={handleQuickRecharge}
                    isRecharging={isRecharging}
                  />
                ))}
              </div>
            </div>

            {/* 自定義金額 */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">自定義金額</h3>
              <CustomAmountForm
                paymentMethods={paymentMethods}
                onRecharge={handleQuickRecharge}
                isRecharging={isRecharging}
              />
            </div>

            {/* 支付方式說明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">支付方式說明</h4>
              <div className="text-sm text-blue-800 space-y-1">
                {paymentMethods.map((method) => (
                  <div key={method.id} className="flex items-center justify-between">
                    <span>{method.icon_url} {method.name}</span>
                    <span>{method.fee_display}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// 快速金額卡片組件
function QuickAmountCard({ amount, paymentMethods, onRecharge, isRecharging }) {
  const [showMethods, setShowMethods] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setShowMethods(!showMethods)}
        disabled={isRecharging}
        className="w-full p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors disabled:opacity-50"
      >
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">${amount}</div>
          <div className="text-sm text-gray-600">快速充值</div>
        </div>
      </button>

      {showMethods && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
          <div className="p-3">
            <div className="text-sm font-medium text-gray-900 mb-2">選擇支付方式</div>
            <div className="space-y-2">
              {paymentMethods.map((method) => (
                <button
                  key={method.id}
                  onClick={() => {
                    onRecharge(amount, method.id);
                    setShowMethods(false);
                  }}
                  disabled={isRecharging}
                  className="w-full text-left p-2 hover:bg-gray-50 rounded border border-gray-100 disabled:opacity-50"
                >
                  <div className="flex items-center space-x-2">
                    <span>{method.icon_url}</span>
                    <div>
                      <div className="text-sm font-medium">{method.name}</div>
                      <div className="text-xs text-gray-500">{method.fee_display}</div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// 自定義金額表單組件
function CustomAmountForm({ paymentMethods, onRecharge, isRecharging }) {
  const [amount, setAmount] = useState('');
  const [selectedMethod, setSelectedMethod] = useState(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!amount || !selectedMethod) {
      alert('請輸入充值金額並選擇支付方式');
      return;
    }
    
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      alert('請輸入有效的充值金額');
      return;
    }

    onRecharge(amountNum, selectedMethod.id);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          充值金額
        </label>
        <input
          type="number"
          step="0.01"
          min="0"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
          placeholder="請輸入充值金額"
          disabled={isRecharging}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          選擇支付方式
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {paymentMethods.map((method) => (
            <div
              key={method.id}
              onClick={() => setSelectedMethod(method)}
              className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                selectedMethod?.id === method.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${isRecharging ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{method.icon_url}</span>
                <div>
                  <h4 className="font-medium">{method.name}</h4>
                  <p className="text-xs text-gray-500">{method.fee_display}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <button
        type="submit"
        disabled={isRecharging || !amount || !selectedMethod}
        className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
      >
        {isRecharging ? '充值中...' : '確認充值'}
      </button>
    </form>
  );
}
