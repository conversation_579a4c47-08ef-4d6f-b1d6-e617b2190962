// 會員訂閱管理 API (資料庫版本)
const jwt = require('jsonwebtoken');
const path = require('path');
const fs = require('fs');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();

    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];

    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('訂閱 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }

    console.log('訂閱 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('訂閱 API: 資料庫連接失敗:', error);
    return null;
  }
};

// 模擬服務數據
const mockServices = [
  {
    id: 1,
    name: 'AI 文字生成服務',
    description: '使用先進的 AI 技術生成高質量文字內容',
    price: 299.00,
    billing_cycle: 'monthly',
    status: 'active',
    category: 'AI',
    features: ['無限文字生成', '多種寫作風格', '24/7 技術支援'],
    created_at: '2025-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    name: '圖片處理服務',
    description: '專業的圖片編輯和處理工具',
    price: 199.00,
    billing_cycle: 'monthly',
    status: 'active',
    category: '圖片處理',
    features: ['批量處理', '多種濾鏡', '雲端存儲'],
    created_at: '2025-01-01T00:00:00.000Z'
  },
  {
    id: 3,
    name: '數據分析服務',
    description: '強大的數據分析和視覺化工具',
    price: 499.00,
    billing_cycle: 'monthly',
    status: 'active',
    category: '數據分析',
    features: ['即時分析', '自定義報表', '數據視覺化'],
    created_at: '2025-01-01T00:00:00.000Z'
  }
];

// 模擬會員訂閱數據 (全局變量，模擬資料庫)
let mockMemberServices = [
  {
    id: 1,
    member_id: 1,
    service_id: 1,
    status: 'active',
    subscribed_at: '2025-01-01T00:00:00.000Z',
    next_billing_date: '2025-02-01',
    auto_renew: true,
    created_at: '2025-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    member_id: 1,
    service_id: 2,
    status: 'active',
    subscribed_at: '2025-01-01T00:00:00.000Z',
    next_billing_date: '2025-02-01',
    auto_renew: true,
    created_at: '2025-01-01T00:00:00.000Z'
  }
];

// 簡化的認證中間件
const authenticateToken = (req) => {
  console.log('🔐 訂閱 API: 開始認證...');
  console.log('🔐 訂閱 API: Headers:', req.headers);

  const authHeader = req.headers.authorization;
  console.log('🔐 訂閱 API: Authorization header:', authHeader);

  const token = authHeader && authHeader.split(' ')[1];
  console.log('🔐 訂閱 API: Token:', token ? token.substring(0, 20) + '...' : 'NULL');

  if (!token) {
    console.log('🔐 訂閱 API: ❌ 沒有 token');
    return null;
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('🔐 訂閱 API: ✅ Token 驗證成功:', decoded);
    return decoded;
  } catch (error) {
    console.error('🔐 訂閱 API: ❌ Token 驗證失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  try {
    // 驗證用戶身份
    const user = authenticateToken(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    console.log('訂閱 API: 用戶已認證', { userId: user.userId, method: req.method });

    if (req.method === 'GET') {
      // 獲取用戶的訂閱列表
      const db = connectDatabase();
      if (!db) {
        return res.status(500).json({
          success: false,
          error: '資料庫連接失敗'
        });
      }

      return new Promise((resolve) => {
        const query = `
          SELECT ms.*, s.name as service_name, s.price as service_price, s.billing_cycle as service_billing_cycle, s.features as service_features
          FROM member_services ms
          JOIN services s ON ms.service_id = s.id
          WHERE ms.member_id = ?
          ORDER BY ms.created_at DESC
        `;

        db.all(query, [user.userId], (err, rows) => {
          db.close();

          if (err) {
            console.error('訂閱 API: 查詢訂閱失敗:', err);
            resolve(res.status(500).json({
              success: false,
              error: '查詢訂閱失敗'
            }));
            return;
          }

          console.log('訂閱 API: 查詢到訂閱數量:', rows.length);

          const userSubscriptions = rows.map(ms => {
            // 處理 features 欄位
            let features = [];
            try {
              if (ms.service_features) {
                features = JSON.parse(ms.service_features);
              }
            } catch (parseError) {
              console.warn('解析服務功能失敗:', parseError);
              features = [];
            }

            return {
              id: ms.id,
              service_id: ms.service_id,
              service_name: ms.service_name,
              service_price: ms.service_price,
              service_billing_cycle: ms.service_billing_cycle,
              features: features,
              status: ms.status,
              subscribed_at: ms.subscribed_at,
              next_billing_date: ms.next_billing_date,
              auto_renew: ms.auto_renew
            };
          });

          resolve(res.status(200).json({
            success: true,
            data: userSubscriptions,
            source: 'database'
          }));
        });
      });

    } else if (req.method === 'POST') {
      // 創建新訂閱
      const { service_id } = req.body;

      if (!service_id) {
        return res.status(400).json({
          success: false,
          error: '請提供服務 ID'
        });
      }

      console.log('訂閱 API: 創建訂閱', { userId: user.userId, serviceId: service_id });

      const db = connectDatabase();
      if (!db) {
        return res.status(500).json({
          success: false,
          error: '資料庫連接失敗'
        });
      }

      return new Promise((resolve) => {
        // 檢查服務是否存在
        db.get('SELECT * FROM services WHERE id = ? AND status = ?', [parseInt(service_id), 'active'], (err, service) => {
          if (err) {
            console.error('訂閱 API: 查詢服務失敗:', err);
            db.close();
            resolve(res.status(500).json({
              success: false,
              error: '查詢服務失敗'
            }));
            return;
          }

          if (!service) {
            console.log('訂閱 API: 服務不存在或未啟用');
            db.close();
            resolve(res.status(404).json({
              success: false,
              error: '服務不存在或未啟用'
            }));
            return;
          }

          console.log('訂閱 API: 找到服務:', service.name);

          // 檢查現有訂閱（包括所有狀態）
          db.get('SELECT id, status FROM member_services WHERE member_id = ? AND service_id = ? ORDER BY created_at DESC LIMIT 1',
            [user.userId, parseInt(service_id)], (checkErr, existing) => {

            if (checkErr) {
              console.error('訂閱 API: 檢查現有訂閱失敗:', checkErr);
              db.close();
              resolve(res.status(500).json({
                success: false,
                error: '檢查訂閱狀態失敗'
              }));
              return;
            }

            // 如果已有活躍訂閱，拒絕重複訂閱
            if (existing && existing.status === 'active') {
              console.log('訂閱 API: 用戶已有活躍訂閱');
              db.close();
              resolve(res.status(409).json({
                success: false,
                error: '您已經訂閱了此服務'
              }));
              return;
            }

            // 如果有已取消或過期的訂閱，重新激活它
            if (existing && (existing.status === 'cancelled' || existing.status === 'expired')) {
              console.log('訂閱 API: 重新激活已取消/過期的訂閱', { subscriptionId: existing.id, oldStatus: existing.status });

              const nextBillingDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

              const updateQuery = `
                UPDATE member_services
                SET status = 'active', next_billing_date = ?, auto_renew = 1, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
              `;

              db.run(updateQuery, [nextBillingDate, existing.id], function(updateErr) {
                if (updateErr) {
                  console.error('訂閱 API: 重新激活訂閱失敗:', updateErr);
                  db.close();
                  resolve(res.status(500).json({
                    success: false,
                    error: '重新激活訂閱失敗'
                  }));
                  return;
                }

                console.log('訂閱 API: 訂閱重新激活成功', { subscriptionId: existing.id });

                // 查詢重新激活的訂閱完整信息
                db.get(`
                  SELECT ms.*, s.name as service_name, s.price as service_price, s.billing_cycle as service_billing_cycle, s.features as service_features
                  FROM member_services ms
                  JOIN services s ON ms.service_id = s.id
                  WHERE ms.id = ?
                `, [existing.id], (selectErr, reactivatedSub) => {
                  db.close();

                  if (selectErr || !reactivatedSub) {
                    console.error('訂閱 API: 查詢重新激活的訂閱失敗:', selectErr);
                    resolve(res.status(500).json({
                      success: false,
                      error: '重新激活後查詢失敗'
                    }));
                    return;
                  }

                  // 處理 features 欄位
                  let features = [];
                  try {
                    if (reactivatedSub.service_features) {
                      features = JSON.parse(reactivatedSub.service_features);
                    }
                  } catch (parseError) {
                    console.warn('解析服務功能失敗:', parseError);
                    features = [];
                  }

                  // 返回重新激活的訂閱信息
                  const subscriptionWithService = {
                    id: reactivatedSub.id,
                    service_id: reactivatedSub.service_id,
                    service_name: reactivatedSub.service_name,
                    service_price: reactivatedSub.service_price,
                    service_billing_cycle: reactivatedSub.service_billing_cycle,
                    features: features,
                    status: reactivatedSub.status,
                    subscribed_at: reactivatedSub.subscribed_at,
                    next_billing_date: reactivatedSub.next_billing_date,
                    auto_renew: reactivatedSub.auto_renew
                  };

                  resolve(res.status(200).json({
                    success: true,
                    data: subscriptionWithService,
                    message: `成功重新訂閱 ${reactivatedSub.service_name}，已重新激活現有記錄`,
                    source: 'database',
                    action: 'reactivated'
                  }));
                });
              });
              return; // 重要：結束這個分支
            }

            // 創建新訂閱
            const nextBillingDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

            const insertQuery = `
              INSERT INTO member_services (member_id, service_id, status, next_billing_date, auto_renew)
              VALUES (?, ?, ?, ?, ?)
            `;

            db.run(insertQuery, [user.userId, parseInt(service_id), 'active', nextBillingDate, 1], function(insertErr) {
              if (insertErr) {
                console.error('訂閱 API: 創建訂閱失敗:', insertErr);
                db.close();
                resolve(res.status(500).json({
                  success: false,
                  error: '創建訂閱失敗'
                }));
                return;
              }

              const newSubscriptionId = this.lastID;
              console.log('訂閱 API: 訂閱創建成功', {
                subscriptionId: newSubscriptionId,
                userId: user.userId,
                serviceId: service_id
              });

              // 查詢剛創建的訂閱完整信息
              db.get(`
                SELECT ms.*, s.name as service_name, s.price as service_price, s.billing_cycle as service_billing_cycle, s.features as service_features
                FROM member_services ms
                JOIN services s ON ms.service_id = s.id
                WHERE ms.id = ?
              `, [newSubscriptionId], (selectErr, newSub) => {
                db.close();

                if (selectErr || !newSub) {
                  console.error('訂閱 API: 查詢新訂閱失敗:', selectErr);
                  resolve(res.status(500).json({
                    success: false,
                    error: '訂閱創建後查詢失敗'
                  }));
                  return;
                }

                // 處理 features 欄位
                let features = [];
                try {
                  if (newSub.service_features) {
                    features = JSON.parse(newSub.service_features);
                  }
                } catch (parseError) {
                  console.warn('解析服務功能失敗:', parseError);
                  features = [];
                }

                // 返回創建的訂閱信息
                const subscriptionWithService = {
                  id: newSub.id,
                  service_id: newSub.service_id,
                  service_name: newSub.service_name,
                  service_price: newSub.service_price,
                  service_billing_cycle: newSub.service_billing_cycle,
                  features: features,
                  status: newSub.status,
                  subscribed_at: newSub.subscribed_at,
                  next_billing_date: newSub.next_billing_date,
                  auto_renew: newSub.auto_renew
                };

                resolve(res.status(201).json({
                  success: true,
                  data: subscriptionWithService,
                  message: `成功訂閱 ${newSub.service_name}，數據已保存到資料庫`,
                  source: 'database'
                }));
              });
            });
          });
        });
      });

    } else if (req.method === 'DELETE') {
      // 取消訂閱
      const { subscription_id } = req.body;

      if (!subscription_id) {
        return res.status(400).json({
          success: false,
          error: '請提供訂閱 ID'
        });
      }

      console.log('訂閱 API: 取消訂閱', { userId: user.userId, subscriptionId: subscription_id });

      // 查找訂閱
      const subscriptionIndex = mockMemberServices.findIndex(
        ms => ms.id === parseInt(subscription_id) && ms.member_id === user.userId
      );

      if (subscriptionIndex === -1) {
        return res.status(404).json({
          success: false,
          error: '訂閱不存在或無權限操作'
        });
      }

      // 更新訂閱狀態為取消
      mockMemberServices[subscriptionIndex].status = 'cancelled';
      mockMemberServices[subscriptionIndex].updated_at = new Date().toISOString();

      console.log('訂閱 API: 訂閱取消成功', { subscriptionId: subscription_id });

      return res.status(200).json({
        success: true,
        message: '訂閱已取消'
      });

    } else {
      res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
      return res.status(405).json({
        success: false,
        error: `方法 ${req.method} 不被允許`
      });
    }

  } catch (error) {
    console.error('訂閱 API 錯誤:', error);
    
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
