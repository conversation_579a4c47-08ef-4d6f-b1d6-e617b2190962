import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function DebugLogin() {
  const router = useRouter();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('123456');
  const [logs, setLogs] = useState([]);

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(message);
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    addLog('🚀 開始登入流程');
    
    // 清除之前的數據
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    addLog('🧹 清除舊數據');
    
    // 檢查表單數據
    addLog(`📧 Email: ${email}`);
    addLog(`🔒 Password: ${password ? '已輸入' : '未輸入'}`);
    
    if (!email || !password) {
      addLog('❌ 表單驗證失敗：缺少必填字段');
      return;
    }
    
    try {
      addLog('⏳ 模擬 API 調用...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 保存認證數據
      const token = `debug-token-${Date.now()}`;
      const userData = {
        name: '調試用戶',
        email: email,
        avatar: null,
        memberSince: new Date().toISOString()
      };
      
      addLog('💾 保存認證數據...');
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(userData));
      
      // 驗證保存
      const savedToken = localStorage.getItem('token');
      const savedUser = localStorage.getItem('user');
      addLog(`✅ Token 保存: ${savedToken ? '成功' : '失敗'}`);
      addLog(`✅ User 保存: ${savedUser ? '成功' : '失敗'}`);
      
      // 獲取重定向 URL
      const redirectUrl = router.query.redirect || '/dashboard';
      addLog(`🎯 重定向目標: ${redirectUrl}`);
      addLog(`🔍 Router query: ${JSON.stringify(router.query)}`);
      
      // 嘗試跳轉
      addLog('🔄 嘗試使用 router.push 跳轉...');
      try {
        await router.push(redirectUrl);
        addLog('✅ router.push 成功');
      } catch (error) {
        addLog(`❌ router.push 失敗: ${error.message}`);
        
        // 嘗試 window.location
        addLog('🔄 嘗試使用 window.location 跳轉...');
        window.location.href = redirectUrl;
      }
      
    } catch (error) {
      addLog(`❌ 登入過程出錯: ${error.message}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const testDashboard = () => {
    addLog('🧪 測試直接跳轉到儀表板');
    router.push('/minimal-dashboard');
  };

  const checkAuth = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    addLog(`🔍 當前認證狀態:`);
    addLog(`   Token: ${token ? '存在' : '不存在'}`);
    addLog(`   User: ${user ? '存在' : '不存在'}`);
    if (user) {
      try {
        const userData = JSON.parse(user);
        addLog(`   用戶名: ${userData.name}`);
        addLog(`   郵箱: ${userData.email}`);
      } catch (e) {
        addLog(`   用戶數據解析失敗: ${e.message}`);
      }
    }
  };

  return (
    <>
      <Head>
        <title>調試登入 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">調試登入頁面</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 登入表單 */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">登入表單</h2>
              
              <form onSubmit={handleLogin} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    電子郵件
                  </label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="請輸入電子郵件"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    密碼
                  </label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="請輸入密碼"
                  />
                </div>

                <button
                  type="submit"
                  className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
                >
                  調試登入
                </button>
              </form>
              
              {/* 調試按鈕 */}
              <div className="mt-6 space-y-2">
                <button
                  onClick={checkAuth}
                  className="w-full py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors"
                >
                  檢查認證狀態
                </button>
                
                <button
                  onClick={testDashboard}
                  className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors"
                >
                  測試跳轉儀表板
                </button>
                
                <button
                  onClick={clearLogs}
                  className="w-full py-2 px-4 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors"
                >
                  清除日誌
                </button>
              </div>
              
              {/* 當前路由信息 */}
              <div className="mt-6 p-3 bg-gray-50 rounded-lg">
                <h3 className="font-semibold text-sm mb-2">當前路由信息:</h3>
                <div className="text-xs space-y-1">
                  <p><strong>路徑:</strong> {router.asPath}</p>
                  <p><strong>查詢:</strong> {JSON.stringify(router.query)}</p>
                  <p><strong>重定向:</strong> {router.query.redirect || '無'}</p>
                </div>
              </div>
            </div>

            {/* 日誌顯示 */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">調試日誌</h2>
              
              <div className="bg-black text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {logs.length === 0 ? (
                  <p className="text-gray-500">等待日誌...</p>
                ) : (
                  logs.map((log, index) => (
                    <div key={index} className="mb-1">
                      {log}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
          
          {/* 返回連結 */}
          <div className="mt-8 text-center">
            <a href="/auth/login" className="text-blue-600 hover:text-blue-500 mr-4">
              返回正式登入頁面
            </a>
            <a href="/" className="text-gray-600 hover:text-gray-500">
              返回首頁
            </a>
          </div>
        </div>
      </div>
    </>
  );
}
