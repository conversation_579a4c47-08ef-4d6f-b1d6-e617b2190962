{"name": "member-system-backend", "version": "1.0.0", "description": "會員系統後端服務", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --coverage", "test:unit": "cross-env NODE_ENV=test jest tests/unit", "test:integration": "cross-env NODE_ENV=test jest tests/integration", "test:e2e": "cross-env NODE_ENV=test jest tests/e2e", "test:ci": "cross-env NODE_ENV=test jest --ci --coverage --watchAll=false", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix", "build": "echo 'Build completed'", "pretest": "echo 'Setting up test environment'", "posttest": "echo 'Test completed'"}, "keywords": ["member", "subscription", "payment", "nodejs", "express"], "author": "Member System Team", "license": "MIT", "dependencies": {"bcrypt": "^6.0.0", "compression": "^1.8.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "decimal.js": "^10.5.0", "dotenv": "^16.5.0", "ecpay_aio_nodejs": "^1.2.2", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "paypal-rest-sdk": "^1.8.1", "pg": "^8.16.2", "qrcode": "^1.5.4", "redis": "^5.5.6", "speakeasy": "^2.0.0", "stripe": "^18.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.7.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/jest": "^30.0.0", "cross-env": "^7.0.3", "eslint": "^9.29.0", "jest": "^30.0.3", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}