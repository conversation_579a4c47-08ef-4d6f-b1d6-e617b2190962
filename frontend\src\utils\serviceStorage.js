// 服務數據本地存儲管理
// 使用 localStorage 實現數據持久化

const STORAGE_KEY = 'memberservice_services';

// 默認服務數據
const defaultServices = [
  {
    id: 1,
    name: 'AI 文字生成服務',
    description: '強大的 AI 文字生成工具，支援多種語言和格式',
    price: 299,
    billingCycle: 'monthly',
    category: 'AI 服務',
    features: ['無限文字生成', '多語言支援', '模板庫', '24/7 客服'],
    status: 'active',
    popular: true,
    subscribers: 1250,
    revenue: 373750,
    maxUsers: 1,
    apiLimit: 50000,
    storageLimit: '10GB',
    supportLevel: 'premium',
    createdAt: '2025-01-15',
    updatedAt: '2025-06-20'
  },
  {
    id: 2,
    name: '圖片處理服務',
    description: '專業的圖片編輯和處理工具',
    price: 199,
    billingCycle: 'monthly',
    category: '媒體處理',
    features: ['批量處理', '格式轉換', '智能壓縮', '雲端存儲'],
    status: 'active',
    popular: false,
    subscribers: 890,
    revenue: 177110,
    maxUsers: 3,
    apiLimit: 1000,
    storageLimit: '50GB',
    supportLevel: 'standard',
    createdAt: '2025-02-01',
    updatedAt: '2025-06-18'
  },
  {
    id: 3,
    name: '數據分析服務',
    description: '深度數據分析和可視化工具',
    price: 499,
    billingCycle: 'monthly',
    category: '數據分析',
    features: ['實時分析', '自定義報表', '數據可視化', 'API 接入'],
    status: 'inactive',
    popular: false,
    subscribers: 320,
    revenue: 159680,
    maxUsers: 5,
    apiLimit: 100,
    storageLimit: '100GB',
    supportLevel: 'premium',
    createdAt: '2025-03-10',
    updatedAt: '2025-06-15'
  },
  {
    id: 4,
    name: '語音轉文字服務',
    description: '高精度語音識別和轉換服務',
    price: 149,
    billingCycle: 'monthly',
    category: 'AI 服務',
    features: ['多語言識別', '實時轉換', '批量處理', '高精度識別'],
    status: 'active',
    popular: false,
    subscribers: 560,
    revenue: 83440,
    maxUsers: 1,
    apiLimit: 10000,
    storageLimit: '5GB',
    supportLevel: 'basic',
    createdAt: '2025-04-05',
    updatedAt: '2025-06-22'
  },
  {
    id: 5,
    name: '雲端存儲服務',
    description: '安全可靠的雲端存儲解決方案',
    price: 99,
    billingCycle: 'monthly',
    category: '存儲服務',
    features: ['無限存儲', '自動備份', '版本控制', '分享功能'],
    status: 'active',
    popular: true,
    subscribers: 2100,
    revenue: 207900,
    maxUsers: 10,
    apiLimit: 0,
    storageLimit: '無限',
    supportLevel: 'standard',
    createdAt: '2025-01-20',
    updatedAt: '2025-06-25'
  },
  {
    id: 6,
    name: '企業級 API 服務',
    description: '為企業提供的高級 API 接入服務',
    price: 999,
    billingCycle: 'monthly',
    category: '企業服務',
    features: ['無限 API 調用', '專屬支援', '自定義功能', 'SLA 保證'],
    status: 'active',
    popular: false,
    subscribers: 85,
    revenue: 84915,
    maxUsers: 50,
    apiLimit: 0,
    storageLimit: '1TB',
    supportLevel: 'enterprise',
    createdAt: '2025-05-01',
    updatedAt: '2025-06-24'
  }
];

// 獲取所有服務
export const getServices = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      return JSON.parse(stored);
    } else {
      // 首次使用，初始化默認數據
      saveServices(defaultServices);
      return defaultServices;
    }
  } catch (error) {
    console.error('讀取服務數據失敗:', error);
    return defaultServices;
  }
};

// 保存所有服務
export const saveServices = (services) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(services));
    return true;
  } catch (error) {
    console.error('保存服務數據失敗:', error);
    return false;
  }
};

// 獲取單個服務
export const getServiceById = (id) => {
  const services = getServices();
  return services.find(service => service.id === parseInt(id));
};

// 新增服務
export const addService = (serviceData) => {
  try {
    const services = getServices();
    const newService = {
      id: Math.max(...services.map(s => s.id), 0) + 1,
      ...serviceData,
      subscribers: 0,
      revenue: 0,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    };
    
    const updatedServices = [...services, newService];
    saveServices(updatedServices);
    return newService;
  } catch (error) {
    console.error('新增服務失敗:', error);
    throw error;
  }
};

// 更新服務
export const updateService = (id, serviceData) => {
  try {
    const services = getServices();
    const index = services.findIndex(service => service.id === parseInt(id));
    
    if (index === -1) {
      throw new Error('服務不存在');
    }
    
    services[index] = {
      ...services[index],
      ...serviceData,
      updatedAt: new Date().toISOString().split('T')[0]
    };
    
    saveServices(services);
    return services[index];
  } catch (error) {
    console.error('更新服務失敗:', error);
    throw error;
  }
};

// 刪除服務
export const deleteService = (id) => {
  try {
    const services = getServices();
    const filteredServices = services.filter(service => service.id !== parseInt(id));
    
    if (filteredServices.length === services.length) {
      throw new Error('服務不存在');
    }
    
    saveServices(filteredServices);
    return true;
  } catch (error) {
    console.error('刪除服務失敗:', error);
    throw error;
  }
};

// 批量更新服務狀態
export const batchUpdateStatus = (serviceIds, status) => {
  try {
    const services = getServices();
    const updatedServices = services.map(service => {
      if (serviceIds.includes(service.id)) {
        return {
          ...service,
          status: status,
          updatedAt: new Date().toISOString().split('T')[0]
        };
      }
      return service;
    });
    
    saveServices(updatedServices);
    return updatedServices.filter(service => serviceIds.includes(service.id));
  } catch (error) {
    console.error('批量更新失敗:', error);
    throw error;
  }
};

// 批量刪除服務
export const batchDeleteServices = (serviceIds) => {
  try {
    const services = getServices();
    const filteredServices = services.filter(service => !serviceIds.includes(service.id));
    saveServices(filteredServices);
    return true;
  } catch (error) {
    console.error('批量刪除失敗:', error);
    throw error;
  }
};

// 重置為默認數據
export const resetToDefault = () => {
  try {
    saveServices(defaultServices);
    return defaultServices;
  } catch (error) {
    console.error('重置數據失敗:', error);
    throw error;
  }
};

// 導出數據
export const exportData = (format = 'json') => {
  try {
    const services = getServices();
    const exportData = {
      exportDate: new Date().toISOString(),
      totalServices: services.length,
      services: services
    };

    let content, mimeType, fileName;

    if (format === 'csv') {
      const csvHeaders = 'ID,名稱,描述,價格,計費週期,類別,狀態,訂閱數,收入,創建時間,更新時間\n';
      const csvRows = services.map(service => 
        `${service.id},"${service.name}","${service.description}",${service.price},${service.billingCycle},"${service.category}",${service.status},${service.subscribers},${service.revenue},${service.createdAt},${service.updatedAt}`
      ).join('\n');
      content = csvHeaders + csvRows;
      mimeType = 'text/csv';
      fileName = `services_${new Date().toISOString().split('T')[0]}.csv`;
    } else {
      content = JSON.stringify(exportData, null, 2);
      mimeType = 'application/json';
      fileName = `services_${new Date().toISOString().split('T')[0]}.json`;
    }

    return { content, mimeType, fileName };
  } catch (error) {
    console.error('導出數據失敗:', error);
    throw error;
  }
};

// 導入數據
export const importData = (jsonData) => {
  try {
    const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
    
    if (data.services && Array.isArray(data.services)) {
      saveServices(data.services);
      return data.services;
    } else {
      throw new Error('無效的數據格式');
    }
  } catch (error) {
    console.error('導入數據失敗:', error);
    throw error;
  }
};

// 獲取統計數據
export const getStatistics = () => {
  try {
    const services = getServices();
    return {
      total: services.length,
      active: services.filter(s => s.status === 'active').length,
      inactive: services.filter(s => s.status === 'inactive').length,
      draft: services.filter(s => s.status === 'draft').length,
      totalSubscribers: services.reduce((sum, s) => sum + s.subscribers, 0),
      totalRevenue: services.reduce((sum, s) => sum + s.revenue, 0),
      avgPrice: services.reduce((sum, s) => sum + s.price, 0) / services.length,
      categories: [...new Set(services.map(s => s.category))],
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('獲取統計數據失敗:', error);
    return null;
  }
};
