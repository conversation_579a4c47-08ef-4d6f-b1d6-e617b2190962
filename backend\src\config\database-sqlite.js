const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// SQLite 資料庫配置
const dbPath = 'C:\\Users\\<USER>\\member\\frontend\\lib\\database\\database.sqlite';

console.log('SQLite 資料庫路徑:', dbPath);
console.log('SQLite 資料庫絕對路徑:', path.resolve(dbPath));

// 確保資料庫目錄存在
const ensureDatabaseDirectory = () => {
  const dbDir = path.dirname(dbPath);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
    console.log('📁 創建資料庫目錄:', dbDir);
  }
};

// 創建 SQLite 連接
const createConnection = () => {
  ensureDatabaseDirectory();
  
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('❌ SQLite 連接失敗:', err.message);
        reject(err);
      } else {
        console.log('✅ SQLite 連接成功:', dbPath);
        resolve(db);
      }
    });
  });
};

// 單例模式的資料庫連接
let dbInstance = null;

const getDatabase = async () => {
  if (!dbInstance) {
    dbInstance = await createConnection();
  }
  return dbInstance;
};

// Promise 化的資料庫操作
const dbRun = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
};

const dbGet = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

const dbAll = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// 關閉資料庫連接
const closeDatabase = () => {
  return new Promise((resolve) => {
    if (dbInstance) {
      dbInstance.close((err) => {
        if (err) {
          console.error('❌ 關閉資料庫失敗:', err.message);
        } else {
          console.log('✅ 資料庫連接已關閉');
        }
        dbInstance = null;
        resolve();
      });
    } else {
      resolve();
    }
  });
};

module.exports = {
  getDatabase,
  dbRun,
  dbGet,
  dbAll,
  closeDatabase
};
