import { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function TestLoginSimple() {
  const router = useRouter();
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const testLogin = async (email, password, apiEndpoint = '/api/auth/test-login') => {
    setIsLoading(true);
    setResult(`測試登錄: ${email} / ${password}\nAPI: ${apiEndpoint}\n`);

    try {
      console.log('發送登錄請求:', { email, password, apiEndpoint });

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      const data = await response.json();
      console.log('Response data:', data);
      
      setResult(prev => prev + `\n回應狀態: ${response.status}\n`);
      setResult(prev => prev + `回應內容: ${JSON.stringify(data, null, 2)}\n`);

      if (data.success) {
        setResult(prev => prev + `\n✅ 登錄成功！\n`);
        setResult(prev => prev + `Token: ${data.token ? '已獲取' : '未獲取'}\n`);
        setResult(prev => prev + `User: ${data.user ? data.user.name : '未獲取'}\n`);
        
        // 保存到 localStorage
        if (data.token && data.user) {
          localStorage.setItem('token', data.token);
          localStorage.setItem('user', JSON.stringify(data.user));
          setResult(prev => prev + `\n💾 已保存到 localStorage\n`);
          
          // 驗證保存
          const savedToken = localStorage.getItem('token');
          const savedUser = localStorage.getItem('user');
          setResult(prev => prev + `驗證 Token: ${savedToken ? '✅ 存在' : '❌ 不存在'}\n`);
          setResult(prev => prev + `驗證 User: ${savedUser ? '✅ 存在' : '❌ 不存在'}\n`);
        }
      } else {
        setResult(prev => prev + `\n❌ 登錄失敗: ${data.error}\n`);
      }
    } catch (error) {
      console.error('請求失敗:', error);
      setResult(prev => prev + `\n❌ 請求失敗: ${error.message}\n`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearStorage = () => {
    localStorage.clear();
    setResult(prev => prev + '\n🗑️ 已清除 localStorage\n');
  };

  const checkStorage = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    setResult(prev => prev + '\n🔍 檢查 localStorage:\n');
    setResult(prev => prev + `Token: ${token ? '✅ 存在 (' + token.length + ' 字符)' : '❌ 不存在'}\n`);
    setResult(prev => prev + `User: ${user ? '✅ 存在' : '❌ 不存在'}\n`);
    
    if (user) {
      try {
        const userData = JSON.parse(user);
        setResult(prev => prev + `用戶名: ${userData.name}\n`);
        setResult(prev => prev + `Email: ${userData.email}\n`);
        setResult(prev => prev + `角色: ${userData.role}\n`);
      } catch (error) {
        setResult(prev => prev + `❌ 用戶數據解析失敗: ${error.message}\n`);
      }
    }
  };

  const goToDashboard = () => {
    router.push('/dashboard-simple');
  };

  return (
    <>
      <Head>
        <title>簡化登錄測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔑 簡化登錄測試</h1>
              <p className="text-gray-600 mt-2">使用簡化的 API 測試登錄功能</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 測試按鈕 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試帳號 (簡化 API)</h2>
                
                <div className="space-y-4">
                  <button
                    onClick={() => testLogin('<EMAIL>', 'password123')}
                    disabled={isLoading}
                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 text-left"
                  >
                    <div className="font-medium">管理員帳號 (新)</div>
                    <div className="text-sm opacity-90"><EMAIL> / password123</div>
                  </button>

                  <button
                    onClick={() => testLogin('<EMAIL>', 'password')}
                    disabled={isLoading}
                    className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 text-left"
                  >
                    <div className="font-medium">管理員帳號 (舊)</div>
                    <div className="text-sm opacity-90"><EMAIL> / password</div>
                  </button>

                  <button
                    onClick={() => testLogin('<EMAIL>', 'password')}
                    disabled={isLoading}
                    className="w-full px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 text-left"
                  >
                    <div className="font-medium">一般用戶</div>
                    <div className="text-sm opacity-90"><EMAIL> / password</div>
                  </button>

                  <button
                    onClick={() => testLogin('<EMAIL>', 'wrongpass')}
                    disabled={isLoading}
                    className="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 text-left"
                  >
                    <div className="font-medium">錯誤憑證 (測試)</div>
                    <div className="text-sm opacity-90"><EMAIL> / wrongpass</div>
                  </button>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mt-6 mb-4">測試原始 API</h3>
                
                <div className="space-y-2">
                  <button
                    onClick={() => testLogin('<EMAIL>', 'password123', '/api/auth/login')}
                    disabled={isLoading}
                    className="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 text-left"
                  >
                    <div className="font-medium">原始 API - 管理員</div>
                    <div className="text-sm opacity-90"><EMAIL> / password123</div>
                  </button>

                  <button
                    onClick={() => testLogin('<EMAIL>', 'password', '/api/auth/login')}
                    disabled={isLoading}
                    className="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 text-left"
                  >
                    <div className="font-medium">原始 API - 舊管理員</div>
                    <div className="text-sm opacity-90"><EMAIL> / password</div>
                  </button>
                </div>

                <div className="mt-6 space-y-2">
                  <button
                    onClick={checkStorage}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    🔍 檢查 localStorage
                  </button>
                  
                  <button
                    onClick={clearStorage}
                    className="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
                  >
                    🗑️ 清除 localStorage
                  </button>

                  <button
                    onClick={goToDashboard}
                    className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                  >
                    🏠 前往儀表板
                  </button>
                </div>
              </div>

              {/* 測試結果 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試結果</h2>
                
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                  {result ? (
                    <pre className="whitespace-pre-wrap">{result}</pre>
                  ) : (
                    <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                  )}
                </div>

                <button
                  onClick={() => setResult('')}
                  className="mt-2 w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🗑️ 清除結果
                </button>
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📋 測試說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>簡化 API:</strong> 使用硬編碼驗證，不依賴 bcrypt 和 JWT</div>
                <div><strong>原始 API:</strong> 使用完整的 bcrypt 密碼驗證和 JWT token</div>
                <div><strong>目標:</strong> 找出哪個 API 可以正常工作</div>
                <div><strong>成功標準:</strong> 獲得 token 和 user 數據，並成功保存到 localStorage</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
