import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function DebugServices() {
  const router = useRouter();
  const [apiServices, setApiServices] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [subscriptions, setSubscriptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  // 模擬訂閱數據
  const mockSubscriptions = [
    {
      id: 1,
      name: 'AI 文字生成服務',
      status: 'active',
      nextBilling: '2025-07-26',
      usage: { used: 2500, limit: 50000, unit: '次調用' }
    }
  ];

  useEffect(() => {
    setSubscriptions(mockSubscriptions);
    addLog('初始化', '設置模擬訂閱數據');
  }, []);

  const addLog = (action, message, data = null) => {
    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toLocaleTimeString(),
      action,
      message,
      data: data ? JSON.stringify(data, null, 2) : null
    };
    setLogs(prev => [logEntry, ...prev]);
    console.log(`[${action}] ${message}`, data);
  };

  const loadApiServices = async () => {
    try {
      setIsLoading(true);
      addLog('API 調用', '開始載入所有服務');
      
      const response = await fetch('/api/services');
      const result = await response.json();
      
      addLog('API 響應', `狀態: ${response.status}`, result);
      
      if (result.success) {
        setApiServices(result.data);
        addLog('數據載入', `成功載入 ${result.data.length} 個服務`, result.data);
        return result.data;
      } else {
        addLog('錯誤', 'API 調用失敗', result.error);
        return [];
      }
    } catch (error) {
      addLog('錯誤', 'API 調用異常', error.message);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const filterAvailableServices = (allServices, subscriptions) => {
    addLog('過濾開始', '開始過濾可用服務');
    
    // 過濾啟用的服務
    const activeServices = allServices.filter(service => service.status === 'active');
    addLog('啟用過濾', `找到 ${activeServices.length} 個啟用服務`, activeServices.map(s => s.name));
    
    // 獲取已訂閱的服務 ID
    const subscribedIds = subscriptions.map(sub => sub.id);
    addLog('訂閱檢查', `已訂閱服務 ID: ${subscribedIds.join(', ')}`);
    
    // 排除已訂閱的服務
    const availableServices = activeServices.filter(service => !subscribedIds.includes(service.id));
    addLog('最終過濾', `可用服務 ${availableServices.length} 個`, availableServices.map(s => s.name));
    
    return availableServices;
  };

  const loadAvailableServices = async () => {
    const allServices = await loadApiServices();
    const available = filterAvailableServices(allServices, subscriptions);
    setAvailableServices(available);
  };

  const createTestService = async () => {
    try {
      const testService = {
        name: `測試服務 ${new Date().getTime()}`,
        description: '這是一個測試服務，用於驗證同步功能',
        price: 99,
        billingCycle: 'monthly',
        category: '測試服務',
        features: ['測試功能1', '測試功能2'],
        status: 'active',
        popular: false,
        maxUsers: 1,
        apiLimit: 1000,
        storageLimit: '1GB',
        supportLevel: 'basic'
      };

      addLog('創建服務', '開始創建測試服務', testService);

      const response = await fetch('/api/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testService),
      });

      const result = await response.json();
      
      addLog('創建結果', `狀態: ${response.status}`, result);
      
      if (result.success) {
        alert(`✅ 測試服務創建成功：${result.data.name}`);
        // 重新載入服務
        await loadAvailableServices();
      } else {
        alert(`❌ 測試服務創建失敗：${result.error}`);
      }
    } catch (error) {
      addLog('創建錯誤', '創建測試服務失敗', error.message);
      alert('❌ 創建測試服務失敗');
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <>
      <Head>
        <title>服務調試頁面 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">服務調試頁面</h1>
            
            {/* 操作按鈕 */}
            <div className="mb-8 flex flex-wrap gap-4">
              <button
                onClick={loadAvailableServices}
                disabled={isLoading}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isLoading ? '載入中...' : '🔄 載入可用服務'}
              </button>
              <button
                onClick={createTestService}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                ➕ 創建測試服務
              </button>
              <button
                onClick={() => router.push('/admin/services')}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                🛠️ 管理員端
              </button>
              <button
                onClick={() => router.push('/subscriptions')}
                className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                👤 用戶端
              </button>
              <button
                onClick={clearLogs}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                🗑️ 清除日誌
              </button>
            </div>

            {/* 數據顯示 */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
              {/* API 服務 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  API 服務 ({apiServices.length})
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                  {apiServices.map((service) => (
                    <div key={service.id} className="mb-2 p-2 bg-white rounded border">
                      <div className="font-medium">{service.name}</div>
                      <div className="text-sm text-gray-500">
                        ID: {service.id} | 狀態: {service.status} | 價格: NT$ {service.price}
                      </div>
                    </div>
                  ))}
                  {apiServices.length === 0 && (
                    <div className="text-gray-500 text-center py-4">尚未載入數據</div>
                  )}
                </div>
              </div>

              {/* 可用服務 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  可用服務 ({availableServices.length})
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                  {availableServices.map((service) => (
                    <div key={service.id} className="mb-2 p-2 bg-white rounded border">
                      <div className="font-medium">{service.name}</div>
                      <div className="text-sm text-gray-500">
                        ID: {service.id} | 價格: NT$ {service.price}
                      </div>
                    </div>
                  ))}
                  {availableServices.length === 0 && (
                    <div className="text-gray-500 text-center py-4">沒有可用服務</div>
                  )}
                </div>
              </div>

              {/* 已訂閱服務 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  已訂閱服務 ({subscriptions.length})
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                  {subscriptions.map((service) => (
                    <div key={service.id} className="mb-2 p-2 bg-white rounded border">
                      <div className="font-medium">{service.name}</div>
                      <div className="text-sm text-gray-500">
                        ID: {service.id} | 狀態: {service.status}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 調試日誌 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">調試日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                {logs.map((log) => (
                  <div key={log.id} className="mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500">[{log.timestamp}]</span>
                      <span className="text-yellow-400">{log.action}:</span>
                      <span>{log.message}</span>
                    </div>
                    {log.data && (
                      <pre className="text-xs text-gray-400 ml-4 mt-1 whitespace-pre-wrap">
                        {log.data}
                      </pre>
                    )}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">尚無日誌記錄</div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-3">調試說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>目的：</strong>調試為什麼新增的服務沒有出現在用戶端的可用服務中。</p>
                <p><strong>步驟：</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>點擊「載入可用服務」查看 API 調用過程</li>
                  <li>檢查「API 服務」是否包含所有服務（包括新增的）</li>
                  <li>檢查「可用服務」是否正確過濾</li>
                  <li>查看調試日誌了解詳細過程</li>
                  <li>如果需要，創建測試服務進行驗證</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
