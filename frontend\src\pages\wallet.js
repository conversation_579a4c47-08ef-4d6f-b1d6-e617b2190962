import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function Wallet() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [walletData, setWalletData] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showRechargeModal, setShowRechargeModal] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState([]);

  // 移除模擬數據，只使用真實的資料庫數據

  useEffect(() => {
    // 檢查認證
    if (typeof window === 'undefined') return;

    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/login-fix-center');
      return;
    }

    // 載入用戶數據
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    // 載入真實錢包數據
    loadWalletData();
    loadPaymentMethods();
  }, [router]);

  const loadPaymentMethods = async () => {
    try {
      const response = await fetch('/api/wallet/payment-methods');
      const result = await response.json();

      if (result.success) {
        setPaymentMethods(result.data.payment_methods);
      }
    } catch (error) {
      console.error('載入支付方式失敗:', error);
    }
  };

  const handleRecharge = async (amount, paymentMethodId) => {
    try {
      const token = localStorage.getItem('token');

      const response = await fetch('/api/wallet/recharge', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount: amount,
          payment_method_id: paymentMethodId,
          description: '錢包充值'
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('充值成功！');
        setShowRechargeModal(false);
        loadWalletData(); // 重新載入數據
      } else {
        alert('充值失敗: ' + result.error);
      }
    } catch (error) {
      console.error('充值失敗:', error);
      alert('充值失敗: ' + error.message);
    }
  };

  const loadWalletData = async () => {
    try {
      const token = localStorage.getItem('token');

      // 載入錢包餘額
      const balanceResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const balanceResult = await balanceResponse.json();
      if (!balanceResult.success) {
        if (balanceResult.error.includes('認證失效') || balanceResult.error.includes('請先登入')) {
          // 認證失效，跳轉到錢包登入修復頁面
          router.push('/wallet-login-fix');
          return;
        }
        if (balanceResult.error.includes('no such column: wallet_balance')) {
          // wallet_balance 欄位不存在，跳轉到修復頁面
          alert('資料庫缺少 wallet_balance 欄位，正在跳轉到修復頁面...');
          router.push('/add-wallet-balance-column');
          return;
        }
        throw new Error(balanceResult.error);
      }

      // 載入交易記錄
      const transactionsResponse = await fetch('/api/wallet/transactions?limit=10', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const transactionsResult = await transactionsResponse.json();
      if (!transactionsResult.success) {
        if (transactionsResult.error.includes('認證失效') || transactionsResult.error.includes('請先登入')) {
          // 認證失效，跳轉到錢包登入修復頁面
          router.push('/wallet-login-fix');
          return;
        }
        throw new Error(transactionsResult.error);
      }

      // 轉換數據格式以適配現有 UI
      const adaptedWalletData = {
        balance: transactionsResult.data.stats.current_balance,
        frozenAmount: 0.00, // 暫時設為 0
        availableBalance: transactionsResult.data.stats.current_balance,
        totalIncome: transactionsResult.data.stats.total_recharge + transactionsResult.data.stats.total_refund,
        totalExpense: transactionsResult.data.stats.total_payment + transactionsResult.data.stats.total_withdraw,
        monthlyIncome: 0, // 需要額外計算
        monthlyExpense: 0  // 需要額外計算
      };

      // 轉換交易記錄格式
      const adaptedTransactions = transactionsResult.data.transactions.map(transaction => ({
        id: `TXN-${transaction.id}`,
        type: transaction.transaction_type === 'recharge' || transaction.transaction_type === 'refund' ? 'income' : 'expense',
        category: transaction.transaction_type,
        amount: transaction.amount,
        description: transaction.description,
        status: transaction.status,
        date: transaction.created_at,
        paymentMethod: transaction.payment_method || 'wallet',
        reference: transaction.payment_reference || `REF-${transaction.id}`
      }));

      setWalletData(adaptedWalletData);
      setTransactions(adaptedTransactions);

    } catch (error) {
      console.error('載入錢包數據失敗:', error);
      // 不使用模擬數據，顯示錯誤狀態
      setWalletData(null);
      setTransactions([]);
      alert('載入錢包數據失敗: ' + error.message + '\n請使用修復工具或檢查資料庫連接');
    } finally {
      setIsLoading(false);
    }
  };

  const getTransactionIcon = (type, category) => {
    if (type === 'income') {
      if (category === 'recharge') return '💳';
      if (category === 'refund') return '↩️';
      return '💰';
    } else {
      if (category === 'subscription') return '📱';
      if (category === 'withdrawal') return '🏦';
      return '💸';
    }
  };

  const getTransactionColor = (type) => {
    return type === 'income' ? 'text-green-600' : 'text-red-600';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'processing': return '處理中';
      case 'failed': return '失敗';
      default: return '未知';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const filteredTransactions = transactions.filter(txn => {
    if (activeTab === 'income') return txn.type === 'income';
    if (activeTab === 'expense') return txn.type === 'expense';
    return true;
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入錢包數據中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>錢包管理 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/minimal-dashboard')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回儀表板
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">錢包管理</h1>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/wallet-init')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                >
                  🚀 初始化
                </button>
                <button
                  onClick={() => router.push('/wallet-login-fix')}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
                >
                  🔧 修復登入
                </button>
                <button
                  onClick={() => setShowRechargeModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                >
                  💳 充值
                </button>
                <span className="text-sm text-gray-600">歡迎，{user?.name}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 錢包概覽 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl shadow-xl p-8 text-white"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center md:text-left">
                  <h2 className="text-lg font-medium text-blue-100 mb-2">總餘額</h2>
                  <p className="text-4xl font-bold mb-2">
                    {walletData ? (
                      `NT$ ${walletData.balance.toLocaleString()}`
                    ) : isLoading ? (
                      <span className="text-3xl">載入中...</span>
                    ) : (
                      <span className="text-3xl text-red-300">載入失敗</span>
                    )}
                  </p>
                  <p className="text-blue-200 text-sm">
                    可用餘額: {walletData ? (
                      `NT$ ${walletData.availableBalance.toLocaleString()}`
                    ) : isLoading ? (
                      '載入中...'
                    ) : (
                      '載入失敗'
                    )}
                  </p>
                </div>
                
                <div className="text-center">
                  <h3 className="text-lg font-medium text-blue-100 mb-2">本月收入</h3>
                  <p className="text-2xl font-bold text-green-300">
                    +NT$ {walletData ? walletData.monthlyIncome.toLocaleString() : '載入中...'}
                  </p>
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-medium text-blue-100 mb-2">本月支出</h3>
                  <p className="text-2xl font-bold text-red-300">
                    -NT$ {walletData ? walletData.monthlyExpense.toLocaleString() : '載入中...'}
                  </p>
                </div>
              </div>
              
              {/* 快速操作按鈕 */}
              <div className="flex flex-wrap gap-4 mt-8">
                <button
                  onClick={() => router.push('/wallet/recharge')}
                  className="px-6 py-3 bg-white text-blue-600 rounded-lg font-medium hover:bg-blue-50 transition-colors"
                >
                  💳 充值
                </button>
                <button
                  onClick={() => router.push('/wallet/withdraw')}
                  className="px-6 py-3 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-400 transition-colors"
                >
                  🏦 提現
                </button>
                <button
                  onClick={() => router.push('/wallet/transfer')}
                  className="px-6 py-3 bg-purple-500 text-white rounded-lg font-medium hover:bg-purple-400 transition-colors"
                >
                  💸 轉帳
                </button>
              </div>
            </motion.div>

            {/* 統計卡片 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            >
              <div className="bg-white rounded-xl shadow-lg p-6 text-center">
                <div className="text-2xl font-bold text-green-600 mb-2">
                  {walletData ? (
                    `NT$ ${walletData.totalIncome.toLocaleString()}`
                  ) : isLoading ? (
                    <div className="animate-pulse bg-gray-200 h-8 w-24 mx-auto rounded"></div>
                  ) : (
                    'NT$ 0'
                  )}
                </div>
                <div className="text-sm text-gray-500">總收入</div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6 text-center">
                <div className="text-2xl font-bold text-red-600 mb-2">
                  {walletData ? (
                    `NT$ ${walletData.totalExpense.toLocaleString()}`
                  ) : isLoading ? (
                    <div className="animate-pulse bg-gray-200 h-8 w-24 mx-auto rounded"></div>
                  ) : (
                    'NT$ 0'
                  )}
                </div>
                <div className="text-sm text-gray-500">總支出</div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6 text-center">
                <div className="text-2xl font-bold text-orange-600 mb-2">
                  {walletData ? (
                    `NT$ ${walletData.frozenAmount.toLocaleString()}`
                  ) : isLoading ? (
                    <div className="animate-pulse bg-gray-200 h-8 w-24 mx-auto rounded"></div>
                  ) : (
                    'NT$ 0'
                  )}
                </div>
                <div className="text-sm text-gray-500">凍結金額</div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6 text-center">
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  {transactions.length}
                </div>
                <div className="text-sm text-gray-500">交易筆數</div>
              </div>
            </motion.div>

            {/* 標籤切換 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'overview'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  全部交易
                </button>
                <button
                  onClick={() => setActiveTab('income')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'income'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  收入記錄
                </button>
                <button
                  onClick={() => setActiveTab('expense')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'expense'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  支出記錄
                </button>
              </div>
            </div>

            {/* 交易記錄 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-6">交易記錄</h3>

              <div className="space-y-4">
                {filteredTransactions.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <svg className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暫無交易記錄</h3>
                    <p className="text-gray-500">您的交易記錄將顯示在這裡</p>
                  </div>
                ) : (
                  filteredTransactions.map((transaction, index) => (
                    <motion.div
                      key={transaction.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="text-2xl">
                          {getTransactionIcon(transaction.type, transaction.category)}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{transaction.description}</h4>
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            <span>{formatDate(transaction.date)}</span>
                            <span>•</span>
                            <span>{transaction.reference}</span>
                          </div>
                        </div>
                      </div>

                      <div className="text-right">
                        <div className={`text-lg font-semibold ${getTransactionColor(transaction.type)}`}>
                          {transaction.type === 'income' ? '+' : '-'}NT$ {transaction.amount.toLocaleString()}
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(transaction.status)}`}>
                          {getStatusText(transaction.status)}
                        </span>
                      </div>
                    </motion.div>
                  ))
                )}
              </div>
            </motion.div>
          </div>
        </main>
      </div>

      {/* 充值彈窗 */}
      {showRechargeModal && (
        <RechargeModal
          paymentMethods={paymentMethods}
          onClose={() => setShowRechargeModal(false)}
          onRecharge={handleRecharge}
        />
      )}
    </>
  );
}

// 充值彈窗組件
function RechargeModal({ paymentMethods, onClose, onRecharge }) {
  const [amount, setAmount] = useState('');
  const [selectedMethod, setSelectedMethod] = useState(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!amount || !selectedMethod) {
      alert('請輸入充值金額並選擇支付方式');
      return;
    }
    onRecharge(parseFloat(amount), selectedMethod.id);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold">錢包充值</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              充值金額
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="請輸入充值金額"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              選擇支付方式
            </label>
            <div className="space-y-2">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  onClick={() => setSelectedMethod(method)}
                  className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                    selectedMethod?.id === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">{method.icon_url}</span>
                    <div>
                      <h4 className="font-medium">{method.name}</h4>
                      <p className="text-xs text-gray-500">{method.fee_display}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex space-x-4 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              確認充值
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
