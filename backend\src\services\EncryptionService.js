const crypto = require('crypto');
const bcrypt = require('bcrypt');

class EncryptionService {
  constructor() {
    // AES-256-GCM 加密配置
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32; // 256 bits
    this.ivLength = 16;  // 128 bits
    this.tagLength = 16; // 128 bits
    this.saltRounds = 12; // bcrypt salt rounds

    // 從環境變數獲取主密鑰
    this.masterKey = this.deriveMasterKey(process.env.ENCRYPTION_KEY || 'default-key-change-in-production');
  }

  // 從密碼派生主密鑰
  deriveMasterKey(password) {
    const salt = process.env.ENCRYPTION_SALT || 'default-salt-change-in-production';
    return crypto.pbkdf2Sync(password, salt, 100000, this.keyLength, 'sha256');
  }

  // 生成隨機密鑰
  generateKey() {
    return crypto.randomBytes(this.keyLength);
  }

  // 生成隨機 IV
  generateIV() {
    return crypto.randomBytes(this.ivLength);
  }

  // AES-256-GCM 加密
  encrypt(plaintext, key = null) {
    try {
      const encryptionKey = key || this.masterKey;
      const iv = this.generateIV();
      const cipher = crypto.createCipherGCM(this.algorithm, encryptionKey, iv);

      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      const tag = cipher.getAuthTag();

      // 返回 IV + Tag + 加密數據的組合
      return {
        encrypted: iv.toString('hex') + tag.toString('hex') + encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex')
      };
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('加密失敗');
    }
  }

  // AES-256-GCM 解密
  decrypt(encryptedData, key = null) {
    try {
      const encryptionKey = key || this.masterKey;

      // 提取 IV、Tag 和加密數據
      const iv = Buffer.from(encryptedData.slice(0, this.ivLength * 2), 'hex');
      const tag = Buffer.from(encryptedData.slice(this.ivLength * 2, (this.ivLength + this.tagLength) * 2), 'hex');
      const encrypted = encryptedData.slice((this.ivLength + this.tagLength) * 2);

      const decipher = crypto.createDecipherGCM(this.algorithm, encryptionKey, iv);
      decipher.setAuthTag(tag);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('解密失敗');
    }
  }

  // 密碼雜湊 (bcrypt)
  async hashPassword(password) {
    try {
      return await bcrypt.hash(password, this.saltRounds);
    } catch (error) {
      console.error('Password hashing error:', error);
      throw new Error('密碼雜湊失敗');
    }
  }

  // 密碼驗證
  async verifyPassword(password, hashedPassword) {
    try {
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      console.error('Password verification error:', error);
      throw new Error('密碼驗證失敗');
    }
  }

  // 敏感資料加密 (用於 PII 資料)
  encryptPII(data) {
    if (!data) return null;

    try {
      const result = this.encrypt(JSON.stringify(data));
      return result.encrypted;
    } catch (error) {
      console.error('PII encryption error:', error);
      throw new Error('敏感資料加密失敗');
    }
  }

  // 敏感資料解密
  decryptPII(encryptedData) {
    if (!encryptedData) return null;

    try {
      const decrypted = this.decrypt(encryptedData);
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('PII decryption error:', error);
      throw new Error('敏感資料解密失敗');
    }
  }

  // 信用卡號碼遮罩
  maskCreditCard(cardNumber) {
    if (!cardNumber) return '';

    const cleaned = cardNumber.replace(/\D/g, '');
    if (cleaned.length < 4) return '*'.repeat(cleaned.length);

    const lastFour = cleaned.slice(-4);
    const masked = '*'.repeat(cleaned.length - 4) + lastFour;

    return masked;
  }

  // 手機號碼遮罩
  maskPhoneNumber(phoneNumber) {
    if (!phoneNumber) return '';

    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.length < 4) return '*'.repeat(cleaned.length);

    const lastThree = cleaned.slice(-3);
    const firstThree = cleaned.slice(0, 3);
    const masked = firstThree + '*'.repeat(cleaned.length - 6) + lastThree;

    return masked;
  }

  // 郵件地址遮罩
  maskEmail(email) {
    if (!email) return '';

    const [localPart, domain] = email.split('@');
    if (!domain) return email;

    if (localPart.length <= 2) {
      return '*'.repeat(localPart.length) + '@' + domain;
    }

    const maskedLocal = localPart[0] + '*'.repeat(localPart.length - 2) + localPart.slice(-1);
    return maskedLocal + '@' + domain;
  }

  // 生成安全的隨機 Token
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  // 生成 TOTP 密鑰
  generateTOTPSecret() {
    const buffer = crypto.randomBytes(20);
    // 手動實現 base32 編碼
    const base32chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let result = '';
    let bits = 0;
    let value = 0;

    for (let i = 0; i < buffer.length; i++) {
      value = (value << 8) | buffer[i];
      bits += 8;

      while (bits >= 5) {
        result += base32chars[(value >>> (bits - 5)) & 31];
        bits -= 5;
      }
    }

    if (bits > 0) {
      result += base32chars[(value << (5 - bits)) & 31];
    }

    return result;
  }

  // HMAC 簽名
  createHMAC(data, secret = null) {
    const key = secret || process.env.HMAC_SECRET || 'default-hmac-secret';
    return crypto.createHmac('sha256', key).update(data).digest('hex');
  }

  // 驗證 HMAC 簽名
  verifyHMAC(data, signature, secret = null) {
    const expectedSignature = this.createHMAC(data, secret);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }

  // 安全的字串比較 (防止時序攻擊)
  secureCompare(a, b) {
    if (a.length !== b.length) return false;
    return crypto.timingSafeEqual(Buffer.from(a), Buffer.from(b));
  }

  // 生成 CSP Nonce
  generateCSPNonce() {
    return crypto.randomBytes(16).toString('base64');
  }
}

module.exports = new EncryptionService();