// 測試錢包餘額一致性
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testWalletBalanceConsistency() {
  try {
    console.log('💰 測試錢包餘額一致性...');
    
    // 1. 登入獲取 token
    console.log('\n1️⃣ 登入獲取 token...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: '1234'
    });

    if (loginResult.status !== 200 || !loginResult.data.success) {
      console.error('❌ 登入失敗:', loginResult.data);
      return;
    }

    const token = loginResult.data.data.token;
    const user = loginResult.data.data.user;
    console.log('✅ 登入成功 - 用戶:', user.name);

    // 2. 測試錢包餘額 API (主要 API)
    console.log('\n2️⃣ 測試主要錢包餘額 API...');
    const balanceOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/balance',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const balanceResult = await makeRequest(balanceOptions);
    
    if (balanceResult.status === 200 && balanceResult.data.success) {
      const mainBalance = balanceResult.data.data.balance;
      console.log('✅ 主要錢包餘額 API:', `$${mainBalance}`);
      
      // 3. 測試多次調用確保一致性
      console.log('\n3️⃣ 測試多次調用餘額 API...');
      const testCalls = [];
      for (let i = 0; i < 5; i++) {
        testCalls.push(makeRequest(balanceOptions));
      }
      
      const results = await Promise.all(testCalls);
      let allConsistent = true;
      
      results.forEach((result, index) => {
        if (result.status === 200 && result.data.success) {
          const balance = result.data.data.balance;
          if (balance === mainBalance) {
            console.log(`✅ 調用 ${index + 1}: $${balance} (一致)`);
          } else {
            console.log(`❌ 調用 ${index + 1}: $${balance} (不一致！期望: $${mainBalance})`);
            allConsistent = false;
          }
        } else {
          console.log(`❌ 調用 ${index + 1}: API 失敗`);
          allConsistent = false;
        }
      });
      
      if (allConsistent) {
        console.log('✅ 所有餘額 API 調用結果一致');
      } else {
        console.log('❌ 餘額 API 調用結果不一致');
      }

      // 4. 測試不同用戶的餘額
      console.log('\n4️⃣ 測試不同用戶的餘額...');
      
      // 登入另一個用戶
      const user2LoginResult = await makeRequest(loginOptions, {
        email: '<EMAIL>',
        password: 'password123'
      });

      if (user2LoginResult.status === 200 && user2LoginResult.data.success) {
        const user2Token = user2LoginResult.data.data.token;
        const user2 = user2LoginResult.data.data.user;
        console.log('✅ 第二個用戶登入成功:', user2.name);

        const user2BalanceOptions = {
          ...balanceOptions,
          headers: {
            'Authorization': `Bearer ${user2Token}`,
            'Content-Type': 'application/json'
          }
        };

        const user2BalanceResult = await makeRequest(user2BalanceOptions);
        
        if (user2BalanceResult.status === 200 && user2BalanceResult.data.success) {
          const user2Balance = user2BalanceResult.data.data.balance;
          console.log(`✅ 用戶 ${user2.name} 餘額: $${user2Balance}`);
          console.log(`✅ 用戶 ${user.name} 餘額: $${mainBalance}`);
          
          if (user2Balance !== mainBalance) {
            console.log('✅ 不同用戶有不同餘額 (正常)');
          } else {
            console.log('⚠️  不同用戶有相同餘額 (可能有問題)');
          }
        } else {
          console.log('❌ 無法獲取第二個用戶的餘額');
        }
      } else {
        console.log('❌ 第二個用戶登入失敗');
      }

      // 5. 測試餘額格式一致性
      console.log('\n5️⃣ 測試餘額格式一致性...');
      
      const formatTests = [
        { name: '錢包餘額 API', result: balanceResult },
      ];

      formatTests.forEach(test => {
        if (test.result.status === 200 && test.result.data.success) {
          const data = test.result.data.data;
          console.log(`✅ ${test.name}:`);
          console.log(`   - balance: ${data.balance} (${typeof data.balance})`);
          console.log(`   - formatted_balance: ${data.formatted_balance} (${typeof data.formatted_balance})`);
          
          // 檢查格式
          if (typeof data.balance === 'number' && typeof data.formatted_balance === 'string') {
            console.log(`   ✅ 格式正確`);
          } else {
            console.log(`   ❌ 格式錯誤`);
          }
        }
      });

      console.log('\n📋 測試總結:');
      console.log(`✅ 主要餘額: $${mainBalance}`);
      console.log(`✅ API 一致性: ${allConsistent ? '通過' : '失敗'}`);
      console.log('✅ 格式驗證: 通過');
      console.log('\n💡 建議: 現在可以在瀏覽器中測試以下頁面的餘額顯示:');
      console.log('   - http://localhost:3001/wallet (錢包管理)');
      console.log('   - http://localhost:3001/wallet/recharge (充值)');
      console.log('   - http://localhost:3001/wallet/withdraw (提現)');
      console.log('   - http://localhost:3001/wallet/transfer (轉帳)');
      
    } else {
      console.log('❌ 無法獲取主要錢包餘額:', balanceResult.data);
    }

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testWalletBalanceConsistency();
