import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function SystemStatus() {
  const [status, setStatus] = useState({
    frontend: 'checking',
    apis: {},
    database: 'checking',
    auth: 'checking',
    timestamp: new Date().toISOString()
  });
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  // 檢查 API 狀態
  const checkAPI = async (name, url) => {
    try {
      addLog(`檢查 ${name} API...`, 'info');
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const isSuccess = response.ok;
      addLog(`${name} API: ${isSuccess ? '✅ 正常' : '❌ 異常'}`, isSuccess ? 'success' : 'error');
      
      return {
        status: isSuccess ? 'online' : 'error',
        statusCode: response.status,
        responseTime: Date.now()
      };
    } catch (error) {
      addLog(`${name} API: ❌ 連接失敗 - ${error.message}`, 'error');
      return {
        status: 'offline',
        error: error.message
      };
    }
  };

  // 檢查認證功能
  const checkAuth = async () => {
    try {
      addLog('檢查認證系統...', 'info');
      
      // 測試登入 API
      const response = await fetch('/api/auth/login-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
      });

      const result = await response.json();
      
      // 預期會失敗，但 API 應該正常響應
      if (response.status === 401 && result.error) {
        addLog('認證系統: ✅ 正常運行', 'success');
        return 'online';
      } else {
        addLog('認證系統: ⚠️ 響應異常', 'warning');
        return 'warning';
      }
    } catch (error) {
      addLog(`認證系統: ❌ 錯誤 - ${error.message}`, 'error');
      return 'offline';
    }
  };

  // 檢查資料庫連接
  const checkDatabase = async () => {
    try {
      addLog('檢查資料庫連接...', 'info');
      
      // 通過服務 API 間接檢查資料庫
      const response = await fetch('/api/services/simple');
      const result = await response.json();
      
      if (result.success && Array.isArray(result.data)) {
        addLog(`資料庫: ✅ 正常，載入 ${result.data.length} 個服務`, 'success');
        return 'online';
      } else {
        addLog('資料庫: ❌ 數據異常', 'error');
        return 'error';
      }
    } catch (error) {
      addLog(`資料庫: ❌ 連接失敗 - ${error.message}`, 'error');
      return 'offline';
    }
  };

  // 執行系統檢查
  const runSystemCheck = async () => {
    addLog('🚀 開始系統狀態檢查...', 'info');
    
    setStatus(prev => ({
      ...prev,
      frontend: 'online', // 如果能載入這個頁面，前端就是正常的
      timestamp: new Date().toISOString()
    }));

    // 檢查各個 API
    const apiChecks = await Promise.all([
      checkAPI('服務管理', '/api/services/simple'),
      checkAPI('密碼工具', '/api/tools/hash-password').catch(() => ({ status: 'offline', error: 'API 不存在' })),
      checkAPI('驗證工具', '/api/tools/verify-password').catch(() => ({ status: 'offline', error: 'API 不存在' }))
    ]);

    // 檢查認證和資料庫
    const [authStatus, dbStatus] = await Promise.all([
      checkAuth(),
      checkDatabase()
    ]);

    setStatus(prev => ({
      ...prev,
      apis: {
        services: apiChecks[0],
        hashPassword: apiChecks[1],
        verifyPassword: apiChecks[2]
      },
      auth: authStatus,
      database: dbStatus,
      timestamp: new Date().toISOString()
    }));

    addLog('🎉 系統狀態檢查完成', 'success');
  };

  useEffect(() => {
    runSystemCheck();
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'text-green-600 bg-green-50';
      case 'offline': return 'text-red-600 bg-red-50';
      case 'error': return 'text-red-600 bg-red-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'checking': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'online': return '✅';
      case 'offline': return '❌';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'checking': return '🔄';
      default: return '❓';
    }
  };

  return (
    <>
      <Head>
        <title>系統狀態 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">會員管理系統狀態</h1>
                <p className="text-gray-600 mt-2">最後檢查: {new Date(status.timestamp).toLocaleString()}</p>
              </div>
              <button
                onClick={runSystemCheck}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
              >
                🔄 重新檢查
              </button>
            </div>

            {/* 系統概覽 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className={`p-6 rounded-lg border-2 ${getStatusColor(status.frontend)}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">前端系統</h3>
                    <p className="text-sm opacity-75">Next.js 應用</p>
                  </div>
                  <span className="text-2xl">{getStatusIcon(status.frontend)}</span>
                </div>
              </div>

              <div className={`p-6 rounded-lg border-2 ${getStatusColor(status.auth)}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">認證系統</h3>
                    <p className="text-sm opacity-75">JWT + bcrypt</p>
                  </div>
                  <span className="text-2xl">{getStatusIcon(status.auth)}</span>
                </div>
              </div>

              <div className={`p-6 rounded-lg border-2 ${getStatusColor(status.database)}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">資料庫</h3>
                    <p className="text-sm opacity-75">SQLite</p>
                  </div>
                  <span className="text-2xl">{getStatusIcon(status.database)}</span>
                </div>
              </div>

              <div className={`p-6 rounded-lg border-2 ${
                Object.values(status.apis).every(api => api?.status === 'online') ? 'text-green-600 bg-green-50' : 'text-yellow-600 bg-yellow-50'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">API 服務</h3>
                    <p className="text-sm opacity-75">RESTful APIs</p>
                  </div>
                  <span className="text-2xl">
                    {Object.values(status.apis).every(api => api?.status === 'online') ? '✅' : '⚠️'}
                  </span>
                </div>
              </div>
            </div>

            {/* API 詳細狀態 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">API 服務詳情</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(status.apis).map(([name, api]) => (
                  <div key={name} className={`p-4 rounded-lg border ${getStatusColor(api?.status || 'checking')}`}>
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{name}</h3>
                      <span>{getStatusIcon(api?.status || 'checking')}</span>
                    </div>
                    <div className="text-sm space-y-1">
                      <div>狀態: {api?.status || 'checking'}</div>
                      {api?.statusCode && <div>HTTP: {api.statusCode}</div>}
                      {api?.error && <div className="text-red-600">錯誤: {api.error}</div>}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 快速操作 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">快速操作</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a
                  href="/auth/login-test"
                  className="p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">🔑</div>
                    <div className="font-medium">測試登入</div>
                  </div>
                </a>

                <a
                  href="/api-path-test"
                  className="p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">🧪</div>
                    <div className="font-medium">API 測試</div>
                  </div>
                </a>

                <a
                  href="/dashboard-simple"
                  className="p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">🏠</div>
                    <div className="font-medium">儀表板</div>
                  </div>
                </a>

                <a
                  href="/admin/database"
                  className="p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors"
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">🗄️</div>
                    <div className="font-medium">資料庫管理</div>
                  </div>
                </a>
              </div>
            </div>

            {/* 系統日誌 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">系統檢查日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
                {logs.map((log) => (
                  <div key={log.id} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'success' ? 'text-green-400' : 
                    log.type === 'warning' ? 'text-yellow-400' :
                    'text-blue-400'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">系統檢查中...</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
