// 簡化版登入 API - 不依賴複雜的模組載入
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// 模擬會員數據（實際應該從資料庫載入）
const mockMembers = [
  {
    id: 1,
    email: '<EMAIL>',
    name: '系統管理員',
    password_hash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: 1234
    role: 'admin',
    status: 'active',
    phone: '0912345678',
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    email: '<EMAIL>',
    name: '一般用戶',
    password_hash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: 1234
    role: 'user',
    status: 'active',
    phone: '0987654321',
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z'
  }
];

// 模擬訂閱數據
const mockSubscriptions = [
  {
    id: 1,
    member_id: 1,
    service_id: 1,
    service_name: 'AI 文字生成服務',
    status: 'active',
    subscribed_at: '2025-01-01T00:00:00.000Z',
    next_billing_date: '2025-02-01',
    price: 299,
    billing_cycle: 'monthly'
  },
  {
    id: 2,
    member_id: 1,
    service_id: 2,
    service_name: '圖片處理服務',
    status: 'active',
    subscribed_at: '2025-01-01T00:00:00.000Z',
    next_billing_date: '2025-02-01',
    price: 199,
    billing_cycle: 'monthly'
  }
];

export default async function handler(req, res) {
  // 只允許 POST 請求
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允許'
    });
  }

  try {
    const { email, password } = req.body;

    // 驗證必要欄位
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email 和密碼為必填欄位'
      });
    }

    console.log('簡化登入 API: 查找用戶', email);

    // 查找會員
    const member = mockMembers.find(m => m.email.toLowerCase() === email.toLowerCase());
    
    if (!member) {
      return res.status(401).json({
        success: false,
        error: '帳號或密碼錯誤'
      });
    }

    console.log('簡化登入 API: 找到用戶', member.name);

    // 驗證密碼 - 臨時使用明文比較（僅用於測試）
    console.log('簡化登入 API: 比較密碼', {
      input: password,
      stored: member.password_hash,
      directMatch: password === '1234'
    });

    // 臨時解決方案：直接比較明文密碼
    const isValidPassword = password === '1234';

    if (!isValidPassword) {
      console.log('簡化登入 API: 密碼驗證失敗');
      return res.status(401).json({
        success: false,
        error: '帳號或密碼錯誤'
      });
    }

    console.log('簡化登入 API: 密碼驗證成功');

    // 檢查帳號狀態
    if (member.status !== 'active') {
      return res.status(403).json({
        success: false,
        error: '帳號已被停用'
      });
    }

    // 生成 JWT Token
    const tokenPayload = {
      userId: member.id,
      email: member.email,
      role: member.role
    };

    const token = jwt.sign(tokenPayload, JWT_SECRET, { 
      expiresIn: '7d',
      issuer: 'member-system'
    });

    console.log('簡化登入 API: Token 生成成功');

    // 獲取用戶的訂閱
    const userSubscriptions = mockSubscriptions.filter(sub => sub.member_id === member.id);

    // 準備返回的用戶數據（不包含密碼）
    const userData = {
      id: member.id,
      email: member.email,
      name: member.name,
      role: member.role,
      status: member.status,
      phone: member.phone,
      avatar_url: member.avatar_url,
      created_at: member.created_at,
      updated_at: member.updated_at
    };

    console.log('簡化登入 API: 登入成功', userData.name);

    // 返回成功響應
    res.status(200).json({
      success: true,
      message: '登入成功',
      data: {
        token,
        user: userData,
        subscriptions: userSubscriptions
      }
    });

  } catch (error) {
    console.error('簡化登入 API 錯誤:', error);
    
    res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: error.message
    });
  }
}
