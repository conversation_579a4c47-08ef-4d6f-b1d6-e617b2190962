import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';

export default function SimpleTest() {
  const router = useRouter();
  const [message, setMessage] = useState('');

  const testRouterPush = async () => {
    setMessage('測試 router.push...');
    try {
      await router.push('/minimal-dashboard');
      setMessage('router.push 成功！');
    } catch (error) {
      setMessage('router.push 失敗: ' + error.message);
      console.error('Router error:', error);
    }
  };

  const testWindowLocation = () => {
    setMessage('測試 window.location...');
    window.location.href = '/minimal-dashboard';
  };

  const testLink = () => {
    setMessage('請點擊下面的 Link 組件');
  };

  const saveTestData = () => {
    localStorage.setItem('token', 'simple-test-token');
    localStorage.setItem('user', JSON.stringify({
      name: '簡單測試用戶',
      email: '<EMAIL>'
    }));
    setMessage('測試數據已保存到 localStorage');
  };

  const clearTestData = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setMessage('localStorage 已清除');
  };

  const checkLocalStorage = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    setMessage(`Token: ${token || '無'}, User: ${user || '無'}`);
  };

  return (
    <>
      <Head>
        <title>簡單測試 - 路由跳轉</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">簡單路由測試</h1>
          
          <div className="bg-white p-6 rounded-lg shadow space-y-4">
            <h2 className="text-xl font-semibold mb-4">測試按鈕</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={testRouterPush}
                className="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                測試 router.push('/minimal-dashboard')
              </button>
              
              <button
                onClick={testWindowLocation}
                className="p-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                測試 window.location
              </button>
              
              <button
                onClick={saveTestData}
                className="p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
              >
                保存測試數據
              </button>
              
              <button
                onClick={clearTestData}
                className="p-3 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                清除測試數據
              </button>
              
              <button
                onClick={checkLocalStorage}
                className="p-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                檢查 localStorage
              </button>
              
              <button
                onClick={testLink}
                className="p-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
              >
                測試 Link 組件
              </button>
            </div>
            
            {/* Link 組件測試 */}
            <div className="border-t pt-4">
              <h3 className="font-semibold mb-2">Link 組件測試:</h3>
              <div className="space-x-4">
                <Link href="/minimal-dashboard" className="inline-block p-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                  Link 到 Dashboard
                </Link>
                <Link href="/test-dashboard" className="inline-block p-2 bg-green-500 text-white rounded hover:bg-green-600">
                  Link 到 Test Dashboard
                </Link>
                <Link href="/" className="inline-block p-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                  Link 到首頁
                </Link>
              </div>
            </div>
            
            {/* 直接 HTML 連結測試 */}
            <div className="border-t pt-4">
              <h3 className="font-semibold mb-2">HTML 連結測試:</h3>
              <div className="space-x-4">
                <a href="/minimal-dashboard" className="inline-block p-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                  HTML 連結到 Dashboard
                </a>
                <a href="/test-dashboard" className="inline-block p-2 bg-green-500 text-white rounded hover:bg-green-600">
                  HTML 連結到 Test Dashboard
                </a>
              </div>
            </div>
            
            {/* 訊息顯示 */}
            {message && (
              <div className="border-t pt-4">
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-blue-700">{message}</p>
                </div>
              </div>
            )}
            
            {/* 當前路由資訊 */}
            <div className="border-t pt-4">
              <h3 className="font-semibold mb-2">當前路由資訊:</h3>
              <div className="text-sm space-y-1">
                <p><strong>路徑:</strong> {router.asPath}</p>
                <p><strong>路由:</strong> {router.route}</p>
                <p><strong>查詢:</strong> {JSON.stringify(router.query)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
