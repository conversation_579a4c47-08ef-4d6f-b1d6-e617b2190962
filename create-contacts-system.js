// 創建完整的聯絡人管理系統
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'frontend', 'lib', 'database', 'database.sqlite');
const db = new sqlite3.Database(dbPath);

console.log('🚀 開始創建聯絡人管理系統...');

async function createContactsSystem() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      
      // 1. 創建常用聯絡人表
      console.log('\n1️⃣ 創建常用聯絡人表...');
      db.run(`
        CREATE TABLE IF NOT EXISTS frequent_contacts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          contact_name VARCHAR(100) NOT NULL,
          contact_email VARCHAR(255) NOT NULL,
          contact_phone VARCHAR(20),
          avatar_emoji VARCHAR(10) DEFAULT '👤',
          nickname VARCHAR(50),
          notes TEXT,
          is_favorite BOOLEAN DEFAULT FALSE,
          transfer_count INTEGER DEFAULT 0,
          last_transfer_at DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES members(id) ON DELETE CASCADE,
          UNIQUE(user_id, contact_email)
        )
      `, (err) => {
        if (err) {
          console.error('❌ 創建 frequent_contacts 表失敗:', err);
          reject(err);
          return;
        }
        console.log('✅ frequent_contacts 表創建成功');
        
        // 2. 創建聯絡人群組表
        console.log('\n2️⃣ 創建聯絡人群組表...');
        db.run(`
          CREATE TABLE IF NOT EXISTS contact_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            group_name VARCHAR(50) NOT NULL,
            group_color VARCHAR(7) DEFAULT '#3B82F6',
            group_icon VARCHAR(10) DEFAULT '📁',
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES members(id) ON DELETE CASCADE,
            UNIQUE(user_id, group_name)
          )
        `, (err) => {
          if (err) {
            console.error('❌ 創建 contact_groups 表失敗:', err);
            reject(err);
            return;
          }
          console.log('✅ contact_groups 表創建成功');
          
          // 3. 創建聯絡人群組關聯表
          console.log('\n3️⃣ 創建聯絡人群組關聯表...');
          db.run(`
            CREATE TABLE IF NOT EXISTS contact_group_members (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              group_id INTEGER NOT NULL,
              contact_id INTEGER NOT NULL,
              added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (group_id) REFERENCES contact_groups(id) ON DELETE CASCADE,
              FOREIGN KEY (contact_id) REFERENCES frequent_contacts(id) ON DELETE CASCADE,
              UNIQUE(group_id, contact_id)
            )
          `, (err) => {
            if (err) {
              console.error('❌ 創建 contact_group_members 表失敗:', err);
              reject(err);
              return;
            }
            console.log('✅ contact_group_members 表創建成功');
            
            // 4. 創建轉帳統計視圖（基於現有的 wallet_transactions 表結構）
            console.log('\n4️⃣ 創建轉帳統計視圖...');
            db.run(`
              CREATE VIEW IF NOT EXISTS transfer_statistics AS
              SELECT
                wt.member_id as user_id,
                CASE
                  WHEN wt.description LIKE '%轉帳給%' THEN
                    SUBSTR(wt.description, INSTR(wt.description, '(') + 1, INSTR(wt.description, ')') - INSTR(wt.description, '(') - 1)
                  ELSE NULL
                END as contact_email,
                CASE
                  WHEN wt.description LIKE '%轉帳給%' THEN
                    SUBSTR(wt.description, 4, INSTR(wt.description, '(') - 4)
                  ELSE NULL
                END as contact_name,
                COUNT(*) as transfer_count,
                SUM(ABS(wt.amount)) as total_amount,
                MAX(wt.created_at) as last_transfer_at,
                AVG(ABS(wt.amount)) as avg_amount
              FROM wallet_transactions wt
              WHERE wt.transaction_type = 'transfer_out' AND wt.description LIKE '%轉帳給%'
              GROUP BY wt.member_id, contact_email, contact_name
              HAVING contact_email IS NOT NULL
              ORDER BY transfer_count DESC, last_transfer_at DESC
            `, (err) => {
              if (err) {
                console.error('❌ 創建 transfer_statistics 視圖失敗:', err);
                reject(err);
                return;
              }
              console.log('✅ transfer_statistics 視圖創建成功');
              
              // 5. 初始化示例數據
              initializeSampleData();
            });
          });
        });
      });
      
      function initializeSampleData() {
        console.log('\n5️⃣ 初始化示例聯絡人數據...');
        
        // 獲取管理員用戶 ID
        db.get('SELECT id FROM members WHERE email = ?', ['<EMAIL>'], (err, admin) => {
          if (err || !admin) {
            console.error('❌ 找不到管理員用戶');
            reject(err);
            return;
          }
          
          const adminId = admin.id;
          
          // 插入示例聯絡人
          const sampleContacts = [
            {
              name: '一般用戶',
              email: '<EMAIL>',
              phone: '0912-345-678',
              avatar: '👨‍💼',
              nickname: '小王',
              notes: '公司同事，經常需要轉帳報銷'
            },
            {
              name: '測試用戶123',
              email: '<EMAIL>',
              phone: '0987-654-321',
              avatar: '👩‍💻',
              nickname: '小李',
              notes: '開發團隊成員'
            },
            {
              name: '系統管理員',
              email: '<EMAIL>',
              phone: '0955-123-456',
              avatar: '👨‍🔧',
              nickname: '技術主管',
              notes: '系統維護相關費用'
            }
          ];
          
          let insertedCount = 0;
          
          sampleContacts.forEach((contact, index) => {
            db.run(`
              INSERT OR IGNORE INTO frequent_contacts 
              (user_id, contact_name, contact_email, contact_phone, avatar_emoji, nickname, notes, is_favorite)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              adminId, 
              contact.name, 
              contact.email, 
              contact.phone, 
              contact.avatar, 
              contact.nickname, 
              contact.notes,
              index === 0 ? 1 : 0  // 第一個設為最愛
            ], function(err) {
              if (err) {
                console.error(`❌ 插入聯絡人 ${contact.name} 失敗:`, err);
              } else if (this.changes > 0) {
                console.log(`✅ 插入聯絡人: ${contact.name} (${contact.email})`);
              }
              
              insertedCount++;
              if (insertedCount === sampleContacts.length) {
                createDefaultGroups(adminId);
              }
            });
          });
        });
      }
      
      function createDefaultGroups(adminId) {
        console.log('\n6️⃣ 創建預設聯絡人群組...');
        
        const defaultGroups = [
          {
            name: '工作夥伴',
            color: '#3B82F6',
            icon: '💼',
            description: '工作相關的聯絡人'
          },
          {
            name: '朋友',
            color: '#10B981',
            icon: '👥',
            description: '個人朋友聯絡人'
          },
          {
            name: '家人',
            color: '#F59E0B',
            icon: '👨‍👩‍👧‍👦',
            description: '家庭成員'
          }
        ];
        
        let groupInsertedCount = 0;
        
        defaultGroups.forEach(group => {
          db.run(`
            INSERT OR IGNORE INTO contact_groups 
            (user_id, group_name, group_color, group_icon, description)
            VALUES (?, ?, ?, ?, ?)
          `, [adminId, group.name, group.color, group.icon, group.description], function(err) {
            if (err) {
              console.error(`❌ 創建群組 ${group.name} 失敗:`, err);
            } else if (this.changes > 0) {
              console.log(`✅ 創建群組: ${group.name}`);
            }
            
            groupInsertedCount++;
            if (groupInsertedCount === defaultGroups.length) {
              finishSetup();
            }
          });
        });
      }
      
      function finishSetup() {
        console.log('\n📊 檢查創建結果...');
        
        // 檢查聯絡人數量
        db.get('SELECT COUNT(*) as count FROM frequent_contacts', [], (err, result) => {
          if (err) {
            console.error('❌ 檢查聯絡人數量失敗:', err);
          } else {
            console.log(`✅ 總聯絡人數: ${result.count}`);
          }
          
          // 檢查群組數量
          db.get('SELECT COUNT(*) as count FROM contact_groups', [], (err2, result2) => {
            if (err2) {
              console.error('❌ 檢查群組數量失敗:', err2);
            } else {
              console.log(`✅ 總群組數: ${result2.count}`);
            }
            
            console.log('\n🎉 聯絡人管理系統創建完成！');
            console.log('\n📋 創建的功能:');
            console.log('   ✅ 常用聯絡人表 (frequent_contacts)');
            console.log('   ✅ 聯絡人群組表 (contact_groups)');
            console.log('   ✅ 群組成員關聯表 (contact_group_members)');
            console.log('   ✅ 轉帳統計視圖 (transfer_statistics)');
            console.log('   ✅ 示例聯絡人數據');
            console.log('   ✅ 預設聯絡人群組');
            
            db.close();
            resolve();
          });
        });
      }
    });
  });
}

createContactsSystem().catch(console.error);
