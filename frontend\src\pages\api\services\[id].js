// Next.js API 路由 - 單個服務管理
// 處理 /api/services/[id] 的請求

import fs from 'fs';
import path from 'path';

const DATA_FILE = path.join(process.cwd(), 'data', 'services.json');

// 讀取服務數據
const readServicesData = () => {
  try {
    if (fs.existsSync(DATA_FILE)) {
      const data = fs.readFileSync(DATA_FILE, 'utf8');
      return JSON.parse(data);
    } else {
      throw new Error('服務數據文件不存在');
    }
  } catch (error) {
    console.error('讀取服務數據失敗:', error);
    throw new Error('無法讀取服務數據');
  }
};

// 寫入服務數據
const writeServicesData = (data) => {
  try {
    // 更新元數據
    const services = data.services || [];
    data.metadata = {
      totalServices: services.length,
      activeServices: services.filter(s => s.status === 'active').length,
      inactiveServices: services.filter(s => s.status === 'inactive').length,
      totalSubscribers: services.reduce((sum, s) => sum + (s.subscribers || 0), 0),
      totalRevenue: services.reduce((sum, s) => sum + (s.revenue || 0), 0),
      categories: [...new Set(services.map(s => s.category).filter(Boolean))]
    };
    
    data.lastUpdated = new Date().toISOString();
    
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2), 'utf8');
  } catch (error) {
    console.error('寫入服務數據失敗:', error);
    throw new Error('無法保存服務數據');
  }
};

export default function handler(req, res) {
  try {
    const { method, query } = req;
    const { id } = query;
    const serviceId = parseInt(id);

    if (isNaN(serviceId)) {
      return res.status(400).json({
        success: false,
        error: '無效的服務 ID'
      });
    }

    switch (method) {
      case 'GET':
        // 獲取單個服務
        const data = readServicesData();
        const service = data.services.find(s => s.id === serviceId);
        
        if (!service) {
          return res.status(404).json({
            success: false,
            error: '服務不存在'
          });
        }

        res.status(200).json({
          success: true,
          data: service
        });
        break;

      case 'PUT':
        // 更新單個服務
        const { body } = req;
        const updateData = readServicesData();
        const serviceIndex = updateData.services.findIndex(s => s.id === serviceId);
        
        if (serviceIndex === -1) {
          return res.status(404).json({
            success: false,
            error: '服務不存在'
          });
        }

        // 驗證必要欄位
        if (body.name !== undefined && !body.name.trim()) {
          return res.status(400).json({
            success: false,
            error: '服務名稱不能為空'
          });
        }

        if (body.price !== undefined && (isNaN(parseFloat(body.price)) || parseFloat(body.price) <= 0)) {
          return res.status(400).json({
            success: false,
            error: '價格必須是大於 0 的數字'
          });
        }

        // 更新服務
        const updatedService = {
          ...updateData.services[serviceIndex],
          ...body,
          price: body.price !== undefined ? parseFloat(body.price) : updateData.services[serviceIndex].price,
          maxUsers: body.maxUsers !== undefined ? parseInt(body.maxUsers) || 1 : updateData.services[serviceIndex].maxUsers,
          apiLimit: body.apiLimit !== undefined ? parseInt(body.apiLimit) || 0 : updateData.services[serviceIndex].apiLimit,
          updatedAt: new Date().toISOString().split('T')[0]
        };

        updateData.services[serviceIndex] = updatedService;
        writeServicesData(updateData);

        res.status(200).json({
          success: true,
          data: updatedService,
          message: '服務更新成功'
        });
        break;

      case 'DELETE':
        // 刪除單個服務
        const deleteData = readServicesData();
        const deleteIndex = deleteData.services.findIndex(s => s.id === serviceId);
        
        if (deleteIndex === -1) {
          return res.status(404).json({
            success: false,
            error: '服務不存在'
          });
        }

        const deletedService = deleteData.services[deleteIndex];
        
        // 檢查是否有訂閱用戶
        if (deletedService.subscribers > 0) {
          // 可以選擇阻止刪除或給出警告
          // 這裡我們允許刪除但記錄警告
          console.warn(`刪除有 ${deletedService.subscribers} 個訂閱用戶的服務: ${deletedService.name}`);
        }

        deleteData.services.splice(deleteIndex, 1);
        writeServicesData(deleteData);

        res.status(200).json({
          success: true,
          data: deletedService,
          message: '服務刪除成功'
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        res.status(405).json({
          success: false,
          error: `方法 ${method} 不被允許`
        });
        break;
    }
  } catch (error) {
    console.error('API 錯誤:', error);
    res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: error.message
    });
  }
}
