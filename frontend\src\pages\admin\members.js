import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function MembersAdmin() {
  const router = useRouter();
  const [members, setMembers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [dataSource, setDataSource] = useState('');

  useEffect(() => {
    // 檢查管理員權限
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (!user.id || user.role !== 'admin') {
      router.push('/auth/login?redirect=/admin/members');
      return;
    }

    loadMembers();
  }, [router]);

  const loadMembers = async () => {
    setIsLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('請先登入');
        return;
      }

      console.log('載入會員列表...');

      const response = await fetch('/api/admin/members', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      console.log('會員列表 API 響應:', result);

      if (!response.ok || !result.success) {
        throw new Error(result.error || `載入失敗 (HTTP ${response.status})`);
      }

      setMembers(result.data || []);
      setDataSource(result.source || 'unknown');
      console.log('會員列表載入成功:', result.data?.length || 0, '個會員');

    } catch (err) {
      console.error('載入會員列表失敗:', err);
      setError(err.message || '載入會員列表失敗');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '從未';
    try {
      return new Date(dateString).toLocaleString('zh-TW');
    } catch {
      return dateString;
    }
  };

  const getStatusBadge = (status) => {
    const styles = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      suspended: 'bg-red-100 text-red-800'
    };
    
    const labels = {
      active: '活躍',
      inactive: '非活躍',
      suspended: '已停用'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${styles[status] || styles.inactive}`}>
        {labels[status] || status}
      </span>
    );
  };

  const getRoleBadge = (role) => {
    const styles = {
      admin: 'bg-purple-100 text-purple-800',
      user: 'bg-blue-100 text-blue-800'
    };
    
    const labels = {
      admin: '管理員',
      user: '一般用戶'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${styles[role] || styles.user}`}>
        {labels[role] || role}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入會員列表中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>會員管理 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">會員管理</h1>
                <p className="text-gray-600 mt-2">查看和管理所有系統會員</p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-500">
                  數據來源: {dataSource === 'database' ? '🗄️ 資料庫' : '🧪 模擬數據'}
                </div>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
                >
                  <span>🏠</span>
                  <span>回儀表板</span>
                </button>
                <button
                  onClick={loadMembers}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  🔄 重新載入
                </button>
              </div>
            </div>

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600">❌ {error}</p>
              </div>
            )}

            {/* 統計信息 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-blue-50 p-6 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{members.length}</div>
                <div className="text-blue-800">總會員數</div>
              </div>
              <div className="bg-green-50 p-6 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {members.filter(m => m.status === 'active').length}
                </div>
                <div className="text-green-800">活躍會員</div>
              </div>
              <div className="bg-purple-50 p-6 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {members.filter(m => m.role === 'admin').length}
                </div>
                <div className="text-purple-800">管理員</div>
              </div>
              <div className="bg-orange-50 p-6 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {members.filter(m => m.email_verified).length}
                </div>
                <div className="text-orange-800">已驗證 Email</div>
              </div>
            </div>

            {/* 會員列表 */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">ID</th>
                    <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">姓名</th>
                    <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">Email</th>
                    <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">角色</th>
                    <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">狀態</th>
                    <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">電話</th>
                    <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">Email驗證</th>
                    <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">最後登入</th>
                    <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">註冊時間</th>
                  </tr>
                </thead>
                <tbody>
                  {members.map((member) => (
                    <tr key={member.id} className="hover:bg-gray-50">
                      <td className="border border-gray-300 px-4 py-3 font-medium">{member.id}</td>
                      <td className="border border-gray-300 px-4 py-3">{member.name}</td>
                      <td className="border border-gray-300 px-4 py-3">
                        <div className="font-mono text-sm">{member.email}</div>
                      </td>
                      <td className="border border-gray-300 px-4 py-3">
                        {getRoleBadge(member.role)}
                      </td>
                      <td className="border border-gray-300 px-4 py-3">
                        {getStatusBadge(member.status)}
                      </td>
                      <td className="border border-gray-300 px-4 py-3">
                        {member.phone || '-'}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-center">
                        {member.email_verified ? (
                          <span className="text-green-600">✅</span>
                        ) : (
                          <span className="text-red-600">❌</span>
                        )}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-sm">
                        {formatDate(member.last_login_at)}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-sm">
                        {formatDate(member.created_at)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {members.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-500 text-lg">📭 暫無會員數據</div>
                  <p className="text-gray-400 mt-2">系統中還沒有註冊的會員</p>
                </div>
              )}
            </div>

            {/* 操作說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📋 會員列表說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>數據來源:</strong> {dataSource === 'database' ? '從 SQLite 資料庫讀取真實數據' : '使用內嵌的模擬數據'}</p>
                <p><strong>權限要求:</strong> 需要管理員權限才能查看此頁面</p>
                <p><strong>實時更新:</strong> 點擊重新載入按鈕可獲取最新數據</p>
                <p><strong>安全性:</strong> 密碼哈希等敏感信息不會顯示</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
