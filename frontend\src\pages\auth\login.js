import React, { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

export default function Login() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Login: 表單提交開始');
    console.log('Login: 表單數據:', formData);

    // 基本驗證
    if (!formData.email || !formData.password) {
      setError('請輸入電子郵件和密碼');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      console.log('Login: 開始 API 調用');

      // 調用後端登入 API
      const response = await fetch('http://localhost:3000/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password
        })
      });

      const result = await response.json();
      console.log('Login: API 響應:', result);

      if (!result.success) {
        setError(result.error || '登入失敗');
        return;
      }

      console.log('Login: API 調用完成，準備保存數據');

      // 從 API 響應中獲取數據
      const { token, user } = result.data;
      const userData = {
        ...user,
        memberSince: user.createdAt // 注意後端回傳的是 createdAt
      };

      // 同時設置 localStorage 和 cookie
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(userData));
      // 後端 API 目前不直接返回訂閱，暫時存儲空陣列
      localStorage.setItem('subscriptions', JSON.stringify([]));

      // 設置 cookie 供 middleware 使用
      document.cookie = `access_token=${token}; path=/; max-age=604800`; // 7天過期

      console.log('Login: 數據已保存');
      console.log('Login: Token:', token);
      console.log('Login: User:', userData);

      // 驗證數據是否保存成功
      const savedToken = localStorage.getItem('token');
      const savedUser = localStorage.getItem('user');
      console.log('Login: 驗證保存 - Token:', !!savedToken, 'User:', !!savedUser);

      // 處理重定向
      const redirectUrl = router.query.redirect || '/dashboard-simple';
      console.log('Login: 準備重定向到:', redirectUrl);
      console.log('Login: Router query:', router.query);

      // 嘗試跳轉
      try {
        await router.push(redirectUrl);
        console.log('Login: 跳轉成功');
      } catch (routerError) {
        console.error('Login: 路由跳轉失敗:', routerError);
        // 嘗試使用 window.location
        console.log('Login: 嘗試使用 window.location 跳轉');
        window.location.href = redirectUrl;
      }

    } catch (err) {
      console.error('Login: 登入過程出錯:', err);
      setError(err.message || '登入失敗，請檢查您的帳號密碼');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>登入 - 會員管理系統</title>
        <meta name="description" content="登入您的會員帳戶" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md w-full space-y-8"
        >
          {/* Logo 和標題 */}
          <div className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center"
            >
              <span className="text-white text-2xl font-bold">M</span>
            </motion.div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              歡迎回來
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              登入您的會員帳戶
            </p>
          </div>

          {/* 登入表單 */}
          <motion.form
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="mt-8 space-y-6 bg-white p-8 rounded-xl shadow-lg"
            onSubmit={handleSubmit}
          >
            {error && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg"
              >
                {error}
              </motion.div>
            )}

            <div className="space-y-4">
              {/* 電子郵件 */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  電子郵件
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm transition-colors"
                  placeholder="請輸入您的電子郵件"
                />
              </div>

              {/* 密碼 */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  密碼
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={formData.password}
                    onChange={handleChange}
                    className="appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm transition-colors"
                    placeholder="請輸入您的密碼"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* 記住我和忘記密碼 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  name="rememberMe"
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-900">
                  記住我
                </label>
              </div>

              <div className="text-sm">
                <Link href="/auth/forgot-password" className="font-medium text-blue-600 hover:text-blue-500 transition-colors">
                  忘記密碼？
                </Link>
              </div>
            </div>

            {/* 登入按鈕 */}
            <div>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    登入中...
                  </div>
                ) : (
                  '登入'
                )}
              </motion.button>
            </div>

            {/* 註冊連結 */}
            <div className="text-center">
              <p className="text-sm text-gray-600">
                還沒有帳戶？{' '}
                <Link href="/auth/register" className="font-medium text-blue-600 hover:text-blue-500 transition-colors">
                  立即註冊
                </Link>
              </p>
            </div>
          </motion.form>

          {/* 返回首頁 */}
          <div className="text-center">
            <Link href="/" className="text-sm text-gray-500 hover:text-gray-700 transition-colors">
              ← 返回首頁
            </Link>
          </div>
        </motion.div>
      </div>
    </>
  );
}
