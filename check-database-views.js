// 檢查數據庫視圖
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'frontend', 'lib', 'database', 'database.sqlite');

function checkDatabaseViews() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('❌ 連接數據庫失敗:', err);
        reject(err);
        return;
      }
      console.log('✅ 數據庫連接成功');
    });

    console.log('🔍 檢查數據庫視圖...');

    // 1. 查看所有視圖
    db.all("SELECT name, sql FROM sqlite_master WHERE type='view'", (err, views) => {
      if (err) {
        console.error('❌ 查詢視圖失敗:', err);
        reject(err);
        return;
      }

      console.log(`📋 找到 ${views.length} 個視圖:`);
      views.forEach((view, index) => {
        console.log(`\n${index + 1}. 視圖名稱: ${view.name}`);
        console.log(`   SQL: ${view.sql}`);
      });

      // 2. 檢查 member_services 表結構
      db.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='member_services'", (err, table) => {
        if (err) {
          console.error('❌ 查詢 member_services 表失敗:', err);
          reject(err);
          return;
        }

        if (table) {
          console.log('\n📋 member_services 表結構:');
          console.log(table.sql);
        } else {
          console.log('\n❌ member_services 表不存在');
        }

        // 3. 檢查 member_services 表的實際列
        db.all("PRAGMA table_info(member_services)", (err, columns) => {
          if (err) {
            console.error('❌ 查詢表列信息失敗:', err);
          } else {
            console.log('\n📋 member_services 表的實際列:');
            columns.forEach(col => {
              console.log(`   - ${col.name} (${col.type})`);
            });
          }

          db.close();
          resolve();
        });
      });
    });
  });
}

// 執行檢查
checkDatabaseViews()
  .then(() => {
    console.log('\n✅ 數據庫視圖檢查完成');
  })
  .catch((error) => {
    console.error('\n❌ 檢查失敗:', error);
  });
