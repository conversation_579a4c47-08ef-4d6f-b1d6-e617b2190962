import { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function QuickLoginTest() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);

  const testLogin = async (apiEndpoint, description) => {
    setIsLoading(true);
    
    try {
      console.log(`測試 ${description}...`);
      
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: '123456'
        })
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`${description} 登入成功！`);
        
        // 保存登入信息
        localStorage.setItem('token', data.data.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
        
        setResult({
          type: 'success',
          api: description,
          data: data,
          message: '登入成功！即將跳轉...'
        });
        
        // 延遲跳轉到儀表板
        setTimeout(() => {
          router.push('/dashboard-simple');
        }, 2000);
        
        return true;
      } else {
        console.log(`${description} 登入失敗:`, data.error);
        setResult({
          type: 'error',
          api: description,
          data: data,
          message: data.error
        });
        return false;
      }
    } catch (error) {
      console.error(`${description} 請求失敗:`, error);
      setResult({
        type: 'error',
        api: description,
        error: error.message,
        message: `請求失敗: ${error.message}`
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const testAllApis = async () => {
    // 按順序測試不同的 API
    const apis = [
      { endpoint: '/api/auth/login-db', name: '資料庫登入 API' },
      { endpoint: '/api/auth/login', name: '原始登入 API' },
      { endpoint: '/api/auth/login-simple', name: '簡化登入 API' }
    ];

    for (const api of apis) {
      const success = await testLogin(api.endpoint, api.name);
      if (success) {
        break; // 如果成功就停止測試
      }
      // 短暫延遲再測試下一個
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  const goToPasswordReset = () => {
    router.push('/password-reset');
  };

  const goToLoginPage = () => {
    router.push('/auth/login');
  };

  return (
    <>
      <Head>
        <title>快速登入測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-8">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900">🚀 快速登入測試</h1>
            <p className="text-gray-600 mt-2">測試 <EMAIL> 登入</p>
          </div>

          {/* 帳號信息 */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">測試帳號</h3>
            <div className="text-sm text-blue-800">
              <div><strong>Email:</strong> <EMAIL></div>
              <div><strong>密碼:</strong> 123456</div>
            </div>
          </div>

          {/* 測試按鈕 */}
          <div className="space-y-4">
            <button
              onClick={() => testLogin('/api/auth/login-db', '資料庫登入 API')}
              disabled={isLoading}
              className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              {isLoading ? '測試中...' : '🗄️ 測試資料庫登入'}
            </button>

            <button
              onClick={() => testLogin('/api/auth/login', '原始登入 API')}
              disabled={isLoading}
              className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {isLoading ? '測試中...' : '🔑 測試原始登入'}
            </button>

            <button
              onClick={testAllApis}
              disabled={isLoading}
              className="w-full px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
            >
              {isLoading ? '測試中...' : '🧪 測試所有 API'}
            </button>

            <div className="border-t pt-4">
              <button
                onClick={goToPasswordReset}
                className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                🔐 密碼重置工具
              </button>

              <button
                onClick={goToLoginPage}
                className="w-full mt-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                📝 前往登入頁面
              </button>
            </div>
          </div>

          {/* 結果顯示 */}
          {result && (
            <div className={`mt-6 p-4 rounded-lg ${
              result.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}>
              <div className={`flex items-center mb-2 ${
                result.type === 'success' ? 'text-green-900' : 'text-red-900'
              }`}>
                <span className="mr-2">
                  {result.type === 'success' ? '✅' : '❌'}
                </span>
                <span className="font-medium">{result.api}</span>
              </div>
              <div className={`text-sm ${
                result.type === 'success' ? 'text-green-700' : 'text-red-700'
              }`}>
                {result.message}
              </div>
              
              {result.type === 'success' && (
                <div className="mt-3 text-sm text-green-600">
                  <div>👤 用戶: {result.data.data.user.name}</div>
                  <div>📧 Email: {result.data.data.user.email}</div>
                  <div>🎯 角色: {result.data.data.user.role}</div>
                  <div className="mt-2 text-green-500">
                    🚀 2秒後自動跳轉到儀表板...
                  </div>
                </div>
              )}

              {result.type === 'error' && result.data && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-xs text-red-600">查看詳細錯誤</summary>
                  <pre className="mt-1 text-xs text-red-600 overflow-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          )}

          {/* 說明 */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-medium text-yellow-900 mb-2">💡 說明</h3>
            <div className="text-sm text-yellow-800 space-y-1">
              <p>• 如果登入失敗，請先使用密碼重置工具</p>
              <p>• 登入成功後會自動跳轉到儀表板</p>
              <p>• 可以測試不同的登入 API 找出問題</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
