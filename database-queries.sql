-- 會員管理系統 - 常用查詢範例
-- 版本: 1.0
-- 創建日期: 2025-01-26

-- ============================================================================
-- 1. 會員相關查詢
-- ============================================================================

-- 查詢所有活躍會員
SELECT id, name, email, role, created_at 
FROM members 
WHERE status = 'active' 
ORDER BY created_at DESC;

-- 查詢管理員用戶
SELECT id, name, email, last_login_at 
FROM members 
WHERE role = 'admin' AND status = 'active';

-- 查詢最近註冊的會員 (最近30天)
SELECT id, name, email, created_at 
FROM members 
WHERE created_at >= datetime('now', '-30 days') 
ORDER BY created_at DESC;

-- 查詢會員登入統計
SELECT 
    COUNT(*) as total_members,
    COUNT(CASE WHEN last_login_at >= datetime('now', '-7 days') THEN 1 END) as active_last_week,
    COUNT(CASE WHEN last_login_at >= datetime('now', '-30 days') THEN 1 END) as active_last_month
FROM members 
WHERE status = 'active';

-- ============================================================================
-- 2. 服務相關查詢
-- ============================================================================

-- 查詢所有可用服務
SELECT id, name, description, price, billing_cycle, category 
FROM services 
WHERE status = 'active' 
ORDER BY category, price;

-- 查詢服務訂閱統計
SELECT 
    s.id,
    s.name,
    s.price,
    COUNT(ms.id) as subscription_count,
    SUM(CASE WHEN ms.status = 'active' THEN 1 ELSE 0 END) as active_subscriptions
FROM services s
LEFT JOIN member_services ms ON s.id = ms.service_id
WHERE s.status = 'active'
GROUP BY s.id, s.name, s.price
ORDER BY subscription_count DESC;

-- 查詢最受歡迎的服務分類
SELECT 
    category,
    COUNT(*) as service_count,
    AVG(price) as avg_price,
    SUM(subscription_count) as total_subscriptions
FROM (
    SELECT 
        s.category,
        s.price,
        COUNT(ms.id) as subscription_count
    FROM services s
    LEFT JOIN member_services ms ON s.id = ms.service_id AND ms.status = 'active'
    WHERE s.status = 'active'
    GROUP BY s.id, s.category, s.price
) 
GROUP BY category
ORDER BY total_subscriptions DESC;

-- ============================================================================
-- 3. 訂閱相關查詢
-- ============================================================================

-- 查詢會員的所有訂閱
SELECT 
    m.name as member_name,
    s.name as service_name,
    ms.status,
    ms.subscribed_at,
    ms.next_billing_date,
    s.price
FROM member_services ms
JOIN members m ON ms.member_id = m.id
JOIN services s ON ms.service_id = s.id
WHERE m.id = 1  -- 替換為具體的會員 ID
ORDER BY ms.subscribed_at DESC;

-- 查詢即將到期的訂閱 (未來7天)
SELECT 
    m.name as member_name,
    m.email,
    s.name as service_name,
    ms.next_billing_date,
    s.price
FROM member_services ms
JOIN members m ON ms.member_id = m.id
JOIN services s ON ms.service_id = s.id
WHERE ms.status = 'active' 
    AND ms.next_billing_date BETWEEN date('now') AND date('now', '+7 days')
ORDER BY ms.next_billing_date;

-- 查詢已過期但未取消的訂閱
SELECT 
    m.name as member_name,
    m.email,
    s.name as service_name,
    ms.expires_at,
    ms.status
FROM member_services ms
JOIN members m ON ms.member_id = m.id
JOIN services s ON ms.service_id = s.id
WHERE ms.expires_at < datetime('now') 
    AND ms.status = 'active'
ORDER BY ms.expires_at;

-- 查詢會員訂閱數量統計
SELECT 
    m.id,
    m.name,
    m.email,
    COUNT(ms.id) as total_subscriptions,
    SUM(CASE WHEN ms.status = 'active' THEN 1 ELSE 0 END) as active_subscriptions,
    SUM(CASE WHEN ms.status = 'active' THEN s.price ELSE 0 END) as monthly_cost
FROM members m
LEFT JOIN member_services ms ON m.id = ms.member_id
LEFT JOIN services s ON ms.service_id = s.id
WHERE m.status = 'active'
GROUP BY m.id, m.name, m.email
HAVING total_subscriptions > 0
ORDER BY monthly_cost DESC;

-- ============================================================================
-- 4. 交易相關查詢
-- ============================================================================

-- 查詢會員的交易記錄
SELECT 
    t.id,
    t.transaction_type,
    t.amount,
    t.currency,
    t.status,
    t.payment_method,
    s.name as service_name,
    t.created_at
FROM transactions t
LEFT JOIN services s ON t.service_id = s.id
WHERE t.member_id = 1  -- 替換為具體的會員 ID
ORDER BY t.created_at DESC;

-- 查詢收入統計 (按月)
SELECT 
    strftime('%Y-%m', t.created_at) as month,
    COUNT(*) as transaction_count,
    SUM(CASE WHEN t.status = 'completed' THEN t.amount ELSE 0 END) as total_revenue,
    SUM(CASE WHEN t.status = 'pending' THEN t.amount ELSE 0 END) as pending_revenue,
    SUM(CASE WHEN t.status = 'failed' THEN t.amount ELSE 0 END) as failed_revenue
FROM transactions t
WHERE t.transaction_type = 'payment'
    AND t.created_at >= datetime('now', '-12 months')
GROUP BY strftime('%Y-%m', t.created_at)
ORDER BY month DESC;

-- 查詢付款方式統計
SELECT 
    payment_method,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(amount) as avg_amount
FROM transactions 
WHERE status = 'completed' 
    AND transaction_type = 'payment'
    AND created_at >= datetime('now', '-30 days')
GROUP BY payment_method
ORDER BY total_amount DESC;

-- 查詢失敗交易
SELECT 
    t.id,
    m.name as member_name,
    m.email,
    s.name as service_name,
    t.amount,
    t.payment_method,
    t.description,
    t.created_at
FROM transactions t
JOIN members m ON t.member_id = m.id
LEFT JOIN services s ON t.service_id = s.id
WHERE t.status = 'failed'
    AND t.created_at >= datetime('now', '-7 days')
ORDER BY t.created_at DESC;

-- ============================================================================
-- 5. 審計日誌查詢
-- ============================================================================

-- 查詢會員的操作記錄
SELECT 
    al.action,
    al.resource_type,
    al.resource_id,
    al.ip_address,
    al.created_at
FROM audit_logs al
WHERE al.member_id = 1  -- 替換為具體的會員 ID
ORDER BY al.created_at DESC
LIMIT 50;

-- 查詢系統操作統計
SELECT 
    action,
    COUNT(*) as action_count,
    COUNT(DISTINCT member_id) as unique_users
FROM audit_logs 
WHERE created_at >= datetime('now', '-7 days')
GROUP BY action
ORDER BY action_count DESC;

-- 查詢可疑登入活動 (同一用戶多個IP)
SELECT 
    m.name,
    m.email,
    COUNT(DISTINCT al.ip_address) as ip_count,
    GROUP_CONCAT(DISTINCT al.ip_address) as ip_addresses
FROM audit_logs al
JOIN members m ON al.member_id = m.id
WHERE al.action = 'login' 
    AND al.created_at >= datetime('now', '-7 days')
GROUP BY m.id, m.name, m.email
HAVING ip_count > 3
ORDER BY ip_count DESC;

-- ============================================================================
-- 6. 複合查詢和報表
-- ============================================================================

-- 會員價值分析 (LTV - Lifetime Value)
SELECT 
    m.id,
    m.name,
    m.email,
    m.created_at as registration_date,
    COUNT(DISTINCT ms.id) as total_subscriptions,
    SUM(CASE WHEN t.status = 'completed' THEN t.amount ELSE 0 END) as total_paid,
    AVG(s.price) as avg_service_price,
    julianday('now') - julianday(m.created_at) as days_since_registration
FROM members m
LEFT JOIN member_services ms ON m.id = ms.member_id
LEFT JOIN transactions t ON m.id = t.member_id AND t.transaction_type = 'payment'
LEFT JOIN services s ON ms.service_id = s.id
WHERE m.status = 'active'
GROUP BY m.id, m.name, m.email, m.created_at
HAVING total_paid > 0
ORDER BY total_paid DESC;

-- 服務表現分析
SELECT 
    s.id,
    s.name,
    s.category,
    s.price,
    COUNT(DISTINCT ms.member_id) as unique_subscribers,
    COUNT(ms.id) as total_subscriptions,
    SUM(CASE WHEN ms.status = 'active' THEN 1 ELSE 0 END) as active_subscriptions,
    SUM(CASE WHEN t.status = 'completed' THEN t.amount ELSE 0 END) as total_revenue,
    AVG(julianday(COALESCE(ms.expires_at, 'now')) - julianday(ms.subscribed_at)) as avg_subscription_days
FROM services s
LEFT JOIN member_services ms ON s.id = ms.service_id
LEFT JOIN transactions t ON ms.id = t.member_service_id AND t.transaction_type = 'payment'
WHERE s.status = 'active'
GROUP BY s.id, s.name, s.category, s.price
ORDER BY total_revenue DESC;

-- 月度業務報表
SELECT 
    strftime('%Y-%m', date) as month,
    new_members,
    new_subscriptions,
    cancelled_subscriptions,
    total_revenue,
    avg_revenue_per_user
FROM (
    SELECT 
        date(created_at) as date,
        COUNT(CASE WHEN table_name = 'members' THEN 1 END) as new_members,
        COUNT(CASE WHEN table_name = 'member_services' AND action = 'subscribe' THEN 1 END) as new_subscriptions,
        COUNT(CASE WHEN table_name = 'member_services' AND action = 'cancel' THEN 1 END) as cancelled_subscriptions,
        SUM(CASE WHEN table_name = 'transactions' AND status = 'completed' THEN amount ELSE 0 END) as total_revenue,
        AVG(CASE WHEN table_name = 'transactions' AND status = 'completed' THEN amount END) as avg_revenue_per_user
    FROM (
        SELECT created_at, 'members' as table_name, NULL as action, NULL as status, NULL as amount FROM members
        UNION ALL
        SELECT created_at, 'member_services' as table_name, 'subscribe' as action, status, NULL as amount FROM member_services
        UNION ALL
        SELECT created_at, 'transactions' as table_name, transaction_type as action, status, amount FROM transactions
    )
    WHERE created_at >= datetime('now', '-12 months')
    GROUP BY date(created_at)
)
GROUP BY strftime('%Y-%m', date)
ORDER BY month DESC;

-- ============================================================================
-- 7. 維護和優化查詢
-- ============================================================================

-- 檢查資料庫大小
SELECT 
    name,
    COUNT(*) as record_count,
    ROUND(SUM(LENGTH(sql)) / 1024.0, 2) as size_kb
FROM sqlite_master 
WHERE type = 'table' 
GROUP BY name;

-- 檢查索引使用情況
EXPLAIN QUERY PLAN 
SELECT m.name, s.name, ms.status 
FROM member_services ms
JOIN members m ON ms.member_id = m.id
JOIN services s ON ms.service_id = s.id
WHERE m.email = '<EMAIL>';

-- 清理過期的審計日誌 (保留90天)
DELETE FROM audit_logs 
WHERE created_at < datetime('now', '-90 days');

-- 更新統計信息
ANALYZE;

-- 重建索引
REINDEX;

-- 檢查資料庫完整性
PRAGMA integrity_check;
