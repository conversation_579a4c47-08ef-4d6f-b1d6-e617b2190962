import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

export default function Dashboard() {
  const router = useRouter();

  useEffect(() => {
    // 檢查用戶是否已登入
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    if (!token || !user) {
      // 如果未登入，跳轉到登入頁面
      router.push('/auth/login?redirect=/dashboard');
      return;
    }

    // 如果已登入，重定向到 dashboard-simple
    router.push('/dashboard-simple');
  }, [router]);

  return (
    <>
      <Head>
        <title>儀表板 - 會員管理系統</title>
      </Head>
      
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在載入儀表板...</p>
        </div>
      </div>
    </>
  );
}
