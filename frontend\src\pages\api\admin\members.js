// 會員管理 API - 查詢所有會員
const jwt = require('jsonwebtoken');
const path = require('path');
const fs = require('fs');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// 簡化的認證中間件
const authenticateAdmin = (req) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return null;
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded;
  } catch (error) {
    console.error('Token 驗證失敗:', error);
    return null;
  }
};

// 嘗試連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();
    const dbPath = path.join(process.cwd(), 'lib', 'database', 'database.sqlite');
    
    console.log('嘗試連接資料庫:', dbPath);
    
    // 檢查資料庫文件是否存在
    if (!fs.existsSync(dbPath)) {
      console.log('資料庫文件不存在，嘗試其他路徑...');
      
      // 嘗試其他可能的路徑
      const alternatePaths = [
        path.join(process.cwd(), 'database.sqlite'),
        path.join(process.cwd(), 'frontend', 'database.sqlite'),
        path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
      ];
      
      for (const altPath of alternatePaths) {
        if (fs.existsSync(altPath)) {
          console.log('找到資料庫文件:', altPath);
          return new sqlite3.Database(altPath);
        }
      }
      
      throw new Error('找不到資料庫文件');
    }
    
    return new sqlite3.Database(dbPath);
  } catch (error) {
    console.error('資料庫連接失敗:', error);
    return null;
  }
};

// 模擬會員數據 (如果資料庫不可用)
const mockMembers = [
  {
    id: 1,
    email: '<EMAIL>',
    name: '系統管理員',
    role: 'admin',
    status: 'active',
    phone: '0912345678',
    email_verified: true,
    last_login_at: '2025-01-26T10:00:00.000Z',
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-26T10:00:00.000Z'
  },
  {
    id: 2,
    email: '<EMAIL>',
    name: '一般用戶',
    role: 'user',
    status: 'active',
    phone: '0987654321',
    email_verified: true,
    last_login_at: '2025-01-26T09:30:00.000Z',
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-26T09:30:00.000Z'
  },
  {
    id: 3,
    email: '<EMAIL>',
    name: '測試用戶123',
    role: 'user',
    status: 'active',
    phone: '0912345678',
    email_verified: false,
    last_login_at: null,
    created_at: '2025-01-26T09:33:00.000Z',
    updated_at: '2025-01-26T09:33:00.000Z'
  }
];

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    // 驗證管理員權限
    const user = authenticateAdmin(req);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: '需要管理員權限'
      });
    }

    console.log('會員查詢 API: 管理員已認證', { userId: user.userId });

    // 嘗試從真實資料庫查詢
    const db = connectDatabase();
    
    if (db) {
      console.log('會員查詢 API: 使用真實資料庫');
      
      return new Promise((resolve) => {
        const query = `
          SELECT 
            id, email, name, role, status, phone, 
            email_verified, last_login_at, created_at, updated_at
          FROM members 
          ORDER BY created_at DESC
        `;
        
        db.all(query, [], (err, rows) => {
          db.close();
          
          if (err) {
            console.error('資料庫查詢錯誤:', err);
            // 如果查詢失敗，使用模擬數據
            resolve(res.status(200).json({
              success: true,
              data: mockMembers,
              source: 'mock',
              message: '資料庫查詢失敗，使用模擬數據',
              total: mockMembers.length
            }));
          } else {
            console.log('會員查詢 API: 查詢成功', { count: rows.length });
            resolve(res.status(200).json({
              success: true,
              data: rows,
              source: 'database',
              message: '從資料庫查詢成功',
              total: rows.length
            }));
          }
        });
      });
    } else {
      console.log('會員查詢 API: 資料庫不可用，使用模擬數據');
      
      // 如果資料庫不可用，使用模擬數據
      return res.status(200).json({
        success: true,
        data: mockMembers,
        source: 'mock',
        message: '資料庫不可用，使用模擬數據',
        total: mockMembers.length
      });
    }

  } catch (error) {
    console.error('會員查詢 API 錯誤:', error);
    
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
