// 服務類型
export interface Service {
  id: string;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  icon_url: string;
  category: string;
  tags: string[];
  features: any;
  pricing_model: 'subscription' | 'one_time' | 'usage_based';
  trial_days: number;
  is_featured: boolean;
  is_active: boolean;
  sort_order: number;
  metadata: any;
  created_at: string;
  updated_at: string;
  plan_count?: number;
  min_price?: number;
  max_price?: number;
}

// 服務方案類型
export interface ServicePlan {
  id: string;
  service_id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  currency: string;
  billing_period: 'monthly' | 'quarterly' | 'yearly' | 'one_time';
  billing_interval: number;
  features: any;
  limits: any;
  is_popular: boolean;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 訂閱類型
export interface Subscription {
  id: string;
  user_id: string;
  service_id: string;
  plan_id: string;
  status: 'trial' | 'active' | 'paused' | 'cancelled' | 'expired';
  trial_ends_at?: string;
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  cancelled_at?: string;
  auto_renew: boolean;
  price: number;
  currency: string;
  billing_period: string;
  next_billing_date?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;

  // 關聯資料
  service_name?: string;
  service_icon?: string;
  service_category?: string;
  plan_name?: string;
  plan_features?: any;
  plan_limits?: any;
}

// 訂閱使用統計
export interface SubscriptionUsage {
  id: string;
  subscription_id: string;
  metric_name: string;
  usage_count: number;
  usage_limit?: number;
  period_start: string;
  period_end: string;
  created_at: string;
  updated_at: string;
}

// 服務詳情（包含方案）
export interface ServiceWithPlans extends Service {
  plans: ServicePlan[];
}

// 訂閱表單數據
export interface SubscriptionFormData {
  planId: string;
  paymentMethodId?: string;
  agreeTerms: boolean;
}

// API 響應類型
export interface ServicesResponse {
  success: boolean;
  message?: string;
  data: Service[];
}

export interface ServiceResponse {
  success: boolean;
  message?: string;
  data: ServiceWithPlans;
}

export interface SubscriptionsResponse {
  success: boolean;
  message?: string;
  data: Subscription[];
}

export interface SubscriptionResponse {
  success: boolean;
  message?: string;
  data: Subscription;
}

export interface UsageResponse {
  success: boolean;
  message?: string;
  data: SubscriptionUsage[];
}