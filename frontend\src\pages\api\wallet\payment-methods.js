// 支付方式 API
const PaymentMethod = require('../../../../lib/database/models/PaymentMethod');

export default async function handler(req, res) {
  try {
    console.log('支付方式 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'GET') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    // 解析查詢參數
    const { type, active_only = 'true' } = req.query;

    const options = {
      activeOnly: active_only === 'true'
    };

    if (type) {
      options.type = type;
    }

    console.log('支付方式 API: 查詢選項', options);

    // 獲取支付方式
    const paymentMethods = await PaymentMethod.findAll(options);

    console.log('支付方式 API: 成功獲取', paymentMethods.length, '個支付方式');

    // 格式化支付方式數據
    const formattedMethods = paymentMethods.map(method => {
      const data = method.toPublic();
      
      // 添加格式化的金額顯示
      data.formatted_min_amount = `$${data.min_amount.toFixed(2)}`;
      data.formatted_max_amount = `$${data.max_amount.toFixed(2)}`;
      
      // 添加手續費顯示
      if (data.fee_type === 'none') {
        data.fee_display = '免手續費';
      } else if (data.fee_type === 'fixed') {
        data.fee_display = `固定手續費 $${data.fee_amount.toFixed(2)}`;
      } else if (data.fee_type === 'percentage') {
        data.fee_display = `手續費 ${data.fee_amount}%`;
      }
      
      // 添加類型顯示名稱
      const typeNames = {
        'credit_card': '信用卡',
        'bank_transfer': '銀行轉帳',
        'digital_wallet': '數位錢包',
        'crypto': '加密貨幣',
        'other': '其他'
      };
      data.type_name = typeNames[data.type] || data.type;
      
      return data;
    });

    // 按類型分組
    const groupedMethods = {};
    formattedMethods.forEach(method => {
      if (!groupedMethods[method.type]) {
        groupedMethods[method.type] = {
          type: method.type,
          type_name: method.type_name,
          methods: []
        };
      }
      groupedMethods[method.type].methods.push(method);
    });

    return res.status(200).json({
      success: true,
      data: {
        payment_methods: formattedMethods,
        grouped_methods: Object.values(groupedMethods),
        total_count: formattedMethods.length
      },
      message: '成功獲取支付方式'
    });

  } catch (error) {
    console.error('支付方式 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
