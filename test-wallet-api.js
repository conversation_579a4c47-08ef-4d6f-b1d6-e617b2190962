// 測試錢包 API
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testWalletAPI() {
  try {
    console.log('🔐 錢包 API 測試...');
    
    // 1. 先登入獲取 token
    console.log('1️⃣ 登入獲取 token...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: '1234'
    });

    console.log('登入狀態:', loginResult.status);
    
    if (loginResult.status !== 200 || !loginResult.data.success) {
      console.error('❌ 登入失敗:', loginResult.data);
      return;
    }

    const token = loginResult.data.data.token;
    const user = loginResult.data.data.user;
    console.log('✅ 登入成功');
    console.log('用戶:', user.name, '(ID:', user.id, ')');

    // 2. 測試錢包餘額 API
    console.log('\n2️⃣ 測試錢包餘額 API...');
    const walletOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/balance',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const walletResult = await makeRequest(walletOptions);
    
    console.log('錢包 API 狀態:', walletResult.status);
    console.log('錢包 API 響應:', JSON.stringify(walletResult.data, null, 2));

    if (walletResult.status === 200 && walletResult.data.success) {
      console.log('✅ 錢包 API 測試成功！');
      console.log('💰 錢包餘額:', walletResult.data.data.formatted_balance);
    } else {
      console.log('❌ 錢包 API 測試失敗');
    }

    // 3. 測試錢包充值 API
    console.log('\n3️⃣ 測試錢包充值 API...');
    const rechargeOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/recharge',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const rechargeResult = await makeRequest(rechargeOptions, {
      amount: 500,
      payment_method: 'credit_card',
      description: '測試充值'
    });
    
    console.log('充值 API 狀態:', rechargeResult.status);
    console.log('充值 API 響應:', JSON.stringify(rechargeResult.data, null, 2));

    if (rechargeResult.status === 200 && rechargeResult.data.success) {
      console.log('✅ 錢包充值 API 測試成功！');
    } else {
      console.log('❌ 錢包充值 API 測試失敗');
    }

    // 4. 再次檢查餘額
    console.log('\n4️⃣ 再次檢查錢包餘額...');
    const finalBalanceResult = await makeRequest(walletOptions);
    
    if (finalBalanceResult.status === 200 && finalBalanceResult.data.success) {
      console.log('✅ 最終錢包餘額:', finalBalanceResult.data.data.formatted_balance);
    }

    // 5. 測試交易記錄 API
    console.log('\n5️⃣ 測試交易記錄 API...');
    const transactionsOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/transactions',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const transactionsResult = await makeRequest(transactionsOptions);
    
    console.log('交易記錄 API 狀態:', transactionsResult.status);
    
    if (transactionsResult.status === 200 && transactionsResult.data.success) {
      console.log('✅ 交易記錄 API 測試成功！');
      console.log('📊 交易記錄數量:', transactionsResult.data.data.transactions.length);
    } else {
      console.log('❌ 交易記錄 API 測試失敗');
      console.log('響應:', JSON.stringify(transactionsResult.data, null, 2));
    }

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testWalletAPI();
