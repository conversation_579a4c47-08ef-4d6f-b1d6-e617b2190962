import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function WalletLoginFix() {
  const router = useRouter();
  const [status, setStatus] = useState('準備修復登入...');
  const [isFixed, setIsFixed] = useState(false);
  const [authData, setAuthData] = useState(null);
  const [testResults, setTestResults] = useState([]);

  useEffect(() => {
    // 自動開始修復
    fixLoginAndTest();
  }, []);

  const addTestResult = (step, success, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, {
      timestamp,
      step,
      success,
      message,
      data
    }]);
  };

  const fixLoginAndTest = async () => {
    try {
      setStatus('🗑️ 清除舊的認證數據...');
      localStorage.clear();
      addTestResult('清理', true, '已清除舊的認證數據');

      setStatus('🔑 重新登入獲取新 Token...');
      
      const loginResponse = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const loginResult = await loginResponse.json();
      addTestResult('登入', loginResult.success, loginResult.success ? '登入成功' : loginResult.error, loginResult);
      
      if (!loginResult.success) {
        setStatus('❌ 登入失敗: ' + loginResult.error);
        return;
      }

      setStatus('💾 保存認證數據...');
      localStorage.setItem('token', loginResult.token);
      localStorage.setItem('user', JSON.stringify(loginResult.user));
      
      setAuthData({
        token: loginResult.token,
        user: loginResult.user
      });

      addTestResult('保存', true, '認證數據已保存到 localStorage');

      setStatus('🧪 測試錢包 API 訪問...');
      
      // 測試錢包餘額 API
      const balanceResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${loginResult.token}`,
          'Content-Type': 'application/json'
        }
      });

      const balanceResult = await balanceResponse.json();
      addTestResult('錢包API', balanceResult.success, 
        balanceResult.success ? `錢包餘額: $${balanceResult.data.balance.toFixed(2)}` : balanceResult.error,
        balanceResult
      );

      if (balanceResult.success) {
        setStatus(`✅ 修復成功！錢包餘額: $${balanceResult.data.balance.toFixed(2)}`);
        setIsFixed(true);
        
        // 自動跳轉到錢包頁面
        setTimeout(() => {
          setStatus('🚀 正在跳轉到錢包頁面...');
          router.push('/wallet');
        }, 3000);
      } else {
        setStatus('❌ 錢包 API 測試失敗: ' + balanceResult.error);
        addTestResult('錯誤', false, '錢包 API 仍然無法訪問，可能需要檢查 JWT 配置');
      }

    } catch (error) {
      setStatus('❌ 修復失敗: ' + error.message);
      addTestResult('錯誤', false, '修復過程中出現錯誤: ' + error.message);
    }
  };

  const manualGoToWallet = () => {
    router.push('/wallet');
  };

  const goToDashboard = () => {
    router.push('/dashboard-simple');
  };

  const retryFix = () => {
    setIsFixed(false);
    setAuthData(null);
    setTestResults([]);
    fixLoginAndTest();
  };

  return (
    <>
      <Head>
        <title>錢包登入修復 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-100 flex items-center justify-center">
        <div className="max-w-2xl w-full mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {isFixed ? (
                  <span className="text-2xl">✅</span>
                ) : (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                )}
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">🔑 錢包登入修復</h1>
              <p className="text-gray-600">修復登入認證並測試錢包訪問</p>
            </div>

            {/* 狀態顯示 */}
            <div className="mb-8">
              <div className={`p-4 rounded-lg ${
                isFixed ? 'bg-green-50 border border-green-200' : 'bg-purple-50 border border-purple-200'
              }`}>
                <p className={`font-medium ${
                  isFixed ? 'text-green-800' : 'text-purple-800'
                }`}>
                  {status}
                </p>
              </div>
            </div>

            {/* 認證信息 */}
            {authData && (
              <div className="mb-8">
                <h3 className="font-medium text-gray-900 mb-3">🔐 認證信息</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>用戶:</span>
                    <span className="text-green-600">✅ {authData.user.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Email:</span>
                    <span className="text-green-600">✅ {authData.user.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>角色:</span>
                    <span className="text-green-600">✅ {authData.user.role}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Token:</span>
                    <span className="text-blue-600 font-mono text-xs">
                      {authData.token.substring(0, 20)}...
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* 測試結果 */}
            {testResults.length > 0 && (
              <div className="mb-8">
                <h3 className="font-medium text-gray-900 mb-3">📋 測試結果</h3>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                  <div className="space-y-2">
                    {testResults.map((result, index) => (
                      <div key={index} className="flex items-start space-x-3 text-sm">
                        <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                          {result.success ? '✅' : '❌'}
                        </span>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="text-gray-500">[{result.timestamp}]</span>
                            <span className="font-medium">{result.step}:</span>
                            <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                              {result.message}
                            </span>
                          </div>
                          {result.data && (
                            <div className="mt-1 p-2 bg-white rounded text-xs font-mono max-h-20 overflow-y-auto">
                              <pre className="whitespace-pre-wrap">
                                {JSON.stringify(result.data, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* 操作按鈕 */}
            <div className="space-y-3">
              {isFixed ? (
                <div className="space-y-3">
                  <button
                    onClick={manualGoToWallet}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                  >
                    💰 前往錢包頁面
                  </button>
                  
                  <button
                    onClick={goToDashboard}
                    className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                  >
                    🏠 回到儀表板
                  </button>
                </div>
              ) : (
                <button
                  onClick={retryFix}
                  className="w-full px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium"
                >
                  🔄 重新嘗試修復
                </button>
              )}
              
              <button
                onClick={goToDashboard}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🏠 返回儀表板
              </button>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">🎯 修復流程</h3>
              <div className="text-sm text-blue-800 space-y-1">
                <div>1. 清除舊的認證數據</div>
                <div>2. 使用管理員帳號重新登入</div>
                <div>3. 獲取新的 JWT Token</div>
                <div>4. 保存認證數據到 localStorage</div>
                <div>5. 測試錢包 API 訪問權限</div>
                <div>6. 驗證修復結果</div>
              </div>
            </div>

            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2">🔑 管理員帳號</h3>
              <div className="text-sm text-green-800">
                <div>Email: <EMAIL></div>
                <div>Password: password123</div>
                <div>Role: admin</div>
              </div>
            </div>

            {/* 進度指示 */}
            {!isFixed && (
              <div className="mt-6">
                <div className="flex justify-center">
                  <div className="animate-pulse flex space-x-1">
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                  </div>
                </div>
                <p className="text-center text-sm text-gray-500 mt-2">
                  正在修復錢包登入問題...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
