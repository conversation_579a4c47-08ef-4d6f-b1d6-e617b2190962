const express = require('express');
const PaymentController = require('../controllers/PaymentController');
const { authenticateToken } = require('../middleware/authMiddleware');
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Payments
 *   description: 支付相關 API
 */

/**
 * @swagger
 * /api/v1/payments/methods:
 *   get:
 *     summary: 獲取支付方式列表
 *     tags: [Payments]
 *     parameters:
 *       - in: query
 *         name: currency
 *         schema:
 *           type: string
 *         description: 幣別
 *     responses:
 *       200:
 *         description: 獲取成功
 */
router.get('/methods', PaymentController.getPaymentMethods);

/**
 * @swagger
 * /api/v1/payments/reward-preview:
 *   get:
 *     summary: 獲取充值獎勵預覽
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: amount
 *         required: true
 *         schema:
 *           type: number
 *         description: 充值金額
 *     responses:
 *       200:
 *         description: 獲取成功
 *       401:
 *         description: 未授權
 */
router.get('/reward-preview', authenticateToken, PaymentController.getRewardPreview);

/**
 * @swagger
 * /api/v1/payments/stripe:
 *   post:
 *     summary: 創建 Stripe 支付
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *             properties:
 *               amount:
 *                 type: number
 *               currency:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       200:
 *         description: 創建成功
 *       401:
 *         description: 未授權
 */
router.post('/stripe', authenticateToken, PaymentController.createStripePayment);

/**
 * @swagger
 * /api/v1/payments/paypal:
 *   post:
 *     summary: 創建 PayPal 支付
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *             properties:
 *               amount:
 *                 type: number
 *               currency:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       200:
 *         description: 創建成功
 *       401:
 *         description: 未授權
 */
router.post('/paypal', authenticateToken, PaymentController.createPayPalPayment);

/**
 * @swagger
 * /api/v1/payments/stripe/webhook:
 *   post:
 *     summary: Stripe Webhook
 *     tags: [Payments]
 *     responses:
 *       200:
 *         description: 處理成功
 */
router.post('/stripe/webhook', express.raw({ type: 'application/json' }), PaymentController.handleStripeWebhook);

module.exports = router;
