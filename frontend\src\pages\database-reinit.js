import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function DatabaseReinit() {
  const router = useRouter();
  const [initResults, setInitResults] = useState([]);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setInitResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const reinitializeDatabase = async () => {
    setIsInitializing(true);
    setInitResults([]);
    setIsCompleted(false);

    try {
      addResult('開始', 'info', '開始重新初始化資料庫');

      // 檢查認證
      const token = localStorage.getItem('token');
      if (!token) {
        addResult('認證', 'error', '未找到認證 token，請先登入');
        return;
      }

      // 步驟 1: 重新初始化資料庫
      addResult('步驟1', 'info', '重新初始化資料庫結構...');
      
      const initResponse = await fetch('/api/database/init', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          force_recreate: true
        })
      });

      if (initResponse.ok) {
        const initResult = await initResponse.json();
        if (initResult.success) {
          addResult('步驟1', 'success', '資料庫初始化成功');
        } else {
          addResult('步驟1', 'warning', `資料庫初始化警告: ${initResult.message}`);
        }
      } else {
        addResult('步驟1', 'error', '資料庫初始化 API 不存在，使用備用方法');
        
        // 備用方法：直接執行 SQL
        addResult('步驟1b', 'info', '使用備用方法初始化資料庫...');
        
        const sqlResponse = await fetch('/api/admin/database-query', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            query: 'SELECT name FROM sqlite_master WHERE type="table"'
          })
        });

        if (sqlResponse.ok) {
          const sqlResult = await sqlResponse.json();
          addResult('步驟1b', 'success', `找到 ${sqlResult.data.length} 個表`);
        } else {
          addResult('步驟1b', 'error', '無法查詢資料庫表');
        }
      }

      // 步驟 2: 檢查錢包相關表
      addResult('步驟2', 'info', '檢查錢包相關表結構...');
      
      const tablesResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "SELECT name FROM sqlite_master WHERE type='table' AND (name LIKE '%wallet%' OR name LIKE '%payment%')"
        })
      });

      if (tablesResponse.ok) {
        const tablesResult = await tablesResponse.json();
        if (tablesResult.success) {
          addResult('步驟2', 'success', `找到 ${tablesResult.data.length} 個錢包相關表`);
          tablesResult.data.forEach(table => {
            addResult('步驟2', 'info', `表: ${table.name}`);
          });
        } else {
          addResult('步驟2', 'error', '無法查詢錢包表');
        }
      }

      // 步驟 3: 檢查 members 表是否有 wallet_balance 欄位
      addResult('步驟3', 'info', '檢查 members 表結構...');
      
      const columnsResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "PRAGMA table_info(members)"
        })
      });

      if (columnsResponse.ok) {
        const columnsResult = await columnsResponse.json();
        if (columnsResult.success) {
          const hasWalletBalance = columnsResult.data.some(col => col.name === 'wallet_balance');
          if (hasWalletBalance) {
            addResult('步驟3', 'success', 'members 表已包含 wallet_balance 欄位');
          } else {
            addResult('步驟3', 'warning', 'members 表缺少 wallet_balance 欄位');
            
            // 嘗試添加 wallet_balance 欄位
            addResult('步驟3b', 'info', '添加 wallet_balance 欄位...');
            
            const alterResponse = await fetch('/api/admin/database-query', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                query: "ALTER TABLE members ADD COLUMN wallet_balance DECIMAL(15,2) DEFAULT 0.00"
              })
            });

            if (alterResponse.ok) {
              const alterResult = await alterResponse.json();
              if (alterResult.success) {
                addResult('步驟3b', 'success', 'wallet_balance 欄位添加成功');
              } else {
                addResult('步驟3b', 'error', `添加欄位失敗: ${alterResult.error}`);
              }
            }
          }
        }
      }

      // 步驟 4: 初始化管理員錢包餘額
      addResult('步驟4', 'info', '初始化管理員錢包餘額...');
      
      const updateResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "UPDATE members SET wallet_balance = ? WHERE id = ?",
          params: [1000.00, 1]
        })
      });

      if (updateResponse.ok) {
        const updateResult = await updateResponse.json();
        if (updateResult.success) {
          addResult('步驟4', 'success', '管理員錢包餘額設置為 $1000.00');
        } else {
          addResult('步驟4', 'error', `更新餘額失敗: ${updateResult.error}`);
        }
      }

      // 步驟 5: 測試錢包 API
      addResult('步驟5', 'info', '測試錢包 API...');
      
      const walletResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (walletResponse.ok) {
        const walletResult = await walletResponse.json();
        if (walletResult.success) {
          addResult('步驟5', 'success', `錢包 API 正常，餘額: $${walletResult.data.balance}`);
        } else {
          addResult('步驟5', 'error', `錢包 API 失敗: ${walletResult.error}`);
        }
      } else {
        addResult('步驟5', 'error', '錢包 API 無法訪問');
      }

      addResult('完成', 'success', '資料庫重新初始化完成！');
      setIsCompleted(true);

    } catch (error) {
      addResult('錯誤', 'error', '初始化過程中出現錯誤: ' + error.message);
    } finally {
      setIsInitializing(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>資料庫重新初始化 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔄 資料庫重新初始化</h1>
                <p className="text-gray-600 mt-2">重新初始化資料庫並添加錢包功能</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/wallet-db-check')}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  🗄️ 檢查資料庫
                </button>
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={reinitializeDatabase}
                  disabled={isInitializing}
                  className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 font-medium"
                >
                  {isInitializing ? '初始化中...' : '🔄 重新初始化'}
                </button>
              </div>
            </div>

            {/* 警告提示 */}
            <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-medium text-red-900 mb-2">⚠️ 重要提醒</h3>
              <div className="text-sm text-red-800 space-y-1">
                <div>• 此操作將重新初始化資料庫結構</div>
                <div>• 會添加錢包相關的表和欄位</div>
                <div>• 現有數據不會丟失，但會進行結構更新</div>
                <div>• 建議在執行前備份資料庫</div>
              </div>
            </div>

            {/* 初始化結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 初始化結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {initResults.length > 0 ? (
                  <div className="space-y-3">
                    {initResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                            {result.data && (
                              <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono max-h-32 overflow-y-auto">
                                <pre className="whitespace-pre-wrap">
                                  {JSON.stringify(result.data, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「重新初始化」來開始資料庫初始化
                  </div>
                )}
              </div>
            </div>

            {/* 完成後的操作 */}
            {isCompleted && (
              <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-medium text-green-900 mb-3">🎉 初始化完成</h3>
                <div className="text-sm text-green-800 mb-4">
                  資料庫已成功重新初始化，錢包功能現在應該可以正常使用了！
                </div>
                <div className="flex space-x-4">
                  <button
                    onClick={() => router.push('/wallet-db-check')}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    🗄️ 檢查資料庫
                  </button>
                  <button
                    onClick={() => router.push('/wallet')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    💰 測試錢包
                  </button>
                </div>
              </div>
            )}

            {/* 說明 */}
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 初始化內容</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>步驟1:</strong> 重新初始化資料庫結構</div>
                <div><strong>步驟2:</strong> 檢查錢包相關表 (wallet_transactions, payment_methods)</div>
                <div><strong>步驟3:</strong> 檢查並添加 members.wallet_balance 欄位</div>
                <div><strong>步驟4:</strong> 初始化管理員錢包餘額為 $1000.00</div>
                <div><strong>步驟5:</strong> 測試錢包 API 是否正常工作</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
