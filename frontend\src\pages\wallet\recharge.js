import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function Recharge() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [amount, setAmount] = useState('');
  const [selectedMethod, setSelectedMethod] = useState('credit_card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentBalance, setCurrentBalance] = useState(1250.50);

  // 預設金額選項
  const quickAmounts = [100, 300, 500, 1000, 2000, 5000];

  // 支付方式
  const paymentMethods = [
    {
      id: 'credit_card',
      name: '信用卡',
      icon: '💳',
      description: '支援 Visa、MasterCard、JCB',
      fee: '2.5%',
      processingTime: '即時到帳'
    },
    {
      id: 'bank_transfer',
      name: '銀行轉帳',
      icon: '🏦',
      description: '透過網路銀行或 ATM 轉帳',
      fee: '免費',
      processingTime: '1-3 個工作天'
    },
    {
      id: 'line_pay',
      name: 'LINE Pay',
      icon: '📱',
      description: '使用 LINE Pay 快速付款',
      fee: '1.5%',
      processingTime: '即時到帳'
    },
    {
      id: 'apple_pay',
      name: 'Apple Pay',
      icon: '🍎',
      description: '使用 Apple Pay 安全付款',
      fee: '2%',
      processingTime: '即時到帳'
    }
  ];

  useEffect(() => {
    // 檢查認證
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=' + encodeURIComponent('/wallet/recharge'));
      return;
    }

    // 載入用戶數據
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }
  }, [router]);

  const handleQuickAmount = (quickAmount) => {
    setAmount(quickAmount.toString());
  };

  const calculateFee = () => {
    const selectedPayment = paymentMethods.find(method => method.id === selectedMethod);
    if (!selectedPayment || !amount) return 0;
    
    if (selectedPayment.fee === '免費') return 0;
    
    const feePercentage = parseFloat(selectedPayment.fee.replace('%', '')) / 100;
    return parseFloat(amount) * feePercentage;
  };

  const getTotalAmount = () => {
    const baseAmount = parseFloat(amount) || 0;
    const fee = calculateFee();
    return baseAmount + fee;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!amount || parseFloat(amount) <= 0) {
      alert('請輸入有效的充值金額');
      return;
    }

    if (parseFloat(amount) < 10) {
      alert('最低充值金額為 NT$ 10');
      return;
    }

    if (parseFloat(amount) > 50000) {
      alert('單次充值金額不能超過 NT$ 50,000');
      return;
    }

    setIsProcessing(true);

    try {
      // 模擬支付處理
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模擬成功
      const newBalance = currentBalance + parseFloat(amount);
      setCurrentBalance(newBalance);
      
      alert(`充值成功！已充值 NT$ ${parseFloat(amount).toLocaleString()}，當前餘額：NT$ ${newBalance.toLocaleString()}`);
      
      // 跳轉回錢包頁面
      router.push('/wallet');
      
    } catch (error) {
      alert('充值失敗，請稍後再試');
    } finally {
      setIsProcessing(false);
    }
  };

  const selectedPayment = paymentMethods.find(method => method.id === selectedMethod);

  return (
    <>
      <Head>
        <title>錢包充值 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/wallet')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回錢包
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">錢包充值</h1>
                </div>
              </div>
              
              <div className="flex items-center">
                <span className="text-sm text-gray-600">當前餘額: NT$ {currentBalance.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 充值表單 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-xl shadow-lg p-8"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">選擇充值金額</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* 金額輸入 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    充值金額 (NT$)
                  </label>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="請輸入充值金額"
                    min="10"
                    max="50000"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    最低充值金額 NT$ 10，最高 NT$ 50,000
                  </p>
                </div>

                {/* 快速金額選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    快速選擇
                  </label>
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
                    {quickAmounts.map((quickAmount) => (
                      <button
                        key={quickAmount}
                        type="button"
                        onClick={() => handleQuickAmount(quickAmount)}
                        className={`py-3 px-4 rounded-lg border-2 transition-colors ${
                          amount === quickAmount.toString()
                            ? 'border-blue-500 bg-blue-50 text-blue-600'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        NT$ {quickAmount}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 支付方式選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    選擇支付方式
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {paymentMethods.map((method) => (
                      <div
                        key={method.id}
                        className={`relative p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                          selectedMethod === method.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedMethod(method.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="text-2xl">{method.icon}</div>
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900">{method.name}</h3>
                            <p className="text-sm text-gray-500 mt-1">{method.description}</p>
                            <div className="flex justify-between text-xs text-gray-400 mt-2">
                              <span>手續費: {method.fee}</span>
                              <span>{method.processingTime}</span>
                            </div>
                          </div>
                        </div>
                        {selectedMethod === method.id && (
                          <div className="absolute top-2 right-2">
                            <div className="h-4 w-4 bg-blue-500 rounded-full flex items-center justify-center">
                              <div className="h-2 w-2 bg-white rounded-full"></div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* 費用明細 */}
                {amount && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="bg-gray-50 rounded-lg p-4"
                  >
                    <h3 className="font-medium text-gray-900 mb-3">費用明細</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>充值金額:</span>
                        <span>NT$ {parseFloat(amount).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>手續費 ({selectedPayment?.fee}):</span>
                        <span>NT$ {calculateFee().toLocaleString()}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-medium">
                        <span>總計:</span>
                        <span>NT$ {getTotalAmount().toLocaleString()}</span>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* 提交按鈕 */}
                <button
                  type="submit"
                  disabled={!amount || isProcessing}
                  className={`w-full py-4 px-6 rounded-lg font-medium text-lg transition-colors ${
                    !amount || isProcessing
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isProcessing ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      處理中...
                    </div>
                  ) : (
                    `確認充值 NT$ ${amount ? getTotalAmount().toLocaleString() : '0'}`
                  )}
                </button>
              </form>
            </motion.div>

            {/* 安全提示 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-blue-50 border border-blue-200 rounded-lg p-6"
            >
              <h3 className="font-medium text-blue-900 mb-3">🔒 安全提示</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 請確認充值金額無誤後再進行支付</li>
                <li>• 充值成功後資金將立即到帳（銀行轉帳除外）</li>
                <li>• 如遇問題請聯繫客服：<EMAIL></li>
                <li>• 我們採用 SSL 加密技術保護您的支付安全</li>
              </ul>
            </motion.div>
          </div>
        </main>
      </div>
    </>
  );
}
