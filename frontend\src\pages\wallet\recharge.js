import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function Recharge() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [amount, setAmount] = useState('');
  const [selectedMethod, setSelectedMethod] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentBalance, setCurrentBalance] = useState(0);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // 預設金額選項
  const quickAmounts = [100, 300, 500, 1000, 2000, 5000];

  useEffect(() => {
    checkAuth();
    loadPaymentMethods();
    loadCurrentBalance();
  }, []);

  const checkAuth = () => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/login');
      return;
    }

    try {
      setUser(JSON.parse(userData));
    } catch (error) {
      console.error('解析用戶數據失敗:', error);
      router.push('/login');
    }
  };

  const loadCurrentBalance = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:3001/api/wallet/balance', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      if (result.success) {
        setCurrentBalance(result.data.balance);
      }
    } catch (error) {
      console.error('載入餘額失敗:', error);
    }
  };

  const loadPaymentMethods = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:3001/api/wallet/payment-methods', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      if (result.success) {
        setPaymentMethods(result.data.payment_methods);
        if (result.data.payment_methods.length > 0) {
          setSelectedMethod(result.data.payment_methods[0]);
        }
      }
    } catch (error) {
      console.error('載入支付方式失敗:', error);
      setError('載入支付方式失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickAmount = (quickAmount) => {
    setAmount(quickAmount.toString());
    setError('');
  };

  const calculateFee = () => {
    if (!selectedMethod || !amount) return 0;

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum)) return 0;

    switch (selectedMethod.fee_type) {
      case 'fixed':
        return parseFloat(selectedMethod.fee_amount || 0);
      case 'percentage':
        return amountNum * (parseFloat(selectedMethod.fee_amount || 0) / 100);
      default:
        return 0;
    }
  };

  const getTotalAmount = () => {
    const baseAmount = parseFloat(amount) || 0;
    const fee = calculateFee();
    return baseAmount + fee;
  };

  const validateAmount = () => {
    if (!amount || !selectedMethod) return false;

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) return false;

    const minAmount = parseFloat(selectedMethod.min_amount || 0);
    const maxAmount = parseFloat(selectedMethod.max_amount || 999999);

    if (amountNum < minAmount) {
      setError(`最小充值金額為 NT$ ${minAmount.toLocaleString()}`);
      return false;
    }

    if (amountNum > maxAmount) {
      setError(`最大充值金額為 NT$ ${maxAmount.toLocaleString()}`);
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateAmount()) return;

    setIsProcessing(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:3001/api/wallet/recharge', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
          payment_method_id: selectedMethod.id,
          description: `使用${selectedMethod.name}充值`
        })
      });

      const result = await response.json();

      if (result.success) {
        // 安全地處理 new_balance 數據
        const newBalance = result.data.new_balance;
        let balanceAmount = 0;
        let formattedBalance = '$0.00';

        if (newBalance && typeof newBalance === 'object') {
          balanceAmount = parseFloat(newBalance.balance) || 0;
          formattedBalance = newBalance.formatted_balance || `$${balanceAmount.toFixed(2)}`;
        } else if (typeof newBalance === 'number') {
          balanceAmount = newBalance;
          formattedBalance = `$${balanceAmount.toFixed(2)}`;
        }

        setSuccess(`充值成功！新餘額: NT$ ${formattedBalance}`);
        setCurrentBalance(balanceAmount);
        setAmount('');

        // 3秒後跳轉到錢包頁面
        setTimeout(() => {
          router.push('/wallet');
        }, 3000);
      } else {
        setError(result.error || '充值失敗');
      }
    } catch (error) {
      console.error('充值失敗:', error);
      setError('充值失敗，請稍後再試');
    } finally {
      setIsProcessing(false);
    }
  };

  if (!user || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>錢包充值 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/wallet')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回錢包
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">錢包充值</h1>
                </div>
              </div>
              
              <div className="flex items-center">
                <span className="text-sm text-gray-600">當前餘額: NT$ {(currentBalance || 0).toLocaleString()}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {/* 充值表單 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-xl shadow-lg p-8"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">選擇充值金額</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* 金額輸入 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    充值金額 (NT$)
                  </label>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="請輸入充值金額"
                    min="10"
                    max="50000"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    最低充值金額 NT$ 10，最高 NT$ 50,000
                  </p>
                </div>

                {/* 快速金額選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    快速選擇
                  </label>
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
                    {quickAmounts.map((quickAmount) => (
                      <button
                        key={quickAmount}
                        type="button"
                        onClick={() => handleQuickAmount(quickAmount)}
                        className={`py-3 px-4 rounded-lg border-2 transition-colors ${
                          amount === quickAmount.toString()
                            ? 'border-blue-500 bg-blue-50 text-blue-600'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        NT$ {quickAmount}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 支付方式選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    選擇支付方式
                  </label>
                  {loading ? (
                    <div className="text-center py-4">載入支付方式中...</div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {paymentMethods.map((method) => (
                        <div
                          key={method.id}
                          className={`relative p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                            selectedMethod?.id === method.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setSelectedMethod(method)}
                        >
                          <div className="flex items-start space-x-3">
                            <div className="text-2xl">{method.icon_url}</div>
                            <div className="flex-1">
                              <h3 className="font-medium text-gray-900">{method.name}</h3>
                              <p className="text-sm text-gray-500 mt-1">{method.description}</p>
                              <div className="flex justify-between text-xs text-gray-400 mt-2">
                                <span>手續費: {method.fee_type === 'none' ? '免費' :
                                            method.fee_type === 'fixed' ? `NT$ ${method.fee_amount}` :
                                            `${method.fee_amount}%`}</span>
                                <span>NT$ {method.min_amount} - NT$ {method.max_amount}</span>
                              </div>
                            </div>
                          </div>
                          {selectedMethod?.id === method.id && (
                            <div className="absolute top-2 right-2">
                              <div className="h-4 w-4 bg-blue-500 rounded-full flex items-center justify-center">
                                <div className="h-2 w-2 bg-white rounded-full"></div>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* 錯誤訊息 */}
                {error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600 text-sm">{error}</p>
                  </div>
                )}

                {/* 成功訊息 */}
                {success && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-green-600 text-sm">{success}</p>
                  </div>
                )}

                {/* 費用明細 */}
                {amount && selectedMethod && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="bg-gray-50 rounded-lg p-4"
                  >
                    <h3 className="font-medium text-gray-900 mb-3">費用明細</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>充值金額:</span>
                        <span>NT$ {parseFloat(amount || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>手續費:</span>
                        <span>NT$ {calculateFee().toLocaleString()}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-medium">
                        <span>總計:</span>
                        <span>NT$ {getTotalAmount().toLocaleString()}</span>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* 提交按鈕 */}
                <button
                  type="submit"
                  disabled={!amount || !selectedMethod || isProcessing}
                  className={`w-full py-4 px-6 rounded-lg font-medium text-lg transition-colors ${
                    !amount || !selectedMethod || isProcessing
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isProcessing ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      處理中...
                    </div>
                  ) : (
                    `確認充值 NT$ ${amount ? getTotalAmount().toLocaleString() : '0'}`
                  )}
                </button>
              </form>
            </motion.div>

            {/* 安全提示 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-blue-50 border border-blue-200 rounded-lg p-6"
            >
              <h3 className="font-medium text-blue-900 mb-3">🔒 安全提示</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 請確認充值金額無誤後再進行支付</li>
                <li>• 充值成功後資金將立即到帳（銀行轉帳除外）</li>
                <li>• 如遇問題請聯繫客服：<EMAIL></li>
                <li>• 我們採用 SSL 加密技術保護您的支付安全</li>
              </ul>
            </motion.div>
          </div>
        </main>
      </div>
    </>
  );
}
