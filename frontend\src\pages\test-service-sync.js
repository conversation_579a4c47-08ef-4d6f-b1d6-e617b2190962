import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function TestServiceSync() {
  const router = useRouter();
  const [adminServices, setAdminServices] = useState([]);
  const [userServices, setUserServices] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [testResults, setTestResults] = useState([]);

  useEffect(() => {
    loadTestData();
  }, []);

  const loadTestData = async () => {
    try {
      setIsLoading(true);
      
      // 載入管理員端的服務數據
      const adminResponse = await fetch('/api/services');
      const adminResult = await adminResponse.json();
      
      if (adminResult.success) {
        setAdminServices(adminResult.data);
      }
      
      // 模擬用戶端看到的可用服務（啟用狀態的服務）
      const availableServices = adminResult.success 
        ? adminResult.data.filter(service => service.status === 'active')
        : [];
      
      setUserServices(availableServices);
      
      // 執行同步測試
      runSyncTests(adminResult.data, availableServices);
      
    } catch (error) {
      console.error('載入測試數據失敗:', error);
      addTestResult('數據載入', '❌ 失敗', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const addTestResult = (test, result, details = '') => {
    setTestResults(prev => [...prev, {
      id: Date.now() + Math.random(),
      test,
      result,
      details,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const runSyncTests = (adminData, userData) => {
    // 測試 1: 檢查數據載入
    if (adminData && adminData.length > 0) {
      addTestResult('管理員端數據載入', '✅ 通過', `載入了 ${adminData.length} 個服務`);
    } else {
      addTestResult('管理員端數據載入', '❌ 失敗', '沒有載入到服務數據');
    }

    // 測試 2: 檢查用戶端數據
    if (userData && userData.length > 0) {
      addTestResult('用戶端數據載入', '✅ 通過', `載入了 ${userData.length} 個可用服務`);
    } else {
      addTestResult('用戶端數據載入', '❌ 失敗', '沒有載入到可用服務');
    }

    // 測試 3: 檢查預設服務
    const expectedServices = [
      'AI 文字生成服務',
      '圖片處理服務', 
      '數據分析服務',
      '語音轉文字服務',
      '雲端存儲服務',
      '企業級 API 服務'
    ];

    expectedServices.forEach(serviceName => {
      const found = adminData.some(service => service.name === serviceName);
      if (found) {
        addTestResult(`預設服務檢查: ${serviceName}`, '✅ 通過', '服務存在於管理員端');
      } else {
        addTestResult(`預設服務檢查: ${serviceName}`, '❌ 失敗', '服務不存在於管理員端');
      }
    });

    // 測試 4: 檢查服務同步
    const activeAdminServices = adminData.filter(s => s.status === 'active');
    if (activeAdminServices.length === userData.length) {
      addTestResult('服務同步檢查', '✅ 通過', '管理員端啟用服務與用戶端可用服務數量一致');
    } else {
      addTestResult('服務同步檢查', '⚠️ 警告', 
        `管理員端啟用服務: ${activeAdminServices.length}, 用戶端可用服務: ${userData.length}`);
    }

    // 測試 5: 檢查新增服務
    const newServices = adminData.filter(service => service.id > 6);
    if (newServices.length > 0) {
      addTestResult('新增服務檢查', '✅ 通過', `發現 ${newServices.length} 個新增服務`);
      newServices.forEach(service => {
        const inUserList = userData.some(s => s.id === service.id && s.status === 'active');
        if (inUserList) {
          addTestResult(`新增服務同步: ${service.name}`, '✅ 通過', '新增服務已同步到用戶端');
        } else {
          addTestResult(`新增服務同步: ${service.name}`, '❌ 失敗', 
            `服務狀態: ${service.status}, 未出現在用戶端`);
        }
      });
    } else {
      addTestResult('新增服務檢查', '⚠️ 提示', '沒有發現新增服務');
    }
  };

  const createTestService = async () => {
    try {
      const testService = {
        name: `測試服務 ${Date.now()}`,
        description: '這是一個測試服務，用於驗證同步功能',
        price: 99,
        billingCycle: 'monthly',
        category: '測試服務',
        features: ['測試功能1', '測試功能2'],
        status: 'active',
        popular: false,
        maxUsers: 1,
        apiLimit: 1000,
        storageLimit: '1GB',
        supportLevel: 'basic'
      };

      const response = await fetch('/api/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testService),
      });

      const result = await response.json();
      
      if (result.success) {
        alert(`✅ 測試服務創建成功：${result.data.name}`);
        // 重新載入測試數據
        await loadTestData();
      } else {
        alert(`❌ 測試服務創建失敗：${result.error}`);
      }
    } catch (error) {
      console.error('創建測試服務失敗:', error);
      alert('❌ 創建測試服務失敗');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入服務同步測試中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>服務同步測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">服務同步測試</h1>
            
            <div className="mb-8 flex space-x-4">
              <button
                onClick={loadTestData}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                🔄 重新測試
              </button>
              <button
                onClick={createTestService}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                ➕ 創建測試服務
              </button>
              <button
                onClick={() => router.push('/admin/services')}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                🛠️ 管理員端
              </button>
              <button
                onClick={() => router.push('/subscriptions')}
                className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                👤 用戶端
              </button>
            </div>

            {/* 數據對比 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* 管理員端服務 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  管理員端服務 ({adminServices.length})
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                  {adminServices.map((service) => (
                    <div key={service.id} className="mb-2 p-2 bg-white rounded border">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{service.name}</span>
                        <span className={`px-2 py-1 text-xs rounded ${
                          service.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {service.status}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        ID: {service.id} | 價格: NT$ {service.price}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 用戶端可用服務 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  用戶端可用服務 ({userServices.length})
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                  {userServices.map((service) => (
                    <div key={service.id} className="mb-2 p-2 bg-white rounded border">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{service.name}</span>
                        <span className="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">
                          可訂閱
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        ID: {service.id} | 價格: NT$ {service.price}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 測試結果 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">測試結果</h2>
              <div className="space-y-2">
                {testResults.map((result) => (
                  <div key={result.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{result.result.includes('✅') ? '✅' : result.result.includes('❌') ? '❌' : '⚠️'}</span>
                      <div>
                        <div className="font-medium text-gray-900">{result.test}</div>
                        <div className="text-sm text-gray-500">{result.details}</div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">{result.timestamp}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-3">測試說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>目的：</strong>驗證管理員端新增的服務是否能正確同步到用戶端的可用服務列表。</p>
                <p><strong>測試步驟：</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>檢查管理員端是否載入了所有服務（包括預設和新增）</li>
                  <li>檢查用戶端是否載入了所有啟用狀態的服務</li>
                  <li>驗證預設服務是否都存在</li>
                  <li>檢查新增服務是否正確同步</li>
                  <li>確認服務狀態過濾是否正確</li>
                </ol>
                <p><strong>預期結果：</strong>管理員端新增的啟用服務應該出現在用戶端的可用服務列表中。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
