const EncryptionService = require('../../src/services/EncryptionService');

describe('EncryptionService', () => {
  describe('encrypt and decrypt', () => {
    test('should encrypt and decrypt text correctly', () => {
      const plaintext = 'Hello, World!';
      const encrypted = EncryptionService.encrypt(plaintext);
      
      expect(encrypted).toBeDefined();
      expect(encrypted.encrypted).toBeDefined();
      expect(encrypted.iv).toBeDefined();
      expect(encrypted.tag).toBeDefined();
      
      const decrypted = EncryptionService.decrypt(encrypted.encrypted);
      expect(decrypted).toBe(plaintext);
    });

    test('should handle empty string', () => {
      const plaintext = '';
      const encrypted = EncryptionService.encrypt(plaintext);
      const decrypted = EncryptionService.decrypt(encrypted.encrypted);
      
      expect(decrypted).toBe(plaintext);
    });

    test('should handle unicode characters', () => {
      const plaintext = '你好世界 🌍 émojis';
      const encrypted = EncryptionService.encrypt(plaintext);
      const decrypted = EncryptionService.decrypt(encrypted.encrypted);
      
      expect(decrypted).toBe(plaintext);
    });

    test('should throw error for invalid encrypted data', () => {
      expect(() => {
        EncryptionService.decrypt('invalid-data');
      }).toThrow('解密失敗');
    });
  });

  describe('password hashing', () => {
    test('should hash password correctly', async () => {
      const password = 'TestPassword123';
      const hashedPassword = await EncryptionService.hashPassword(password);
      
      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(50);
    });

    test('should verify password correctly', async () => {
      const password = 'TestPassword123';
      const hashedPassword = await EncryptionService.hashPassword(password);
      
      const isValid = await EncryptionService.verifyPassword(password, hashedPassword);
      expect(isValid).toBe(true);
      
      const isInvalid = await EncryptionService.verifyPassword('WrongPassword', hashedPassword);
      expect(isInvalid).toBe(false);
    });
  });

  describe('PII encryption', () => {
    test('should encrypt and decrypt PII data', () => {
      const piiData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890'
      };
      
      const encrypted = EncryptionService.encryptPII(piiData);
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
      
      const decrypted = EncryptionService.decryptPII(encrypted);
      expect(decrypted).toEqual(piiData);
    });

    test('should handle null PII data', () => {
      const encrypted = EncryptionService.encryptPII(null);
      expect(encrypted).toBeNull();
      
      const decrypted = EncryptionService.decryptPII(null);
      expect(decrypted).toBeNull();
    });
  });

  describe('data masking', () => {
    test('should mask credit card numbers correctly', () => {
      expect(EncryptionService.maskCreditCard('***************1'))
        .toBe('************1111');
      expect(EncryptionService.maskCreditCard('4111-1111-1111-1111'))
        .toBe('************1111');
      expect(EncryptionService.maskCreditCard('***************'))
        .toBe('***********1111');
      expect(EncryptionService.maskCreditCard('123'))
        .toBe('***');
      expect(EncryptionService.maskCreditCard(''))
        .toBe('');
    });

    test('should mask phone numbers correctly', () => {
      expect(EncryptionService.maskPhoneNumber('+886912345678'))
        .toBe('886******678');
      expect(EncryptionService.maskPhoneNumber('0912345678'))
        .toBe('091****678');
      expect(EncryptionService.maskPhoneNumber('123'))
        .toBe('***');
      expect(EncryptionService.maskPhoneNumber(''))
        .toBe('');
    });

    test('should mask email addresses correctly', () => {
      expect(EncryptionService.maskEmail('<EMAIL>'))
        .toBe('t**<EMAIL>');
      expect(EncryptionService.maskEmail('<EMAIL>'))
        .toBe('*@example.com');
      expect(EncryptionService.maskEmail('<EMAIL>'))
        .toBe('**@example.com');
      expect(EncryptionService.maskEmail('invalid-email'))
        .toBe('invalid-email');
      expect(EncryptionService.maskEmail(''))
        .toBe('');
    });
  });

  describe('token generation', () => {
    test('should generate secure tokens', () => {
      const token1 = EncryptionService.generateSecureToken();
      const token2 = EncryptionService.generateSecureToken();
      
      expect(token1).toBeDefined();
      expect(token2).toBeDefined();
      expect(token1).not.toBe(token2);
      expect(token1.length).toBe(64); // 32 bytes = 64 hex chars
      
      const customLengthToken = EncryptionService.generateSecureToken(16);
      expect(customLengthToken.length).toBe(32); // 16 bytes = 32 hex chars
    });

    test('should generate TOTP secrets', () => {
      const secret1 = EncryptionService.generateTOTPSecret();
      const secret2 = EncryptionService.generateTOTPSecret();
      
      expect(secret1).toBeDefined();
      expect(secret2).toBeDefined();
      expect(secret1).not.toBe(secret2);
      expect(secret1.length).toBeGreaterThan(0);
    });

    test('should generate CSP nonces', () => {
      const nonce1 = EncryptionService.generateCSPNonce();
      const nonce2 = EncryptionService.generateCSPNonce();
      
      expect(nonce1).toBeDefined();
      expect(nonce2).toBeDefined();
      expect(nonce1).not.toBe(nonce2);
      expect(nonce1.length).toBeGreaterThan(0);
    });
  });

  describe('HMAC operations', () => {
    test('should create and verify HMAC signatures', () => {
      const data = 'test data for signing';
      const signature = EncryptionService.createHMAC(data);
      
      expect(signature).toBeDefined();
      expect(typeof signature).toBe('string');
      expect(signature.length).toBe(64); // SHA256 hex = 64 chars
      
      const isValid = EncryptionService.verifyHMAC(data, signature);
      expect(isValid).toBe(true);
      
      const isInvalid = EncryptionService.verifyHMAC('different data', signature);
      expect(isInvalid).toBe(false);
    });

    test('should use custom secret for HMAC', () => {
      const data = 'test data';
      const secret = 'custom-secret';
      
      const signature1 = EncryptionService.createHMAC(data, secret);
      const signature2 = EncryptionService.createHMAC(data); // default secret
      
      expect(signature1).not.toBe(signature2);
      
      const isValid = EncryptionService.verifyHMAC(data, signature1, secret);
      expect(isValid).toBe(true);
    });
  });

  describe('secure comparison', () => {
    test('should compare strings securely', () => {
      const str1 = 'test-string';
      const str2 = 'test-string';
      const str3 = 'different-string';
      
      expect(EncryptionService.secureCompare(str1, str2)).toBe(true);
      expect(EncryptionService.secureCompare(str1, str3)).toBe(false);
      expect(EncryptionService.secureCompare('', '')).toBe(true);
      expect(EncryptionService.secureCompare('a', 'ab')).toBe(false);
    });
  });
});
