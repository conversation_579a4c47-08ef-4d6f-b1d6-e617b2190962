// 測試充值頁面 UI 修復
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testRechargeUI() {
  try {
    console.log('🔧 測試充值頁面 UI 修復...');
    
    // 1. 登入獲取 token
    console.log('\n1️⃣ 登入獲取 token...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResult.status !== 200 || !loginResult.data.success) {
      console.error('❌ 登入失敗:', loginResult.data);
      return;
    }

    const token = loginResult.data.data.token;
    const user = loginResult.data.data.user;
    console.log('✅ 登入成功 - 用戶:', user.name);

    // 2. 測試錢包餘額 API
    console.log('\n2️⃣ 測試錢包餘額 API...');
    const balanceOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/balance',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const balanceResult = await makeRequest(balanceOptions);
    
    if (balanceResult.status === 200 && balanceResult.data.success) {
      console.log('✅ 錢包餘額 API 正常:', balanceResult.data.data.formatted_balance);
    } else {
      console.log('❌ 錢包餘額 API 失敗:', balanceResult.data);
    }

    // 3. 測試支付方式 API
    console.log('\n3️⃣ 測試支付方式 API...');
    const paymentMethodsOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/payment-methods',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const paymentMethodsResult = await makeRequest(paymentMethodsOptions);
    
    if (paymentMethodsResult.status === 200 && paymentMethodsResult.data.success) {
      const methods = paymentMethodsResult.data.data.payment_methods;
      console.log(`✅ 支付方式 API 正常: 找到 ${methods.length} 個支付方式`);
      
      // 顯示支付方式詳情
      methods.forEach((method, index) => {
        const fee = method.fee_type === 'none' ? '免費' : 
                   method.fee_type === 'fixed' ? `固定 $${method.fee_amount}` :
                   `${method.fee_amount}%`;
        console.log(`   ${index + 1}. ${method.name} - ${fee} (限額: $${method.min_amount}-$${method.max_amount})`);
      });
    } else {
      console.log('❌ 支付方式 API 失敗:', paymentMethodsResult.data);
    }

    // 4. 測試小額充值
    console.log('\n4️⃣ 測試小額充值...');
    const rechargeOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/recharge',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    // 使用第一個支付方式進行小額充值
    if (paymentMethodsResult.data.success && paymentMethodsResult.data.data.payment_methods.length > 0) {
      const firstMethod = paymentMethodsResult.data.data.payment_methods[0];
      const testAmount = 100; // 小額測試

      const rechargeResult = await makeRequest(rechargeOptions, {
        amount: testAmount,
        payment_method_id: firstMethod.id,
        description: 'UI 測試充值'
      });
      
      if (rechargeResult.status === 200 && rechargeResult.data.success) {
        console.log('✅ 小額充值測試成功!');
        console.log(`   充值金額: $${testAmount}`);
        console.log(`   手續費: $${rechargeResult.data.data.payment_details.fee}`);
        console.log(`   新餘額: ${rechargeResult.data.data.new_balance.formatted_balance}`);
      } else {
        console.log('❌ 小額充值測試失敗:', rechargeResult.data);
      }
    }

    // 5. 測試交易記錄
    console.log('\n5️⃣ 測試交易記錄 API...');
    const transactionsOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/transactions',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const transactionsResult = await makeRequest(transactionsOptions);
    
    if (transactionsResult.status === 200 && transactionsResult.data.success) {
      const transactions = transactionsResult.data.data.transactions;
      console.log(`✅ 交易記錄 API 正常: 找到 ${transactions.length} 筆交易`);
      
      if (transactions.length > 0) {
        console.log('   最新交易:');
        const latest = transactions[0];
        console.log(`   - 類型: ${latest.transaction_type}`);
        console.log(`   - 金額: $${latest.amount}`);
        console.log(`   - 時間: ${latest.created_at}`);
      }
    } else {
      console.log('❌ 交易記錄 API 失敗:', transactionsResult.data);
    }

    console.log('\n🎉 UI 修復測試完成！');
    console.log('\n📋 測試總結:');
    console.log('✅ 錢包餘額顯示修復');
    console.log('✅ 支付方式載入正常');
    console.log('✅ 充值功能正常');
    console.log('✅ 交易記錄正常');
    console.log('\n💡 建議: 現在可以在瀏覽器中訪問 http://localhost:3001/wallet/recharge 測試 UI');

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testRechargeUI();
