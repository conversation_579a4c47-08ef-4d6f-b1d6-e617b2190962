import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function TestCenter() {
  const router = useRouter();
  const [authStatus, setAuthStatus] = useState(null);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let user = null;
    if (userStr) {
      try {
        user = JSON.parse(userStr);
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setAuthStatus({
      hasToken: !!token,
      hasUser: !!user,
      user: user,
      isLoggedIn: !!(token && user)
    });
  };

  const testOptions = [
    {
      category: '認證相關',
      tests: [
        {
          title: 'Token 生成器',
          description: '生成和測試認證 Token',
          url: '/token-generator',
          icon: '🔑',
          needsAuth: false,
          status: 'available'
        },
        {
          title: 'Token 修復',
          description: '自動檢查和修復認證問題',
          url: '/token-fix',
          icon: '🔧',
          needsAuth: false,
          status: 'available'
        },
        {
          title: '超級簡化登錄',
          description: '一鍵登錄獲取 Token',
          url: '/super-simple-login',
          icon: '⚡',
          needsAuth: false,
          status: 'available'
        },
        {
          title: '登錄修復中心',
          description: '統一的登錄問題解決方案',
          url: '/login-fix-center',
          icon: '🏥',
          needsAuth: false,
          status: 'available'
        }
      ]
    },
    {
      category: '前端邏輯測試',
      tests: [
        {
          title: '無認證邏輯測試',
          description: '測試前端邏輯（不需要 Token）',
          url: '/no-auth-logic-test',
          icon: '🧪',
          needsAuth: false,
          status: 'recommended'
        },
        {
          title: '前端邏輯測試',
          description: '完整的前端邏輯測試（需要 Token）',
          url: '/frontend-logic-test',
          icon: '🔬',
          needsAuth: true,
          status: authStatus?.isLoggedIn ? 'available' : 'needsAuth'
        },
        {
          title: '服務狀態檢查',
          description: '檢查服務狀態和重複問題',
          url: '/service-status-checker',
          icon: '🔍',
          needsAuth: true,
          status: authStatus?.isLoggedIn ? 'available' : 'needsAuth'
        },
        {
          title: '重複問題分析',
          description: '分析訂閱服務重複問題',
          url: '/subscription-overlap-analyzer',
          icon: '📊',
          needsAuth: true,
          status: authStatus?.isLoggedIn ? 'available' : 'needsAuth'
        }
      ]
    },
    {
      category: '功能測試',
      tests: [
        {
          title: '訂閱頁面',
          description: '測試完整的訂閱功能',
          url: '/subscriptions',
          icon: '📦',
          needsAuth: true,
          status: authStatus?.isLoggedIn ? 'available' : 'needsAuth'
        },
        {
          title: '儀表板',
          description: '測試儀表板功能',
          url: '/dashboard-simple',
          icon: '🏠',
          needsAuth: true,
          status: authStatus?.isLoggedIn ? 'available' : 'needsAuth'
        },
        {
          title: '管理員功能',
          description: '測試管理員相關功能',
          url: '/admin/database-management',
          icon: '👑',
          needsAuth: true,
          status: authStatus?.isLoggedIn ? 'available' : 'needsAuth'
        }
      ]
    },
    {
      category: '修復工具',
      tests: [
        {
          title: '重複問題修復',
          description: '修復訂閱重複問題',
          url: '/fix-subscription-overlap',
          icon: '🔧',
          needsAuth: true,
          status: authStatus?.isLoggedIn ? 'available' : 'needsAuth'
        },
        {
          title: '前端邏輯修復總結',
          description: '查看所有修復內容',
          url: '/frontend-logic-fixes',
          icon: '📋',
          needsAuth: false,
          status: 'available'
        }
      ]
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'recommended': return 'bg-green-100 text-green-800 border-green-200';
      case 'available': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'needsAuth': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'recommended': return '推薦';
      case 'available': return '可用';
      case 'needsAuth': return '需要登錄';
      default: return '未知';
    }
  };

  return (
    <>
      <Head>
        <title>測試中心 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🧪 測試中心</h1>
                <p className="text-gray-600 mt-2">一站式測試和修復工具</p>
              </div>
              <button
                onClick={checkAuthStatus}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                🔄 刷新狀態
              </button>
            </div>

            {/* 認證狀態 */}
            {authStatus && (
              <div className={`mb-8 p-4 rounded-lg border ${
                authStatus.isLoggedIn 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <h3 className={`font-medium mb-2 ${
                  authStatus.isLoggedIn ? 'text-green-900' : 'text-red-900'
                }`}>
                  {authStatus.isLoggedIn ? '✅ 已登錄' : '❌ 未登錄'}
                </h3>
                <div className={`text-sm ${
                  authStatus.isLoggedIn ? 'text-green-800' : 'text-red-800'
                }`}>
                  {authStatus.isLoggedIn ? (
                    <div>
                      <div>用戶: {authStatus.user?.name}</div>
                      <div>角色: {authStatus.user?.role}</div>
                      <div>Email: {authStatus.user?.email}</div>
                    </div>
                  ) : (
                    <div>請先登錄以使用需要認證的功能</div>
                  )}
                </div>
              </div>
            )}

            {/* 測試選項 */}
            <div className="space-y-8">
              {testOptions.map((category, categoryIndex) => (
                <div key={categoryIndex}>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    {category.category}
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {category.tests.map((test, testIndex) => (
                      <div key={testIndex} className="bg-gray-50 border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{test.icon}</span>
                            <div>
                              <h3 className="font-semibold text-gray-900">{test.title}</h3>
                              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(test.status)}`}>
                                {getStatusText(test.status)}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-4">{test.description}</p>
                        
                        <button
                          onClick={() => router.push(test.url)}
                          disabled={test.status === 'needsAuth'}
                          className={`w-full px-4 py-2 rounded-lg font-medium transition-colors ${
                            test.status === 'needsAuth'
                              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              : test.status === 'recommended'
                                ? 'bg-green-600 text-white hover:bg-green-700'
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                          }`}
                        >
                          {test.status === 'needsAuth' ? '需要登錄' : '開始測試'}
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* 快速操作 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-4">🚀 快速操作</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button
                  onClick={() => router.push('/no-auth-logic-test')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                >
                  🧪 無認證測試
                </button>
                
                <button
                  onClick={() => router.push('/token-generator')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                >
                  🔑 生成 Token
                </button>
                
                <button
                  onClick={() => router.push('/subscriptions')}
                  disabled={!authStatus?.isLoggedIn}
                  className={`px-4 py-2 rounded-lg font-medium ${
                    authStatus?.isLoggedIn
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  📦 測試訂閱
                </button>
                
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 font-medium"
                >
                  🏠 儀表板
                </button>
              </div>
            </div>

            {/* 測試流程建議 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">📋 建議測試流程</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <div><strong>1. 前端邏輯驗證:</strong> 先使用「無認證邏輯測試」驗證計算邏輯是否正確</div>
                <div><strong>2. 認證修復:</strong> 使用「Token 生成器」或「Token 修復」獲取有效認證</div>
                <div><strong>3. 完整測試:</strong> 使用「前端邏輯測試」進行完整的認證版本測試</div>
                <div><strong>4. 功能測試:</strong> 在「訂閱頁面」測試實際的訂閱功能</div>
                <div><strong>5. 問題修復:</strong> 如發現問題，使用相應的修復工具</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
