const express = require('express');
const authRoutes = require('./auth-sqlite');

const router = express.Router();

// 認證路由
router.use('/auth', authRoutes);

// 基本健康檢查
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'API 運行正常',
    timestamp: new Date().toISOString()
  });
});

// 根路由
router.get('/', (req, res) => {
  res.json({
    message: '會員管理系統 API (SQLite 版本)',
    version: '1.0.0',
    endpoints: {
      auth: '/api/v1/auth',
      health: '/api/v1/health'
    }
  });
});

module.exports = router;
