import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function ServiceStatusChecker() {
  const router = useRouter();
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('請先登入');
        return;
      }

      // 載入所有數據
      const [subscriptionsRes, servicesRes] = await Promise.all([
        fetch('/api/subscriptions/simple', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('/api/services/database')
      ]);

      const subscriptionsResult = await subscriptionsRes.json();
      const servicesResult = await servicesRes.json();

      if (!subscriptionsResult.success || !servicesResult.success) {
        throw new Error('載入數據失敗');
      }

      const subscriptions = subscriptionsResult.data;
      const allServices = servicesResult.data.filter(s => s.status === 'active');

      // 分析數據
      const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
      const cancelledSubscriptions = subscriptions.filter(sub => sub.status === 'cancelled');
      const expiredSubscriptions = subscriptions.filter(sub => sub.status === 'expired');

      const activeSubscribedServiceIds = activeSubscriptions.map(sub => sub.service_id);
      const cancelledServiceIds = cancelledSubscriptions.map(sub => sub.service_id);

      // 計算可用服務
      const availableServices = allServices.filter(service =>
        !activeSubscribedServiceIds.includes(service.id)
      );

      // 檢查重複
      const activeVsAvailable = activeSubscribedServiceIds.filter(id =>
        availableServices.some(service => service.id === id)
      );

      const cancelledVsAvailable = cancelledServiceIds.filter(id =>
        availableServices.some(service => service.id === id)
      );

      // 詳細分析每個服務
      const serviceAnalysis = allServices.map(service => {
        const activeSubscription = activeSubscriptions.find(sub => sub.service_id === service.id);
        const cancelledSubscription = cancelledSubscriptions.find(sub => sub.service_id === service.id);
        const isAvailable = availableServices.some(s => s.id === service.id);

        return {
          id: service.id,
          name: service.name,
          hasActiveSubscription: !!activeSubscription,
          hasCancelledSubscription: !!cancelledSubscription,
          isAvailable: isAvailable,
          activeSubscriptionId: activeSubscription?.id,
          cancelledSubscriptionId: cancelledSubscription?.id,
          status: activeSubscription ? 'subscribed' : (isAvailable ? 'available' : 'unknown')
        };
      });

      setData({
        subscriptions,
        allServices,
        activeSubscriptions,
        cancelledSubscriptions,
        expiredSubscriptions,
        availableServices,
        counts: {
          totalServices: allServices.length,
          activeSubscriptions: activeSubscriptions.length,
          availableServices: availableServices.length,
          calculatedTotal: activeSubscriptions.length + availableServices.length
        },
        overlaps: {
          activeVsAvailable,
          cancelledVsAvailable
        },
        serviceAnalysis,
        serviceIds: {
          all: allServices.map(s => s.id),
          active: activeSubscribedServiceIds,
          cancelled: cancelledServiceIds,
          available: availableServices.map(s => s.id)
        }
      });

    } catch (error) {
      console.error('載入失敗:', error);
      alert('載入失敗: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">請先載入數據</p>
          <button
            onClick={loadAllData}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            載入數據
          </button>
        </div>
      </div>
    );
  }

  const hasOverlap = data.overlaps.activeVsAvailable.length > 0;
  const isBalanced = data.counts.calculatedTotal === data.counts.totalServices;

  return (
    <>
      <Head>
        <title>服務狀態檢查 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔍 服務狀態檢查</h1>
                <p className="text-gray-600 mt-2">詳細分析服務訂閱狀態</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={loadAllData}
                  disabled={isLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  🔄 重新載入
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 總體狀態 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 總體狀態</h2>
                
                <div className="space-y-4">
                  {/* 數量檢查 */}
                  <div className={`p-4 border rounded-lg ${
                    isBalanced ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}>
                    <h3 className={`font-medium mb-2 ${
                      isBalanced ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {isBalanced ? '✅' : '❌'} 數量平衡
                    </h3>
                    <div className={`text-sm space-y-1 ${
                      isBalanced ? 'text-green-800' : 'text-red-800'
                    }`}>
                      <div>總服務數: {data.counts.totalServices}</div>
                      <div>活躍訂閱: {data.counts.activeSubscriptions}</div>
                      <div>可用服務: {data.counts.availableServices}</div>
                      <div>計算總和: {data.counts.calculatedTotal}</div>
                    </div>
                  </div>

                  {/* 重複檢查 */}
                  <div className={`p-4 border rounded-lg ${
                    !hasOverlap ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}>
                    <h3 className={`font-medium mb-2 ${
                      !hasOverlap ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {!hasOverlap ? '✅' : '❌'} 重複檢查
                    </h3>
                    <div className={`text-sm space-y-1 ${
                      !hasOverlap ? 'text-green-800' : 'text-red-800'
                    }`}>
                      <div>活躍 vs 可用: {
                        data.overlaps.activeVsAvailable.length === 0 
                          ? '無重複' 
                          : `重複 [${data.overlaps.activeVsAvailable.join(', ')}]`
                      }</div>
                      <div>已取消 vs 可用: {
                        data.overlaps.cancelledVsAvailable.length === 0 
                          ? '無重複' 
                          : `重複 [${data.overlaps.cancelledVsAvailable.join(', ')}] (正常)`
                      }</div>
                    </div>
                  </div>

                  {/* 服務 ID 分布 */}
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">📋 服務 ID 分布</h3>
                    <div className="text-xs text-gray-700 space-y-2">
                      <div>
                        <strong>所有服務:</strong>
                        <div className="font-mono bg-white p-1 rounded mt-1">
                          [{data.serviceIds.all.join(', ')}]
                        </div>
                      </div>
                      <div>
                        <strong>活躍訂閱:</strong>
                        <div className="font-mono bg-white p-1 rounded mt-1">
                          [{data.serviceIds.active.join(', ')}]
                        </div>
                      </div>
                      <div>
                        <strong>可用服務:</strong>
                        <div className="font-mono bg-white p-1 rounded mt-1">
                          [{data.serviceIds.available.join(', ')}]
                        </div>
                      </div>
                      <div>
                        <strong>已取消:</strong>
                        <div className="font-mono bg-white p-1 rounded mt-1">
                          [{data.serviceIds.cancelled.join(', ')}]
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 詳細分析 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">🔍 詳細分析</h2>
                
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {data.serviceAnalysis.map(service => (
                    <div key={service.id} className={`p-3 border rounded-lg ${
                      service.hasActiveSubscription && service.isAvailable 
                        ? 'bg-red-50 border-red-200' 
                        : service.hasActiveSubscription 
                          ? 'bg-blue-50 border-blue-200'
                          : service.isAvailable
                            ? 'bg-green-50 border-green-200'
                            : 'bg-gray-50 border-gray-200'
                    }`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium text-gray-900">
                            服務 {service.id}: {service.name}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {service.hasActiveSubscription && (
                              <span className="mr-2">📦 已訂閱 (記錄 {service.activeSubscriptionId})</span>
                            )}
                            {service.hasCancelledSubscription && (
                              <span className="mr-2">❌ 已取消 (記錄 {service.cancelledSubscriptionId})</span>
                            )}
                            {service.isAvailable && (
                              <span className="mr-2">✅ 可用</span>
                            )}
                          </div>
                        </div>
                        <div className={`px-2 py-1 rounded text-xs font-medium ${
                          service.hasActiveSubscription && service.isAvailable 
                            ? 'bg-red-600 text-white' 
                            : service.hasActiveSubscription 
                              ? 'bg-blue-600 text-white'
                              : service.isAvailable
                                ? 'bg-green-600 text-white'
                                : 'bg-gray-600 text-white'
                        }`}>
                          {service.hasActiveSubscription && service.isAvailable 
                            ? '重複' 
                            : service.status
                          }
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 操作按鈕 */}
            {hasOverlap && (
              <div className="mt-8 p-4 bg-red-50 border border-red-200 rounded-lg">
                <h3 className="font-medium text-red-900 mb-3">🚨 發現重複問題</h3>
                <p className="text-sm text-red-800 mb-4">
                  有服務同時出現在活躍訂閱和可用服務中，這會導致數量計算錯誤。
                </p>
                <div className="flex space-x-4">
                  <button
                    onClick={() => router.push('/fix-subscription-overlap')}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    🔧 修復重複問題
                  </button>
                  <button
                    onClick={() => router.push('/subscriptions')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    📦 前往訂閱頁面
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
