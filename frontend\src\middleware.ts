﻿import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// 需要認證的路由
const protectedRoutes = ['/dashboard', '/minimal-dashboard', '/profile', '/wallet', '/subscriptions', '/settings'];

// 認證相關路由（已登入用戶不應該訪問）
const authRoutes = ['/auth/login', '/auth/register'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 檢查是否有認證 token (從 cookie 中)
  const token = request.cookies.get('access_token')?.value;

  // 如果是受保護的路由且沒有 token，重定向到登入頁
  if (protectedRoutes.some(route => pathname.startsWith(route)) && !token) {
    const loginUrl = new URL('/auth/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // 如果是認證路由且已有 token，重定向到儀表板
  if (authRoutes.some(route => pathname.startsWith(route)) && token) {
    return NextResponse.redirect(new URL('/dashboard-simple', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
