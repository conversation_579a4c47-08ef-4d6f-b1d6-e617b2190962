# 功能說明文檔

本文檔詳細說明 MemberService 會員管理系統的所有功能特色和使用方法。

## 🎨 設計理念

### 極簡設計原則
- **無多餘圖標** - 移除所有不必要的裝飾性圖標和圓形元素
- **純淨界面** - 通過顏色、間距和字體大小建立視覺層次
- **一致性** - 統一的設計語言和交互模式
- **專業外觀** - 商業級的簡潔設計風格

### 用戶體驗優先
- **直觀操作** - 清晰的操作流程和導航
- **即時反饋** - 操作狀態的即時顯示
- **錯誤處理** - 友好的錯誤提示和引導
- **響應式設計** - 完美適配各種螢幕尺寸

## 🔐 認證系統

### 登入功能
- **路徑**: `/auth/login`
- **支援方式**: 郵件 + 密碼
- **安全特性**:
  - JWT Token 認證
  - 同時設置 localStorage 和 Cookie
  - Middleware 路由保護
  - 自動重定向到目標頁面

### 路由保護
- **Middleware 保護**: 自動檢查認證狀態
- **受保護路由**: `/minimal-dashboard`, `/wallet`, `/subscriptions`
- **智能重定向**: 未登入自動跳轉到登入頁面，登入後回到原頁面

## 📊 極簡儀表板

### 主儀表板 (`/minimal-dashboard`)

#### 歡迎區塊
- **漸變背景**: 藍色到紫色的漸變設計
- **用戶信息**: 顯示用戶名稱和歡迎訊息
- **狀態指示**: 認證成功和會員等級（使用小圓點）

#### 統計數據卡片
- **錢包餘額**: NT$ 1,250（藍色）
- **總交易次數**: 24 次（綠色）
- **活躍訂閱**: 3 個（紫色）
- **本月消費**: NT$ 899（橙色）

#### 用戶資訊區塊
- **姓名**: 用戶真實姓名
- **電子郵件**: 註冊郵件地址
- **註冊時間**: 會員加入日期
- **會員狀態**: 已認證（綠色小圓點）

#### 快速操作
- **錢包管理**: 跳轉到 `/wallet`
- **服務訂閱**: 跳轉到 `/subscriptions`
- **數據分析**: 預留功能
- **帳戶設定**: 預留功能

#### 最近活動
- **AI 文字生成服務**: 訂閱已續費
- **錢包充值**: NT$ 500
- **個人資料**: 已更新

#### 系統狀態
- **認證狀態**: 已認證
- **用戶信息**: 當前用戶
- **當前路徑**: 頁面路由
- **載入時間**: 頁面載入時間

## 💰 錢包管理系統

### 錢包主頁 (`/wallet`)

#### 錢包概覽
- **總餘額**: NT$ 1,250.50
- **可用餘額**: NT$ 1,100.50
- **凍結金額**: NT$ 150.00
- **本月收入**: NT$ 890.00
- **本月支出**: NT$ 645.50

#### 統計卡片
- **總收入**: NT$ 5,680.00
- **總支出**: NT$ 4,429.50
- **凍結金額**: NT$ 150.00
- **交易筆數**: 6 筆

#### 交易記錄
- **標籤切換**: 全部交易、收入記錄、支出記錄
- **交易詳情**: 包含金額、時間、狀態、參考號碼
- **交易類型**: 充值、訂閱、提現、退款
- **狀態顯示**: 已完成、處理中、失敗

### 充值功能 (`/wallet/recharge`)

#### 支付方式
1. **信用卡**
   - 支援: Visa、MasterCard、JCB
   - 手續費: 2.5%
   - 到帳時間: 即時到帳

2. **銀行轉帳**
   - 支援: 網路銀行、ATM 轉帳
   - 手續費: 免費
   - 到帳時間: 1-3 個工作天

3. **LINE Pay**
   - 支援: LINE Pay 快速付款
   - 手續費: 1.5%
   - 到帳時間: 即時到帳

4. **Apple Pay**
   - 支援: Apple Pay 安全付款
   - 手續費: 2%
   - 到帳時間: 即時到帳

#### 充值流程
- **金額選擇**: 自定義輸入或快速選擇（100, 300, 500, 1000, 2000, 5000）
- **支付方式**: 選擇合適的支付方式
- **費用明細**: 顯示充值金額、手續費、總計
- **安全提示**: SSL 加密和安全說明

### 提現功能 (`/wallet/withdraw`)

#### 銀行帳戶管理
- **帳戶列表**: 顯示已綁定的銀行帳戶
- **新增帳戶**: 動態表單新增銀行帳戶
- **預設帳戶**: 標記常用提現帳戶

#### 提現流程
- **金額選擇**: 自定義輸入或快速選擇
- **帳戶選擇**: 選擇提現目標帳戶
- **費用計算**: 
  - 1000 元以下: 固定 15 元手續費
  - 1000 元以上: 0.5% 手續費（最低 15 元）
- **提現明細**: 顯示提現金額、手續費、實際到帳

#### 處理時間
- **工作日 16:00 前**: 當日處理
- **工作日 16:00 後**: 次日處理
- **預計到帳**: 1-3 個工作天

### 轉帳功能 (`/wallet/transfer`)

#### 收款人選擇
- **常用聯絡人**: 快速選擇常用收款人
- **郵件輸入**: 手動輸入收款人郵件地址
- **郵件驗證**: 自動驗證郵件格式

#### 轉帳流程
- **金額選擇**: 自定義輸入或快速選擇
- **轉帳備註**: 可選的轉帳說明（最多 100 字符）
- **費用計算**:
  - 100 元以下: 免手續費
  - 100 元以上: 0.1% 手續費（最低 5 元）
- **即時到帳**: 轉帳成功後立即到達收款人帳戶

## 📱 服務訂閱系統

### 訂閱管理 (`/subscriptions`)

#### 標籤分類
1. **使用中訂閱**: 當前活躍的服務
2. **已過期/已取消**: 歷史訂閱記錄
3. **可用服務**: 可以訂閱的新服務

#### 使用中訂閱
- **AI 文字生成服務**: NT$ 299/月
  - 使用量: 15,000/50,000 字符
  - 下次計費: 2025-07-26
  - 功能: 無限文字生成、多語言支援、模板庫、24/7 客服

- **圖片處理服務**: NT$ 199/月
  - 使用量: 250/1,000 張圖片
  - 下次計費: 2025-07-15
  - 功能: 批量處理、格式轉換、智能壓縮、雲端存儲

#### 可用服務
- **語音轉文字服務**: NT$ 149/月
- **雲端存儲服務**: NT$ 99/月（熱門推薦）
- **企業級 API 服務**: NT$ 999/月

#### 訂閱操作
- **一鍵訂閱**: 從可用服務列表直接訂閱
- **取消訂閱**: 確認對話框防止誤操作
- **續費服務**: 重新激活過期服務
- **管理訂閱**: 查看詳細使用情況

### 訂閱詳情 (`/subscription-detail`)

#### 功能標籤
1. **功能概覽**: 服務包含的所有功能
2. **使用記錄**: 歷史使用情況追蹤
3. **帳單記錄**: 完整的付款歷史

#### 使用量追蹤
- **進度條顯示**: 視覺化使用量
- **百分比計算**: 精確的使用比例
- **剩餘額度**: 清楚顯示剩餘可用量

## 🎯 技術特色

### 前端技術
- **Next.js 14**: 現代化 React 框架
- **Tailwind CSS**: 實用優先的 CSS 框架
- **Framer Motion**: 流暢的動畫效果
- **響應式設計**: 完美支援各種設備

### 狀態管理
- **localStorage**: 客戶端數據持久化
- **Cookie**: 服務端認證狀態
- **React Hooks**: 現代化狀態管理
- **實時更新**: 操作後自動更新狀態

### 用戶體驗
- **載入動畫**: 優雅的載入狀態
- **錯誤處理**: 友好的錯誤提示
- **表單驗證**: 完整的輸入驗證
- **操作確認**: 重要操作的二次確認

## 🔧 開發規範

### 代碼風格
- **組件命名**: PascalCase
- **文件命名**: kebab-case
- **變量命名**: camelCase
- **CSS 類名**: Tailwind CSS 實用類

### 設計原則
- **極簡主義**: 移除不必要的視覺元素
- **功能優先**: 每個元素都有明確的功能目的
- **一致性**: 統一的設計語言和交互模式
- **可訪問性**: 考慮不同用戶的使用需求

---

**更新日期**: 2025-06-26  
**版本**: v1.0.0  
**維護者**: MemberService 開發團隊
