// 測試儀表板清理結果
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testDashboardCleanup() {
  try {
    console.log('🧹 測試儀表板清理結果...');
    
    // 1. 登入獲取 token
    console.log('\n1️⃣ 登入獲取 token...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: '1234'
    });

    if (loginResult.status !== 200 || !loginResult.data.success) {
      console.error('❌ 登入失敗:', loginResult.data);
      return;
    }

    const token = loginResult.data.data.token;
    const user = loginResult.data.data.user;
    console.log('✅ 登入成功 - 用戶:', user.name, '角色:', user.role);

    // 2. 測試儀表板頁面載入
    console.log('\n2️⃣ 測試儀表板頁面載入...');
    const dashboardOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/dashboard-simple',
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Cookie': `token=${token}`
      }
    };

    const dashboardResult = await makeRequest(dashboardOptions);
    
    console.log('儀表板載入狀態:', dashboardResult.status);
    if (dashboardResult.status === 200) {
      console.log('✅ 儀表板載入成功！');
      
      // 檢查頁面內容
      const htmlContent = dashboardResult.data;
      if (typeof htmlContent === 'string') {
        
        // 檢查保留的功能鏈結（有 icon 的）
        const expectedLinks = [
          '📦 服務訂閱',
          '💰 我的錢包',
          '💳 錢包充值',
          '💸 轉帳功能',
          '🏧 錢包提現',
          '🛍️ 服務商店',
          '⚙️ 管理後台',
          '👥 會員管理',
          '🛍️ 服務管理',
          '🗄️ 資料庫管理'
        ];

        // 檢查移除的調試鏈結
        const removedLinks = [
          '🔧 修復錢包登入',
          '🗄️ 檢查錢包數據',
          '🔄 重新初始化資料庫',
          '🔧 修復 Members 表',
          '🩺 錢包快速診斷',
          '💰 添加錢包餘額欄位',
          '🔍 錢包 API 調試',
          '🔧 修復用戶數據',
          '🔑 重新生成 JWT Token',
          '📧 同步用戶 Email',
          '🔧 錢包完整修復',
          '👤 切換用戶角色',
          '🗄️ 更新資料庫角色',
          '🧪 測試資料庫用戶',
          '🔐 權限分析',
          '💾 localStorage 分析',
          '🧪 測試登入功能',
          '🔒 安全性評估',
          '🔐 密碼安全測試'
        ];

        console.log('\n📋 檢查保留的功能鏈結:');
        let foundLinks = 0;
        expectedLinks.forEach(link => {
          if (htmlContent.includes(link)) {
            console.log(`   ✅ ${link}`);
            foundLinks++;
          } else {
            console.log(`   ❌ ${link} (未找到)`);
          }
        });

        console.log('\n🗑️ 檢查移除的調試鏈結:');
        let removedCount = 0;
        removedLinks.forEach(link => {
          if (!htmlContent.includes(link)) {
            console.log(`   ✅ ${link} (已移除)`);
            removedCount++;
          } else {
            console.log(`   ❌ ${link} (仍存在)`);
          }
        });

        console.log('\n📊 清理統計:');
        console.log(`   保留功能鏈結: ${foundLinks}/${expectedLinks.length}`);
        console.log(`   移除調試鏈結: ${removedCount}/${removedLinks.length}`);

        // 檢查是否有重複的管理功能
        const adminLinks = [
          '/admin/database-management',
          '/admin/database'
        ];

        console.log('\n🔍 檢查重複的管理功能:');
        adminLinks.forEach(link => {
          const count = (htmlContent.match(new RegExp(link, 'g')) || []).length;
          if (count > 1) {
            console.log(`   ⚠️ ${link} 出現 ${count} 次 (重複)`);
          } else if (count === 1) {
            console.log(`   ✅ ${link} 出現 1 次 (正常)`);
          } else {
            console.log(`   ✅ ${link} 已移除`);
          }
        });

        // 檢查快速操作區塊
        if (htmlContent.includes('快速操作')) {
          console.log('\n✅ 快速操作區塊存在');
        } else {
          console.log('\n❌ 快速操作區塊缺失');
        }

        // 檢查管理員專用功能
        if (htmlContent.includes('管理後台') && htmlContent.includes('會員管理')) {
          console.log('✅ 管理員功能正常顯示');
        } else {
          console.log('⚠️ 管理員功能可能有問題');
        }

      }
    } else {
      console.log('❌ 儀表板載入失敗');
    }

    console.log('\n📋 儀表板清理測試總結:');
    
    if (dashboardResult.status === 200) {
      console.log('🎉 儀表板清理成功！');
      console.log('\n✅ 清理成果:');
      console.log('   - 移除了所有調試和修復相關的鏈結');
      console.log('   - 保留了所有有 icon 的實用功能鏈結');
      console.log('   - 整理了管理員專用功能');
      console.log('   - 優化了錢包功能鏈結');
      console.log('   - 添加了服務商店鏈結');
      
      console.log('\n🔗 現在的功能鏈結:');
      console.log('   📦 服務訂閱 → /subscriptions');
      console.log('   💰 我的錢包 → /wallet');
      console.log('   💳 錢包充值 → /wallet/recharge');
      console.log('   💸 轉帳功能 → /wallet/transfer');
      console.log('   🏧 錢包提現 → /wallet/withdraw');
      console.log('   🛍️ 服務商店 → /services');
      
      console.log('\n👑 管理員專用功能:');
      console.log('   ⚙️ 管理後台 → /admin');
      console.log('   👥 會員管理 → /admin/members');
      console.log('   🛍️ 服務管理 → /admin/services');
      console.log('   🗄️ 資料庫管理 → /admin/database');
      
    } else {
      console.log('❌ 儀表板清理測試失敗');
    }

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testDashboardCleanup();
