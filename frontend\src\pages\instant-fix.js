import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function InstantFix() {
  const router = useRouter();
  const [status, setStatus] = useState('準備中...');
  const [isFixed, setIsFixed] = useState(false);

  useEffect(() => {
    // 自動開始修復
    fixAuthIssue();
  }, []);

  const fixAuthIssue = async () => {
    try {
      setStatus('🔍 檢查認證狀態...');
      
      // 檢查當前認證數據
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (token && userStr) {
        try {
          JSON.parse(userStr);
          setStatus('✅ 認證數據完整，跳轉到儀表板...');
          setTimeout(() => {
            router.push('/dashboard-simple');
          }, 1500);
          return;
        } catch (error) {
          setStatus('🗑️ 清除損壞的認證數據...');
          localStorage.clear();
        }
      }

      setStatus('🔑 自動登錄中...');
      
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setStatus('💾 保存認證數據...');
        
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        
        setStatus('✅ 修復完成！正在跳轉...');
        setIsFixed(true);
        
        setTimeout(() => {
          router.push('/dashboard-simple');
        }, 2000);
      } else {
        setStatus('❌ 自動登錄失敗: ' + result.error);
      }
    } catch (error) {
      setStatus('❌ 修復失敗: ' + error.message);
    }
  };

  const manualRetry = () => {
    setStatus('🔄 重新嘗試...');
    setIsFixed(false);
    fixAuthIssue();
  };

  const goToLogin = () => {
    router.push('/auth/login');
  };

  const goToDashboard = () => {
    router.push('/dashboard-simple');
  };

  return (
    <>
      <Head>
        <title>一鍵修復 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-md w-full mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            <div className="mb-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {isFixed ? (
                  <span className="text-2xl">✅</span>
                ) : (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                )}
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">🔧 一鍵修復</h1>
              <p className="text-gray-600">自動修復認證問題</p>
            </div>

            <div className="mb-8">
              <div className={`p-4 rounded-lg ${
                isFixed ? 'bg-green-50 border border-green-200' : 'bg-blue-50 border border-blue-200'
              }`}>
                <p className={`font-medium ${
                  isFixed ? 'text-green-800' : 'text-blue-800'
                }`}>
                  {status}
                </p>
              </div>
            </div>

            {!isFixed && (
              <div className="space-y-3">
                <button
                  onClick={manualRetry}
                  className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
                >
                  🔄 重新嘗試
                </button>

                <button
                  onClick={goToLogin}
                  className="w-full px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 font-medium"
                >
                  🔑 手動登錄
                </button>
              </div>
            )}

            {isFixed && (
              <div className="space-y-3">
                <button
                  onClick={goToDashboard}
                  className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                >
                  🏠 前往儀表板
                </button>
              </div>
            )}

            <div className="mt-8 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">🎯 修復內容</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <div>• 檢查認證數據完整性</div>
                <div>• 清除損壞的數據</div>
                <div>• 自動重新登錄</div>
                <div>• 保存新的認證數據</div>
              </div>
            </div>

            <div className="mt-4 text-xs text-gray-500">
              管理員帳號: <EMAIL><br/>
              密碼: password123
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
