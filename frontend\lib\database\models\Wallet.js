// 錢包數據模型
const { getDatabase, dbRun, dbGet, dbAll } = require('../init');

class Wallet {
  constructor(data = {}) {
    this.id = data.id;
    this.member_id = data.member_id;
    this.transaction_type = data.transaction_type;
    this.amount = data.amount;
    this.balance_before = data.balance_before;
    this.balance_after = data.balance_after;
    this.description = data.description;
    this.reference_id = data.reference_id;
    this.reference_type = data.reference_type;
    this.status = data.status || 'completed';
    this.payment_method = data.payment_method;
    this.payment_reference = data.payment_reference;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // 獲取會員錢包餘額
  static async getBalance(memberId) {
    try {
      const db = await getDatabase();
      const result = await dbGet(db, 'SELECT wallet_balance FROM members WHERE id = ?', [memberId]);
      
      if (!result) {
        throw new Error('會員不存在');
      }

      return parseFloat(result.wallet_balance) || 0.00;
    } catch (error) {
      console.error('獲取錢包餘額失敗:', error);
      throw error;
    }
  }

  // 充值
  static async recharge(memberId, amount, paymentMethod, description = '錢包充值', paymentReference = null) {
    try {
      const db = await getDatabase();
      
      // 開始事務
      await dbRun(db, 'BEGIN TRANSACTION');

      try {
        // 獲取當前餘額
        const currentBalance = await Wallet.getBalance(memberId);
        const newBalance = currentBalance + parseFloat(amount);

        // 更新會員錢包餘額
        await dbRun(db, 
          'UPDATE members SET wallet_balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [newBalance, memberId]
        );

        // 記錄交易
        const transactionResult = await dbRun(db, `
          INSERT INTO wallet_transactions 
          (member_id, transaction_type, amount, balance_before, balance_after, description, payment_method, payment_reference, status)
          VALUES (?, 'recharge', ?, ?, ?, ?, ?, ?, 'completed')
        `, [memberId, amount, currentBalance, newBalance, description, paymentMethod, paymentReference]);

        // 提交事務
        await dbRun(db, 'COMMIT');

        // 返回交易記錄
        return await Wallet.findById(transactionResult.id);
      } catch (error) {
        // 回滾事務
        await dbRun(db, 'ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error('充值失敗:', error);
      throw error;
    }
  }

  // 提現
  static async withdraw(memberId, amount, description = '錢包提現', paymentReference = null) {
    try {
      const db = await getDatabase();
      
      // 開始事務
      await dbRun(db, 'BEGIN TRANSACTION');

      try {
        // 獲取當前餘額
        const currentBalance = await Wallet.getBalance(memberId);
        
        if (currentBalance < parseFloat(amount)) {
          throw new Error('餘額不足');
        }

        const newBalance = currentBalance - parseFloat(amount);

        // 更新會員錢包餘額
        await dbRun(db, 
          'UPDATE members SET wallet_balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [newBalance, memberId]
        );

        // 記錄交易
        const transactionResult = await dbRun(db, `
          INSERT INTO wallet_transactions 
          (member_id, transaction_type, amount, balance_before, balance_after, description, payment_reference, status)
          VALUES (?, 'withdraw', ?, ?, ?, ?, ?, 'completed')
        `, [memberId, amount, currentBalance, newBalance, description, paymentReference]);

        // 提交事務
        await dbRun(db, 'COMMIT');

        // 返回交易記錄
        return await Wallet.findById(transactionResult.id);
      } catch (error) {
        // 回滾事務
        await dbRun(db, 'ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error('提現失敗:', error);
      throw error;
    }
  }

  // 支付（扣款）
  static async payment(memberId, amount, description, referenceId = null, referenceType = null) {
    try {
      const db = await getDatabase();
      
      // 開始事務
      await dbRun(db, 'BEGIN TRANSACTION');

      try {
        // 獲取當前餘額
        const currentBalance = await Wallet.getBalance(memberId);
        
        if (currentBalance < parseFloat(amount)) {
          throw new Error('餘額不足');
        }

        const newBalance = currentBalance - parseFloat(amount);

        // 更新會員錢包餘額
        await dbRun(db, 
          'UPDATE members SET wallet_balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [newBalance, memberId]
        );

        // 記錄交易
        const transactionResult = await dbRun(db, `
          INSERT INTO wallet_transactions 
          (member_id, transaction_type, amount, balance_before, balance_after, description, reference_id, reference_type, status)
          VALUES (?, 'payment', ?, ?, ?, ?, ?, ?, 'completed')
        `, [memberId, amount, currentBalance, newBalance, description, referenceId, referenceType]);

        // 提交事務
        await dbRun(db, 'COMMIT');

        // 返回交易記錄
        return await Wallet.findById(transactionResult.id);
      } catch (error) {
        // 回滾事務
        await dbRun(db, 'ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error('支付失敗:', error);
      throw error;
    }
  }

  // 退款
  static async refund(memberId, amount, description, referenceId = null, referenceType = null) {
    try {
      const db = await getDatabase();
      
      // 開始事務
      await dbRun(db, 'BEGIN TRANSACTION');

      try {
        // 獲取當前餘額
        const currentBalance = await Wallet.getBalance(memberId);
        const newBalance = currentBalance + parseFloat(amount);

        // 更新會員錢包餘額
        await dbRun(db, 
          'UPDATE members SET wallet_balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [newBalance, memberId]
        );

        // 記錄交易
        const transactionResult = await dbRun(db, `
          INSERT INTO wallet_transactions 
          (member_id, transaction_type, amount, balance_before, balance_after, description, reference_id, reference_type, status)
          VALUES (?, 'refund', ?, ?, ?, ?, ?, ?, 'completed')
        `, [memberId, amount, currentBalance, newBalance, description, referenceId, referenceType]);

        // 提交事務
        await dbRun(db, 'COMMIT');

        // 返回交易記錄
        return await Wallet.findById(transactionResult.id);
      } catch (error) {
        // 回滾事務
        await dbRun(db, 'ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error('退款失敗:', error);
      throw error;
    }
  }

  // 根據 ID 查找交易記錄
  static async findById(id) {
    try {
      const db = await getDatabase();
      const row = await dbGet(db, 'SELECT * FROM wallet_transactions WHERE id = ?', [id]);
      
      if (!row) {
        return null;
      }

      return new Wallet(row);
    } catch (error) {
      console.error('根據 ID 查找交易記錄失敗:', error);
      throw error;
    }
  }

  // 獲取會員的交易記錄
  static async findByMemberId(memberId, options = {}) {
    try {
      const db = await getDatabase();
      let sql = 'SELECT * FROM wallet_transactions WHERE member_id = ?';
      const params = [memberId];
      const conditions = [];

      // 交易類型篩選
      if (options.type) {
        conditions.push('transaction_type = ?');
        params.push(options.type);
      }

      // 狀態篩選
      if (options.status) {
        conditions.push('status = ?');
        params.push(options.status);
      }

      // 日期範圍篩選
      if (options.startDate) {
        conditions.push('created_at >= ?');
        params.push(options.startDate);
      }

      if (options.endDate) {
        conditions.push('created_at <= ?');
        params.push(options.endDate);
      }

      if (conditions.length > 0) {
        sql += ' AND ' + conditions.join(' AND ');
      }

      // 排序
      sql += ' ORDER BY created_at DESC';

      // 分頁
      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit);
        
        if (options.offset) {
          sql += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const rows = await dbAll(db, sql, params);
      return rows.map(row => new Wallet(row));
    } catch (error) {
      console.error('獲取會員交易記錄失敗:', error);
      throw error;
    }
  }

  // 獲取會員錢包統計
  static async getStats(memberId) {
    try {
      const db = await getDatabase();
      
      const stats = await dbGet(db, `
        SELECT 
          COUNT(*) as total_transactions,
          COUNT(CASE WHEN transaction_type = 'recharge' THEN 1 END) as recharge_count,
          COUNT(CASE WHEN transaction_type = 'withdraw' THEN 1 END) as withdraw_count,
          COUNT(CASE WHEN transaction_type = 'payment' THEN 1 END) as payment_count,
          COUNT(CASE WHEN transaction_type = 'refund' THEN 1 END) as refund_count,
          COALESCE(SUM(CASE WHEN transaction_type = 'recharge' THEN amount ELSE 0 END), 0) as total_recharge,
          COALESCE(SUM(CASE WHEN transaction_type = 'withdraw' THEN amount ELSE 0 END), 0) as total_withdraw,
          COALESCE(SUM(CASE WHEN transaction_type = 'payment' THEN amount ELSE 0 END), 0) as total_payment,
          COALESCE(SUM(CASE WHEN transaction_type = 'refund' THEN amount ELSE 0 END), 0) as total_refund
        FROM wallet_transactions 
        WHERE member_id = ?
      `, [memberId]);

      const currentBalance = await Wallet.getBalance(memberId);

      return {
        ...stats,
        current_balance: currentBalance
      };
    } catch (error) {
      console.error('獲取錢包統計失敗:', error);
      throw error;
    }
  }

  // 轉換為 JSON 格式
  toJSON() {
    return {
      id: this.id,
      member_id: this.member_id,
      transaction_type: this.transaction_type,
      amount: parseFloat(this.amount),
      balance_before: parseFloat(this.balance_before),
      balance_after: parseFloat(this.balance_after),
      description: this.description,
      reference_id: this.reference_id,
      reference_type: this.reference_type,
      status: this.status,
      payment_method: this.payment_method,
      payment_reference: this.payment_reference,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = Wallet;
