import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function TestLogin() {
  const router = useRouter();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('123456');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('開始登入...');

    try {
      // 模擬登入延遲
      await new Promise(resolve => setTimeout(resolve, 1000));

      setMessage('保存認證數據...');

      // 保存認證數據
      localStorage.setItem('token', 'test-jwt-token');
      localStorage.setItem('user', JSON.stringify({
        name: '測試用戶',
        email: email,
        avatar: null,
        memberSince: new Date().toISOString()
      }));

      setMessage('認證數據已保存，準備跳轉...');

      // 驗證數據是否保存成功
      const savedToken = localStorage.getItem('token');
      const savedUser = localStorage.getItem('user');

      if (!savedToken || !savedUser) {
        throw new Error('localStorage 保存失敗');
      }

      setMessage('正在跳轉到儀表板...');

      // 嘗試跳轉
      try {
        await router.push('/minimal-dashboard');
        setMessage('跳轉成功！');
      } catch (routerError) {
        console.error('路由跳轉錯誤:', routerError);
        setMessage('路由跳轉失敗: ' + routerError.message);

        // 嘗試使用 window.location
        setTimeout(() => {
          window.location.href = '/minimal-dashboard';
        }, 1000);
      }

    } catch (err) {
      console.error('登入錯誤:', err);
      setMessage('登入失敗: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const checkStorage = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    setMessage(`Token: ${token ? '存在' : '不存在'}, User: ${user ? '存在' : '不存在'}`);
  };

  const clearStorage = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setMessage('已清除 localStorage');
  };

  const goToDashboard = async () => {
    setMessage('嘗試跳轉到儀表板...');
    try {
      await router.push('/minimal-dashboard');
      setMessage('跳轉成功！');
    } catch (error) {
      console.error('跳轉錯誤:', error);
      setMessage('跳轉失敗: ' + error.message);

      // 嘗試使用 window.location
      setMessage('嘗試使用 window.location 跳轉...');
      setTimeout(() => {
        window.location.href = '/minimal-dashboard';
      }, 1000);
    }
  };

  const goToTestDashboard = async () => {
    setMessage('嘗試跳轉到測試儀表板...');
    try {
      await router.push('/test-dashboard');
      setMessage('跳轉成功！');
    } catch (error) {
      console.error('跳轉錯誤:', error);
      setMessage('跳轉失敗: ' + error.message);

      // 嘗試使用 window.location
      setMessage('嘗試使用 window.location 跳轉...');
      setTimeout(() => {
        window.location.href = '/test-dashboard';
      }, 1000);
    }
  };

  return (
    <>
      <Head>
        <title>測試登入 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900">測試登入</h2>
            <p className="mt-2 text-sm text-gray-600">用於調試登入流程</p>
          </div>

          <div className="bg-white p-8 rounded-xl shadow-lg space-y-6">
            {/* 登入表單 */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  電子郵件
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="請輸入電子郵件"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  密碼
                </label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="請輸入密碼"
                />
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isLoading ? '登入中...' : '測試登入'}
              </button>
            </form>

            {/* 調試按鈕 */}
            <div className="border-t pt-4 space-y-2">
              <button
                onClick={checkStorage}
                className="w-full py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors"
              >
                檢查 localStorage
              </button>
              
              <button
                onClick={clearStorage}
                className="w-full py-2 px-4 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors"
              >
                清除 localStorage
              </button>
              
              <button
                onClick={goToDashboard}
                className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors"
              >
                跳轉到正式儀表板
              </button>

              <button
                onClick={goToTestDashboard}
                className="w-full py-2 px-4 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors"
              >
                跳轉到測試儀表板
              </button>
            </div>

            {/* 訊息顯示 */}
            {message && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-700">{message}</p>
              </div>
            )}

            {/* 返回連結 */}
            <div className="text-center">
              <a href="/auth/login" className="text-sm text-blue-600 hover:text-blue-500">
                返回正式登入頁面
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
