// 初始化服務數據 API
// 用於確保所有模擬服務都存在於數據文件中

import fs from 'fs';
import path from 'path';

const DATA_FILE = path.join(process.cwd(), 'data', 'services.json');

// 模擬服務數據
const mockServices = [
  {
    id: 1,
    name: 'AI 文字生成服務',
    description: '強大的 AI 文字生成工具，支援多種語言和格式',
    price: 299,
    billingCycle: 'monthly',
    category: 'AI 服務',
    features: ['無限文字生成', '多語言支援', '模板庫', '24/7 客服'],
    status: 'active',
    popular: true,
    subscribers: 1250,
    revenue: 373750,
    maxUsers: 1,
    apiLimit: 50000,
    storageLimit: '10GB',
    supportLevel: 'premium',
    createdAt: '2025-01-15',
    updatedAt: '2025-06-20'
  },
  {
    id: 2,
    name: '圖片處理服務',
    description: '專業的圖片編輯和處理工具',
    price: 199,
    billingCycle: 'monthly',
    category: '媒體處理',
    features: ['批量處理', '格式轉換', '智能壓縮', '雲端存儲'],
    status: 'active',
    popular: false,
    subscribers: 890,
    revenue: 177110,
    maxUsers: 3,
    apiLimit: 1000,
    storageLimit: '50GB',
    supportLevel: 'standard',
    createdAt: '2025-02-01',
    updatedAt: '2025-06-18'
  },
  {
    id: 3,
    name: '數據分析服務',
    description: '深度數據分析和可視化工具',
    price: 499,
    billingCycle: 'monthly',
    category: '數據分析',
    features: ['實時分析', '自定義報表', '數據可視化', 'API 接入'],
    status: 'inactive',
    popular: false,
    subscribers: 320,
    revenue: 159680,
    maxUsers: 5,
    apiLimit: 100,
    storageLimit: '100GB',
    supportLevel: 'premium',
    createdAt: '2025-03-10',
    updatedAt: '2025-06-15'
  },
  {
    id: 4,
    name: '語音轉文字服務',
    description: '高精度語音識別和轉換服務',
    price: 149,
    billingCycle: 'monthly',
    category: 'AI 服務',
    features: ['多語言識別', '實時轉換', '批量處理', '高精度識別'],
    status: 'active',
    popular: false,
    subscribers: 560,
    revenue: 83440,
    maxUsers: 1,
    apiLimit: 10000,
    storageLimit: '5GB',
    supportLevel: 'basic',
    createdAt: '2025-04-05',
    updatedAt: '2025-06-22'
  },
  {
    id: 5,
    name: '雲端存儲服務',
    description: '安全可靠的雲端存儲解決方案',
    price: 99,
    billingCycle: 'monthly',
    category: '存儲服務',
    features: ['無限存儲', '自動備份', '版本控制', '分享功能'],
    status: 'active',
    popular: true,
    subscribers: 2100,
    revenue: 207900,
    maxUsers: 10,
    apiLimit: 0,
    storageLimit: '無限',
    supportLevel: 'standard',
    createdAt: '2025-01-20',
    updatedAt: '2025-06-25'
  },
  {
    id: 6,
    name: '企業級 API 服務',
    description: '為企業提供的高級 API 接入服務',
    price: 999,
    billingCycle: 'monthly',
    category: '企業服務',
    features: ['無限 API 調用', '專屬支援', '自定義功能', 'SLA 保證'],
    status: 'active',
    popular: false,
    subscribers: 85,
    revenue: 84915,
    maxUsers: 50,
    apiLimit: 0,
    storageLimit: '1TB',
    supportLevel: 'enterprise',
    createdAt: '2025-05-01',
    updatedAt: '2025-06-24'
  }
];

// 確保數據目錄存在
const ensureDataDirectory = () => {
  const dataDir = path.dirname(DATA_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// 讀取現有數據
const readExistingData = () => {
  try {
    ensureDataDirectory();
    if (fs.existsSync(DATA_FILE)) {
      const data = fs.readFileSync(DATA_FILE, 'utf8');
      return JSON.parse(data);
    }
    return null;
  } catch (error) {
    console.error('讀取現有數據失敗:', error);
    return null;
  }
};

// 寫入數據
const writeData = (data) => {
  try {
    ensureDataDirectory();
    
    // 更新元數據
    const services = data.services || [];
    data.metadata = {
      totalServices: services.length,
      activeServices: services.filter(s => s.status === 'active').length,
      inactiveServices: services.filter(s => s.status === 'inactive').length,
      totalSubscribers: services.reduce((sum, s) => sum + (s.subscribers || 0), 0),
      totalRevenue: services.reduce((sum, s) => sum + (s.revenue || 0), 0),
      categories: [...new Set(services.map(s => s.category).filter(Boolean))]
    };
    
    data.lastUpdated = new Date().toISOString();
    
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('寫入數據失敗:', error);
    return false;
  }
};

// 合併服務數據
const mergeServices = (existingServices, mockServices) => {
  const serviceMap = new Map();
  
  // 首先添加現有服務
  existingServices.forEach(service => {
    serviceMap.set(service.id, service);
  });
  
  // 然後添加模擬數據中不存在的服務
  mockServices.forEach(mockService => {
    if (!serviceMap.has(mockService.id)) {
      serviceMap.set(mockService.id, mockService);
    }
  });
  
  // 轉換為數組並按 ID 排序
  return Array.from(serviceMap.values()).sort((a, b) => a.id - b.id);
};

export default function handler(req, res) {
  try {
    const { method } = req;

    if (method !== 'POST') {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({
        success: false,
        error: `方法 ${method} 不被允許`
      });
    }

    // 讀取現有數據
    const existingData = readExistingData();
    
    let finalData;
    
    if (existingData && existingData.services) {
      // 如果有現有數據，合併模擬數據
      const mergedServices = mergeServices(existingData.services, mockServices);
      finalData = {
        ...existingData,
        services: mergedServices,
        version: "1.0.0"
      };
    } else {
      // 如果沒有現有數據，創建新的數據結構
      finalData = {
        lastUpdated: new Date().toISOString(),
        version: "1.0.0",
        services: mockServices,
        metadata: {}
      };
    }

    // 寫入數據
    const success = writeData(finalData);
    
    if (success) {
      res.status(200).json({
        success: true,
        message: '服務數據初始化成功',
        data: {
          totalServices: finalData.services.length,
          addedServices: finalData.services.filter(s => 
            mockServices.some(mock => mock.id === s.id)
          ).length,
          existingServices: existingData ? existingData.services.length : 0
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: '初始化失敗'
      });
    }
  } catch (error) {
    console.error('初始化 API 錯誤:', error);
    res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: error.message
    });
  }
}
