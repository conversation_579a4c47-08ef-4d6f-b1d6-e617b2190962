// 檢查 services 表結構
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'frontend', 'lib', 'database', 'database.sqlite');

function checkServicesTable() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('❌ 連接數據庫失敗:', err);
        reject(err);
        return;
      }
      console.log('✅ 數據庫連接成功');
    });

    console.log('🔍 檢查 services 表結構...');

    // 1. 檢查 services 表結構
    db.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='services'", (err, table) => {
      if (err) {
        console.error('❌ 查詢 services 表失敗:', err);
        reject(err);
        return;
      }

      if (table) {
        console.log('\n📋 services 表結構:');
        console.log(table.sql);
      } else {
        console.log('\n❌ services 表不存在');
      }

      // 2. 檢查 services 表的實際列
      db.all("PRAGMA table_info(services)", (err, columns) => {
        if (err) {
          console.error('❌ 查詢表列信息失敗:', err);
        } else {
          console.log('\n📋 services 表的實際列:');
          columns.forEach(col => {
            console.log(`   - ${col.name} (${col.type})`);
          });
        }

        // 3. 檢查 service_stats 表是否存在
        db.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='service_stats'", (err, statsTable) => {
          if (err) {
            console.error('❌ 查詢 service_stats 表失敗:', err);
          } else if (statsTable) {
            console.log('\n📋 service_stats 表結構:');
            console.log(statsTable.sql);
          } else {
            console.log('\n❌ service_stats 表不存在');
          }

          db.close();
          resolve();
        });
      });
    });
  });
}

// 執行檢查
checkServicesTable()
  .then(() => {
    console.log('\n✅ services 表檢查完成');
  })
  .catch((error) => {
    console.error('\n❌ 檢查失敗:', error);
  });
