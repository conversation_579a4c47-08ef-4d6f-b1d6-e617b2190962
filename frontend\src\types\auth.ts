// 用戶相關類型定義
export interface User {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  status: 'active' | 'inactive' | 'suspended' | 'deleted';
  email_verified: boolean;
  phone_verified: boolean;
  two_factor_enabled: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;

  // 用戶資料
  first_name?: string;
  last_name?: string;
  display_name?: string;
  avatar_url?: string;
  birth_date?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  country?: string;
  timezone?: string;
  language: string;
  currency: string;
}

// 註冊表單數據
export interface RegisterFormData {
  username: string;
  email?: string;
  phone?: string;
  password: string;
  confirmPassword: string;
  first_name?: string;
  last_name?: string;
  agree_terms: boolean;
  recaptcha_token?: string;
}

// 登入表單數據
export interface LoginFormData {
  identifier: string; // 可以是 username, email 或 phone
  password: string;
  remember_me?: boolean;
  recaptcha_token?: string;
}

// 忘記密碼表單數據
export interface ForgotPasswordFormData {
  email: string;
  recaptcha_token?: string;
}

// 重設密碼表單數據
export interface ResetPasswordFormData {
  token: string;
  password: string;
  confirmPassword: string;
}

// 驗證碼表單數據
export interface VerificationFormData {
  code: string;
  type: 'email' | 'phone';
}

// 認證響應
export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    access_token: string;
    refresh_token: string;
    expires_in: number;
  };
  errors?: Record<string, string[]>;
}

// OAuth 提供商
export type OAuthProvider = 'google' | 'facebook' | 'line' | 'apple';

// OAuth 響應
export interface OAuthResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    access_token: string;
    refresh_token: string;
    expires_in: number;
    is_new_user: boolean;
  };
}

// 用戶狀態
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// API 錯誤響應
export interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
  status: number;
}