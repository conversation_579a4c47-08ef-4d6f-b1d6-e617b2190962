{"name": "member-system-frontend", "version": "1.0.0", "description": "會員系統前端應用", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "dev:ngrok": "next dev --hostname 0.0.0.0", "build": "next build", "start": "next start", "start:ngrok": "next start --hostname 0.0.0.0", "lint": "next lint", "ngrok": "ngrok http 3001 --hostname=topyun.ngrok.app", "db:init": "node ../scripts/init-database.js", "db:reset": "node ../scripts/init-database.js --reset", "db:test": "node ../scripts/init-database.js --with-test-data"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "axios": "^1.10.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.19.1", "jsonwebtoken": "^9.0.2", "next": "15.3.4", "next-i18next": "^15.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}