// 測試後端登入 API
const { default: fetch } = require('node-fetch');

async function testLogin() {
  try {
    console.log('測試後端登入 API...');
    
    const response = await fetch('http://localhost:3000/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password'
      })
    });

    console.log('響應狀態:', response.status);
    console.log('響應頭:', response.headers.raw());
    
    const result = await response.text();
    console.log('響應內容:', result);
    
    // 嘗試解析 JSON
    try {
      const jsonResult = JSON.parse(result);
      console.log('JSON 解析成功:', jsonResult);
    } catch (e) {
      console.log('JSON 解析失敗:', e.message);
    }
    
  } catch (error) {
    console.error('測試失敗:', error.message);
  }
}

testLogin();
