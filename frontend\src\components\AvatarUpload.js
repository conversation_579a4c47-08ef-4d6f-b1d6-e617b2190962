import { useState, useRef } from 'react';
import { motion } from 'framer-motion';

export default function AvatarUpload({ currentAvatar, onAvatarChange }) {
  const [preview, setPreview] = useState(currentAvatar);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef(null);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // 檢查文件類型
    if (!file.type.startsWith('image/')) {
      alert('請選擇圖片文件');
      return;
    }

    // 檢查文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('圖片大小不能超過 5MB');
      return;
    }

    // 創建預覽
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target.result);
    };
    reader.readAsDataURL(file);

    // 模擬上傳
    handleUpload(file);
  };

  const handleUpload = async (file) => {
    setIsUploading(true);

    try {
      // 模擬上傳延遲
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模擬成功上傳
      const avatarUrl = URL.createObjectURL(file);
      onAvatarChange(avatarUrl);
      
      alert('頭像上傳成功！');
    } catch (error) {
      alert('頭像上傳失敗，請稍後再試');
      setPreview(currentAvatar);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveAvatar = () => {
    setPreview(null);
    onAvatarChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* 頭像預覽 */}
      <div className="relative">
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="relative w-32 h-32 rounded-full overflow-hidden bg-gray-200 border-4 border-white shadow-lg"
        >
          {preview ? (
            <img
              src={preview}
              alt="頭像預覽"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-blue-600">
              <span className="text-white text-4xl font-bold">
                {currentAvatar ? '?' : '👤'}
              </span>
            </div>
          )}
          
          {/* 上傳中的覆蓋層 */}
          {isUploading && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
          )}
        </motion.div>

        {/* 編輯按鈕 */}
        <button
          onClick={triggerFileInput}
          disabled={isUploading}
          className="absolute bottom-0 right-0 w-10 h-10 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
        >
          📷
        </button>
      </div>

      {/* 操作按鈕 */}
      <div className="flex space-x-3">
        <button
          onClick={triggerFileInput}
          disabled={isUploading}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            isUploading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isUploading ? '上傳中...' : '選擇圖片'}
        </button>

        {preview && (
          <button
            onClick={handleRemoveAvatar}
            disabled={isUploading}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            移除頭像
          </button>
        )}
      </div>

      {/* 文件輸入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* 提示文字 */}
      <div className="text-center">
        <p className="text-sm text-gray-500">
          支援 JPG、PNG、GIF 格式
        </p>
        <p className="text-xs text-gray-400">
          文件大小不超過 5MB
        </p>
      </div>
    </div>
  );
}

// 頭像顯示組件
export function AvatarDisplay({ avatar, name, size = 'md' }) {
  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-12 h-12 text-lg',
    lg: 'w-16 h-16 text-xl',
    xl: 'w-24 h-24 text-3xl'
  };

  const getInitials = (name) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`${sizeClasses[size]} rounded-full overflow-hidden bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center`}>
      {avatar ? (
        <img
          src={avatar}
          alt={name || '用戶頭像'}
          className="w-full h-full object-cover"
        />
      ) : (
        <span className="text-white font-bold">
          {getInitials(name)}
        </span>
      )}
    </div>
  );
}

// 頭像選擇器組件
export function AvatarSelector({ selectedAvatar, onSelect }) {
  const defaultAvatars = [
    '👤', '👨', '👩', '🧑', '👦', '👧',
    '👨‍💼', '👩‍💼', '👨‍💻', '👩‍💻', '👨‍🎓', '👩‍🎓',
    '🐱', '🐶', '🐼', '🦊', '🐸', '🐧'
  ];

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">選擇預設頭像</h4>
      <div className="grid grid-cols-6 gap-3">
        {defaultAvatars.map((emoji, index) => (
          <button
            key={index}
            onClick={() => onSelect(emoji)}
            className={`w-12 h-12 rounded-full border-2 transition-colors flex items-center justify-center text-2xl ${
              selectedAvatar === emoji
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300 bg-white'
            }`}
          >
            {emoji}
          </button>
        ))}
      </div>
    </div>
  );
}
