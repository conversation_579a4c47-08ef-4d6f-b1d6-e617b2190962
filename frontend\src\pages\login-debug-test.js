import { useState } from 'react';
import Head from 'next/head';

export default function LoginDebugTest() {
  const [formData, setFormData] = useState({
    email: '<EMAIL>',
    password: '1234'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [logs, setLogs] = useState([]);
  const [userInfo, setUserInfo] = useState(null);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const checkUserInDatabase = async () => {
    addLog('🔍 檢查用戶在資料庫中的信息...', 'info');
    
    try {
      const response = await fetch('/api/members/list');
      const data = await response.json();
      
      if (data.success) {
        const user = data.data.find(m => m.email === formData.email);
        if (user) {
          setUserInfo(user);
          addLog(`✅ 找到用戶: ${user.name} (ID: ${user.id})`, 'success');
          addLog(`📧 Email: ${user.email}`, 'info');
          addLog(`👤 角色: ${user.role}`, 'info');
          addLog(`📱 狀態: ${user.status}`, 'info');
          addLog(`✉️ Email驗證: ${user.email_verified ? '已驗證' : '未驗證'}`, 'info');
          addLog(`🕐 註冊時間: ${user.created_at}`, 'info');
          addLog(`🕐 最後登入: ${user.last_login_at || '從未'}`, 'info');
        } else {
          setUserInfo(null);
          addLog(`❌ 在資料庫中未找到 ${formData.email}`, 'error');
        }
      } else {
        addLog(`❌ 查詢資料庫失敗: ${data.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 資料庫查詢請求失敗: ${error.message}`, 'error');
    }
  };

  const testOriginalLogin = async () => {
    setIsLoading(true);
    setResult(null);
    addLog('🔑 測試原始登入 API...', 'info');

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        addLog('✅ 原始登入 API 成功', 'success');
        setResult({ type: 'success', api: 'original', data });
      } else {
        addLog(`❌ 原始登入 API 失敗: ${data.error}`, 'error');
        setResult({ type: 'error', api: 'original', data });
      }
    } catch (error) {
      addLog(`❌ 原始登入 API 請求失敗: ${error.message}`, 'error');
      setResult({ type: 'error', api: 'original', error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const testDatabaseLogin = async () => {
    setIsLoading(true);
    setResult(null);
    addLog('🗄️ 測試資料庫登入 API...', 'info');

    try {
      const response = await fetch('/api/auth/login-db', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        addLog('✅ 資料庫登入 API 成功', 'success');
        addLog(`👤 用戶: ${data.data.user.name}`, 'success');
        addLog(`📧 Email: ${data.data.user.email}`, 'success');
        addLog(`📦 訂閱數量: ${data.data.subscriptions.length}`, 'success');
        setResult({ type: 'success', api: 'database', data });
        
        // 保存登入信息
        localStorage.setItem('token', data.data.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
        addLog('💾 登入信息已保存到 localStorage', 'success');
      } else {
        addLog(`❌ 資料庫登入 API 失敗: ${data.error}`, 'error');
        setResult({ type: 'error', api: 'database', data });
      }
    } catch (error) {
      addLog(`❌ 資料庫登入 API 請求失敗: ${error.message}`, 'error');
      setResult({ type: 'error', api: 'database', error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const testPasswordVariations = async () => {
    const passwords = ['1234', '123456', 'password', 'test'];
    addLog('🔐 測試不同密碼組合...', 'info');
    
    for (const pwd of passwords) {
      addLog(`🧪 嘗試密碼: ${pwd}`, 'info');
      
      try {
        const response = await fetch('/api/auth/login-db', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: formData.email,
            password: pwd
          })
        });

        const data = await response.json();
        
        if (response.ok && data.success) {
          addLog(`✅ 密碼 "${pwd}" 登入成功！`, 'success');
          setFormData(prev => ({ ...prev, password: pwd }));
          return;
        } else {
          addLog(`❌ 密碼 "${pwd}" 登入失敗: ${data.error}`, 'error');
        }
      } catch (error) {
        addLog(`❌ 密碼 "${pwd}" 測試出錯: ${error.message}`, 'error');
      }
      
      // 短暫延遲避免過快請求
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    addLog('🔍 所有密碼測試完成', 'info');
  };

  const clearLogs = () => {
    setLogs([]);
    setResult(null);
    setUserInfo(null);
  };

  const goToDashboard = () => {
    window.location.href = '/dashboard';
  };

  return (
    <>
      <Head>
        <title>登入調試測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔑 登入調試測試</h1>
              <p className="text-gray-600 mt-2">調試 <EMAIL> 的登入問題</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* 登入表單 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">登入表單</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">密碼</label>
                    <input
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                {/* 測試按鈕 */}
                <div className="mt-6 space-y-3">
                  <button
                    onClick={checkUserInDatabase}
                    className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                  >
                    🔍 檢查用戶資料
                  </button>
                  
                  <button
                    onClick={testOriginalLogin}
                    disabled={isLoading}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isLoading ? '測試中...' : '🔑 測試原始登入 API'}
                  </button>
                  
                  <button
                    onClick={testDatabaseLogin}
                    disabled={isLoading}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                  >
                    {isLoading ? '測試中...' : '🗄️ 測試資料庫登入 API'}
                  </button>
                  
                  <button
                    onClick={testPasswordVariations}
                    disabled={isLoading}
                    className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50"
                  >
                    {isLoading ? '測試中...' : '🔐 測試密碼組合'}
                  </button>
                  
                  <button
                    onClick={clearLogs}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  >
                    🗑️ 清除日誌
                  </button>

                  {result && result.type === 'success' && (
                    <button
                      onClick={goToDashboard}
                      className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                    >
                      🚀 前往儀表板
                    </button>
                  )}
                </div>
              </div>

              {/* 用戶信息 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">用戶信息</h2>
                
                {userInfo ? (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="space-y-2 text-sm">
                      <div><strong>ID:</strong> {userInfo.id}</div>
                      <div><strong>姓名:</strong> {userInfo.name}</div>
                      <div><strong>Email:</strong> {userInfo.email}</div>
                      <div><strong>角色:</strong> {userInfo.role}</div>
                      <div><strong>狀態:</strong> {userInfo.status}</div>
                      <div><strong>電話:</strong> {userInfo.phone || '未提供'}</div>
                      <div><strong>Email驗證:</strong> {userInfo.email_verified ? '✅ 已驗證' : '❌ 未驗證'}</div>
                      <div><strong>註冊時間:</strong> {new Date(userInfo.created_at).toLocaleString()}</div>
                      <div><strong>最後登入:</strong> {userInfo.last_login_at ? new Date(userInfo.last_login_at).toLocaleString() : '從未'}</div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center text-gray-500">
                    點擊 "檢查用戶資料" 載入信息
                  </div>
                )}

                {/* API 響應 */}
                {result && (
                  <div className={`mt-4 p-4 rounded-lg ${
                    result.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                  }`}>
                    <h3 className={`font-medium ${
                      result.type === 'success' ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {result.api === 'original' ? '原始 API' : '資料庫 API'} 響應
                    </h3>
                    <pre className={`mt-2 text-xs overflow-auto max-h-32 ${
                      result.type === 'success' ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {JSON.stringify(result.data || result.error, null, 2)}
                    </pre>
                  </div>
                )}
              </div>

              {/* 測試日誌 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試日誌</h2>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                  {logs.map((log) => (
                    <div key={log.id} className={`mb-1 ${
                      log.type === 'error' ? 'text-red-400' : 
                      log.type === 'success' ? 'text-green-400' : 
                      'text-blue-400'
                    }`}>
                      [{log.timestamp}] {log.message}
                    </div>
                  ))}
                  {logs.length === 0 && (
                    <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                  )}
                </div>
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">🔍 調試說明</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>問題:</strong> <EMAIL> 無法登入</p>
                <p><strong>可能原因:</strong> 密碼不匹配、API 問題、資料庫問題</p>
                <p><strong>測試步驟:</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>檢查用戶是否存在於資料庫</li>
                  <li>測試不同的登入 API</li>
                  <li>嘗試不同的密碼組合</li>
                  <li>確認登入成功後的跳轉</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
