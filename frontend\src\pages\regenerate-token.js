import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function RegenerateToken() {
  const router = useRouter();
  const [regenerateResults, setRegenerateResults] = useState([]);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [tokenInfo, setTokenInfo] = useState(null);

  useEffect(() => {
    checkCurrentToken();
  }, []);

  const addResult = (step, status, message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setRegenerateResults(prev => [...prev, {
      timestamp,
      step,
      status, // 'info', 'success', 'warning', 'error'
      message,
      data
    }]);
  };

  const checkCurrentToken = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let tokenData = null;
    let userData = null;

    if (token) {
      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          tokenData = JSON.parse(atob(tokenParts[1]));
        }
      } catch (error) {
        console.error('Token 解析失敗:', error);
      }
    }

    if (userStr) {
      try {
        userData = JSON.parse(userStr);
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setTokenInfo({
      hasToken: !!token,
      hasUser: !!userData,
      token: token,
      tokenData: tokenData,
      userData: userData,
      tokenLength: token ? token.length : 0,
      isExpired: tokenData ? (tokenData.exp * 1000 < Date.now()) : false,
      expiresAt: tokenData ? new Date(tokenData.exp * 1000).toLocaleString() : null
    });
  };

  const regenerateToken = async () => {
    setIsRegenerating(true);
    setRegenerateResults([]);

    try {
      addResult('開始', 'info', '開始重新生成 JWT Token');

      // 步驟 1: 清除舊的認證數據
      addResult('步驟1', 'info', '清除舊的認證數據...');
      localStorage.clear();
      addResult('步驟1', 'success', '舊認證數據已清除');

      // 步驟 2: 重新登入獲取新 Token
      addResult('步驟2', 'info', '重新登入獲取新 Token...');
      
      const loginResponse = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      if (!loginResponse.ok) {
        addResult('步驟2', 'error', `登入請求失敗: ${loginResponse.status}`);
        return;
      }

      const loginResult = await loginResponse.json();
      
      if (loginResult.success) {
        addResult('步驟2', 'success', '重新登入成功');
        addResult('步驟2', 'info', `新 Token 長度: ${loginResult.token.length}`);
        
        // 解析新 Token
        try {
          const newTokenParts = loginResult.token.split('.');
          const newTokenData = JSON.parse(atob(newTokenParts[1]));
          addResult('步驟2', 'info', `新 Token 用戶 ID: ${newTokenData.userId}`);
          addResult('步驟2', 'info', `新 Token 過期時間: ${new Date(newTokenData.exp * 1000).toLocaleString()}`);
        } catch (error) {
          addResult('步驟2', 'warning', `新 Token 解析失敗: ${error.message}`);
        }
      } else {
        addResult('步驟2', 'error', `登入失敗: ${loginResult.error}`);
        return;
      }

      // 步驟 3: 保存新的認證數據
      addResult('步驟3', 'info', '保存新的認證數據...');
      
      localStorage.setItem('token', loginResult.token);
      localStorage.setItem('user', JSON.stringify(loginResult.user));
      
      addResult('步驟3', 'success', '新認證數據已保存');
      addResult('步驟3', 'info', `用戶: ${loginResult.user.name} (ID: ${loginResult.user.userId})`);

      // 步驟 4: 測試新 Token 的錢包 API
      addResult('步驟4', 'info', '測試新 Token 的錢包 API...');
      
      const walletResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${loginResult.token}`,
          'Content-Type': 'application/json'
        }
      });

      addResult('步驟4', 'info', `錢包 API 響應狀態: ${walletResponse.status}`);

      if (walletResponse.ok) {
        const walletResult = await walletResponse.json();
        if (walletResult.success) {
          addResult('步驟4', 'success', `錢包 API 成功！餘額: $${walletResult.data.balance}`);
        } else {
          addResult('步驟4', 'error', `錢包 API 業務失敗: ${walletResult.error}`);
        }
      } else {
        const errorResult = await walletResponse.json();
        addResult('步驟4', 'error', `錢包 API HTTP 失敗: ${walletResponse.status} - ${errorResult.error}`);
      }

      // 步驟 5: 測試資料庫查詢 API
      addResult('步驟5', 'info', '測試資料庫查詢 API...');
      
      const dbResponse = await fetch('/api/admin/database-query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${loginResult.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: "SELECT id, name, email, wallet_balance FROM members WHERE id = ?",
          params: [loginResult.user.userId]
        })
      });

      if (dbResponse.ok) {
        const dbResult = await dbResponse.json();
        if (dbResult.success && dbResult.data.length > 0) {
          const memberData = dbResult.data[0];
          addResult('步驟5', 'success', `資料庫查詢成功，餘額: $${memberData.wallet_balance || 0}`);
        } else {
          addResult('步驟5', 'error', `資料庫查詢失敗: ${dbResult.error || '無數據'}`);
        }
      } else {
        addResult('步驟5', 'error', `資料庫查詢 HTTP 失敗: ${dbResponse.status}`);
      }

      addResult('完成', 'success', 'JWT Token 重新生成完成！');
      
      // 更新 Token 信息顯示
      checkCurrentToken();

    } catch (error) {
      addResult('錯誤', 'error', '重新生成過程中出現錯誤: ' + error.message);
    } finally {
      setIsRegenerating(false);
    }
  };

  const testCurrentToken = async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      addResult('測試', 'error', '沒有找到 Token');
      return;
    }

    addResult('測試', 'info', '測試當前 Token...');

    try {
      const walletResponse = await fetch('/api/wallet/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (walletResponse.ok) {
        const walletResult = await walletResponse.json();
        if (walletResult.success) {
          addResult('測試', 'success', `當前 Token 有效！餘額: $${walletResult.data.balance}`);
        } else {
          addResult('測試', 'error', `當前 Token 業務失敗: ${walletResult.error}`);
        }
      } else {
        const errorResult = await walletResponse.json();
        addResult('測試', 'error', `當前 Token 無效: ${walletResponse.status} - ${errorResult.error}`);
      }
    } catch (error) {
      addResult('測試', 'error', `測試錯誤: ${error.message}`);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>重新生成 JWT Token - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔑 重新生成 JWT Token</h1>
                <p className="text-gray-600 mt-2">解決 JWT Token 認證失敗問題</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={testCurrentToken}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  🧪 測試當前 Token
                </button>
                <button
                  onClick={() => router.push('/wallet')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  💰 前往錢包
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
                <button
                  onClick={regenerateToken}
                  disabled={isRegenerating}
                  className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 font-medium"
                >
                  {isRegenerating ? '生成中...' : '🔑 重新生成 Token'}
                </button>
              </div>
            </div>

            {/* 當前 Token 信息 */}
            {tokenInfo && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">🔍 當前 Token 信息</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm space-y-2">
                        <div>Token 存在: {tokenInfo.hasToken ? '✅' : '❌'}</div>
                        <div>Token 長度: {tokenInfo.tokenLength}</div>
                        <div>用戶數據: {tokenInfo.hasUser ? '✅' : '❌'}</div>
                        <div>是否過期: {tokenInfo.isExpired ? '❌ 已過期' : '✅ 未過期'}</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm space-y-2">
                        <div>用戶 ID: {tokenInfo.tokenData?.userId || 'N/A'}</div>
                        <div>角色: {tokenInfo.tokenData?.role || 'N/A'}</div>
                        <div>Email: {tokenInfo.tokenData?.email || 'N/A'}</div>
                        <div>過期時間: {tokenInfo.expiresAt || 'N/A'}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 重新生成結果 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 重新生成結果</h2>
              
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                {regenerateResults.length > 0 ? (
                  <div className="space-y-3">
                    {regenerateResults.map((result, index) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start space-x-3">
                          <span className={getStatusColor(result.status)}>
                            {getStatusIcon(result.status)}
                          </span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-gray-500">[{result.timestamp}]</span>
                              <span className="font-medium text-gray-700">{result.step}:</span>
                              <span className={getStatusColor(result.status)}>{result.message}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-center py-8">
                    點擊「重新生成 Token」或「測試當前 Token」來開始
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-medium text-red-900 mb-3">🎯 問題說明</h3>
              <div className="text-sm text-red-800 space-y-2">
                <div><strong>問題:</strong> 即使用戶數據一致，錢包 API 仍然返回 401 Unauthorized</div>
                <div><strong>可能原因:</strong> JWT Token 過期、JWT_SECRET 不匹配、或 Token 格式錯誤</div>
                <div><strong>解決方案:</strong> 重新生成新的 JWT Token 並測試所有相關 API</div>
                <div><strong>測試範圍:</strong> 錢包 API、資料庫查詢 API、用戶認證</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
