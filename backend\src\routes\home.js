const express = require('express');
const { Pool } = require('pg');
const router = express.Router();

// 資料庫連接
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'member_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
});

/**
 * @swagger
 * /api/home/<USER>
 *   get:
 *     summary: 獲取系統統計數據
 *     tags: [Home]
 *     responses:
 *       200:
 *         description: 系統統計數據
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalUsers:
 *                       type: integer
 *                       description: 總用戶數
 *                     totalServices:
 *                       type: integer
 *                       description: 總服務數
 *                     totalTransactions:
 *                       type: integer
 *                       description: 總交易數
 *                     totalRevenue:
 *                       type: number
 *                       description: 總收入
 *                     systemUptime:
 *                       type: string
 *                       description: 系統運行時間
 */
router.get('/stats', async (req, res) => {
  try {
    const client = await pool.connect();
    
    // 獲取用戶統計
    const userStats = await client.query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as new_users_30d
      FROM users 
      WHERE deleted_at IS NULL
    `);

    // 獲取服務統計
    const serviceStats = await client.query(`
      SELECT 
        COUNT(*) as total_services,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_services
      FROM services
    `);

    // 獲取交易統計
    const transactionStats = await client.query(`
      SELECT 
        COUNT(*) as total_transactions,
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as transactions_30d
      FROM payments
    `);

    // 獲取訂閱統計
    const subscriptionStats = await client.query(`
      SELECT 
        COUNT(*) as total_subscriptions,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscriptions,
        SUM(CASE WHEN status = 'active' THEN price ELSE 0 END) as monthly_recurring_revenue
      FROM subscriptions
    `);

    // 計算系統運行時間 (模擬)
    const systemUptime = '99.9%';

    client.release();

    const stats = {
      totalUsers: parseInt(userStats.rows[0].total_users) || 0,
      activeUsers: parseInt(userStats.rows[0].active_users) || 0,
      newUsers30d: parseInt(userStats.rows[0].new_users_30d) || 0,
      totalServices: parseInt(serviceStats.rows[0].total_services) || 0,
      activeServices: parseInt(serviceStats.rows[0].active_services) || 0,
      totalTransactions: parseInt(transactionStats.rows[0].total_transactions) || 0,
      totalRevenue: parseFloat(transactionStats.rows[0].total_revenue) || 0,
      transactions30d: parseInt(transactionStats.rows[0].transactions_30d) || 0,
      totalSubscriptions: parseInt(subscriptionStats.rows[0].total_subscriptions) || 0,
      activeSubscriptions: parseInt(subscriptionStats.rows[0].active_subscriptions) || 0,
      monthlyRecurringRevenue: parseFloat(subscriptionStats.rows[0].monthly_recurring_revenue) || 0,
      systemUptime
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('獲取統計數據失敗:', error);
    res.status(500).json({
      success: false,
      message: '獲取統計數據失敗',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/home/<USER>
 *   get:
 *     summary: 獲取精選服務
 *     tags: [Home]
 *     responses:
 *       200:
 *         description: 精選服務列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       category:
 *                         type: string
 *                       plans:
 *                         type: array
 *                         items:
 *                           type: object
 */
router.get('/featured-services', async (req, res) => {
  try {
    const client = await pool.connect();
    
    // 獲取精選服務及其方案
    const result = await client.query(`
      SELECT 
        s.id,
        s.name,
        s.description,
        s.category,
        s.metadata,
        json_agg(
          json_build_object(
            'id', sp.id,
            'name', sp.name,
            'description', sp.description,
            'price', sp.price,
            'currency', sp.currency,
            'billing_period', sp.billing_period,
            'features', sp.features
          ) ORDER BY sp.price ASC
        ) as plans
      FROM services s
      LEFT JOIN service_plans sp ON s.id = sp.service_id AND sp.status = 'active'
      WHERE s.status = 'active'
      GROUP BY s.id, s.name, s.description, s.category, s.metadata
      ORDER BY s.created_at DESC
      LIMIT 6
    `);

    client.release();

    res.json({
      success: true,
      data: result.rows
    });

  } catch (error) {
    console.error('獲取精選服務失敗:', error);
    res.status(500).json({
      success: false,
      message: '獲取精選服務失敗',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/home/<USER>
 *   get:
 *     summary: 獲取最近活動
 *     tags: [Home]
 *     responses:
 *       200:
 *         description: 最近活動列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 */
router.get('/recent-activities', async (req, res) => {
  try {
    const client = await pool.connect();
    
    // 獲取最近的用戶註冊
    const recentRegistrations = await client.query(`
      SELECT 
        'registration' as type,
        u.username,
        u.created_at,
        'new_user' as activity
      FROM users u
      WHERE u.deleted_at IS NULL
      ORDER BY u.created_at DESC
      LIMIT 5
    `);

    // 獲取最近的支付
    const recentPayments = await client.query(`
      SELECT 
        'payment' as type,
        u.username,
        p.amount,
        p.currency,
        p.created_at,
        'payment_completed' as activity
      FROM payments p
      JOIN users u ON p.user_id = u.id
      WHERE p.status = 'completed'
      ORDER BY p.created_at DESC
      LIMIT 5
    `);

    // 獲取最近的訂閱
    const recentSubscriptions = await client.query(`
      SELECT 
        'subscription' as type,
        u.username,
        s.name as service_name,
        sub.created_at,
        'new_subscription' as activity
      FROM subscriptions sub
      JOIN users u ON sub.user_id = u.id
      JOIN services s ON sub.service_id = s.id
      WHERE sub.status = 'active'
      ORDER BY sub.created_at DESC
      LIMIT 5
    `);

    // 合併並排序所有活動
    const allActivities = [
      ...recentRegistrations.rows,
      ...recentPayments.rows,
      ...recentSubscriptions.rows
    ].sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
     .slice(0, 10);

    client.release();

    res.json({
      success: true,
      data: allActivities
    });

  } catch (error) {
    console.error('獲取最近活動失敗:', error);
    res.status(500).json({
      success: false,
      message: '獲取最近活動失敗',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/home/<USER>
 *   get:
 *     summary: 獲取系統健康狀態
 *     tags: [Home]
 *     responses:
 *       200:
 *         description: 系統健康狀態
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     database:
 *                       type: object
 *                     redis:
 *                       type: object
 *                     api:
 *                       type: object
 */
router.get('/system-health', async (req, res) => {
  const health = {
    database: { status: 'unknown', responseTime: 0 },
    redis: { status: 'unknown', responseTime: 0 },
    api: { status: 'healthy', responseTime: 0, uptime: process.uptime() }
  };

  try {
    // 檢查資料庫連接
    const dbStart = Date.now();
    const client = await pool.connect();
    await client.query('SELECT 1');
    client.release();
    health.database = {
      status: 'healthy',
      responseTime: Date.now() - dbStart
    };
  } catch (error) {
    health.database = {
      status: 'unhealthy',
      responseTime: 0,
      error: error.message
    };
  }

  try {
    // 檢查 Redis 連接 (如果有配置)
    if (process.env.REDIS_HOST) {
      const redis = require('redis');
      const redisClient = redis.createClient({
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT || 6379
      });

      const redisStart = Date.now();
      await redisClient.connect();
      await redisClient.ping();
      await redisClient.quit();
      
      health.redis = {
        status: 'healthy',
        responseTime: Date.now() - redisStart
      };
    } else {
      health.redis = {
        status: 'not_configured',
        responseTime: 0
      };
    }
  } catch (error) {
    health.redis = {
      status: 'unhealthy',
      responseTime: 0,
      error: error.message
    };
  }

  res.json({
    success: true,
    data: health
  });
});

module.exports = router;
