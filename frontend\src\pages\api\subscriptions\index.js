// 會員訂閱管理 API
const path = require('path');
// 動態導入認證中間件
const getAuth = () => {
  try {
    const authPaths = [
      path.join(process.cwd(), 'lib', 'middleware', 'auth'),
      path.join(process.cwd(), 'lib', 'middleware', 'auth.js'),
      path.join(__dirname, '..', '..', '..', 'lib', 'middleware', 'auth'),
      path.join(__dirname, '..', '..', '..', 'lib', 'middleware', 'auth.js')
    ];

    for (const authPath of authPaths) {
      try {
        return require(authPath);
      } catch (err) {
        continue;
      }
    }

    throw new Error('無法找到認證中間件');
  } catch (error) {
    console.error('認證中間件載入失敗:', error);
    throw error;
  }
};

// 使用 frontend 內的資料庫模型
const getModels = async () => {
  try {
    // 嘗試載入 Service 模型
    const servicePaths = [
      path.join(process.cwd(), 'lib', 'database', 'models', 'Service'),
      path.join(process.cwd(), 'lib', 'database', 'models', 'Service.js'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'Service'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'Service.js')
    ];

    const memberServicePaths = [
      path.join(process.cwd(), 'lib', 'database', 'models', 'MemberService'),
      path.join(process.cwd(), 'lib', 'database', 'models', 'MemberService.js'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'MemberService'),
      path.join(__dirname, '..', '..', '..', 'lib', 'database', 'models', 'MemberService.js')
    ];

    let Service, MemberService;

    // 載入 Service 模型
    for (const servicePath of servicePaths) {
      try {
        Service = require(servicePath);
        break;
      } catch (err) {
        continue;
      }
    }

    // 載入 MemberService 模型
    for (const memberServicePath of memberServicePaths) {
      try {
        MemberService = require(memberServicePath);
        break;
      } catch (err) {
        continue;
      }
    }

    if (!Service || !MemberService) {
      throw new Error('無法載入必要的模型');
    }

    return { Service, MemberService };
  } catch (error) {
    console.error('模型載入失敗:', error);
    throw error;
  }
};

async function handler(req, res) {
  try {
    const { Service, MemberService } = await getModels();
    const { method } = req;

    switch (method) {
      case 'GET':
        // 獲取會員訂閱 - 使用登入用戶的 ID
        const { status, service_id } = req.query;
        const memberId = req.userId; // 從認證中間件獲取

        if (service_id) {
          // 檢查特定服務的訂閱狀態
          const isSubscribed = await MemberService.isSubscribed(
            memberId,
            parseInt(service_id)
          );
          
          return res.status(200).json({
            success: true,
            data: { isSubscribed },
            message: isSubscribed ? '已訂閱此服務' : '未訂閱此服務'
          });
        } else {
          // 獲取會員的所有訂閱
          const subscriptions = await MemberService.findByMemberId(
            memberId,
            { status }
          );
          
          return res.status(200).json({
            success: true,
            data: subscriptions.map(sub => sub.toJSON()),
            message: `找到 ${subscriptions.length} 個訂閱`
          });
        }
        
      case 'POST':
        // 創建新訂閱 - 使用登入用戶的 ID
        const { service_id: serviceId, usage_data } = req.body;
        const memberIdForSubscribe = req.userId; // 從認證中間件獲取

        if (!serviceId) {
          return res.status(400).json({
            success: false,
            error: '缺少必要參數：service_id'
          });
        }

        try {
          const subscription = await MemberService.subscribe(
            memberIdForSubscribe,
            parseInt(serviceId),
            { usage_data }
          );
          
          return res.status(201).json({
            success: true,
            data: subscription.toJSON(),
            message: '訂閱成功'
          });
        } catch (error) {
          if (error.message.includes('已經訂閱')) {
            return res.status(409).json({
              success: false,
              error: error.message
            });
          } else if (error.message.includes('服務不存在') || error.message.includes('服務未啟用')) {
            return res.status(404).json({
              success: false,
              error: error.message
            });
          }
          throw error;
        }
        
      case 'PUT':
        // 更新訂閱狀態
        const { subscription_id, action, usage_data: updateUsageData } = req.body;
        
        if (!subscription_id) {
          return res.status(400).json({
            success: false,
            error: '缺少 subscription_id 參數'
          });
        }

        const subscription = await MemberService.findById(parseInt(subscription_id));
        if (!subscription) {
          return res.status(404).json({
            success: false,
            error: '訂閱不存在'
          });
        }

        let result;
        switch (action) {
          case 'cancel':
            result = await subscription.cancel();
            break;
          case 'reactivate':
            result = await subscription.reactivate();
            break;
          case 'update_usage':
            if (!updateUsageData) {
              return res.status(400).json({
                success: false,
                error: '缺少 usage_data 參數'
              });
            }
            result = await subscription.updateUsage(updateUsageData);
            break;
          default:
            return res.status(400).json({
              success: false,
              error: '無效的操作：' + action
            });
        }

        return res.status(200).json({
          success: true,
          data: result.toJSON(),
          message: `訂閱${action === 'cancel' ? '取消' : action === 'reactivate' ? '重新啟用' : '更新'}成功`
        });
        
      case 'DELETE':
        // 刪除訂閱（通常用於取消訂閱）
        const { subscription_id: subId } = req.body;
        
        if (!subId) {
          return res.status(400).json({
            success: false,
            error: '缺少 subscription_id 參數'
          });
        }

        const subToDelete = await MemberService.findById(parseInt(subId));
        if (!subToDelete) {
          return res.status(404).json({
            success: false,
            error: '訂閱不存在'
          });
        }

        await subToDelete.cancel();

        return res.status(200).json({
          success: true,
          message: '訂閱已取消'
        });

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).json({
          success: false,
          error: `方法 ${method} 不被允許`
        });
    }
  } catch (error) {
    console.error('訂閱 API 錯誤:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

// 使用認證包裝器
export default function wrappedHandler(req, res) {
  const { withAuth } = getAuth();
  return withAuth(handler)(req, res);
};
