#!/bin/sh

# Pre-commit hook
# 在提交前運行代碼檢查和測試

echo "🔍 執行 pre-commit 檢查..."

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 檢查是否在正確的目錄
if [ ! -f "backend/package.json" ]; then
    echo "${RED}❌ 找不到 backend/package.json，請在專案根目錄執行${NC}"
    exit 1
fi

# 進入後端目錄
cd backend

# 1. 檢查代碼風格
echo "📝 檢查代碼風格..."
if npm run lint; then
    echo "${GREEN}✅ 代碼風格檢查通過${NC}"
else
    echo "${RED}❌ 代碼風格檢查失敗${NC}"
    echo "請運行 'npm run lint:fix' 修復問題"
    exit 1
fi

# 2. 運行單元測試
echo "🧪 運行單元測試..."
if npm run test:unit; then
    echo "${GREEN}✅ 單元測試通過${NC}"
else
    echo "${RED}❌ 單元測試失敗${NC}"
    exit 1
fi

# 3. 檢查安全漏洞
echo "🔒 檢查安全漏洞..."
if npm audit --audit-level high; then
    echo "${GREEN}✅ 安全檢查通過${NC}"
else
    echo "${YELLOW}⚠️ 發現安全漏洞，請檢查並修復${NC}"
    # 不阻止提交，但給出警告
fi

# 4. 檢查環境變數範例文件
echo "⚙️ 檢查環境變數範例..."
if [ ! -f ".env.example" ]; then
    echo "${YELLOW}⚠️ 缺少 .env.example 文件${NC}"
fi

cd ..

# 5. 檢查 README 是否更新
if git diff --cached --name-only | grep -q "README.md"; then
    echo "${GREEN}✅ README.md 已更新${NC}"
fi

echo "${GREEN}🎉 Pre-commit 檢查完成${NC}"
exit 0
