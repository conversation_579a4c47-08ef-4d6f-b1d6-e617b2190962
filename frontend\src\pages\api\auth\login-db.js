// 會員登入 API (資料庫版本)
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const path = require('path');
const fs = require('fs');

// JWT 密鑰
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// 連接 SQLite 資料庫
const connectDatabase = () => {
  try {
    const sqlite3 = require('sqlite3').verbose();
    
    const possiblePaths = [
      path.join(process.cwd(), 'lib', 'database', 'database.sqlite'),
      path.join(process.cwd(), 'frontend', 'lib', 'database', 'database.sqlite')
    ];
    
    for (const dbPath of possiblePaths) {
      if (fs.existsSync(dbPath)) {
        console.log('登入 API: 找到資料庫文件:', dbPath);
        return new sqlite3.Database(dbPath);
      }
    }
    
    console.log('登入 API: 未找到資料庫文件');
    return null;
  } catch (error) {
    console.error('登入 API: 資料庫連接失敗:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: `方法 ${req.method} 不被允許`
    });
  }

  try {
    const { email, password, rememberMe } = req.body;

    console.log('登入 API: 收到請求', { email, rememberMe, password: '***' });

    // 驗證必要欄位
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: '請提供 email 和密碼'
      });
    }

    // 連接資料庫
    const db = connectDatabase();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: '資料庫連接失敗'
      });
    }

    // 使用 Promise 包裝資料庫操作
    return new Promise((resolve) => {
      // 查詢用戶
      db.get('SELECT * FROM members WHERE email = ?', [email.toLowerCase()], async (err, user) => {
        if (err) {
          console.error('登入 API: 查詢用戶失敗:', err);
          db.close();
          resolve(res.status(500).json({
            success: false,
            error: '資料庫查詢失敗'
          }));
          return;
        }

        if (!user) {
          console.log('登入 API: 用戶不存在');
          db.close();
          resolve(res.status(401).json({
            success: false,
            error: '電子郵件或密碼錯誤'
          }));
          return;
        }

        console.log('登入 API: 找到用戶', { id: user.id, email: user.email, role: user.role });

        // 檢查用戶狀態
        if (user.status !== 'active') {
          console.log('登入 API: 用戶狀態非活躍');
          db.close();
          resolve(res.status(401).json({
            success: false,
            error: '帳號已被停用'
          }));
          return;
        }

        // 驗證密碼
        try {
          const passwordMatch = await bcrypt.compare(password, user.password_hash);
          
          if (!passwordMatch) {
            console.log('登入 API: 密碼錯誤');
            db.close();
            resolve(res.status(401).json({
              success: false,
              error: '電子郵件或密碼錯誤'
            }));
            return;
          }

          console.log('登入 API: 密碼驗證成功');

          // 更新最後登入時間
          db.run('UPDATE members SET last_login_at = CURRENT_TIMESTAMP WHERE id = ?', [user.id], (updateErr) => {
            if (updateErr) {
              console.error('登入 API: 更新登入時間失敗:', updateErr);
            } else {
              console.log('登入 API: 更新登入時間成功');
            }
          });

          // 查詢用戶的訂閱
          db.all(`
            SELECT ms.*, s.name as service_name, s.price, s.billing_cycle
            FROM member_services ms
            JOIN services s ON ms.service_id = s.id
            WHERE ms.member_id = ? AND ms.status = 'active'
          `, [user.id], (subErr, subscriptions) => {
            db.close();

            if (subErr) {
              console.error('登入 API: 查詢訂閱失敗:', subErr);
              // 即使訂閱查詢失敗，仍然允許登入
            }

            console.log('登入 API: 查詢到訂閱數量:', subscriptions ? subscriptions.length : 0);

            // 生成 JWT Token
            const tokenExpiry = rememberMe ? '30d' : JWT_EXPIRES_IN;
            const token = jwt.sign(
              { 
                userId: user.id, 
                email: user.email,
                role: user.role 
              },
              JWT_SECRET,
              { expiresIn: tokenExpiry }
            );

            console.log('登入 API: JWT Token 生成成功');

            // 準備用戶數據 (移除敏感信息)
            const userData = {
              id: user.id,
              email: user.email,
              name: user.name,
              role: user.role,
              status: user.status,
              phone: user.phone,
              email_verified: user.email_verified,
              last_login_at: user.last_login_at,
              created_at: user.created_at
            };

            // 準備訂閱數據
            const subscriptionData = subscriptions ? subscriptions.map(sub => ({
              id: sub.id,
              service_id: sub.service_id,
              service_name: sub.service_name,
              price: sub.price,
              billing_cycle: sub.billing_cycle,
              status: sub.status,
              subscribed_at: sub.subscribed_at,
              next_billing_date: sub.next_billing_date,
              auto_renew: sub.auto_renew
            })) : [];

            console.log('登入 API: 登入成功，返回響應');

            // 返回成功響應
            resolve(res.status(200).json({
              success: true,
              data: {
                token,
                user: userData,
                subscriptions: subscriptionData
              },
              message: '登入成功！數據來自資料庫'
            }));
          });

        } catch (bcryptError) {
          console.error('登入 API: 密碼驗證失敗:', bcryptError);
          db.close();
          resolve(res.status(500).json({
            success: false,
            error: '密碼驗證失敗'
          }));
        }
      });
    });

  } catch (error) {
    console.error('登入 API: 未預期的錯誤:', error);
    
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
