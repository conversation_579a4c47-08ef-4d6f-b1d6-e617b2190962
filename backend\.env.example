# 服務器配置
NODE_ENV=development
PORT=3000
API_VERSION=v1

# 資料庫配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=member_system
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=30d

# 郵件服務配置 (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=Member System

# 簡訊服務配置 (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# OAuth 配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/api/v1/auth/google/callback

FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_CALLBACK_URL=http://localhost:3000/api/v1/auth/facebook/callback

LINE_CHANNEL_ID=your-line-channel-id
LINE_CHANNEL_SECRET=your-line-channel-secret
LINE_CALLBACK_URL=http://localhost:3000/api/v1/auth/line/callback

# 支付服務配置
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox

ECPAY_MERCHANT_ID=your-ecpay-merchant-id
ECPAY_HASH_KEY=your-ecpay-hash-key
ECPAY_HASH_IV=your-ecpay-hash-iv

# 檔案上傳配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 前端配置
FRONTEND_URL=http://localhost:3001
CORS_ORIGINS=http://localhost:3001,http://localhost:3000

# 監控配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# reCAPTCHA 配置
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key