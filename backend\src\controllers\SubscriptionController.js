const SubscriptionService = require('../services/SubscriptionService');
const { validationResult } = require('express-validator');

class SubscriptionController {
  // 獲取所有可用服務
  static async getAvailableServices(req, res) {
    try {
      const services = await SubscriptionService.getAvailableServices();

      res.json({
        success: true,
        data: services,
      });
    } catch (error) {
      console.error('Get available services error:', error);
      res.status(500).json({
        success: false,
        message: '獲取服務列表失敗',
        error: error.message,
      });
    }
  }

  // 獲取服務詳情和方案
  static async getServiceDetails(req, res) {
    try {
      const { serviceId } = req.params;
      const service = await SubscriptionService.getServiceWithPlans(serviceId);

      res.json({
        success: true,
        data: service,
      });
    } catch (error) {
      console.error('Get service details error:', error);
      res.status(404).json({
        success: false,
        message: error.message || '獲取服務詳情失敗',
      });
    }
  }

  // 獲取用戶訂閱列表
  static async getUserSubscriptions(req, res) {
    try {
      const userId = req.user.id;
      const subscriptions = await SubscriptionService.getUserSubscriptions(userId);

      res.json({
        success: true,
        data: subscriptions,
      });
    } catch (error) {
      console.error('Get user subscriptions error:', error);
      res.status(500).json({
        success: false,
        message: '獲取訂閱列表失敗',
        error: error.message,
      });
    }
  }

  // 創建訂閱
  static async createSubscription(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '輸入資料有誤',
          errors: errors.array(),
        });
      }

      const { planId, paymentMethodId } = req.body;
      const userId = req.user.id;

      const subscription = await SubscriptionService.createSubscription(
        userId,
        planId,
        paymentMethodId
      );

      res.status(201).json({
        success: true,
        message: '訂閱創建成功',
        data: subscription,
      });
    } catch (error) {
      console.error('Create subscription error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '創建訂閱失敗',
      });
    }
  }

  // 取消訂閱
  static async cancelSubscription(req, res) {
    try {
      const { subscriptionId } = req.params;
      const { cancelAtPeriodEnd = true } = req.body;
      const userId = req.user.id;

      const result = await SubscriptionService.cancelSubscription(
        userId,
        subscriptionId,
        cancelAtPeriodEnd
      );

      res.json({
        success: true,
        message: cancelAtPeriodEnd ? '訂閱將在當前週期結束時取消' : '訂閱已立即取消',
        data: result,
      });
    } catch (error) {
      console.error('Cancel subscription error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '取消訂閱失敗',
      });
    }
  }

  // 恢復訂閱
  static async resumeSubscription(req, res) {
    try {
      const { subscriptionId } = req.params;
      const userId = req.user.id;

      const result = await SubscriptionService.resumeSubscription(userId, subscriptionId);

      res.json({
        success: true,
        message: '訂閱已恢復',
        data: result,
      });
    } catch (error) {
      console.error('Resume subscription error:', error);
      res.status(400).json({
        success: false,
        message: error.message || '恢復訂閱失敗',
      });
    }
  }

  // 獲取訂閱使用統計
  static async getSubscriptionUsage(req, res) {
    try {
      const { subscriptionId } = req.params;
      const userId = req.user.id;

      const usage = await SubscriptionService.getSubscriptionUsage(userId, subscriptionId);

      res.json({
        success: true,
        data: usage,
      });
    } catch (error) {
      console.error('Get subscription usage error:', error);
      res.status(500).json({
        success: false,
        message: '獲取使用統計失敗',
        error: error.message,
      });
    }
  }
}

module.exports = SubscriptionController;