import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function SubscriptionCountTest() {
  const [allServices, setAllServices] = useState([]);
  const [subscriptions, setSubscriptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [analysis, setAnalysis] = useState(null);

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setIsLoading(true);
    
    try {
      await Promise.all([
        loadAllServices(),
        loadSubscriptions()
      ]);
      
      // 計算可用服務
      calculateAvailableServices();
    } catch (error) {
      console.error('載入數據失敗:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadAllServices = async () => {
    try {
      const response = await fetch('/api/services/database');
      const result = await response.json();

      if (result.success) {
        setAllServices(result.data);
        console.log('載入所有服務:', result.data.length);
      }
    } catch (error) {
      console.error('載入服務失敗:', error);
    }
  };

  const loadSubscriptions = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        // 使用正確的數據格式
        const formattedSubscriptions = result.data.map(sub => ({
          id: sub.id, // 訂閱記錄 ID
          service_id: sub.service_id, // 服務 ID
          service_name: sub.service_name,
          status: sub.status,
          subscribed_at: sub.subscribed_at
        }));
        
        setSubscriptions(formattedSubscriptions);
        console.log('載入訂閱:', formattedSubscriptions.length);
      }
    } catch (error) {
      console.error('載入訂閱失敗:', error);
    }
  };

  const calculateAvailableServices = () => {
    // 過濾活躍服務
    const activeServices = allServices.filter(service => service.status === 'active');
    
    // 獲取活躍訂閱的服務 ID
    const activeSubscribedServiceIds = subscriptions
      .filter(sub => sub.status === 'active')
      .map(sub => sub.service_id);
    
    // 計算可用服務
    const available = activeServices.filter(service =>
      !activeSubscribedServiceIds.includes(service.id)
    );
    
    setAvailableServices(available);
    
    // 分析數據
    const analysisData = {
      totalServices: allServices.length,
      activeServices: activeServices.length,
      inactiveServices: allServices.length - activeServices.length,
      
      totalSubscriptions: subscriptions.length,
      activeSubscriptions: subscriptions.filter(sub => sub.status === 'active').length,
      cancelledSubscriptions: subscriptions.filter(sub => sub.status === 'cancelled').length,
      expiredSubscriptions: subscriptions.filter(sub => sub.status === 'expired').length,
      
      availableServices: available.length,
      
      // 驗證計算
      expectedTotal: activeServices.length,
      actualTotal: subscriptions.filter(sub => sub.status === 'active').length + available.length,
      
      // 詳細分析
      activeSubscribedServiceIds,
      availableServiceIds: available.map(s => s.id),
      
      // 檢查重複
      duplicateSubscriptions: findDuplicateSubscriptions(),
      duplicateServices: findDuplicateServices()
    };
    
    setAnalysis(analysisData);
  };

  const findDuplicateSubscriptions = () => {
    const serviceIdCount = {};
    const duplicates = [];
    
    subscriptions.forEach(sub => {
      if (sub.status === 'active') {
        if (serviceIdCount[sub.service_id]) {
          serviceIdCount[sub.service_id]++;
          if (serviceIdCount[sub.service_id] === 2) {
            duplicates.push(sub.service_id);
          }
        } else {
          serviceIdCount[sub.service_id] = 1;
        }
      }
    });
    
    return duplicates;
  };

  const findDuplicateServices = () => {
    const serviceIdCount = {};
    const duplicates = [];
    
    allServices.forEach(service => {
      if (serviceIdCount[service.id]) {
        serviceIdCount[service.id]++;
        if (serviceIdCount[service.id] === 2) {
          duplicates.push(service.id);
        }
      } else {
        serviceIdCount[service.id] = 1;
      }
    });
    
    return duplicates;
  };

  const getStatusColor = (isCorrect) => {
    return isCorrect ? 'text-green-600' : 'text-red-600';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>訂閱數量驗證 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">📊 訂閱數量驗證</h1>
              <p className="text-gray-600 mt-2">檢查訂閱數量計算是否正確</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 數量統計 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">數量統計</h2>
                
                {analysis && (
                  <div className="space-y-4">
                    {/* 服務統計 */}
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h3 className="font-medium text-blue-900 mb-2">🛍️ 服務統計</h3>
                      <div className="text-sm text-blue-800 space-y-1">
                        <div>總服務數: {analysis.totalServices}</div>
                        <div>活躍服務數: {analysis.activeServices}</div>
                        <div>停用服務數: {analysis.inactiveServices}</div>
                      </div>
                    </div>

                    {/* 訂閱統計 */}
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <h3 className="font-medium text-green-900 mb-2">📦 訂閱統計</h3>
                      <div className="text-sm text-green-800 space-y-1">
                        <div>總訂閱數: {analysis.totalSubscriptions}</div>
                        <div>活躍訂閱數: {analysis.activeSubscriptions}</div>
                        <div>已取消訂閱數: {analysis.cancelledSubscriptions}</div>
                        <div>已過期訂閱數: {analysis.expiredSubscriptions}</div>
                      </div>
                    </div>

                    {/* 可用服務統計 */}
                    <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                      <h3 className="font-medium text-purple-900 mb-2">🎯 可用服務統計</h3>
                      <div className="text-sm text-purple-800 space-y-1">
                        <div>可用服務數: {analysis.availableServices}</div>
                      </div>
                    </div>

                    {/* 驗證結果 */}
                    <div className={`p-4 border rounded-lg ${
                      analysis.expectedTotal === analysis.actualTotal 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-red-50 border-red-200'
                    }`}>
                      <h3 className={`font-medium mb-2 ${
                        analysis.expectedTotal === analysis.actualTotal 
                          ? 'text-green-900' 
                          : 'text-red-900'
                      }`}>
                        {analysis.expectedTotal === analysis.actualTotal ? '✅' : '❌'} 數量驗證
                      </h3>
                      <div className={`text-sm space-y-1 ${
                        analysis.expectedTotal === analysis.actualTotal 
                          ? 'text-green-800' 
                          : 'text-red-800'
                      }`}>
                        <div>預期總數: {analysis.expectedTotal} (活躍服務)</div>
                        <div>實際總數: {analysis.actualTotal} (活躍訂閱 + 可用服務)</div>
                        <div>計算: {analysis.activeSubscriptions} + {analysis.availableServices} = {analysis.actualTotal}</div>
                        <div className={getStatusColor(analysis.expectedTotal === analysis.actualTotal)}>
                          {analysis.expectedTotal === analysis.actualTotal ? '數量正確' : '數量不匹配！'}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <button
                  onClick={loadAllData}
                  disabled={isLoading}
                  className="mt-4 w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  🔄 重新載入數據
                </button>
              </div>

              {/* 詳細分析 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">詳細分析</h2>
                
                {analysis && (
                  <div className="space-y-4">
                    {/* 重複檢查 */}
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <h3 className="font-medium text-yellow-900 mb-2">🔍 重複檢查</h3>
                      <div className="text-sm text-yellow-800 space-y-1">
                        <div>重複的活躍訂閱服務: {analysis.duplicateSubscriptions.length > 0 ? analysis.duplicateSubscriptions.join(', ') : '無'}</div>
                        <div>重複的服務記錄: {analysis.duplicateServices.length > 0 ? analysis.duplicateServices.join(', ') : '無'}</div>
                      </div>
                    </div>

                    {/* 服務 ID 列表 */}
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                      <h3 className="font-medium text-gray-900 mb-2">📋 服務 ID 分析</h3>
                      <div className="text-sm text-gray-700 space-y-2">
                        <div>
                          <strong>已訂閱服務 ID:</strong>
                          <div className="mt-1 text-xs font-mono bg-white p-2 rounded border">
                            [{analysis.activeSubscribedServiceIds.join(', ')}]
                          </div>
                        </div>
                        <div>
                          <strong>可用服務 ID:</strong>
                          <div className="mt-1 text-xs font-mono bg-white p-2 rounded border">
                            [{analysis.availableServiceIds.join(', ')}]
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 訂閱詳情 */}
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg max-h-64 overflow-y-auto">
                      <h3 className="font-medium text-gray-900 mb-2">📦 訂閱詳情</h3>
                      <div className="text-xs text-gray-600 space-y-1">
                        {subscriptions.map(sub => (
                          <div key={sub.id} className="flex justify-between">
                            <span>訂閱 {sub.id}: 服務 {sub.service_id} ({sub.service_name})</span>
                            <span className={`px-1 rounded ${
                              sub.status === 'active' ? 'bg-green-100 text-green-800' :
                              sub.status === 'cancelled' ? 'bg-gray-100 text-gray-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {sub.status}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📋 驗證說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>正確的計算公式:</strong></p>
                <p className="ml-4 font-mono">活躍訂閱數 + 可用服務數 = 總活躍服務數</p>
                <p><strong>常見問題:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>訂閱記錄 ID 與服務 ID 混淆</li>
                  <li>重複計算同一服務的多個訂閱</li>
                  <li>未正確過濾訂閱狀態</li>
                  <li>服務狀態過濾錯誤</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
