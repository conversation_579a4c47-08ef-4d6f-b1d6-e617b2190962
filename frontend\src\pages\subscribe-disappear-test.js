import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function SubscribeDisappearTest() {
  const [subscriptions, setSubscriptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [allServices, setAllServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    setIsLoading(true);
    addLog('🔄 載入所有數據...', 'info');

    try {
      await Promise.all([
        loadSubscriptions(),
        loadAllServices()
      ]);
    } catch (error) {
      addLog(`❌ 載入失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubscriptions = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        addLog('❌ 未登入', 'error');
        return;
      }

      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        const formattedSubscriptions = result.data.map(sub => ({
          id: sub.id,
          service_id: sub.service_id,
          service_name: sub.service_name,
          status: sub.status
        }));
        
        setSubscriptions(formattedSubscriptions);
        addLog(`✅ 載入 ${formattedSubscriptions.length} 個訂閱`, 'success');
      } else {
        addLog(`❌ 載入訂閱失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 訂閱請求失敗: ${error.message}`, 'error');
    }
  };

  const loadAllServices = async () => {
    try {
      const response = await fetch('/api/services/database');
      const result = await response.json();

      if (result.success) {
        setAllServices(result.data);
        addLog(`✅ 載入 ${result.data.length} 個服務`, 'success');
      } else {
        addLog(`❌ 載入服務失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 服務請求失敗: ${error.message}`, 'error');
    }
  };

  // 計算可用服務
  useEffect(() => {
    if (allServices.length > 0) {
      const activeServices = allServices.filter(service => service.status === 'active');
      const activeSubscribedServiceIds = subscriptions
        .filter(sub => sub.status === 'active')
        .map(sub => sub.service_id);
      
      const available = activeServices.filter(service =>
        !activeSubscribedServiceIds.includes(service.id)
      );

      setAvailableServices(available);
      addLog(`🎯 計算可用服務: ${available.length} 個`, 'info');
    }
  }, [allServices, subscriptions]);

  const testSubscribe = async (serviceId) => {
    const service = availableServices.find(s => s.id === serviceId);
    if (!service) {
      addLog('❌ 找不到指定的服務', 'error');
      return;
    }

    addLog(`🧪 開始測試訂閱: ${service.name} (ID: ${serviceId})`, 'info');
    
    // 記錄訂閱前的狀態
    const beforeAvailableCount = availableServices.length;
    const beforeSubscriptionCount = subscriptions.filter(sub => sub.status === 'active').length;
    
    addLog(`📊 訂閱前狀態: 可用服務 ${beforeAvailableCount} 個, 活躍訂閱 ${beforeSubscriptionCount} 個`, 'info');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        addLog('❌ 請先登入', 'error');
        return;
      }

      addLog('📡 發送訂閱請求...', 'info');

      const response = await fetch('/api/subscriptions/simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          service_id: serviceId
        })
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || `訂閱失敗 (HTTP ${response.status})`);
      }

      addLog(`✅ 訂閱成功: ${result.message}`, 'success');
      addLog(`📋 操作類型: ${result.action || 'unknown'}`, 'info');

      // 立即更新本地狀態（模擬訂閱頁面的邏輯）
      addLog('🔄 立即更新本地狀態...', 'info');

      // 1. 更新訂閱列表
      const newSubscription = {
        id: result.data.id,
        service_id: result.data.service_id,
        service_name: result.data.service_name,
        status: result.data.status
      };

      setSubscriptions(prev => {
        if (result.action === 'reactivated') {
          const updated = prev.map(sub => 
            sub.id === newSubscription.id 
              ? newSubscription 
              : sub
          );
          addLog(`🔄 重新激活記錄 ${newSubscription.id}`, 'info');
          return updated;
        } else {
          addLog(`🆕 添加新訂閱記錄 ${newSubscription.id}`, 'info');
          return [...prev, newSubscription];
        }
      });

      // 2. 從可用服務列表中移除
      setAvailableServices(prev => {
        const filtered = prev.filter(s => s.id !== serviceId);
        addLog(`🎯 從可用列表移除服務 ${serviceId}, 剩餘 ${filtered.length} 個`, 'success');
        return filtered;
      });

      // 等待狀態更新完成
      setTimeout(() => {
        const afterAvailableCount = availableServices.filter(s => s.id !== serviceId).length;
        const afterSubscriptionCount = subscriptions.filter(sub => sub.status === 'active').length + 1;
        
        addLog(`📊 訂閱後狀態: 可用服務 ${afterAvailableCount} 個, 活躍訂閱 ${afterSubscriptionCount} 個`, 'info');
        addLog(`✅ 測試完成: 服務已從可用列表消失`, 'success');
      }, 100);

    } catch (error) {
      addLog(`❌ 訂閱失敗: ${error.message}`, 'error');
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <>
      <Head>
        <title>訂閱消失測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🎯 訂閱消失測試</h1>
              <p className="text-gray-600 mt-2">測試訂閱服務後是否立即從可用列表中消失</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* 控制面板 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試控制</h2>
                
                <div className="space-y-4">
                  <button
                    onClick={loadAllData}
                    disabled={isLoading}
                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isLoading ? '載入中...' : '🔄 重新載入數據'}
                  </button>

                  <button
                    onClick={clearLogs}
                    className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    🗑️ 清除日誌
                  </button>
                </div>

                {/* 數據摘要 */}
                <div className="mt-6 space-y-4">
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">📊 數據摘要</h3>
                    <div className="text-sm text-blue-800 space-y-1">
                      <div>總服務數: {allServices.length}</div>
                      <div>活躍訂閱: {subscriptions.filter(s => s.status === 'active').length}</div>
                      <div>可用服務: {availableServices.length}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 可用服務列表 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">🎯 可用服務 ({availableServices.length})</h2>
                
                {availableServices.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    暫無可用服務
                  </div>
                ) : (
                  <div className="space-y-3">
                    {availableServices.map((service) => (
                      <div key={service.id} className="p-4 border border-green-200 bg-green-50 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-green-900">{service.name}</h4>
                            <p className="text-sm text-green-700">服務ID: {service.id}</p>
                          </div>
                          <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                            可訂閱
                          </span>
                        </div>
                        
                        <button
                          onClick={() => testSubscribe(service.id)}
                          className="w-full mt-2 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                        >
                          🧪 測試訂閱
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 測試日誌 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試日誌</h2>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                  {logs.map((log) => (
                    <div key={log.id} className={`mb-1 ${
                      log.type === 'error' ? 'text-red-400' : 
                      log.type === 'success' ? 'text-green-400' : 
                      log.type === 'warning' ? 'text-yellow-400' :
                      'text-blue-400'
                    }`}>
                      [{log.timestamp}] {log.message}
                    </div>
                  ))}
                  {logs.length === 0 && (
                    <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                  )}
                </div>
              </div>
            </div>

            {/* 我的訂閱 */}
            <div className="mt-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📦 我的訂閱 ({subscriptions.filter(s => s.status === 'active').length})</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {subscriptions.filter(sub => sub.status === 'active').map((subscription) => (
                  <div key={subscription.id} className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900">{subscription.service_name}</h4>
                    <p className="text-sm text-blue-700">記錄ID: {subscription.id} | 服務ID: {subscription.service_id}</p>
                    <span className="inline-block mt-2 px-2 py-1 bg-blue-600 text-white rounded text-xs">
                      {subscription.status}
                    </span>
                  </div>
                ))}
                
                {subscriptions.filter(sub => sub.status === 'active').length === 0 && (
                  <div className="col-span-full text-center py-8 text-gray-500">
                    暫無活躍訂閱
                  </div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">🎯 測試目標</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>預期行為:</strong> 點擊訂閱按鈕後，該服務應該立即從可用服務列表中消失</p>
                <p><strong>測試步驟:</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>觀察可用服務列表的數量</li>
                  <li>點擊某個服務的「測試訂閱」按鈕</li>
                  <li>觀察該服務是否立即從列表中消失</li>
                  <li>檢查可用服務數量是否減少</li>
                  <li>確認該服務出現在「我的訂閱」中</li>
                </ol>
                <p><strong>成功標準:</strong> 訂閱後服務立即消失，不需要手動刷新頁面</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
