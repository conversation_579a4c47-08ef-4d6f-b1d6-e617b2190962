import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function Home() {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalServices: 0,
    totalTransactions: 0,
    systemUptime: '99.9%'
  });

  useEffect(() => {
    // 模擬載入統計數據
    const loadStats = async () => {
      try {
        // 這裡可以調用實際的 API
        setStats({
          totalUsers: 1250,
          totalServices: 12,
          totalTransactions: 8430,
          systemUptime: '99.9%'
        });
      } catch (error) {
        console.error('載入統計數據失敗:', error);
      }
    };

    loadStats();
  }, []);

  const features = [
    {
      icon: '👤',
      title: '會員管理',
      description: '完整的用戶註冊、登入、個人資料管理系統',
      link: '/auth/register'
    },
    {
      icon: '💰',
      title: '多元支付',
      description: '支援 Stripe、PayPal、ECPay 等多種支付方式',
      link: '/payments'
    },
    {
      icon: '🏦',
      title: '錢包系統',
      description: '多幣別錢包管理，安全的資金存取功能',
      link: '/wallet'
    },
    {
      icon: '📱',
      title: '服務訂閱',
      description: '靈活的訂閱方案，滿足不同用戶需求',
      link: '/services'
    },
    {
      icon: '🔐',
      title: '安全合規',
      description: 'PCI DSS 和 GDPR 合規，保障用戶資料安全',
      link: '/security'
    },
    {
      icon: '📊',
      title: '數據分析',
      description: '詳細的使用統計和分析報告',
      link: '/analytics'
    }
  ];

  const services = [
    {
      name: 'AI 文字生成服務',
      description: '使用先進的 AI 技術生成高品質文字內容',
      price: 'NT$299/月起',
      features: ['文章生成', '摘要製作', '翻譯服務']
    },
    {
      name: '圖片處理服務',
      description: '提供圖片壓縮、格式轉換、濾鏡等功能',
      price: 'NT$199/月起',
      features: ['圖片壓縮', '格式轉換', '濾鏡效果']
    },
    {
      name: '資料分析服務',
      description: '強大的資料分析和視覺化工具',
      price: 'NT$499/月起',
      features: ['資料視覺化', '統計分析', '報表生成']
    }
  ];

  return (
    <>
      <Head>
        <title>會員管理系統 - 首頁</title>
        <meta name="description" content="功能完整的會員管理系統，支援多元支付和訂閱服務" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* 導航欄 */}
        <nav className="bg-white shadow-lg">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <h1 className="text-2xl font-bold text-indigo-600">會員管理系統</h1>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Link href="/auth/login?redirect=%2Fdashboard-simple" className="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">
                  登入
                </Link>
                <Link href="/auth/register" className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                  註冊
                </Link>
              </div>
            </div>
          </div>
        </nav>

        {/* 英雄區塊 */}
        <section className="relative overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <motion.h1 
                className="text-4xl md:text-6xl font-bold text-gray-900 mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                現代化的
                <span className="text-indigo-600"> 會員管理系統</span>
              </motion.h1>
              
              <motion.p 
                className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                提供完整的會員註冊、支付處理、訂閱管理功能，
                符合 PCI DSS 和 GDPR 合規要求，為您的業務提供安全可靠的解決方案。
              </motion.p>

              <motion.div 
                className="flex flex-col sm:flex-row gap-4 justify-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <Link href="/auth/register" className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-lg text-lg font-medium transition-colors">
                  立即開始
                </Link>
                <Link href="/services" className="border border-indigo-600 text-indigo-600 hover:bg-indigo-50 px-8 py-3 rounded-lg text-lg font-medium transition-colors">
                  查看服務
                </Link>
              </motion.div>
            </div>
          </div>
        </section>

        {/* 統計數據 */}
        <section className="bg-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-indigo-600">{stats.totalUsers.toLocaleString()}</div>
                <div className="text-gray-600">註冊用戶</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-indigo-600">{stats.totalServices}</div>
                <div className="text-gray-600">可用服務</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-indigo-600">{stats.totalTransactions.toLocaleString()}</div>
                <div className="text-gray-600">交易筆數</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-indigo-600">{stats.systemUptime}</div>
                <div className="text-gray-600">系統穩定性</div>
              </div>
            </div>
          </div>
        </section>

        {/* 功能特色 */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">核心功能</h2>
              <p className="text-xl text-gray-600">為您的業務提供全方位的會員管理解決方案</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <Link href={feature.link} className="text-indigo-600 hover:text-indigo-800 font-medium">
                    了解更多 →
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* 服務方案 */}
        <section className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">熱門服務</h2>
              <p className="text-xl text-gray-600">選擇適合您需求的服務方案</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{service.name}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <div className="text-2xl font-bold text-indigo-600 mb-4">{service.price}</div>
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600">
                        <span className="text-green-500 mr-2">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Link href="/services" className="block w-full bg-indigo-600 hover:bg-indigo-700 text-white text-center py-2 rounded-lg transition-colors">
                    選擇方案
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 頁腳 */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">會員管理系統</h3>
                <p className="text-gray-400">
                  提供安全、可靠、功能完整的會員管理解決方案。
                </p>
              </div>
              <div>
                <h4 className="text-lg font-semibold mb-4">產品</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><Link href="/services" className="hover:text-white">服務列表</Link></li>
                  <li><Link href="/pricing" className="hover:text-white">價格方案</Link></li>
                  <li><Link href="/features" className="hover:text-white">功能特色</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="text-lg font-semibold mb-4">支援</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><Link href="/docs" className="hover:text-white">文檔</Link></li>
                  <li><Link href="/support" className="hover:text-white">技術支援</Link></li>
                  <li><Link href="/contact" className="hover:text-white">聯絡我們</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="text-lg font-semibold mb-4">法律</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><Link href="/privacy" className="hover:text-white">隱私政策</Link></li>
                  <li><Link href="/terms" className="hover:text-white">服務條款</Link></li>
                  <li><Link href="/security" className="hover:text-white">安全政策</Link></li>
                </ul>
              </div>
            </div>
            <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
              <p>&copy; 2024 會員管理系統. 版權所有.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
