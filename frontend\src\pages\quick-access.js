import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function QuickAccess() {
  const router = useRouter();
  const [authStatus, setAuthStatus] = useState(null);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let user = null;
    if (userStr) {
      try {
        user = JSON.parse(userStr);
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setAuthStatus({
      hasToken: !!token,
      hasUser: !!user,
      user: user,
      isLoggedIn: !!(token && user)
    });
  };

  const quickActions = [
    {
      title: '⚡ 一鍵認證修復',
      description: '自動修復認證問題並跳轉測試',
      url: '/instant-auth-fix',
      color: 'bg-red-600 hover:bg-red-700',
      needsAuth: false,
      priority: 1
    },
    {
      title: '🧪 前端邏輯測試',
      description: '測試前端計算邏輯（需要認證）',
      url: '/frontend-logic-test',
      color: 'bg-blue-600 hover:bg-blue-700',
      needsAuth: true,
      priority: 2
    },
    {
      title: '🧪 無認證邏輯測試',
      description: '測試前端邏輯（不需要認證）',
      url: '/no-auth-logic-test',
      color: 'bg-green-600 hover:bg-green-700',
      needsAuth: false,
      priority: 3
    },
    {
      title: '📦 訂閱頁面測試',
      description: '測試完整的訂閱功能',
      url: '/subscriptions',
      color: 'bg-purple-600 hover:bg-purple-700',
      needsAuth: true,
      priority: 4
    },
    {
      title: '🔑 Token 生成器',
      description: '手動生成和測試 Token',
      url: '/token-generator',
      color: 'bg-indigo-600 hover:bg-indigo-700',
      needsAuth: false,
      priority: 5
    },
    {
      title: '🏥 測試中心',
      description: '完整的測試工具集合',
      url: '/test-center',
      color: 'bg-gray-600 hover:bg-gray-700',
      needsAuth: false,
      priority: 6
    }
  ];

  const getRecommendedAction = () => {
    if (!authStatus?.isLoggedIn) {
      return quickActions.find(action => action.url === '/instant-auth-fix');
    } else {
      return quickActions.find(action => action.url === '/frontend-logic-test');
    }
  };

  const recommendedAction = getRecommendedAction();

  return (
    <>
      <Head>
        <title>快速訪問 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">🚀 快速訪問</h1>
              <p className="text-gray-600">快速訪問測試和修復工具</p>
            </div>

            {/* 認證狀態 */}
            {authStatus && (
              <div className={`mb-8 p-4 rounded-lg border ${
                authStatus.isLoggedIn 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <h3 className={`font-medium mb-2 ${
                  authStatus.isLoggedIn ? 'text-green-900' : 'text-red-900'
                }`}>
                  {authStatus.isLoggedIn ? '✅ 已登錄' : '❌ 未登錄'}
                </h3>
                <div className={`text-sm ${
                  authStatus.isLoggedIn ? 'text-green-800' : 'text-red-800'
                }`}>
                  {authStatus.isLoggedIn ? (
                    <div>
                      用戶: {authStatus.user?.name} ({authStatus.user?.role})
                    </div>
                  ) : (
                    <div>需要登錄才能使用完整功能</div>
                  )}
                </div>
              </div>
            )}

            {/* 推薦操作 */}
            {recommendedAction && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">🎯 推薦操作</h2>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-yellow-900 mb-2">
                        {recommendedAction.title}
                      </h3>
                      <p className="text-sm text-yellow-800">
                        {recommendedAction.description}
                      </p>
                    </div>
                    <button
                      onClick={() => router.push(recommendedAction.url)}
                      className={`px-6 py-3 text-white rounded-lg font-medium ${recommendedAction.color}`}
                    >
                      立即執行
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* 所有操作 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">🛠️ 所有工具</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {quickActions
                  .sort((a, b) => a.priority - b.priority)
                  .map((action, index) => (
                  <div key={index} className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2">
                          {action.title}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {action.description}
                        </p>
                      </div>
                      {action.needsAuth && !authStatus?.isLoggedIn && (
                        <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                          需要登錄
                        </span>
                      )}
                    </div>
                    
                    <button
                      onClick={() => router.push(action.url)}
                      disabled={action.needsAuth && !authStatus?.isLoggedIn}
                      className={`w-full px-4 py-2 text-white rounded-lg font-medium transition-colors ${
                        action.needsAuth && !authStatus?.isLoggedIn
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : action.color
                      }`}
                    >
                      {action.needsAuth && !authStatus?.isLoggedIn ? '需要登錄' : '開始使用'}
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* 快速操作 */}
            <div className="flex flex-wrap gap-4 justify-center">
              <button
                onClick={() => router.push('/dashboard-simple')}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
              >
                🏠 儀表板
              </button>
              
              <button
                onClick={checkAuthStatus}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
              >
                🔄 刷新狀態
              </button>
              
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 font-medium"
              >
                🔄 重新載入
              </button>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">📋 使用建議</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>如果未登錄:</strong> 使用「一鍵認證修復」自動解決認證問題</div>
                <div><strong>如果已登錄:</strong> 直接使用「前端邏輯測試」驗證功能</div>
                <div><strong>測試邏輯:</strong> 可以先用「無認證邏輯測試」驗證計算邏輯</div>
                <div><strong>完整測試:</strong> 使用「訂閱頁面測試」進行實際功能測試</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
