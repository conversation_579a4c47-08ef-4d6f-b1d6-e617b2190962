#!/bin/bash

# 開發環境快速啟動腳本
# 使用方法: ./scripts/start-dev.sh [ngrok]

echo "🚀 啟動 MemberService 開發環境..."

# 檢查參數
USE_NGROK=false
if [ "$1" = "ngrok" ]; then
    USE_NGROK=true
    echo "🌐 使用 ngrok 域名模式"
else
    echo "🏠 使用本地開發模式"
fi

# 檢查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安裝，請先安裝 Node.js 18+"
    exit 1
fi

# 檢查 npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安裝，請先安裝 npm"
    exit 1
fi

# 安裝依賴 (如果需要)
if [ ! -d "frontend/node_modules" ]; then
    echo "📦 安裝前端依賴..."
    cd frontend && npm install && cd ..
fi

if [ ! -d "backend/node_modules" ]; then
    echo "📦 安裝後端依賴..."
    cd backend && npm install && cd ..
fi

# 檢查環境變數文件
if [ ! -f "frontend/.env.local" ]; then
    echo "⚠️  前端環境變數文件不存在，請配置 frontend/.env.local"
fi

if [ ! -f "backend/.env" ]; then
    echo "⚠️  後端環境變數文件不存在，請配置 backend/.env"
fi

# 啟動後端
echo "🔧 啟動後端服務 (Port 3000)..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

# 等待後端啟動
sleep 3

# 啟動前端
echo "🎨 啟動前端服務..."
cd frontend

if [ "$USE_NGROK" = true ]; then
    echo "🌐 前端運行在 0.0.0.0:3001 (ngrok 模式)"
    npm run dev:ngrok &
    FRONTEND_PID=$!
    
    # 等待前端啟動
    sleep 5
    
    # 啟動 ngrok
    if command -v ngrok &> /dev/null; then
        echo "🚇 啟動 ngrok 隧道..."
        npm run ngrok &
        NGROK_PID=$!
    else
        echo "⚠️  ngrok 未安裝，請手動啟動 ngrok"
    fi
else
    echo "🏠 前端運行在 localhost:3001 (本地模式)"
    npm run dev &
    FRONTEND_PID=$!
fi

cd ..

# 等待服務啟動
sleep 5

# 顯示訪問信息
echo ""
echo "✅ 服務啟動完成！"
echo ""
echo "📍 訪問地址:"
if [ "$USE_NGROK" = true ]; then
    echo "   🌐 ngrok 域名: https://topyun.ngrok.app"
    echo "   📊 ngrok 控制台: http://localhost:4040"
fi
echo "   🏠 本地前端: http://localhost:3001"
echo "   🔧 後端 API: http://localhost:3000"
echo ""
echo "🛑 停止服務: Ctrl+C"
echo ""

# 等待用戶中斷
trap 'echo ""; echo "🛑 正在停止服務..."; kill $BACKEND_PID $FRONTEND_PID $NGROK_PID 2>/dev/null; exit 0' INT

# 保持腳本運行
wait
