# 部署指南

MemberService 會員管理系統的完整部署說明文檔。

## 🚀 快速開始

### 本地開發環境

#### 前置要求
- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器
- Git

#### 安裝步驟

1. **克隆項目**
```bash
git clone https://github.com/jihnyu/MemberService.git
cd MemberService
```

2. **安裝前端依賴**
```bash
cd frontend
npm install
```

3. **安裝後端依賴**
```bash
cd ../backend
npm install
```

4. **環境變數設置**

前端 (`frontend/.env.local`):
```env
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3001
```

後端 (`backend/.env`):
```env
NODE_ENV=development
PORT=3000
JWT_SECRET=your-super-secret-jwt-key
DB_CONNECTION_STRING=mongodb://localhost:27017/memberservice
REDIS_URL=redis://localhost:6379
```

5. **啟動開發服務器**

前端 (端口 3001):
```bash
cd frontend
npm run dev
```

後端 (端口 3000):
```bash
cd backend
npm run dev
```

6. **訪問應用**
- 前端: http://localhost:3001
- 後端 API: http://localhost:3000

## 🐳 Docker 部署

### 使用 Docker Compose

1. **創建 docker-compose.yml**
```yaml
version: '3.8'

services:
  frontend:
    build: ./frontend
    ports:
      - "3001:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3000
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - JWT_SECRET=your-production-jwt-secret
      - DB_CONNECTION_STRING=mongodb://mongo:27017/memberservice
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongo
      - redis

  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

  redis:
    image: redis:7.0
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mongo_data:
  redis_data:
```

2. **啟動服務**
```bash
docker-compose up -d
```

### 單獨構建 Docker 鏡像

#### 前端 Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

#### 後端 Dockerfile
```dockerfile
# backend/Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

## ☁️ 雲端部署

### Vercel 部署 (前端)

1. **安裝 Vercel CLI**
```bash
npm i -g vercel
```

2. **部署前端**
```bash
cd frontend
vercel --prod
```

3. **環境變數設置**
在 Vercel 控制台設置：
- `NEXT_PUBLIC_API_URL`: 後端 API 地址
- `NEXT_PUBLIC_APP_URL`: 前端應用地址

### Heroku 部署 (後端)

1. **安裝 Heroku CLI**
```bash
# macOS
brew tap heroku/brew && brew install heroku

# Windows
# 下載並安裝 Heroku CLI
```

2. **創建 Heroku 應用**
```bash
cd backend
heroku create memberservice-api
```

3. **設置環境變數**
```bash
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your-production-jwt-secret
heroku config:set DB_CONNECTION_STRING=your-mongodb-url
heroku config:set REDIS_URL=your-redis-url
```

4. **部署應用**
```bash
git push heroku main
```

### AWS EC2 部署

#### 1. 準備 EC2 實例
```bash
# 連接到 EC2
ssh -i your-key.pem ubuntu@your-ec2-ip

# 更新系統
sudo apt update && sudo apt upgrade -y

# 安裝 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安裝 PM2
sudo npm install -g pm2
```

#### 2. 部署應用
```bash
# 克隆項目
git clone https://github.com/jihnyu/MemberService.git
cd MemberService

# 安裝依賴
cd backend && npm install
cd ../frontend && npm install && npm run build

# 使用 PM2 啟動
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### 3. Nginx 配置
```nginx
# /etc/nginx/sites-available/memberservice
server {
    listen 80;
    server_name your-domain.com;

    # 前端
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 後端 API
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔧 生產環境配置

### PM2 配置文件
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'memberservice-backend',
      script: './backend/server.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      }
    },
    {
      name: 'memberservice-frontend',
      script: 'npm',
      args: 'start',
      cwd: './frontend',
      env: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001
      }
    }
  ]
};
```

### 環境變數管理

#### 開發環境
```env
NODE_ENV=development
DEBUG=true
LOG_LEVEL=debug
```

#### 測試環境
```env
NODE_ENV=test
DEBUG=false
LOG_LEVEL=info
```

#### 生產環境
```env
NODE_ENV=production
DEBUG=false
LOG_LEVEL=error
RATE_LIMIT_ENABLED=true
CORS_ORIGIN=https://your-domain.com
```

## 🔒 安全配置

### SSL/TLS 設置
```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 防火牆配置
```bash
# UFW 防火牆
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 3000
sudo ufw allow 3001
```

### 安全標頭
```javascript
// 在 Express 應用中添加
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
```

## 📊 監控和日誌

### 日誌配置
```javascript
// 使用 Winston
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

### 健康檢查
```javascript
// 健康檢查端點
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});
```

## 🔄 CI/CD 流程

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        cd frontend && npm ci
        cd ../backend && npm ci
        
    - name: Run tests
      run: |
        cd backend && npm test
        
    - name: Build frontend
      run: |
        cd frontend && npm run build
        
    - name: Deploy to server
      run: |
        # 部署腳本
```

## 🚨 故障排除

### 常見問題

1. **端口衝突**
```bash
# 檢查端口使用情況
sudo netstat -tulpn | grep :3000
sudo netstat -tulpn | grep :3001

# 終止進程
sudo kill -9 <PID>
```

2. **權限問題**
```bash
# 修改文件權限
sudo chown -R $USER:$USER /path/to/project
chmod -R 755 /path/to/project
```

3. **內存不足**
```bash
# 檢查內存使用
free -h
top

# 增加 swap 空間
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 日誌查看
```bash
# PM2 日誌
pm2 logs

# Nginx 日誌
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# 系統日誌
sudo journalctl -u nginx
sudo journalctl -f
```

---

**更新日期**: 2025-06-26  
**版本**: v1.0.0  
**維護者**: MemberService 開發團隊
