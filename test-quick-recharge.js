// 測試快速充值功能
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testQuickRecharge() {
  try {
    console.log('💳 測試快速充值功能...');
    
    // 1. 登入獲取 token
    console.log('\n1️⃣ 登入獲取 token...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: '1234'
    });

    if (loginResult.status !== 200 || !loginResult.data.success) {
      console.error('❌ 登入失敗:', loginResult.data);
      return;
    }

    const token = loginResult.data.data.token;
    const user = loginResult.data.data.user;
    console.log('✅ 登入成功 - 用戶:', user.name);

    // 2. 檢查初始餘額
    console.log('\n2️⃣ 檢查初始餘額...');
    const balanceOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/balance',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const initialBalanceResult = await makeRequest(balanceOptions);
    
    if (initialBalanceResult.status === 200 && initialBalanceResult.data.success) {
      const initialBalance = initialBalanceResult.data.data.balance;
      console.log(`✅ 初始餘額: $${initialBalance}`);
      
      // 3. 測試快速充值 - 小金額
      console.log('\n3️⃣ 測試快速充值 - $100...');
      const rechargeOptions = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/wallet/recharge',
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      const quickRecharge1 = await makeRequest(rechargeOptions, {
        amount: 100,
        payment_method_id: 'credit_card',
        description: '快速充值測試 - $100'
      });

      if (quickRecharge1.status === 200 && quickRecharge1.data.success) {
        console.log('✅ 快速充值 $100 成功!');
        console.log(`   - 充值金額: $${quickRecharge1.data.data.payment_details.amount}`);
        console.log(`   - 手續費: $${quickRecharge1.data.data.payment_details.fee}`);
        console.log(`   - 新餘額: ${quickRecharge1.data.data.new_balance.formatted_balance}`);
        console.log(`   - 餘額數值: ${quickRecharge1.data.data.new_balance.balance}`);
        console.log(`   - 餘額類型: ${typeof quickRecharge1.data.data.new_balance.balance}`);
      } else {
        console.log('❌ 快速充值 $100 失敗:', quickRecharge1.data);
      }

      // 4. 測試快速充值 - 中等金額
      console.log('\n4️⃣ 測試快速充值 - $500...');
      const quickRecharge2 = await makeRequest(rechargeOptions, {
        amount: 500,
        payment_method_id: 'bank_transfer',
        description: '快速充值測試 - $500'
      });

      if (quickRecharge2.status === 200 && quickRecharge2.data.success) {
        console.log('✅ 快速充值 $500 成功!');
        console.log(`   - 充值金額: $${quickRecharge2.data.data.payment_details.amount}`);
        console.log(`   - 手續費: $${quickRecharge2.data.data.payment_details.fee}`);
        console.log(`   - 新餘額: ${quickRecharge2.data.data.new_balance.formatted_balance}`);
        console.log(`   - 餘額數值: ${quickRecharge2.data.data.new_balance.balance}`);
        console.log(`   - 餘額類型: ${typeof quickRecharge2.data.data.new_balance.balance}`);
      } else {
        console.log('❌ 快速充值 $500 失敗:', quickRecharge2.data);
      }

      // 5. 測試快速充值 - 大金額
      console.log('\n5️⃣ 測試快速充值 - $2000...');
      const quickRecharge3 = await makeRequest(rechargeOptions, {
        amount: 2000,
        payment_method_id: 'digital_wallet',
        description: '快速充值測試 - $2000'
      });

      if (quickRecharge3.status === 200 && quickRecharge3.data.success) {
        console.log('✅ 快速充值 $2000 成功!');
        console.log(`   - 充值金額: $${quickRecharge3.data.data.payment_details.amount}`);
        console.log(`   - 手續費: $${quickRecharge3.data.data.payment_details.fee}`);
        console.log(`   - 新餘額: ${quickRecharge3.data.data.new_balance.formatted_balance}`);
        console.log(`   - 餘額數值: ${quickRecharge3.data.data.new_balance.balance}`);
        console.log(`   - 餘額類型: ${typeof quickRecharge3.data.data.new_balance.balance}`);
      } else {
        console.log('❌ 快速充值 $2000 失敗:', quickRecharge3.data);
      }

      // 6. 檢查最終餘額
      console.log('\n6️⃣ 檢查最終餘額...');
      const finalBalanceResult = await makeRequest(balanceOptions);
      
      if (finalBalanceResult.status === 200 && finalBalanceResult.data.success) {
        const finalBalance = finalBalanceResult.data.data.balance;
        console.log(`✅ 最終餘額: $${finalBalance}`);
        console.log(`✅ 餘額增加: $${finalBalance - initialBalance}`);
      }

      // 7. 測試錯誤情況
      console.log('\n7️⃣ 測試錯誤情況...');
      
      // 測試無效金額
      const invalidAmountResult = await makeRequest(rechargeOptions, {
        amount: -100,
        payment_method_id: 'credit_card',
        description: '測試無效金額'
      });

      if (invalidAmountResult.status === 400) {
        console.log('✅ 無效金額測試通過:', invalidAmountResult.data.error);
      } else {
        console.log('❌ 無效金額測試失敗');
      }

      // 測試無效支付方式
      const invalidMethodResult = await makeRequest(rechargeOptions, {
        amount: 100,
        payment_method_id: 'invalid_method',
        description: '測試無效支付方式'
      });

      if (invalidMethodResult.status === 400) {
        console.log('✅ 無效支付方式測試通過:', invalidMethodResult.data.error);
      } else {
        console.log('❌ 無效支付方式測試失敗');
      }

      console.log('\n📋 快速充值測試總結:');
      console.log('✅ 小金額充值: 正常運作');
      console.log('✅ 中等金額充值: 正常運作');
      console.log('✅ 大金額充值: 正常運作');
      console.log('✅ 餘額更新: 正常運作');
      console.log('✅ 錯誤處理: 正常運作');
      console.log('✅ 數據格式: 正確返回');

    } else {
      console.log('❌ 無法獲取初始餘額:', initialBalanceResult.data);
    }

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testQuickRecharge();
