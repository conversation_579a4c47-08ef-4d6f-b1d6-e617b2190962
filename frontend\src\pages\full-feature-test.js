import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function FullFeatureTest() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [logs, setLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    // 檢查用戶登入狀態
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        addLog(`✅ 用戶已登入: ${parsedUser.name} (${parsedUser.email})`, 'success');
      } catch (error) {
        addLog('⚠️ 用戶數據解析失敗', 'warning');
      }
    } else {
      addLog('⚠️ 用戶未登入', 'warning');
    }
  }, []);

  // 測試註冊功能
  const testRegister = async () => {
    setIsLoading(true);
    addLog('🧪 開始測試註冊功能...', 'info');

    try {
      const testEmail = `test${Date.now()}@example.com`;
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: '測試用戶',
          email: testEmail,
          password: '1234',
          confirmPassword: '1234',
          phone: '0912345678'
        })
      });

      const result = await response.json();
      
      if (response.ok && result.success) {
        addLog(`✅ 註冊成功: ${testEmail}`, 'success');
        addLog(`📋 用戶 ID: ${result.data.user.id}`, 'info');
      } else {
        addLog(`❌ 註冊失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 註冊測試錯誤: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 測試登入功能
  const testLogin = async () => {
    setIsLoading(true);
    addLog('🧪 開始測試登入功能...', 'info');

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: '1234'
        })
      });

      const result = await response.json();
      
      if (response.ok && result.success) {
        addLog(`✅ 登入成功: ${result.data.user.name}`, 'success');
        
        // 保存登入信息
        localStorage.setItem('token', result.data.token);
        localStorage.setItem('user', JSON.stringify(result.data.user));
        setUser(result.data.user);
        
        addLog(`📋 Token 已保存`, 'info');
      } else {
        addLog(`❌ 登入失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 登入測試錯誤: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 測試訂閱功能
  const testSubscription = async () => {
    if (!user) {
      addLog('⚠️ 請先登入', 'warning');
      return;
    }

    setIsLoading(true);
    addLog('🧪 開始測試訂閱功能...', 'info');

    try {
      const token = localStorage.getItem('token');
      
      // 先獲取可用服務
      addLog('📦 載入可用服務...', 'info');
      const servicesResponse = await fetch('/api/services/simple');
      const servicesResult = await servicesResponse.json();
      
      if (servicesResult.success && servicesResult.data.length > 0) {
        const firstService = servicesResult.data[0];
        addLog(`🎯 選擇服務: ${firstService.name} (ID: ${firstService.id})`, 'info');
        
        // 嘗試訂閱第一個服務
        const subscribeResponse = await fetch('/api/subscriptions/simple', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            service_id: firstService.id
          })
        });

        const subscribeResult = await subscribeResponse.json();
        
        if (subscribeResponse.ok && subscribeResult.success) {
          addLog(`✅ 訂閱成功: ${subscribeResult.data.service_name}`, 'success');
          addLog(`📅 下次計費日期: ${subscribeResult.data.next_billing_date}`, 'info');
        } else {
          addLog(`❌ 訂閱失敗: ${subscribeResult.error}`, 'error');
        }
      } else {
        addLog('❌ 無法載入服務列表', 'error');
      }
    } catch (error) {
      addLog(`❌ 訂閱測試錯誤: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 測試獲取訂閱列表
  const testGetSubscriptions = async () => {
    if (!user) {
      addLog('⚠️ 請先登入', 'warning');
      return;
    }

    setIsLoading(true);
    addLog('🧪 開始測試獲取訂閱列表...', 'info');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      
      if (response.ok && result.success) {
        addLog(`✅ 獲取訂閱列表成功: ${result.data.length} 個訂閱`, 'success');
        result.data.forEach((sub, index) => {
          addLog(`📋 訂閱 ${index + 1}: ${sub.service_name} (狀態: ${sub.status})`, 'info');
        });
      } else {
        addLog(`❌ 獲取訂閱列表失敗: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ 獲取訂閱列表錯誤: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 運行完整測試流程
  const runFullTest = async () => {
    addLog('🚀 開始完整功能測試流程...', 'info');
    
    await testRegister();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testLogin();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testSubscription();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testGetSubscriptions();
    
    addLog('🎉 完整測試流程結束', 'success');
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <>
      <Head>
        <title>完整功能測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">完整功能測試</h1>
                <p className="text-gray-600 mt-2">測試註冊、登入、訂閱等核心功能</p>
              </div>
              <div className="text-right">
                {user ? (
                  <div className="text-sm">
                    <div className="text-green-600 font-medium">✅ 已登入</div>
                    <div className="text-gray-600">{user.name}</div>
                    <div className="text-gray-500">{user.email}</div>
                  </div>
                ) : (
                  <div className="text-sm text-gray-500">未登入</div>
                )}
              </div>
            </div>

            {/* 測試按鈕 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
              <button
                onClick={testRegister}
                disabled={isLoading}
                className="p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                <div className="text-lg font-medium">🧪 測試註冊</div>
                <div className="text-sm opacity-75">創建新用戶帳戶</div>
              </button>

              <button
                onClick={testLogin}
                disabled={isLoading}
                className="p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
              >
                <div className="text-lg font-medium">🔑 測試登入</div>
                <div className="text-sm opacity-75">使用管理員帳戶登入</div>
              </button>

              <button
                onClick={testSubscription}
                disabled={isLoading || !user}
                className="p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
              >
                <div className="text-lg font-medium">📦 測試訂閱</div>
                <div className="text-sm opacity-75">訂閱第一個可用服務</div>
              </button>

              <button
                onClick={testGetSubscriptions}
                disabled={isLoading || !user}
                className="p-4 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors"
              >
                <div className="text-lg font-medium">📋 獲取訂閱</div>
                <div className="text-sm opacity-75">查看當前訂閱列表</div>
              </button>

              <button
                onClick={runFullTest}
                disabled={isLoading}
                className="p-4 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors"
              >
                <div className="text-lg font-medium">🚀 完整測試</div>
                <div className="text-sm opacity-75">運行所有測試流程</div>
              </button>

              <button
                onClick={clearLogs}
                className="p-4 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <div className="text-lg font-medium">🗑️ 清除日誌</div>
                <div className="text-sm opacity-75">清空測試日誌</div>
              </button>
            </div>

            {/* 測試日誌 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">測試日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                {logs.map((log) => (
                  <div key={log.id} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'success' ? 'text-green-400' : 
                    log.type === 'warning' ? 'text-yellow-400' :
                    'text-blue-400'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">點擊上方按鈕開始測試...</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
