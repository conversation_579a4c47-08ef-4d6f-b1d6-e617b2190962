import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function TokenGenerator() {
  const router = useRouter();
  const [status, setStatus] = useState('準備中...');
  const [tokenInfo, setTokenInfo] = useState(null);
  const [testResults, setTestResults] = useState([]);
  const [isWorking, setIsWorking] = useState(false);

  useEffect(() => {
    checkCurrentToken();
  }, []);

  const addTestResult = (step, result, details = '') => {
    setTestResults(prev => [...prev, {
      timestamp: new Date().toLocaleTimeString(),
      step,
      result,
      details
    }]);
  };

  const checkCurrentToken = () => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    let user = null;
    if (userStr) {
      try {
        user = JSON.parse(userStr);
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    setTokenInfo({
      hasToken: !!token,
      token: token,
      tokenLength: token ? token.length : 0,
      hasUser: !!user,
      user: user,
      isValid: !!(token && user)
    });

    if (token && user) {
      setStatus('✅ 發現現有認證數據');
    } else {
      setStatus('❌ 沒有有效的認證數據');
    }
  };

  const generateNewToken = async () => {
    setIsWorking(true);
    setTestResults([]);
    setStatus('🔄 開始生成新 Token...');

    try {
      addTestResult('步驟1', 'info', '清除舊的認證數據');
      localStorage.clear();

      addTestResult('步驟2', 'info', '調用登錄 API');
      const response = await fetch('/api/auth/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      addTestResult('步驟2', 'info', `API 響應狀態: ${response.status}`);

      const result = await response.json();
      addTestResult('步驟2', 'info', `API 響應內容: ${JSON.stringify(result, null, 2)}`);

      if (result.success) {
        addTestResult('步驟3', 'success', '登錄成功，保存認證數據');
        
        localStorage.setItem('token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));

        setTokenInfo({
          hasToken: true,
          token: result.token,
          tokenLength: result.token.length,
          hasUser: true,
          user: result.user,
          isValid: true
        });

        addTestResult('步驟4', 'info', '測試新 Token 的有效性');
        await testTokenValidity(result.token);

        setStatus('✅ Token 生成成功！');
      } else {
        addTestResult('步驟3', 'error', `登錄失敗: ${result.error}`);
        setStatus('❌ Token 生成失敗');
      }

    } catch (error) {
      addTestResult('錯誤', 'error', `生成過程出錯: ${error.message}`);
      setStatus('❌ Token 生成失敗');
    } finally {
      setIsWorking(false);
    }
  };

  const testTokenValidity = async (token) => {
    try {
      addTestResult('測試', 'info', '測試 Token 訪問訂閱 API');
      
      const response = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      addTestResult('測試', 'info', `訂閱 API 響應狀態: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        addTestResult('測試', 'success', `Token 有效！載入了 ${data.data ? data.data.length : 0} 個訂閱記錄`);
        return true;
      } else {
        const errorData = await response.json();
        addTestResult('測試', 'error', `Token 無效: ${errorData.error}`);
        return false;
      }
    } catch (error) {
      addTestResult('測試', 'error', `測試失敗: ${error.message}`);
      return false;
    }
  };

  const testCurrentToken = async () => {
    if (!tokenInfo?.token) {
      alert('沒有 Token 可供測試');
      return;
    }

    setIsWorking(true);
    setTestResults([]);
    
    const isValid = await testTokenValidity(tokenInfo.token);
    
    if (isValid) {
      setStatus('✅ 當前 Token 有效');
    } else {
      setStatus('❌ 當前 Token 無效');
    }
    
    setIsWorking(false);
  };

  const testSubscriptionsPage = () => {
    router.push('/subscriptions');
  };

  const testFrontendLogic = () => {
    router.push('/frontend-logic-test');
  };

  const copyToken = () => {
    if (tokenInfo?.token) {
      navigator.clipboard.writeText(tokenInfo.token);
      alert('Token 已複製到剪貼板');
    }
  };

  const getResultColor = (result) => {
    switch (result) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  const getResultIcon = (result) => {
    switch (result) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  return (
    <>
      <Head>
        <title>Token 生成器 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔑 Token 生成器</h1>
                <p className="text-gray-600 mt-2">生成和測試認證 Token</p>
              </div>
              <button
                onClick={() => router.push('/dashboard-simple')}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                🏠 回儀表板
              </button>
            </div>

            {/* 狀態顯示 */}
            <div className="mb-8">
              <div className={`p-4 rounded-lg ${
                tokenInfo?.isValid ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
              }`}>
                <p className={`font-medium ${
                  tokenInfo?.isValid ? 'text-green-800' : 'text-red-800'
                }`}>
                  {status}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Token 信息 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 Token 信息</h2>
                
                {tokenInfo && (
                  <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Token:</span>
                      <span className={tokenInfo.hasToken ? 'text-green-600' : 'text-red-600'}>
                        {tokenInfo.hasToken ? `✅ ${tokenInfo.tokenLength} 字符` : '❌ 缺失'}
                      </span>
                    </div>
                    
                    {tokenInfo.token && (
                      <div className="bg-white p-3 rounded border">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">Token 內容:</span>
                          <button
                            onClick={copyToken}
                            className="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                          >
                            複製
                          </button>
                        </div>
                        <div className="text-xs font-mono bg-gray-100 p-2 rounded break-all">
                          {tokenInfo.token}
                        </div>
                      </div>
                    )}

                    <div className="flex justify-between items-center">
                      <span className="font-medium">User:</span>
                      <span className={tokenInfo.hasUser ? 'text-green-600' : 'text-red-600'}>
                        {tokenInfo.hasUser ? '✅ 存在' : '❌ 缺失'}
                      </span>
                    </div>

                    {tokenInfo.user && (
                      <div className="bg-white p-3 rounded border">
                        <div className="text-sm space-y-1">
                          <div><strong>姓名:</strong> {tokenInfo.user.name}</div>
                          <div><strong>Email:</strong> {tokenInfo.user.email}</div>
                          <div><strong>角色:</strong> {tokenInfo.user.role}</div>
                          <div><strong>ID:</strong> {tokenInfo.user.userId}</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* 操作按鈕 */}
                <div className="mt-6 space-y-3">
                  <button
                    onClick={generateNewToken}
                    disabled={isWorking}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
                  >
                    {isWorking ? '生成中...' : '🔄 生成新 Token'}
                  </button>

                  {tokenInfo?.hasToken && (
                    <button
                      onClick={testCurrentToken}
                      disabled={isWorking}
                      className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                    >
                      🧪 測試當前 Token
                    </button>
                  )}

                  {tokenInfo?.isValid && (
                    <div className="space-y-2">
                      <button
                        onClick={testSubscriptionsPage}
                        className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                      >
                        📦 測試訂閱頁面
                      </button>
                      
                      <button
                        onClick={testFrontendLogic}
                        className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                      >
                        🧪 測試前端邏輯
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* 測試結果 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 測試日誌</h2>
                
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                  {testResults.length > 0 ? (
                    <div className="space-y-2">
                      {testResults.map((result, index) => (
                        <div key={index} className="bg-white rounded p-3 border">
                          <div className="flex items-start space-x-2 text-sm">
                            <span className={getResultColor(result.result)}>
                              {getResultIcon(result.result)}
                            </span>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <span className="text-gray-500">[{result.timestamp}]</span>
                                <span className="font-medium">{result.step}:</span>
                              </div>
                              <div className={`mt-1 ${getResultColor(result.result)}`}>
                                {result.details}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-gray-500 text-center py-8">
                      測試日誌將顯示在這裡...
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-3">🎯 使用說明</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <div><strong>生成新 Token:</strong> 清除舊數據，重新登錄獲取新 Token</div>
                <div><strong>測試 Token:</strong> 驗證當前 Token 是否可以訪問 API</div>
                <div><strong>測試頁面:</strong> 使用有效 Token 測試實際功能</div>
                <div><strong>管理員帳號:</strong> <EMAIL> / password123</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
