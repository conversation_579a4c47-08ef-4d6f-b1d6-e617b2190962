-- 會員管理系統資料庫結構
-- 創建時間: 2025-06-26

-- 1. 會員表 (members)
CREATE TABLE IF NOT EXISTS members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user', -- 'user', 'admin'
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'inactive', 'suspended'
    avatar_url VARCHAR(500),
    phone VARCHAR(20),
    wallet_balance DECIMAL(15,2) DEFAULT 0.00, -- 錢包餘額
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 服務表 (services)
CREATE TABLE IF NOT EXISTS services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    billing_cycle VARCHAR(20) DEFAULT 'monthly', -- 'monthly', 'yearly'
    category VARCHAR(100),
    features TEXT, -- JSON 格式存儲功能列表
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'inactive', 'draft'
    popular BOOLEAN DEFAULT FALSE,
    max_users INTEGER DEFAULT 1,
    api_limit INTEGER DEFAULT 0, -- 0 表示無限制
    storage_limit VARCHAR(50),
    support_level VARCHAR(20) DEFAULT 'basic', -- 'basic', 'standard', 'premium', 'enterprise'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 3. 會員服務關聯表 (member_services)
CREATE TABLE IF NOT EXISTS member_services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER NOT NULL,
    service_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'cancelled', 'suspended', 'expired'
    subscribed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    next_billing_date DATE,
    cancelled_at DATETIME NULL,
    usage_data TEXT, -- JSON 格式存儲使用情況
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外鍵約束
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    
    -- 唯一約束：同一會員不能重複訂閱同一服務
    UNIQUE(member_id, service_id)
);

-- 4. 服務統計表 (service_stats) - 用於快速查詢統計數據
CREATE TABLE IF NOT EXISTS service_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    service_id INTEGER NOT NULL,
    total_subscribers INTEGER DEFAULT 0,
    active_subscribers INTEGER DEFAULT 0,
    total_revenue DECIMAL(15,2) DEFAULT 0,
    monthly_revenue DECIMAL(15,2) DEFAULT 0,
    last_calculated DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE(service_id)
);

-- 創建索引以提高查詢性能
CREATE INDEX IF NOT EXISTS idx_members_email ON members(email);
CREATE INDEX IF NOT EXISTS idx_members_status ON members(status);
CREATE INDEX IF NOT EXISTS idx_services_status ON services(status);
CREATE INDEX IF NOT EXISTS idx_services_category ON services(category);
CREATE INDEX IF NOT EXISTS idx_member_services_member_id ON member_services(member_id);
CREATE INDEX IF NOT EXISTS idx_member_services_service_id ON member_services(service_id);
CREATE INDEX IF NOT EXISTS idx_member_services_status ON member_services(status);

-- 插入預設管理員帳戶
INSERT OR IGNORE INTO members (email, name, password_hash, role) VALUES 
('<EMAIL>', '系統管理員', '$2b$10$hash_placeholder', 'admin');

-- 插入預設服務數據
INSERT OR IGNORE INTO services (id, name, description, price, billing_cycle, category, features, status, popular, max_users, api_limit, storage_limit, support_level) VALUES 
(1, 'AI 文字生成服務', '強大的 AI 文字生成工具，支援多種語言和格式', 299.00, 'monthly', 'AI 服務', '["無限文字生成", "多語言支援", "模板庫", "24/7 客服"]', 'active', TRUE, 1, 50000, '10GB', 'premium'),
(2, '圖片處理服務', '專業的圖片編輯和處理工具', 199.00, 'monthly', '媒體處理', '["批量處理", "格式轉換", "智能壓縮", "雲端存儲"]', 'active', FALSE, 3, 1000, '50GB', 'standard'),
(3, '數據分析服務', '深度數據分析和可視化工具', 499.00, 'monthly', '數據分析', '["實時分析", "自定義報表", "數據可視化", "API 接入"]', 'inactive', FALSE, 5, 100, '100GB', 'premium'),
(4, '語音轉文字服務', '高精度語音識別和轉換服務', 149.00, 'monthly', 'AI 服務', '["多語言識別", "實時轉換", "批量處理", "高精度識別"]', 'active', FALSE, 1, 10000, '5GB', 'basic'),
(5, '雲端存儲服務', '安全可靠的雲端存儲解決方案', 99.00, 'monthly', '存儲服務', '["無限存儲", "自動備份", "版本控制", "分享功能"]', 'active', TRUE, 10, 0, '無限', 'standard'),
(6, '企業級 API 服務', '為企業提供的高級 API 接入服務', 999.00, 'monthly', '企業服務', '["無限 API 調用", "專屬支援", "自定義功能", "SLA 保證"]', 'active', FALSE, 50, 0, '1TB', 'enterprise');

-- 初始化服務統計數據
INSERT OR IGNORE INTO service_stats (service_id, total_subscribers, active_subscribers, total_revenue, monthly_revenue) VALUES 
(1, 1250, 1200, 373750.00, 358800.00),
(2, 890, 850, 177110.00, 169150.00),
(3, 320, 0, 159680.00, 0.00),
(4, 560, 540, 83440.00, 80460.00),
(5, 2100, 2050, 207900.00, 202950.00),
(6, 85, 80, 84915.00, 79920.00);

-- 創建觸發器自動更新 updated_at 欄位
CREATE TRIGGER IF NOT EXISTS update_members_timestamp 
    AFTER UPDATE ON members
BEGIN
    UPDATE members SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_services_timestamp 
    AFTER UPDATE ON services
BEGIN
    UPDATE services SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_member_services_timestamp 
    AFTER UPDATE ON member_services
BEGIN
    UPDATE member_services SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 創建視圖方便查詢
CREATE VIEW IF NOT EXISTS member_service_details AS
SELECT 
    ms.id as subscription_id,
    m.id as member_id,
    m.name as member_name,
    m.email as member_email,
    s.id as service_id,
    s.name as service_name,
    s.description as service_description,
    s.price as service_price,
    s.billing_cycle,
    s.category,
    ms.status as subscription_status,
    ms.subscribed_at,
    ms.next_billing_date,
    ms.usage_data
FROM member_services ms
JOIN members m ON ms.member_id = m.id
JOIN services s ON ms.service_id = s.id;

-- 創建統計視圖
CREATE VIEW IF NOT EXISTS service_summary AS
SELECT 
    s.id,
    s.name,
    s.description,
    s.price,
    s.category,
    s.status,
    s.popular,
    COALESCE(ss.total_subscribers, 0) as total_subscribers,
    COALESCE(ss.active_subscribers, 0) as active_subscribers,
    COALESCE(ss.total_revenue, 0) as total_revenue,
    COALESCE(ss.monthly_revenue, 0) as monthly_revenue,
    s.created_at,
    s.updated_at
FROM services s
LEFT JOIN service_stats ss ON s.id = ss.service_id;

-- 5. 錢包交易記錄表 (wallet_transactions)
CREATE TABLE IF NOT EXISTS wallet_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER NOT NULL,
    transaction_type VARCHAR(20) NOT NULL, -- 'recharge', 'withdraw', 'payment', 'refund', 'bonus'
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'completed', -- 'pending', 'completed', 'failed', 'cancelled'
    payment_method VARCHAR(50), -- 'credit_card', 'bank_transfer', 'digital_wallet', 'crypto', 'system'
    payment_reference VARCHAR(100), -- 外部支付參考號
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
);

-- 6. 支付方式表 (payment_methods)
CREATE TABLE IF NOT EXISTS payment_methods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'credit_card', 'bank_transfer', 'digital_wallet', 'crypto', 'other'
    description TEXT,
    icon_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    fee_type VARCHAR(20) DEFAULT 'none', -- 'none', 'fixed', 'percentage'
    fee_amount DECIMAL(10,4) DEFAULT 0.0000,
    min_amount DECIMAL(15,2) DEFAULT 0.00,
    max_amount DECIMAL(15,2) DEFAULT 999999.99,
    processing_time VARCHAR(50), -- '即時', '1-3個工作日', etc.
    supported_currencies TEXT, -- JSON 格式存儲支援的貨幣
    terms_url VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 錢包相關索引
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_member_id ON wallet_transactions(member_id);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_type ON wallet_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_status ON wallet_transactions(status);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_created_at ON wallet_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_methods_type ON payment_methods(type);
CREATE INDEX IF NOT EXISTS idx_payment_methods_active ON payment_methods(is_active);

-- 插入預設支付方式
INSERT OR IGNORE INTO payment_methods (id, name, type, description, icon_url, is_active, fee_type, fee_amount, min_amount, max_amount, processing_time) VALUES
(1, '信用卡', 'credit_card', '支援 Visa、MasterCard、JCB 等主要信用卡', '💳', TRUE, 'percentage', 2.5000, 10.00, 5000.00, '即時'),
(2, '銀行轉帳', 'bank_transfer', '透過銀行轉帳進行充值', '🏦', TRUE, 'fixed', 15.0000, 50.00, 10000.00, '1-3個工作日'),
(3, '數位錢包', 'digital_wallet', '支援 PayPal、Apple Pay、Google Pay 等', '📱', TRUE, 'none', 0.0000, 1.00, 3000.00, '即時'),
(4, '加密貨幣', 'crypto', '支援 Bitcoin、Ethereum 等主要加密貨幣', '₿', TRUE, 'percentage', 1.0000, 20.00, 50000.00, '10-30分鐘');

-- 錢包相關觸發器
CREATE TRIGGER IF NOT EXISTS update_wallet_transactions_timestamp
    AFTER UPDATE ON wallet_transactions
BEGIN
    UPDATE wallet_transactions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_payment_methods_timestamp
    AFTER UPDATE ON payment_methods
BEGIN
    UPDATE payment_methods SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
