import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function ApiPathTest() {
  const [logs, setLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  // 測試登入 API
  const testLogin = async () => {
    setIsLoading(true);
    try {
      addLog('測試簡化登入 API...', 'info');

      const response = await fetch('/api/auth/login-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: '1234'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        addLog('✅ 登入 API 測試成功', 'success');
        addLog(`用戶: ${result.data.user.name}`, 'info');
        
        // 保存 token 供後續測試使用
        localStorage.setItem('token', result.data.token);
        localStorage.setItem('user', JSON.stringify(result.data.user));
      } else {
        addLog(`❌ 登入 API 測試失敗: ${result.error}`, 'error');
        if (result.details) {
          addLog(`詳細錯誤: ${result.details}`, 'error');
        }
      }
    } catch (error) {
      addLog(`❌ 登入 API 請求失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 測試服務 API
  const testServices = async () => {
    setIsLoading(true);
    try {
      addLog('測試簡化服務 API...', 'info');

      const response = await fetch('/api/services/simple');
      const result = await response.json();
      
      if (result.success) {
        addLog(`✅ 服務 API 測試成功，載入 ${result.data.length} 個服務`, 'success');
      } else {
        addLog(`❌ 服務 API 測試失敗: ${result.error}`, 'error');
        if (result.details) {
          addLog(`詳細錯誤: ${result.details}`, 'error');
        }
      }
    } catch (error) {
      addLog(`❌ 服務 API 請求失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 測試 Token 驗證 API
  const testVerify = async () => {
    setIsLoading(true);
    try {
      addLog('測試簡化 Token 驗證 API...', 'info');

      const token = localStorage.getItem('token');
      if (!token) {
        addLog('⚠️ 沒有找到 Token，請先登入', 'warning');
        setIsLoading(false);
        return;
      }

      const response = await fetch('/api/auth/verify-simple', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      
      if (result.success) {
        addLog('✅ Token 驗證 API 測試成功', 'success');
        addLog(`用戶: ${result.data.user.name}`, 'info');
      } else {
        addLog(`❌ Token 驗證 API 測試失敗: ${result.error}`, 'error');
        if (result.details) {
          addLog(`詳細錯誤: ${result.details}`, 'error');
        }
      }
    } catch (error) {
      addLog(`❌ Token 驗證 API 請求失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 測試所有 API
  const testAllApis = async () => {
    addLog('🚀 開始測試所有 API...', 'info');
    await testServices();
    await new Promise(resolve => setTimeout(resolve, 500));
    await testLogin();
    await new Promise(resolve => setTimeout(resolve, 500));
    await testVerify();
    addLog('🎉 所有 API 測試完成', 'info');
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <>
      <Head>
        <title>API 路徑測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">API 路徑修復測試</h1>
            
            {/* 控制按鈕 */}
            <div className="mb-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <button
                onClick={testServices}
                disabled={isLoading}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
              >
                🛍️ 測試服務 API
              </button>
              
              <button
                onClick={testLogin}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                🔑 測試登入 API
              </button>
              
              <button
                onClick={testVerify}
                disabled={isLoading}
                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50"
              >
                ✅ 測試驗證 API
              </button>
              
              <button
                onClick={testAllApis}
                disabled={isLoading}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                🚀 測試所有 API
              </button>
              
              <button
                onClick={clearLogs}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🧹 清除日誌
              </button>
              
              <a
                href="/auth/login"
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-center"
              >
                🔗 前往登入頁面
              </a>
            </div>

            {/* 狀態指示 */}
            {isLoading && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
                  <span className="text-blue-800">API 測試進行中...</span>
                </div>
              </div>
            )}

            {/* 測試日誌 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">測試日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                {logs.map((log) => (
                  <div key={log.id} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'success' ? 'text-green-400' : 
                    log.type === 'warning' ? 'text-yellow-400' :
                    'text-blue-400'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">尚無測試記錄</div>
                )}
              </div>
            </div>

            {/* 說明 */}
            <div className="mt-8 p-6 bg-yellow-50 rounded-lg">
              <h3 className="font-semibold text-yellow-900 mb-3">API 路徑修復說明</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>修復內容：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>修復了所有 API 文件中的模組路徑問題</li>
                  <li>添加了多路徑嘗試機制，提高載入成功率</li>
                  <li>改善了錯誤處理和日誌記錄</li>
                  <li>確保 Member、Service、MemberService 模型正確載入</li>
                </ul>
                <p><strong>測試步驟：</strong></p>
                <ol className="list-decimal list-inside ml-4 space-y-1">
                  <li>先測試服務 API（不需要認證）</li>
                  <li>測試登入 API（會保存 Token）</li>
                  <li>測試驗證 API（使用保存的 Token）</li>
                  <li>或者點擊"測試所有 API"一次性測試</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
