// 更新用戶角色 API
const jwt = require('jsonwebtoken');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export default async function handler(req, res) {
  try {
    console.log('更新用戶角色 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    const { userId, newRole } = req.body;

    if (!userId || !newRole) {
      return res.status(400).json({
        success: false,
        error: '請提供用戶 ID 和新角色'
      });
    }

    // 驗證角色值
    if (!['admin', 'user'].includes(newRole)) {
      return res.status(400).json({
        success: false,
        error: '無效的角色值'
      });
    }

    console.log('更新用戶角色 API: 更新用戶', { userId, newRole });

    // 使用資料庫連接
    const { getDatabase, dbRun, dbGet } = require('../../../lib/database/init');
    const db = await getDatabase();

    // 檢查用戶是否存在
    const user = await dbGet(db, 'SELECT id, name, email, role FROM members WHERE id = ?', [userId]);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用戶不存在'
      });
    }

    console.log('更新用戶角色 API: 當前用戶數據:', user);

    // 更新用戶角色
    await dbRun(db, 'UPDATE members SET role = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [newRole, userId]);

    // 查詢更新後的用戶數據
    const updatedUser = await dbGet(db, 'SELECT id, name, email, role FROM members WHERE id = ?', [userId]);

    console.log('更新用戶角色 API: 更新成功');

    return res.status(200).json({
      success: true,
      data: {
        userId: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        oldRole: user.role,
        newRole: updatedUser.role
      },
      message: `用戶角色已從 ${user.role} 更新為 ${updatedUser.role}`
    });

  } catch (error) {
    console.error('更新用戶角色 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
