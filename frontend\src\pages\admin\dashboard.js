import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { AdminLayout } from '../../components/AdminNav';

export default function AdminDashboard() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);

  // 模擬管理員儀表板數據
  const mockDashboardData = {
    overview: {
      totalUsers: 5420,
      activeSubscriptions: 3850,
      totalRevenue: 1287500,
      monthlyGrowth: 12.5
    },
    services: {
      total: 6,
      active: 5,
      inactive: 1,
      mostPopular: 'AI 文字生成服務'
    },
    revenue: {
      thisMonth: 425000,
      lastMonth: 378000,
      growth: 12.4,
      topService: 'AI 文字生成服務'
    },
    users: {
      newThisMonth: 320,
      activeUsers: 4890,
      churnRate: 2.1,
      avgLifetime: '8.5 個月'
    },
    recentActivities: [
      {
        id: 1,
        type: 'user_signup',
        message: '新用戶註冊：張小明',
        time: '5 分鐘前',
        icon: '👤'
      },
      {
        id: 2,
        type: 'subscription',
        message: 'AI 文字生成服務新增訂閱',
        time: '12 分鐘前',
        icon: '📱'
      },
      {
        id: 3,
        type: 'payment',
        message: '收到付款：NT$ 299',
        time: '18 分鐘前',
        icon: '💰'
      },
      {
        id: 4,
        type: 'service_update',
        message: '圖片處理服務已更新',
        time: '1 小時前',
        icon: '🛠️'
      },
      {
        id: 5,
        type: 'user_activity',
        message: '100+ 用戶同時在線',
        time: '2 小時前',
        icon: '📊'
      }
    ],
    topServices: [
      {
        name: 'AI 文字生成服務',
        subscribers: 1250,
        revenue: 373750,
        growth: 15.2
      },
      {
        name: '雲端存儲服務',
        subscribers: 2100,
        revenue: 207900,
        growth: 8.7
      },
      {
        name: '圖片處理服務',
        subscribers: 890,
        revenue: 177110,
        growth: 5.3
      }
    ]
  };

  useEffect(() => {
    // 檢查管理員權限
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login?redirect=' + encodeURIComponent('/admin/dashboard'));
      return;
    }

    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        if (userData.email !== '<EMAIL>') {
          alert('您沒有管理員權限');
          router.push('/minimal-dashboard');
          return;
        }
      } catch (error) {
        console.error('用戶數據解析失敗:', error);
      }
    }

    // 載入儀表板數據
    setDashboardData(mockDashboardData);
    setIsLoading(false);
  }, [router]);

  const formatCurrency = (amount) => {
    return `NT$ ${amount.toLocaleString()}`;
  };

  const getGrowthColor = (growth) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getGrowthIcon = (growth) => {
    if (growth > 0) return '↗️';
    if (growth < 0) return '↘️';
    return '➡️';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入管理員儀表板中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>管理員儀表板 - 會員管理系統</title>
      </Head>

      <AdminLayout currentPage="dashboard" title="管理員儀表板">
        <div className="space-y-8">
          {/* 概覽統計 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">總用戶數</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData.overview.totalUsers.toLocaleString()}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-lg">
                  <span className="text-2xl">👥</span>
                </div>
              </div>
              <div className={`text-sm mt-2 ${getGrowthColor(dashboardData.overview.monthlyGrowth)}`}>
                {getGrowthIcon(dashboardData.overview.monthlyGrowth)} {dashboardData.overview.monthlyGrowth}% 本月增長
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">活躍訂閱</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData.overview.activeSubscriptions.toLocaleString()}
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-lg">
                  <span className="text-2xl">📱</span>
                </div>
              </div>
              <div className="text-sm text-gray-500 mt-2">
                {dashboardData.services.active} 個服務啟用中
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">總收入</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(dashboardData.overview.totalRevenue)}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-lg">
                  <span className="text-2xl">💰</span>
                </div>
              </div>
              <div className={`text-sm mt-2 ${getGrowthColor(dashboardData.revenue.growth)}`}>
                {getGrowthIcon(dashboardData.revenue.growth)} {dashboardData.revenue.growth}% 月增長
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">本月收入</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(dashboardData.revenue.thisMonth)}
                  </p>
                </div>
                <div className="p-3 bg-yellow-100 rounded-lg">
                  <span className="text-2xl">📊</span>
                </div>
              </div>
              <div className="text-sm text-gray-500 mt-2">
                較上月增長 {formatCurrency(dashboardData.revenue.thisMonth - dashboardData.revenue.lastMonth)}
              </div>
            </motion.div>
          </div>

          {/* 主要內容區域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 熱門服務 */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">熱門服務</h3>
                <button
                  onClick={() => router.push('/admin/services')}
                  className="text-blue-600 hover:text-blue-700 text-sm"
                >
                  查看全部 →
                </button>
              </div>
              
              <div className="space-y-4">
                {dashboardData.topServices.map((service, index) => (
                  <div key={service.name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{service.name}</h4>
                        <p className="text-sm text-gray-500">{service.subscribers.toLocaleString()} 訂閱</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-gray-900">
                        {formatCurrency(service.revenue)}
                      </div>
                      <div className={`text-sm ${getGrowthColor(service.growth)}`}>
                        {getGrowthIcon(service.growth)} {service.growth}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* 最近活動 */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-6">最近活動</h3>
              
              <div className="space-y-4">
                {dashboardData.recentActivities.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
                    className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg"
                  >
                    <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                      <span className="text-lg">{activity.icon}</span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* 快速操作 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-6">快速操作</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => router.push('/admin/services')}
                className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
              >
                <div className="text-center">
                  <span className="text-3xl mb-2 block">🛠️</span>
                  <h4 className="font-medium text-gray-900">管理服務</h4>
                  <p className="text-sm text-gray-500">新增或編輯服務項目</p>
                </div>
              </button>

              <button
                onClick={() => router.push('/admin/users')}
                className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors"
              >
                <div className="text-center">
                  <span className="text-3xl mb-2 block">👥</span>
                  <h4 className="font-medium text-gray-900">用戶管理</h4>
                  <p className="text-sm text-gray-500">查看和管理用戶</p>
                </div>
              </button>

              <button
                onClick={() => router.push('/admin/analytics')}
                className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors"
              >
                <div className="text-center">
                  <span className="text-3xl mb-2 block">📈</span>
                  <h4 className="font-medium text-gray-900">數據分析</h4>
                  <p className="text-sm text-gray-500">查看業務數據</p>
                </div>
              </button>
            </div>
          </motion.div>
        </div>
      </AdminLayout>
    </>
  );
}
