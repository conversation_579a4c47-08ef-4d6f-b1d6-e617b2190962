// 刪除會員與服務關聯表中的重複記錄
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 連接資料庫
const dbPath = path.join(__dirname, 'database.sqlite');

if (!fs.existsSync(dbPath)) {
  console.error('資料庫文件不存在:', dbPath);
  process.exit(1);
}

const db = new sqlite3.Database(dbPath);

console.log('開始刪除重複的訂閱記錄...');

// 首先查看要刪除的記錄
const recordsToDelete = [8, 9];

console.log('查看要刪除的記錄:');

// 查詢要刪除的記錄詳情
const selectQuery = `
  SELECT ms.id, ms.member_id, ms.service_id, ms.status, ms.subscribed_at, 
         m.name as member_name, m.email as member_email,
         s.name as service_name
  FROM member_services ms
  LEFT JOIN members m ON ms.member_id = m.id
  LEFT JOIN services s ON ms.service_id = s.id
  WHERE ms.id IN (${recordsToDelete.join(',')})
  ORDER BY ms.id
`;

db.all(selectQuery, [], (err, records) => {
  if (err) {
    console.error('查詢記錄失敗:', err);
    db.close();
    process.exit(1);
  }

  console.log('\n要刪除的記錄:');
  records.forEach(record => {
    console.log(`  記錄 ${record.id}: 會員 ${record.member_name} (${record.member_email}) - 服務 ${record.service_name} - 狀態 ${record.status}`);
  });

  // 檢查是否有重複的會員-服務組合
  console.log('\n檢查重複情況:');
  
  const duplicateCheckQuery = `
    SELECT member_id, service_id, COUNT(*) as count,
           GROUP_CONCAT(id) as record_ids,
           GROUP_CONCAT(status) as statuses
    FROM member_services 
    GROUP BY member_id, service_id 
    HAVING COUNT(*) > 1
    ORDER BY member_id, service_id
  `;

  db.all(duplicateCheckQuery, [], (dupErr, duplicates) => {
    if (dupErr) {
      console.error('檢查重複失敗:', dupErr);
      db.close();
      process.exit(1);
    }

    if (duplicates.length > 0) {
      console.log('發現重複的會員-服務組合:');
      duplicates.forEach(dup => {
        console.log(`  會員 ${dup.member_id} - 服務 ${dup.service_id}: ${dup.count} 個記錄 [${dup.record_ids}] 狀態 [${dup.statuses}]`);
      });
    } else {
      console.log('沒有發現重複的會員-服務組合');
    }

    // 執行刪除操作
    console.log('\n開始刪除記錄...');
    
    let deletedCount = 0;
    const deletePromises = recordsToDelete.map(recordId => {
      return new Promise((resolve, reject) => {
        const deleteQuery = 'DELETE FROM member_services WHERE id = ?';
        
        db.run(deleteQuery, [recordId], function(deleteErr) {
          if (deleteErr) {
            console.error(`刪除記錄 ${recordId} 失敗:`, deleteErr);
            reject(deleteErr);
          } else {
            console.log(`✅ 已刪除記錄 ${recordId} (影響行數: ${this.changes})`);
            deletedCount += this.changes;
            resolve();
          }
        });
      });
    });

    Promise.all(deletePromises)
      .then(() => {
        console.log(`\n✅ 刪除完成！總共刪除了 ${deletedCount} 個記錄`);
        
        // 再次檢查重複情況
        console.log('\n刪除後檢查重複情況:');
        
        db.all(duplicateCheckQuery, [], (finalErr, finalDuplicates) => {
          if (finalErr) {
            console.error('最終檢查失敗:', finalErr);
          } else {
            if (finalDuplicates.length > 0) {
              console.log('⚠️ 仍有重複的會員-服務組合:');
              finalDuplicates.forEach(dup => {
                console.log(`  會員 ${dup.member_id} - 服務 ${dup.service_id}: ${dup.count} 個記錄 [${dup.record_ids}] 狀態 [${dup.statuses}]`);
              });
            } else {
              console.log('✅ 沒有重複的會員-服務組合');
            }
          }

          // 顯示最終的記錄統計
          const countQuery = 'SELECT COUNT(*) as total FROM member_services';
          db.get(countQuery, [], (countErr, countResult) => {
            if (!countErr) {
              console.log(`\n📊 最終統計: member_services 表共有 ${countResult.total} 個記錄`);
            }

            db.close((closeErr) => {
              if (closeErr) {
                console.error('關閉資料庫失敗:', closeErr);
              } else {
                console.log('\n🎯 操作完成！');
              }
            });
          });
        });
      })
      .catch((error) => {
        console.error('刪除過程中出現錯誤:', error);
        db.close();
        process.exit(1);
      });
  });
});
