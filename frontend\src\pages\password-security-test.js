import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function PasswordSecurityTest() {
  const router = useRouter();
  const [password, setPassword] = useState('');
  const [validation, setValidation] = useState(null);
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [passwordSecurity, setPasswordSecurity] = useState(null);

  useEffect(() => {
    // 動態載入密碼安全模組
    const loadPasswordSecurity = async () => {
      try {
        const module = await import('../../lib/security/passwordSecurity.js');
        setPasswordSecurity(module.passwordSecurity);
      } catch (error) {
        console.error('載入密碼安全模組失敗:', error);
      }
    };

    loadPasswordSecurity();
  }, []);

  useEffect(() => {
    if (passwordSecurity && password) {
      const result = passwordSecurity.validatePassword(password);
      setValidation(result);
    } else {
      setValidation(null);
    }
  }, [password, passwordSecurity]);

  const generatePassword = () => {
    if (passwordSecurity) {
      const newPassword = passwordSecurity.generateSecurePassword(12);
      setGeneratedPassword(newPassword);
      setPassword(newPassword);
    }
  };

  const testCommonPasswords = () => {
    const commonPasswords = [
      'password',
      '123456',
      'qwerty',
      'admin',
      'Password123!',
      'MySecureP@ssw0rd2024'
    ];

    commonPasswords.forEach(pwd => {
      console.log(`測試密碼: ${pwd}`);
      if (passwordSecurity) {
        const result = passwordSecurity.validatePassword(pwd);
        console.log(`強度: ${result.strength}, 有效: ${result.isValid}`);
      }
    });
  };

  const getStrengthBarColor = (strength) => {
    if (strength >= 80) return 'bg-green-500';
    if (strength >= 60) return 'bg-blue-500';
    if (strength >= 40) return 'bg-yellow-500';
    if (strength >= 20) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getStrengthTextColor = (strength) => {
    if (strength >= 80) return 'text-green-600';
    if (strength >= 60) return 'text-blue-600';
    if (strength >= 40) return 'text-yellow-600';
    if (strength >= 20) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <>
      <Head>
        <title>密碼安全測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">🔐 密碼安全測試</h1>
                <p className="text-gray-600 mt-2">測試密碼強度和安全性</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/security-assessment')}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  🔒 安全評估
                </button>
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  🏠 回儀表板
                </button>
              </div>
            </div>

            {/* 密碼輸入和測試 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">🧪 密碼強度測試</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    輸入密碼進行測試
                  </label>
                  <div className="flex space-x-4">
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="輸入要測試的密碼"
                    />
                    <button
                      onClick={generatePassword}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                    >
                      🎲 生成安全密碼
                    </button>
                    <button
                      onClick={testCommonPasswords}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      🧪 測試常見密碼
                    </button>
                  </div>
                </div>

                {/* 生成的密碼顯示 */}
                {generatedPassword && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="text-sm font-medium text-green-700 mb-2">生成的安全密碼:</div>
                    <div className="font-mono text-green-900 bg-white p-2 rounded border">
                      {generatedPassword}
                    </div>
                    <div className="text-xs text-green-600 mt-1">
                      點擊上方的密碼輸入框來測試此密碼的強度
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 密碼驗證結果 */}
            {validation && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 驗證結果</h2>
                
                <div className="space-y-4">
                  {/* 密碼強度 */}
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">密碼強度</span>
                      <span className={`text-sm font-bold ${getStrengthTextColor(validation.strength)}`}>
                        {validation.strength}/100 - {validation.strengthText}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className={`h-3 rounded-full transition-all duration-300 ${getStrengthBarColor(validation.strength)}`}
                        style={{ width: `${validation.strength}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* 驗證狀態 */}
                  <div className={`p-4 rounded-lg border ${
                    validation.isValid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={validation.isValid ? 'text-green-600' : 'text-red-600'}>
                        {validation.isValid ? '✅' : '❌'}
                      </span>
                      <span className={`font-medium ${
                        validation.isValid ? 'text-green-900' : 'text-red-900'
                      }`}>
                        {validation.isValid ? '密碼符合安全要求' : '密碼不符合安全要求'}
                      </span>
                    </div>
                  </div>

                  {/* 錯誤信息 */}
                  {validation.errors.length > 0 && (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="text-sm font-medium text-red-700 mb-2">需要修正的問題:</div>
                      <ul className="text-sm text-red-600 space-y-1">
                        {validation.errors.map((error, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <span>•</span>
                            <span>{error}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* 警告信息 */}
                  {validation.warnings.length > 0 && (
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="text-sm font-medium text-yellow-700 mb-2">建議改進:</div>
                      <ul className="text-sm text-yellow-600 space-y-1">
                        {validation.warnings.map((warning, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <span>•</span>
                            <span>{warning}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 密碼安全建議 */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">💡 密碼安全建議</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="font-medium text-blue-900 mb-3">✅ 好的密碼特徵</h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• 至少 8 個字符長度</li>
                    <li>• 包含大小寫字母</li>
                    <li>• 包含數字和特殊字符</li>
                    <li>• 避免個人信息</li>
                    <li>• 每個帳號使用不同密碼</li>
                  </ul>
                </div>

                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h3 className="font-medium text-red-900 mb-3">❌ 避免的密碼特徵</h3>
                  <ul className="text-sm text-red-800 space-y-1">
                    <li>• 常見密碼（password、123456）</li>
                    <li>• 連續字符（abc、123）</li>
                    <li>• 重複字符（aaa、111）</li>
                    <li>• 個人信息（生日、姓名）</li>
                    <li>• 字典單詞</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* 密碼管理建議 */}
            <div className="p-6 bg-purple-50 border border-purple-200 rounded-lg">
              <h3 className="font-medium text-purple-900 mb-3">🔐 密碼管理最佳實踐</h3>
              <div className="text-sm text-purple-800 space-y-2">
                <div><strong>使用密碼管理器:</strong> 讓專業工具生成和管理複雜密碼</div>
                <div><strong>啟用雙重認證:</strong> 為重要帳號添加額外的安全層</div>
                <div><strong>定期更換密碼:</strong> 特別是重要帳號的密碼</div>
                <div><strong>檢查密碼洩露:</strong> 定期檢查密碼是否在數據洩露中出現</div>
                <div><strong>安全存儲:</strong> 永遠不要在不安全的地方記錄密碼</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
