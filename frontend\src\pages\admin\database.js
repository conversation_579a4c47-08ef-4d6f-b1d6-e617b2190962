import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function DatabaseAdmin() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [dbStats, setDbStats] = useState({});
  const [tables, setTables] = useState([]);
  const [queryResult, setQueryResult] = useState(null);
  const [sqlQuery, setSqlQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { id: Date.now(), timestamp, message, type }]);
  };

  // 檢查管理員權限
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    if (!token || !userStr) {
      router.push('/auth/login');
      return;
    }

    try {
      const userData = JSON.parse(userStr);
      if (userData.role !== 'admin') {
        router.push('/dashboard-simple');
        return;
      }
      setUser(userData);
    } catch (error) {
      router.push('/auth/login');
    }
  }, [router]);

  // 載入資料庫統計
  const loadDbStats = async () => {
    try {
      addLog('載入資料庫統計...', 'info');
      
      // 模擬資料庫統計數據
      const stats = {
        totalTables: 5,
        totalRecords: 1247,
        dbSize: '2.3 MB',
        lastBackup: '2025-01-26 14:30:00',
        connections: 3,
        uptime: '7 days 14 hours'
      };
      
      setDbStats(stats);
      addLog('資料庫統計載入完成', 'success');
    } catch (error) {
      addLog(`載入統計失敗: ${error.message}`, 'error');
    }
  };

  // 載入資料表列表
  const loadTables = async () => {
    try {
      addLog('載入資料表列表...', 'info');
      
      // 模擬資料表數據
      const tableList = [
        { name: 'members', records: 156, size: '45 KB', lastModified: '2025-01-26 10:15:00' },
        { name: 'services', records: 12, size: '8 KB', lastModified: '2025-01-25 16:20:00' },
        { name: 'member_services', records: 89, size: '12 KB', lastModified: '2025-01-26 09:45:00' },
        { name: 'transactions', records: 234, size: '67 KB', lastModified: '2025-01-26 11:30:00' },
        { name: 'audit_logs', records: 756, size: '123 KB', lastModified: '2025-01-26 14:25:00' }
      ];
      
      setTables(tableList);
      addLog('資料表列表載入完成', 'success');
    } catch (error) {
      addLog(`載入資料表失敗: ${error.message}`, 'error');
    }
  };

  // 執行 SQL 查詢
  const executeQuery = async () => {
    if (!sqlQuery.trim()) {
      addLog('請輸入 SQL 查詢語句', 'warning');
      return;
    }

    setIsLoading(true);
    try {
      addLog(`執行查詢: ${sqlQuery.substring(0, 50)}...`, 'info');
      
      // 模擬查詢結果
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockResult = {
        query: sqlQuery,
        rows: [
          { id: 1, name: '張小明', email: '<EMAIL>', status: 'active' },
          { id: 2, name: '李小華', email: '<EMAIL>', status: 'active' },
          { id: 3, name: '王小美', email: '<EMAIL>', status: 'inactive' }
        ],
        rowCount: 3,
        executionTime: '0.045s'
      };
      
      setQueryResult(mockResult);
      addLog(`查詢執行完成，返回 ${mockResult.rowCount} 行記錄`, 'success');
    } catch (error) {
      addLog(`查詢執行失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 備份資料庫
  const backupDatabase = async () => {
    setIsLoading(true);
    try {
      addLog('開始備份資料庫...', 'info');
      await new Promise(resolve => setTimeout(resolve, 2000));
      addLog('資料庫備份完成', 'success');
    } catch (error) {
      addLog(`備份失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 優化資料庫
  const optimizeDatabase = async () => {
    setIsLoading(true);
    try {
      addLog('開始優化資料庫...', 'info');
      await new Promise(resolve => setTimeout(resolve, 1500));
      addLog('資料庫優化完成', 'success');
    } catch (error) {
      addLog(`優化失敗: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 初始載入
  useEffect(() => {
    if (user) {
      loadDbStats();
      loadTables();
    }
  }, [user]);

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>資料庫管理 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/dashboard-simple')}
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  ← 返回儀表板
                </button>
                <div className="ml-6">
                  <h1 className="text-xl font-semibold text-gray-900">資料庫管理</h1>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">管理員: {user.name}</span>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            
            {/* 標籤導航 */}
            <div className="mb-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  {[
                    { id: 'overview', name: '概覽', icon: '📊' },
                    { id: 'tables', name: '資料表', icon: '🗃️' },
                    { id: 'query', name: 'SQL 查詢', icon: '💻' },
                    { id: 'maintenance', name: '維護', icon: '🔧' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {tab.icon} {tab.name}
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* 概覽標籤 */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* 統計卡片 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                            <span className="text-blue-600 text-lg">🗃️</span>
                          </div>
                        </div>
                        <div className="ml-5 w-0 flex-1">
                          <dl>
                            <dt className="text-sm font-medium text-gray-500 truncate">
                              資料表數量
                            </dt>
                            <dd className="text-lg font-medium text-gray-900">
                              {dbStats.totalTables}
                            </dd>
                          </dl>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                            <span className="text-green-600 text-lg">📝</span>
                          </div>
                        </div>
                        <div className="ml-5 w-0 flex-1">
                          <dl>
                            <dt className="text-sm font-medium text-gray-500 truncate">
                              總記錄數
                            </dt>
                            <dd className="text-lg font-medium text-gray-900">
                              {dbStats.totalRecords?.toLocaleString()}
                            </dd>
                          </dl>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                            <span className="text-purple-600 text-lg">💾</span>
                          </div>
                        </div>
                        <div className="ml-5 w-0 flex-1">
                          <dl>
                            <dt className="text-sm font-medium text-gray-500 truncate">
                              資料庫大小
                            </dt>
                            <dd className="text-lg font-medium text-gray-900">
                              {dbStats.dbSize}
                            </dd>
                          </dl>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 系統狀態 */}
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      系統狀態
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <dt className="text-sm font-medium text-gray-500">運行時間</dt>
                        <dd className="mt-1 text-sm text-gray-900">{dbStats.uptime}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">活躍連接</dt>
                        <dd className="mt-1 text-sm text-gray-900">{dbStats.connections}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">最後備份</dt>
                        <dd className="mt-1 text-sm text-gray-900">{dbStats.lastBackup}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">狀態</dt>
                        <dd className="mt-1 text-sm text-green-600 font-medium">正常運行</dd>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 資料表標籤 */}
            {activeTab === 'tables' && (
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      資料表列表
                    </h3>
                    <button
                      onClick={loadTables}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      🔄 重新載入
                    </button>
                  </div>

                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            資料表名稱
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            記錄數
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            大小
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            最後修改
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {tables.map((table, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {table.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {table.records.toLocaleString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {table.size}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {table.lastModified}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <button
                                onClick={() => setSqlQuery(`SELECT * FROM ${table.name} LIMIT 10;`)}
                                className="text-blue-600 hover:text-blue-500 mr-3"
                              >
                                查看
                              </button>
                              <button
                                onClick={() => setSqlQuery(`DESCRIBE ${table.name};`)}
                                className="text-green-600 hover:text-green-500"
                              >
                                結構
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {/* SQL 查詢標籤 */}
            {activeTab === 'query' && (
              <div className="space-y-6">
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      SQL 查詢執行器
                    </h3>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          SQL 查詢語句
                        </label>
                        <textarea
                          value={sqlQuery}
                          onChange={(e) => setSqlQuery(e.target.value)}
                          rows={6}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                          placeholder="輸入 SQL 查詢語句..."
                        />
                      </div>

                      <div className="flex space-x-4">
                        <button
                          onClick={executeQuery}
                          disabled={isLoading}
                          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                        >
                          {isLoading ? '執行中...' : '▶️ 執行查詢'}
                        </button>

                        <button
                          onClick={() => setSqlQuery('')}
                          className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                        >
                          🧹 清除
                        </button>

                        <div className="flex space-x-2">
                          <button
                            onClick={() => setSqlQuery('SELECT * FROM members LIMIT 10;')}
                            className="px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm"
                          >
                            查看會員
                          </button>
                          <button
                            onClick={() => setSqlQuery('SELECT * FROM services;')}
                            className="px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 text-sm"
                          >
                            查看服務
                          </button>
                          <button
                            onClick={() => setSqlQuery('SELECT COUNT(*) as total FROM member_services;')}
                            className="px-3 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 text-sm"
                          >
                            訂閱統計
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 查詢結果 */}
                {queryResult && (
                  <div className="bg-white shadow rounded-lg">
                    <div className="px-4 py-5 sm:p-6">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg leading-6 font-medium text-gray-900">
                          查詢結果
                        </h3>
                        <div className="text-sm text-gray-500">
                          {queryResult.rowCount} 行記錄 | 執行時間: {queryResult.executionTime}
                        </div>
                      </div>

                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              {queryResult.rows.length > 0 && Object.keys(queryResult.rows[0]).map((key) => (
                                <th key={key} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {key}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {queryResult.rows.map((row, index) => (
                              <tr key={index} className="hover:bg-gray-50">
                                {Object.values(row).map((value, cellIndex) => (
                                  <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {value}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 維護標籤 */}
            {activeTab === 'maintenance' && (
              <div className="space-y-6">
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      資料庫維護工具
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h4 className="text-md font-medium text-gray-900 mb-2">備份資料庫</h4>
                        <p className="text-sm text-gray-600 mb-4">
                          創建完整的資料庫備份文件
                        </p>
                        <button
                          onClick={backupDatabase}
                          disabled={isLoading}
                          className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                        >
                          {isLoading ? '備份中...' : '💾 開始備份'}
                        </button>
                      </div>

                      <div className="border border-gray-200 rounded-lg p-4">
                        <h4 className="text-md font-medium text-gray-900 mb-2">優化資料庫</h4>
                        <p className="text-sm text-gray-600 mb-4">
                          清理碎片並優化查詢性能
                        </p>
                        <button
                          onClick={optimizeDatabase}
                          disabled={isLoading}
                          className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                        >
                          {isLoading ? '優化中...' : '⚡ 開始優化'}
                        </button>
                      </div>

                      <div className="border border-gray-200 rounded-lg p-4">
                        <h4 className="text-md font-medium text-gray-900 mb-2">檢查完整性</h4>
                        <p className="text-sm text-gray-600 mb-4">
                          檢查資料庫完整性和一致性
                        </p>
                        <button
                          onClick={() => addLog('資料庫完整性檢查完成，未發現問題', 'success')}
                          className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                        >
                          🔍 檢查完整性
                        </button>
                      </div>

                      <div className="border border-gray-200 rounded-lg p-4">
                        <h4 className="text-md font-medium text-gray-900 mb-2">清理日誌</h4>
                        <p className="text-sm text-gray-600 mb-4">
                          清理舊的審計日誌和臨時文件
                        </p>
                        <button
                          onClick={() => addLog('日誌清理完成，釋放 45MB 空間', 'success')}
                          className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700"
                        >
                          🧹 清理日誌
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 危險操作區域 */}
                <div className="bg-red-50 border border-red-200 rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-red-900 mb-4">
                      ⚠️ 危險操作
                    </h3>
                    <p className="text-sm text-red-700 mb-4">
                      以下操作可能會影響系統穩定性，請謹慎使用。
                    </p>

                    <div className="space-y-3">
                      <button
                        onClick={() => {
                          if (confirm('確定要重置所有統計數據嗎？此操作不可逆！')) {
                            addLog('統計數據已重置', 'warning');
                          }
                        }}
                        className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                      >
                        🔄 重置統計數據
                      </button>

                      <button
                        onClick={() => {
                          if (confirm('確定要清空審計日誌嗎？此操作不可逆！')) {
                            addLog('審計日誌已清空', 'warning');
                          }
                        }}
                        className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                      >
                        🗑️ 清空審計日誌
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 操作日誌 */}
            <div className="mt-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">操作日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-48 overflow-y-auto font-mono text-sm">
                {logs.map((log) => (
                  <div key={log.id} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' : 
                    log.type === 'success' ? 'text-green-400' : 
                    log.type === 'warning' ? 'text-yellow-400' :
                    'text-blue-400'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">尚無操作記錄</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
