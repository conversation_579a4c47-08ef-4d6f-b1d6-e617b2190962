import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function BackupDashboard() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptions, setSubscriptions] = useState([]);

  useEffect(() => {
    // 簡單的認證檢查
    const checkAuth = () => {
      try {
        const token = localStorage.getItem('token');
        const userStr = localStorage.getItem('user');
        
        console.log('Backup Dashboard: 檢查認證', { token: !!token, user: !!userStr });
        
        if (token && userStr) {
          const userData = JSON.parse(userStr);
          setUser(userData);
          console.log('Backup Dashboard: 用戶已認證', userData.name);
          
          // 載入訂閱數據
          const subscriptionsStr = localStorage.getItem('subscriptions');
          if (subscriptionsStr) {
            setSubscriptions(JSON.parse(subscriptionsStr));
          }
        } else {
          console.log('Backup Dashboard: 沒有認證，跳轉到登入');
          router.push('/auth/login');
          return;
        }
      } catch (error) {
        console.error('Backup Dashboard: 認證檢查錯誤:', error);
        router.push('/auth/login');
        return;
      }
      
      setIsLoading(false);
    };

    // 確保在客戶端執行
    if (typeof window !== 'undefined') {
      checkAuth();
    } else {
      setIsLoading(false);
    }
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('subscriptions');
    document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    router.push('/');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">重定向到登入頁面...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>備用儀表板 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* 導航欄 */}
        <nav className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div>
                <h1 className="text-xl font-semibold text-gray-900">會員儀表板</h1>
                <p className="text-sm text-gray-500">備用版本</p>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">歡迎，{user.name}</span>
                <button
                  onClick={handleLogout}
                  className="px-4 py-2 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                >
                  登出
                </button>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要內容 */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* 歡迎區域 */}
            <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
              <div className="px-4 py-5 sm:p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-2">
                  歡迎回來，{user.name}！
                </h2>
                <p className="text-sm text-gray-600">
                  Email: {user.email}
                </p>
                {user.created_at && (
                  <p className="text-sm text-gray-600">
                    註冊時間: {new Date(user.created_at).toLocaleDateString()}
                  </p>
                )}
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-sm text-yellow-800">
                    ℹ️ 這是備用儀表板頁面。如果主儀表板有問題，您可以使用這個頁面。
                  </p>
                </div>
              </div>
            </div>

            {/* 統計卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">📋</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          我的訂閱
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {subscriptions.length}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">✅</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          帳戶狀態
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {user.status || '正常'}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">👤</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          會員等級
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {user.role === 'admin' ? '管理員' : '一般會員'}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <a
                    href="/subscriptions"
                    className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">📱</div>
                      <div className="text-sm font-medium text-gray-900">服務訂閱</div>
                      <div className="text-xs text-gray-500">管理您的訂閱</div>
                    </div>
                  </a>

                  <a
                    href="/auth-integration-test"
                    className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">🧪</div>
                      <div className="text-sm font-medium text-gray-900">系統測試</div>
                      <div className="text-xs text-gray-500">測試系統功能</div>
                    </div>
                  </a>

                  <a
                    href="/database-test"
                    className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">🗄️</div>
                      <div className="text-sm font-medium text-gray-900">資料庫測試</div>
                      <div className="text-xs text-gray-500">測試資料庫功能</div>
                    </div>
                  </a>

                  <a
                    href="/minimal-dashboard"
                    className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">🏠</div>
                      <div className="text-sm font-medium text-gray-900">主儀表板</div>
                      <div className="text-xs text-gray-500">返回主儀表板</div>
                    </div>
                  </a>
                </div>
              </div>
            </div>

            {/* 調試信息 */}
            <div className="mt-6 bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">調試信息</h3>
                <div className="bg-gray-50 p-4 rounded">
                  <pre className="text-sm text-gray-600">
                    {JSON.stringify({
                      user: user ? {
                        name: user.name,
                        email: user.email,
                        role: user.role,
                        status: user.status
                      } : null,
                      subscriptions: subscriptions.length,
                      hasToken: !!localStorage.getItem('token'),
                      timestamp: new Date().toISOString()
                    }, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
