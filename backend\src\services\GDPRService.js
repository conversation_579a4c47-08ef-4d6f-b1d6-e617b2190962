const { query, transaction } = require('../config/database');
const EncryptionService = require('./EncryptionService');

class GDPRService {
  // 記錄用戶同意
  static async recordConsent(userId, consentType, version, ipAddress, userAgent) {
    try {
      await query(
        `INSERT INTO user_consents (
          user_id, consent_type, version, granted_at,
          ip_address, user_agent, status
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP, $4, $5, 'granted')`,
        [userId, consentType, version, ipAddress, userAgent]
      );
    } catch (error) {
      console.error('Record consent error:', error);
      throw new Error('記錄用戶同意失敗');
    }
  }

  // 撤銷用戶同意
  static async revokeConsent(userId, consentType) {
    try {
      await query(
        `UPDATE user_consents
         SET status = 'revoked', revoked_at = CURRENT_TIMESTAMP
         WHERE user_id = $1 AND consent_type = $2 AND status = 'granted'`,
        [userId, consentType]
      );
    } catch (error) {
      console.error('Revoke consent error:', error);
      throw new Error('撤銷用戶同意失敗');
    }
  }

  // 獲取用戶同意狀態
  static async getConsentStatus(userId, consentType = null) {
    try {
      let queryText = `
        SELECT consent_type, version, granted_at, revoked_at, status
        FROM user_consents
        WHERE user_id = $1
      `;
      let params = [userId];

      if (consentType) {
        queryText += ' AND consent_type = $2';
        params.push(consentType);
      }

      queryText += ' ORDER BY granted_at DESC';

      const result = await query(queryText, params);
      return result.rows;
    } catch (error) {
      console.error('Get consent status error:', error);
      throw new Error('獲取用戶同意狀態失敗');
    }
  }

  // 檢查是否有有效同意
  static async hasValidConsent(userId, consentType) {
    try {
      const result = await query(
        `SELECT COUNT(*) as count
         FROM user_consents
         WHERE user_id = $1 AND consent_type = $2 AND status = 'granted'`,
        [userId, consentType]
      );

      return parseInt(result.rows[0].count) > 0;
    } catch (error) {
      console.error('Check valid consent error:', error);
      return false;
    }
  }

  // 導出用戶資料 (資料可攜權)
  static async exportUserData(userId) {
    return await transaction(async (client) => {
      try {
        const userData = {};

        // 基本用戶資料
        const userResult = await client.query(
          `SELECT id, username, email, first_name, last_name, phone,
                  date_of_birth, gender, timezone, language, status,
                  email_verified_at, phone_verified_at, created_at, updated_at
           FROM users WHERE id = $1`,
          [userId]
        );

        if (userResult.rows.length === 0) {
          throw new Error('用戶不存在');
        }

        userData.profile = userResult.rows[0];

        // 錢包資料
        const walletsResult = await client.query(
          `SELECT currency, balance, frozen_balance, total_deposited,
                  total_withdrawn, status, created_at, updated_at
           FROM wallets WHERE user_id = $1`,
          [userId]
        );
        userData.wallets = walletsResult.rows;

        // 交易記錄
        const transactionsResult = await client.query(
          `SELECT t.type, t.amount, t.currency, t.description,
                  t.status, t.created_at
           FROM transactions t
           JOIN wallets w ON t.wallet_id = w.id
           WHERE w.user_id = $1
           ORDER BY t.created_at DESC`,
          [userId]
        );
        userData.transactions = transactionsResult.rows;

        // 支付記錄
        const paymentsResult = await client.query(
          `SELECT payment_method, provider, amount, currency,
                  net_amount, status, created_at, completed_at
           FROM payments WHERE user_id = $1
           ORDER BY created_at DESC`,
          [userId]
        );
        userData.payments = paymentsResult.rows;

        // 訂閱記錄
        const subscriptionsResult = await client.query(
          `SELECT s.status, s.price, s.currency, s.billing_period,
                  s.current_period_start, s.current_period_end,
                  s.next_billing_date, s.created_at,
                  srv.name as service_name, sp.name as plan_name
           FROM subscriptions s
           JOIN services srv ON s.service_id = srv.id
           JOIN service_plans sp ON s.plan_id = sp.id
           WHERE s.user_id = $1
           ORDER BY s.created_at DESC`,
          [userId]
        );
        userData.subscriptions = subscriptionsResult.rows;

        // 同意記錄
        const consentsResult = await client.query(
          `SELECT consent_type, version, granted_at, revoked_at, status
           FROM user_consents WHERE user_id = $1
           ORDER BY granted_at DESC`,
          [userId]
        );
        userData.consents = consentsResult.rows;

        // 活動日誌
        const activityResult = await client.query(
          `SELECT activity_type, description, ip_address, user_agent,
                  created_at
           FROM user_activity_logs WHERE user_id = $1
           ORDER BY created_at DESC
           LIMIT 1000`,
          [userId]
        );
        userData.activities = activityResult.rows;

        // 記錄資料導出請求
        await client.query(
          `INSERT INTO data_export_requests (
            user_id, status, requested_at, export_type
          ) VALUES ($1, 'completed', CURRENT_TIMESTAMP, 'full_export')`,
          [userId]
        );

        return {
          exportDate: new Date().toISOString(),
          userId: userId,
          data: userData
        };
      } catch (error) {
        console.error('Export user data error:', error);
        throw new Error('導出用戶資料失敗');
      }
    });
  }

  // 刪除用戶資料 (被遺忘權)
  static async deleteUserData(userId, deleteType = 'soft') {
    return await transaction(async (client) => {
      try {
        if (deleteType === 'hard') {
          // 硬刪除 - 完全移除資料
          await this.hardDeleteUserData(client, userId);
        } else {
          // 軟刪除 - 匿名化處理
          await this.softDeleteUserData(client, userId);
        }

        // 記錄刪除請求
        await client.query(
          `INSERT INTO data_deletion_requests (
            user_id, deletion_type, status, requested_at, completed_at
          ) VALUES ($1, $2, 'completed', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
          [userId, deleteType]
        );

        return { success: true, deletionType: deleteType };
      } catch (error) {
        console.error('Delete user data error:', error);
        throw new Error('刪除用戶資料失敗');
      }
    });
  }

  // 軟刪除 - 匿名化處理
  static async softDeleteUserData(client, userId) {
    const anonymousId = `anonymous_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 匿名化用戶基本資料
    await client.query(
      `UPDATE users SET
        username = $2,
        email = $2,
        first_name = 'Anonymous',
        last_name = 'User',
        phone = NULL,
        date_of_birth = NULL,
        avatar_url = NULL,
        status = 'deleted',
        deleted_at = CURRENT_TIMESTAMP
       WHERE id = $1`,
      [userId, anonymousId]
    );

    // 匿名化活動日誌中的敏感資料
    await client.query(
      `UPDATE user_activity_logs SET
        ip_address = '0.0.0.0',
        user_agent = 'Anonymous'
       WHERE user_id = $1`,
      [userId]
    );
  }

  // 硬刪除 - 完全移除資料
  static async hardDeleteUserData(client, userId) {
    // 刪除順序很重要，要考慮外鍵約束

    // 刪除活動日誌
    await client.query('DELETE FROM user_activity_logs WHERE user_id = $1', [userId]);

    // 刪除同意記錄
    await client.query('DELETE FROM user_consents WHERE user_id = $1', [userId]);

    // 刪除訂閱使用記錄
    await client.query(
      `DELETE FROM subscription_usage
       WHERE subscription_id IN (
         SELECT id FROM subscriptions WHERE user_id = $1
       )`,
      [userId]
    );

    // 刪除訂閱記錄
    await client.query('DELETE FROM subscriptions WHERE user_id = $1', [userId]);

    // 刪除交易記錄
    await client.query(
      `DELETE FROM transactions
       WHERE wallet_id IN (
         SELECT id FROM wallets WHERE user_id = $1
       )`,
      [userId]
    );

    // 刪除支付記錄
    await client.query('DELETE FROM payments WHERE user_id = $1', [userId]);

    // 刪除錢包
    await client.query('DELETE FROM wallets WHERE user_id = $1', [userId]);

    // 刪除社群媒體綁定
    await client.query('DELETE FROM user_social_accounts WHERE user_id = $1', [userId]);

    // 最後刪除用戶記錄
    await client.query('DELETE FROM users WHERE id = $1', [userId]);
  }

  // 資料修正 (更正權)
  static async correctUserData(userId, corrections) {
    try {
      const allowedFields = [
        'first_name', 'last_name', 'phone', 'date_of_birth',
        'gender', 'timezone', 'language'
      ];

      const updates = [];
      const values = [];
      let paramIndex = 2;

      for (const [field, value] of Object.entries(corrections)) {
        if (allowedFields.includes(field)) {
          updates.push(`${field} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      }

      if (updates.length === 0) {
        throw new Error('沒有可更新的欄位');
      }

      values.unshift(userId); // 將 userId 加到參數列表開頭

      await query(
        `UPDATE users SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
         WHERE id = $1`,
        values
      );

      // 記錄資料修正請求
      await query(
        `INSERT INTO data_correction_requests (
          user_id, corrections, status, requested_at, completed_at
        ) VALUES ($1, $2, 'completed', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [userId, JSON.stringify(corrections)]
      );

      return { success: true };
    } catch (error) {
      console.error('Correct user data error:', error);
      throw new Error('資料修正失敗');
    }
  }
}

module.exports = GDPRService;