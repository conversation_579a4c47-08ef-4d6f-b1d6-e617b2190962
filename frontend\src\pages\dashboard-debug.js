import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function DashboardDebug() {
  const router = useRouter();
  const [debugInfo, setDebugInfo] = useState({});
  const [logs, setLogs] = useState([]);

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    addLog('Dashboard Debug: 開始診斷');
    
    const diagnose = async () => {
      const info = {
        timestamp: new Date().toISOString(),
        isClient: typeof window !== 'undefined',
        hasLocalStorage: typeof localStorage !== 'undefined',
        token: null,
        user: null,
        cookies: null,
        apiTest: null
      };

      if (typeof window !== 'undefined') {
        addLog('在客戶端環境');
        
        // 檢查 localStorage
        try {
          info.token = localStorage.getItem('token');
          info.user = localStorage.getItem('user');
          addLog(`Token 存在: ${!!info.token}`);
          addLog(`User 存在: ${!!info.user}`);
        } catch (error) {
          addLog(`localStorage 錯誤: ${error.message}`);
        }

        // 檢查 cookies
        try {
          info.cookies = document.cookie;
          addLog(`Cookies: ${info.cookies || '無'}`);
        } catch (error) {
          addLog(`Cookie 錯誤: ${error.message}`);
        }

        // 測試 API
        if (info.token) {
          try {
            addLog('測試 API 連接...');
            const response = await fetch('/api/auth/verify', {
              headers: {
                'Authorization': `Bearer ${info.token}`
              }
            });
            const result = await response.json();
            info.apiTest = {
              status: response.status,
              success: result.success,
              error: result.error
            };
            addLog(`API 測試結果: ${response.status} - ${result.success ? '成功' : '失敗'}`);
            if (!result.success) {
              addLog(`API 錯誤: ${result.error}`);
            }
          } catch (error) {
            info.apiTest = { error: error.message };
            addLog(`API 測試錯誤: ${error.message}`);
          }
        } else {
          addLog('沒有 Token，跳過 API 測試');
        }
      } else {
        addLog('在服務器端環境');
      }

      setDebugInfo(info);
      addLog('診斷完成');
    };

    diagnose();
  }, []);

  const clearStorage = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('subscriptions');
      document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      addLog('已清除所有存儲數據');
      window.location.reload();
    }
  };

  const testLogin = () => {
    router.push('/auth/login');
  };

  const testMinimalDashboard = () => {
    router.push('/minimal-dashboard');
  };

  const testBackupDashboard = () => {
    router.push('/backup-dashboard');
  };

  return (
    <>
      <Head>
        <title>儀表板診斷 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">儀表板診斷工具</h1>
            
            {/* 控制按鈕 */}
            <div className="mb-8 flex flex-wrap gap-4">
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                🔄 重新診斷
              </button>
              
              <button
                onClick={clearStorage}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                🗑️ 清除存儲
              </button>
              
              <button
                onClick={testLogin}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                🔑 測試登入
              </button>
              
              <button
                onClick={testMinimalDashboard}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
              >
                🏠 測試主儀表板
              </button>
              
              <button
                onClick={testBackupDashboard}
                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
              >
                🏠 測試備用儀表板
              </button>
            </div>

            {/* 診斷結果 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 系統信息 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">系統信息</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>環境:</span>
                      <span className={debugInfo.isClient ? 'text-green-600' : 'text-red-600'}>
                        {debugInfo.isClient ? '客戶端' : '服務器端'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>LocalStorage:</span>
                      <span className={debugInfo.hasLocalStorage ? 'text-green-600' : 'text-red-600'}>
                        {debugInfo.hasLocalStorage ? '可用' : '不可用'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Token:</span>
                      <span className={debugInfo.token ? 'text-green-600' : 'text-red-600'}>
                        {debugInfo.token ? '存在' : '不存在'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>User:</span>
                      <span className={debugInfo.user ? 'text-green-600' : 'text-red-600'}>
                        {debugInfo.user ? '存在' : '不存在'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cookies:</span>
                      <span className={debugInfo.cookies ? 'text-green-600' : 'text-gray-600'}>
                        {debugInfo.cookies ? '有數據' : '無數據'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* API 測試結果 */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">API 測試</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  {debugInfo.apiTest ? (
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>狀態碼:</span>
                        <span className={debugInfo.apiTest.status === 200 ? 'text-green-600' : 'text-red-600'}>
                          {debugInfo.apiTest.status || 'N/A'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>成功:</span>
                        <span className={debugInfo.apiTest.success ? 'text-green-600' : 'text-red-600'}>
                          {debugInfo.apiTest.success ? '是' : '否'}
                        </span>
                      </div>
                      {debugInfo.apiTest.error && (
                        <div className="mt-2">
                          <span className="text-red-600 text-xs">
                            錯誤: {debugInfo.apiTest.error}
                          </span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500">
                      {debugInfo.token ? '測試中...' : '無 Token，未測試'}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 詳細數據 */}
            <div className="mt-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">詳細數據</h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-xs text-gray-600 overflow-auto">
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              </div>
            </div>

            {/* 操作日誌 */}
            <div className="mt-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">操作日誌</h2>
              <div className="bg-gray-900 text-green-400 rounded-lg p-4 max-h-64 overflow-y-auto font-mono text-sm">
                {logs.map((log, index) => (
                  <div key={index} className="mb-1">{log}</div>
                ))}
              </div>
            </div>

            {/* 建議 */}
            <div className="mt-8 p-6 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-3">診斷建議</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>如果主儀表板一直載入中：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>檢查瀏覽器控制台是否有 JavaScript 錯誤</li>
                  <li>嘗試清除存儲數據並重新登入</li>
                  <li>使用備用儀表板作為替代方案</li>
                  <li>檢查網路連接和 API 響應</li>
                </ul>
                
                <p className="mt-4"><strong>常見問題解決：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Token 過期：重新登入</li>
                  <li>API 錯誤：檢查服務器狀態</li>
                  <li>存儲問題：清除瀏覽器數據</li>
                  <li>網路問題：檢查連接</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
