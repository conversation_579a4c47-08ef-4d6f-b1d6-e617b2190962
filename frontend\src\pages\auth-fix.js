import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

export default function AuthFix() {
  const [status, setStatus] = useState('準備修復認證...');
  const [isFixed, setIsFixed] = useState(false);
  const [tokenInfo, setTokenInfo] = useState(null);
  const router = useRouter();

  useEffect(() => {
    fixAuthentication();
  }, []);

  const fixAuthentication = async () => {
    try {
      setStatus('🔍 檢查當前認證狀態...');
      
      // 檢查現有 token
      const existingToken = localStorage.getItem('token');
      const existingUser = localStorage.getItem('user');
      
      if (existingToken) {
        setStatus('🧪 測試現有 Token 有效性...');
        
        // 測試現有 token 是否有效
        const testResponse = await fetch('/api/subscriptions/simple', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${existingToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (testResponse.ok) {
          setStatus('✅ 現有 Token 有效！');
          setTokenInfo({
            token: existingToken.substring(0, 20) + '...',
            user: existingUser ? JSON.parse(existingUser) : null,
            isValid: true
          });
          setIsFixed(true);
          
          // 自動跳轉到訂閱頁面
          setTimeout(() => {
            router.push('/subscriptions');
          }, 2000);
          return;
        } else {
          setStatus('❌ 現有 Token 無效，清除舊數據...');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          localStorage.removeItem('subscriptions');
          // 清除 cookie
          document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        }
      }

      setStatus('🔑 使用後端 API 重新登入...');
      
      // 使用後端 API 登入
      const loginResponse = await fetch('http://localhost:3000/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: '1234'
        })
      });

      const loginResult = await loginResponse.json();
      
      if (loginResult.success) {
        setStatus('💾 保存新的認證數據...');
        
        const token = loginResult.data.token;
        const user = loginResult.data.user;
        
        // 保存到 localStorage
        localStorage.setItem('token', token);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('subscriptions', JSON.stringify([]));
        
        // 設置 cookie
        document.cookie = `access_token=${token}; path=/; max-age=604800`;
        
        setTokenInfo({
          token: token.substring(0, 20) + '...',
          user: user,
          isValid: true
        });

        setStatus('🧪 驗證新 Token...');
        
        // 驗證新 token
        const verifyResponse = await fetch('/api/subscriptions/simple', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (verifyResponse.ok) {
          setStatus('✅ 認證修復成功！Token 有效');
          setIsFixed(true);
          
          // 自動跳轉到訂閱頁面
          setTimeout(() => {
            router.push('/subscriptions');
          }, 2000);
        } else {
          setStatus('❌ 新 Token 驗證失敗');
        }
      } else {
        setStatus('❌ 後端登入失敗: ' + loginResult.error);
      }

    } catch (error) {
      setStatus('❌ 修復失敗: ' + error.message);
      console.error('認證修復錯誤:', error);
    }
  };

  const manualLogin = () => {
    router.push('/auth/login');
  };

  const testSubscriptions = () => {
    router.push('/subscriptions');
  };

  return (
    <>
      <Head>
        <title>認證修復 - 會員管理系統</title>
      </Head>
      
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            🔧 認證修復工具
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            自動修復 JWT Token 認證問題
          </p>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            
            {/* 狀態顯示 */}
            <div className="mb-6">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-3 ${
                  isFixed ? 'bg-green-500' : 'bg-yellow-500 animate-pulse'
                }`}></div>
                <span className="text-sm font-medium text-gray-900">
                  {status}
                </span>
              </div>
            </div>

            {/* Token 信息 */}
            {tokenInfo && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-sm font-medium text-gray-900 mb-2">認證信息</h3>
                <div className="space-y-1 text-xs text-gray-600">
                  <div>Token: {tokenInfo.token}</div>
                  <div>用戶: {tokenInfo.user?.name || '未知'}</div>
                  <div>郵箱: {tokenInfo.user?.email || '未知'}</div>
                  <div>角色: {tokenInfo.user?.role || '未知'}</div>
                  <div className={`font-medium ${
                    tokenInfo.isValid ? 'text-green-600' : 'text-red-600'
                  }`}>
                    狀態: {tokenInfo.isValid ? '✅ 有效' : '❌ 無效'}
                  </div>
                </div>
              </div>
            )}

            {/* 操作按鈕 */}
            <div className="space-y-3">
              {isFixed ? (
                <button
                  onClick={testSubscriptions}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  🎉 前往訂閱頁面
                </button>
              ) : (
                <button
                  onClick={fixAuthentication}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  🔄 重新修復
                </button>
              )}
              
              <button
                onClick={manualLogin}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                🔑 手動登入
              </button>
            </div>

            {/* 說明 */}
            <div className="mt-6 text-xs text-gray-500">
              <p>此工具會自動：</p>
              <ul className="mt-1 list-disc list-inside space-y-1">
                <li>檢查現有 Token 有效性</li>
                <li>清除無效的認證數據</li>
                <li>使用後端 API 重新登入</li>
                <li>驗證新 Token 的有效性</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
