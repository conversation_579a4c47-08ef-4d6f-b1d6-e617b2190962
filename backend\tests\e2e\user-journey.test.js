const request = require('supertest');
const app = require('../../src/app');

describe('User Journey E2E Tests', () => {
  describe('Complete User Registration and Payment Flow', () => {
    let accessToken;
    let refreshToken;
    let userId;
    let walletId;

    test('1. User should register successfully', async () => {
      const userData = {
        username: 'e2euser',
        email: '<EMAIL>',
        password: 'Test123456',
        first_name: 'E2<PERSON>',
        last_name: 'User',
        phone: '+886912345678'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toBeDefined();
      expect(response.body.data.tokens).toBeDefined();

      accessToken = response.body.data.tokens.accessToken;
      refreshToken = response.body.data.tokens.refreshToken;
      userId = response.body.data.user.id;
    });

    test('2. User should get their profile', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
    });

    test('3. User should get their wallets', async () => {
      const response = await request(app)
        .get('/api/wallets')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.wallets).toBeDefined();
      expect(Array.isArray(response.body.data.wallets)).toBe(true);

      // Should have default TWD wallet
      const twdWallet = response.body.data.wallets.find(w => w.currency === 'TWD');
      expect(twdWallet).toBeDefined();
      walletId = twdWallet.id;
    });

    test('4. User should create a Stripe payment intent', async () => {
      const paymentData = {
        amount: 1000,
        currency: 'TWD',
        description: 'E2E Test Payment'
      };

      const response = await request(app)
        .post('/api/payments/stripe/create-intent')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(paymentData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.clientSecret).toBeDefined();
      expect(response.body.data.paymentIntentId).toBeDefined();
    });

    test('5. User should view available services', async () => {
      const response = await request(app)
        .get('/api/services')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.services).toBeDefined();
      expect(Array.isArray(response.body.data.services)).toBe(true);
    });

    test('6. User should subscribe to a service', async () => {
      // First get available services
      const servicesResponse = await request(app)
        .get('/api/services')
        .set('Authorization', `Bearer ${accessToken}`);

      const services = servicesResponse.body.data.services;
      if (services.length > 0) {
        const service = services[0];
        
        // Get service plans
        const plansResponse = await request(app)
          .get(`/api/services/${service.id}/plans`)
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        const plans = plansResponse.body.data.plans;
        if (plans.length > 0) {
          const plan = plans[0];

          const subscriptionData = {
            serviceId: service.id,
            planId: plan.id,
            paymentMethod: 'wallet'
          };

          const response = await request(app)
            .post('/api/subscriptions')
            .set('Authorization', `Bearer ${accessToken}`)
            .send(subscriptionData)
            .expect(201);

          expect(response.body.success).toBe(true);
          expect(response.body.data.subscription).toBeDefined();
        }
      }
    });

    test('7. User should view their subscriptions', async () => {
      const response = await request(app)
        .get('/api/subscriptions')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.subscriptions).toBeDefined();
      expect(Array.isArray(response.body.data.subscriptions)).toBe(true);
    });

    test('8. User should view payment history', async () => {
      const response = await request(app)
        .get('/api/payments/history')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.payments).toBeDefined();
      expect(Array.isArray(response.body.data.payments)).toBe(true);
    });

    test('9. User should view transaction history', async () => {
      const response = await request(app)
        .get('/api/wallets/transactions')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.transactions).toBeDefined();
      expect(Array.isArray(response.body.data.transactions)).toBe(true);
    });

    test('10. User should update their profile', async () => {
      const updateData = {
        first_name: 'Updated',
        last_name: 'Name',
        phone: '+886987654321'
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.first_name).toBe('Updated');
      expect(response.body.data.user.last_name).toBe('Name');
    });

    test('11. User should refresh their token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.tokens).toBeDefined();
      expect(response.body.data.tokens.accessToken).toBeDefined();

      // Update access token for subsequent tests
      accessToken = response.body.data.tokens.accessToken;
    });

    test('12. User should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('登出成功');
    });

    test('13. User should not access protected routes after logout', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('14. User should login again with same credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Test123456'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.tokens).toBeDefined();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle invalid authentication gracefully', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('should handle non-existent resources gracefully', async () => {
      // Register a user first to get valid token
      const userData = {
        username: 'errortest',
        email: '<EMAIL>',
        password: 'Test123456',
        first_name: 'Error',
        last_name: 'Test'
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(userData);

      const accessToken = registerResponse.body.data.tokens.accessToken;

      // Try to access non-existent payment
      const response = await request(app)
        .get('/api/payments/99999')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    test('should handle malformed requests gracefully', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({ invalid: 'data' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    test('should handle server errors gracefully', async () => {
      // This test would require mocking database failures
      // For now, we'll test a route that might cause server errors
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: null // This might cause internal errors
        });

      // Should not return 500, should handle gracefully
      expect(response.status).not.toBe(500);
    });
  });

  describe('Security Tests', () => {
    test('should reject SQL injection attempts', async () => {
      const maliciousData = {
        email: "'; DROP TABLE users; --",
        password: 'password'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(maliciousData);

      // Should not crash and should handle safely
      expect(response.status).not.toBe(500);
    });

    test('should reject XSS attempts in registration', async () => {
      const xssData = {
        username: '<script>alert("xss")</script>',
        email: '<EMAIL>',
        password: 'Test123456',
        first_name: '<script>alert("xss")</script>',
        last_name: 'Test'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(xssData);

      // Should either reject or sanitize the input
      if (response.status === 201) {
        expect(response.body.data.user.username).not.toContain('<script>');
        expect(response.body.data.user.first_name).not.toContain('<script>');
      }
    });

    test('should enforce rate limiting', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      // Make multiple failed login attempts
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .post('/api/auth/login')
            .send(loginData)
        );
      }

      const responses = await Promise.all(promises);
      
      // At least some should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });
});
