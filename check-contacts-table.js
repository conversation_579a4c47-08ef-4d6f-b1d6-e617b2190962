// 檢查資料庫中是否有聯絡人相關的表
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'frontend', 'lib', 'database', 'database.sqlite');
const db = new sqlite3.Database(dbPath);

console.log('🔍 檢查資料庫中的聯絡人功能...');

// 檢查所有表
db.all('SELECT name FROM sqlite_master WHERE type="table"', [], (err, tables) => {
  if (err) {
    console.error('❌ 查詢失敗:', err);
    db.close();
    return;
  }

  console.log('\n📋 資料庫中的所有表:');
  tables.forEach(table => {
    console.log(`   - ${table.name}`);
  });

  // 檢查是否有聯絡人相關的表
  const contactTables = tables.filter(t => 
    t.name.toLowerCase().includes('contact') || 
    t.name.toLowerCase().includes('friend') ||
    t.name.toLowerCase().includes('frequent') ||
    t.name.toLowerCase().includes('address')
  );

  if (contactTables.length > 0) {
    console.log('\n📞 找到聯絡人相關的表:');
    contactTables.forEach(table => {
      console.log(`   - ${table.name}`);
    });
  } else {
    console.log('\n❌ 沒有找到聯絡人相關的表');
  }

  // 檢查 members 表是否可以作為聯絡人來源
  console.log('\n👥 檢查 members 表作為聯絡人來源...');
  
  db.all('SELECT id, name, email FROM members WHERE id != 1 LIMIT 5', [], (err2, members) => {
    if (err2) {
      console.error('❌ 查詢 members 表失敗:', err2);
    } else {
      console.log(`✅ 找到 ${members.length} 個其他會員可作為聯絡人:`);
      members.forEach(member => {
        console.log(`   - ${member.name} (${member.email})`);
      });
      
      if (members.length > 0) {
        console.log('\n💡 建議: 可以從 members 表載入真實的會員作為常用聯絡人');
      } else {
        console.log('\n⚠️ 目前只有一個會員，無法建立聯絡人列表');
      }
    }
    
    db.close();
  });
});
