const { stripe, paypal, ecpayConfig, rewardConfig, paymentMethods } = require('../config/payment');
const { query, transaction } = require('../config/database');
const Decimal = require('decimal.js');
const crypto = require('crypto');

class PaymentService {
  // 計算充值獎勵
  static calculateReward(amount, isFirstTime = false) {
    let bonusAmount = 0;

    // 階梯獎勵
    const applicableTier = rewardConfig.tiers
      .filter(tier => amount >= tier.minAmount)
      .sort((a, b) => b.minAmount - a.minAmount)[0];

    if (applicableTier) {
      bonusAmount = new Decimal(amount)
        .mul(applicableTier.bonusPercent)
        .div(100)
        .toNumber();
    }

    // 首次充值獎勵
    if (isFirstTime) {
      bonusAmount += rewardConfig.firstTimeBonus;
    }

    return {
      originalAmount: amount,
      bonusAmount: Math.floor(bonusAmount),
      totalAmount: amount + Math.floor(bonusAmount),
      bonusPercent: applicableTier ? applicableTier.bonusPercent : 0,
      isFirstTime,
    };
  }

  // 檢查充值限額
  static async checkDepositLimits(userId, amount) {
    const today = new Date().toISOString().split('T')[0];

    // 檢查單筆限額
    if (amount > rewardConfig.singleLimit) {
      throw new Error(`單筆充值金額不能超過 ${rewardConfig.singleLimit}`);
    }

    // 檢查最小金額
    if (amount < rewardConfig.minAmount) {
      throw new Error(`充值金額不能少於 ${rewardConfig.minAmount}`);
    }

    // 檢查每日限額
    const dailyTotal = await query(
      `SELECT COALESCE(SUM(amount), 0) as total
       FROM payments
       WHERE user_id = $1
       AND status = 'completed'
       AND DATE(created_at) = $2`,
      [userId, today]
    );

    const currentDailyTotal = parseFloat(dailyTotal.rows[0].total);
    if (currentDailyTotal + amount > rewardConfig.dailyLimit) {
      throw new Error(`每日充值限額為 ${rewardConfig.dailyLimit}，今日已充值 ${currentDailyTotal}`);
    }

    return true;
  }

  // 檢查是否為首次充值
  static async isFirstTimeDeposit(userId) {
    const result = await query(
      `SELECT COUNT(*) as count
       FROM payments
       WHERE user_id = $1
       AND status = 'completed'`,
      [userId]
    );

    return parseInt(result.rows[0].count) === 0;
  }

  // Stripe 支付
  static async createStripePayment(userId, amount, currency = 'TWD') {
    try {
      // 檢查限額
      await this.checkDepositLimits(userId, amount);

      // 檢查是否為首次充值
      const isFirstTime = await this.isFirstTimeDeposit(userId);

      // 計算獎勵
      const reward = this.calculateReward(amount, isFirstTime);

      // 創建 Stripe PaymentIntent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Stripe 使用分為單位
        currency: currency.toLowerCase(),
        metadata: {
          userId,
          originalAmount: amount,
          bonusAmount: reward.bonusAmount,
          totalAmount: reward.totalAmount,
        },
      });

      // 創建支付記錄
      const paymentRecord = await query(
        `INSERT INTO payments (
          user_id, payment_method, provider, provider_payment_id,
          amount, currency, net_amount, status, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *`,
        [
          userId,
          'credit_card',
          'stripe',
          paymentIntent.id,
          amount,
          currency,
          reward.totalAmount,
          'pending',
          JSON.stringify(reward)
        ]
      );

      return {
        paymentId: paymentRecord.rows[0].id,
        clientSecret: paymentIntent.client_secret,
        reward,
      };
    } catch (error) {
      console.error('Stripe payment creation error:', error);
      throw error;
    }
  }

  // PayPal 支付
  static async createPayPalPayment(userId, amount, currency = 'USD') {
    try {
      // 檢查限額
      await this.checkDepositLimits(userId, amount);

      // 檢查是否為首次充值
      const isFirstTime = await this.isFirstTimeDeposit(userId);

      // 計算獎勵
      const reward = this.calculateReward(amount, isFirstTime);

      const paymentData = {
        intent: 'sale',
        payer: {
          payment_method: 'paypal',
        },
        redirect_urls: {
          return_url: `${process.env.FRONTEND_URL}/payment/paypal/success`,
          cancel_url: `${process.env.FRONTEND_URL}/payment/paypal/cancel`,
        },
        transactions: [{
          amount: {
            total: amount.toFixed(2),
            currency,
          },
          description: `錢包充值 - ${amount} ${currency}`,
        }],
      };

      return new Promise((resolve, reject) => {
        paypal.payment.create(paymentData, async (error, payment) => {
          if (error) {
            reject(error);
          } else {
            // 創建支付記錄
            const paymentRecord = await query(
              `INSERT INTO payments (
                user_id, payment_method, provider, provider_payment_id,
                amount, currency, net_amount, status, metadata
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
              RETURNING *`,
              [
                userId,
                'paypal',
                'paypal',
                payment.id,
                amount,
                currency,
                reward.totalAmount,
                'pending',
                JSON.stringify(reward)
              ]
            );

            const approvalUrl = payment.links.find(link => link.rel === 'approval_url').href;

            resolve({
              paymentId: paymentRecord.rows[0].id,
              approvalUrl,
              reward,
            });
          }
        });
      });
    } catch (error) {
      console.error('PayPal payment creation error:', error);
      throw error;
    }
  }

  // 處理支付成功
  static async handlePaymentSuccess(paymentId, providerPaymentId) {
    return await transaction(async (client) => {
      // 獲取支付記錄
      const paymentResult = await client.query(
        'SELECT * FROM payments WHERE id = $1',
        [paymentId]
      );

      if (paymentResult.rows.length === 0) {
        throw new Error('支付記錄不存在');
      }

      const payment = paymentResult.rows[0];

      if (payment.status === 'completed') {
        throw new Error('支付已經完成');
      }

      // 更新支付狀態
      await client.query(
        'UPDATE payments SET status = $1, completed_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['completed', paymentId]
      );

      // 獲取用戶錢包
      const walletResult = await client.query(
        'SELECT * FROM wallets WHERE user_id = $1 AND currency = $2',
        [payment.user_id, payment.currency]
      );

      let wallet;
      if (walletResult.rows.length === 0) {
        // 創建新錢包
        const newWallet = await client.query(
          'INSERT INTO wallets (user_id, currency, balance) VALUES ($1, $2, $3) RETURNING *',
          [payment.user_id, payment.currency, 0]
        );
        wallet = newWallet.rows[0];
      } else {
        wallet = walletResult.rows[0];
      }

      // 更新錢包餘額
      const newBalance = parseFloat(wallet.balance) + parseFloat(payment.net_amount);
      await client.query(
        'UPDATE wallets SET balance = $1, total_deposited = total_deposited + $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3',
        [newBalance, payment.net_amount, wallet.id]
      );

      // 創建交易記錄
      await client.query(
        `INSERT INTO transactions (
          wallet_id, type, amount, balance_before, balance_after,
          currency, description, reference_id, reference_type, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        [
          wallet.id,
          'deposit',
          payment.net_amount,
          wallet.balance,
          newBalance,
          payment.currency,
          `充值成功 - ${payment.provider}`,
          payment.id,
          'payment',
          'completed'
        ]
      );

      return {
        success: true,
        newBalance,
        depositAmount: payment.amount,
        bonusAmount: payment.net_amount - payment.amount,
        totalAmount: payment.net_amount,
      };
    });
  }

  // 處理支付失敗
  static async handlePaymentFailure(paymentId, reason) {
    await query(
      'UPDATE payments SET status = $1, failure_reason = $2 WHERE id = $3',
      ['failed', reason, paymentId]
    );
  }

  // 獲取支付方式列表
  static getPaymentMethods(currency = 'TWD') {
    return Object.entries(paymentMethods)
      .filter(([key, method]) => method.supportedCurrencies.includes(currency))
      .map(([key, method]) => ({
        id: key,
        name: method.name,
        fees: method.fees,
      }));
  }

  // 獲取充值獎勵預覽
  static getRewardPreview(amount, isFirstTime = false) {
    return this.calculateReward(amount, isFirstTime);
  }
}

module.exports = PaymentService;