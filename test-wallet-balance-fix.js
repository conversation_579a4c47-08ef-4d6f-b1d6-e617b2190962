// 測試錢包餘額修復
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testWalletBalanceFix() {
  try {
    console.log('🔧 測試錢包餘額修復...');
    
    // 1. 登入獲取 token
    console.log('\n1️⃣ 登入獲取 token...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResult = await makeRequest(loginOptions, {
      email: '<EMAIL>',
      password: '1234'
    });

    if (loginResult.status !== 200 || !loginResult.data.success) {
      console.error('❌ 登入失敗:', loginResult.data);
      return;
    }

    const token = loginResult.data.data.token;
    const user = loginResult.data.data.user;
    console.log('✅ 登入成功 - 用戶:', user.name);

    // 2. 測試錢包餘額 API
    console.log('\n2️⃣ 測試錢包餘額 API...');
    const balanceOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/wallet/balance',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const balanceResult = await makeRequest(balanceOptions);
    
    console.log('錢包餘額 API 狀態:', balanceResult.status);
    console.log('錢包餘額 API 響應:', JSON.stringify(balanceResult.data, null, 2));

    if (balanceResult.status === 200 && balanceResult.data.success) {
      console.log('✅ 錢包餘額 API 正常工作！');
      console.log(`💰 當前餘額: ${balanceResult.data.data.formatted_balance}`);
      console.log(`💰 餘額數值: ${balanceResult.data.data.balance}`);
    } else {
      console.log('❌ 錢包餘額 API 失敗:', balanceResult.data);
    }

    // 3. 測試舊的錯誤端點（應該返回 404）
    console.log('\n3️⃣ 測試舊的錯誤端點...');
    const oldEndpointOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/wallets',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const oldEndpointResult = await makeRequest(oldEndpointOptions);
    
    console.log('舊端點狀態:', oldEndpointResult.status);
    if (oldEndpointResult.status === 404) {
      console.log('✅ 舊端點正確返回 404（符合預期）');
    } else {
      console.log('⚠️ 舊端點狀態異常:', oldEndpointResult.data);
    }

    // 4. 測試數據庫中的實際餘額
    console.log('\n4️⃣ 檢查數據庫中的實際餘額...');
    
    // 使用管理員 API 直接查詢數據庫
    const dbQueryOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/admin/database-query',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const dbQueryResult = await makeRequest(dbQueryOptions, {
      query: 'SELECT id, name, email, wallet_balance FROM members WHERE email = ?',
      params: ['<EMAIL>']
    });

    if (dbQueryResult.status === 200 && dbQueryResult.data.success) {
      const memberData = dbQueryResult.data.data[0];
      console.log('✅ 數據庫查詢成功');
      console.log(`💾 數據庫中的餘額: $${memberData.wallet_balance || 0}`);
      
      // 比較 API 和數據庫的餘額
      if (balanceResult.data.success) {
        const apiBalance = balanceResult.data.data.balance;
        const dbBalance = parseFloat(memberData.wallet_balance) || 0;
        
        if (Math.abs(apiBalance - dbBalance) < 0.01) {
          console.log('✅ API 和數據庫餘額一致');
        } else {
          console.log('❌ API 和數據庫餘額不一致');
          console.log(`   API 餘額: $${apiBalance}`);
          console.log(`   數據庫餘額: $${dbBalance}`);
        }
      }
    } else {
      console.log('❌ 數據庫查詢失敗:', dbQueryResult.data);
    }

    console.log('\n📋 測試總結:');
    if (balanceResult.status === 200 && balanceResult.data.success) {
      console.log('🎉 錢包餘額修復成功！');
      console.log('✅ 前端現在應該能正確顯示錢包餘額');
      console.log('✅ 建議刷新瀏覽器頁面查看效果');
    } else {
      console.log('❌ 錢包餘額仍有問題，需要進一步調試');
    }

  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testWalletBalanceFix();
