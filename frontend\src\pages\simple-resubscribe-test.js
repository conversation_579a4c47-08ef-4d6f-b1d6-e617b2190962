import { useState } from 'react';
import Head from 'next/head';

export default function SimpleResubscribeTest() {
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const testResubscribe = async () => {
    setIsLoading(true);
    setResult('開始測試...\n');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setResult(prev => prev + '❌ 請先登入\n');
        return;
      }

      // 步驟 1: 獲取當前訂閱
      setResult(prev => prev + '1️⃣ 獲取當前訂閱...\n');
      
      const subsResponse = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const subsResult = await subsResponse.json();
      if (!subsResult.success) {
        setResult(prev => prev + `❌ 獲取訂閱失敗: ${subsResult.error}\n`);
        return;
      }

      const activeSubscriptions = subsResult.data.filter(sub => sub.status === 'active');
      if (activeSubscriptions.length === 0) {
        setResult(prev => prev + '❌ 沒有活躍訂閱可以測試\n');
        return;
      }

      const testSubscription = activeSubscriptions[0];
      setResult(prev => prev + `✅ 選擇測試訂閱: ${testSubscription.service_name} (記錄ID: ${testSubscription.id})\n`);

      // 步驟 2: 取消訂閱
      setResult(prev => prev + '2️⃣ 取消訂閱...\n');
      
      const cancelResponse = await fetch('/api/subscriptions/cancel', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription_id: testSubscription.id
        })
      });

      const cancelResult = await cancelResponse.json();
      if (!cancelResult.success) {
        setResult(prev => prev + `❌ 取消失敗: ${cancelResult.error}\n`);
        return;
      }

      setResult(prev => prev + `✅ 取消成功: ${cancelResult.message}\n`);

      // 步驟 3: 等待一下
      setResult(prev => prev + '3️⃣ 等待 2 秒...\n');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 步驟 4: 重新訂閱
      setResult(prev => prev + '4️⃣ 重新訂閱...\n');
      
      const resubResponse = await fetch('/api/subscriptions/simple', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          service_id: testSubscription.service_id
        })
      });

      const resubResult = await resubResponse.json();
      if (!resubResult.success) {
        setResult(prev => prev + `❌ 重新訂閱失敗: ${resubResult.error}\n`);
        return;
      }

      setResult(prev => prev + `✅ 重新訂閱成功: ${resubResult.message}\n`);
      setResult(prev => prev + `📋 操作類型: ${resubResult.action || '未知'}\n`);
      setResult(prev => prev + `🆔 返回的記錄ID: ${resubResult.data.id}\n`);

      // 步驟 5: 驗證結果
      setResult(prev => prev + '5️⃣ 驗證結果...\n');
      
      if (resubResult.action === 'reactivated') {
        if (resubResult.data.id === testSubscription.id) {
          setResult(prev => prev + `✅ 完美！重新激活了原記錄 (ID: ${testSubscription.id})\n`);
        } else {
          setResult(prev => prev + `⚠️ 操作類型正確但記錄ID不同 (原: ${testSubscription.id}, 新: ${resubResult.data.id})\n`);
        }
      } else if (resubResult.action === 'created') {
        setResult(prev => prev + `❌ 錯誤！創建了新記錄而不是重用原記錄\n`);
        setResult(prev => prev + `   原記錄ID: ${testSubscription.id}\n`);
        setResult(prev => prev + `   新記錄ID: ${resubResult.data.id}\n`);
      } else {
        setResult(prev => prev + `❓ 未知操作類型: ${resubResult.action}\n`);
      }

      // 步驟 6: 檢查資料庫中的記錄數量
      setResult(prev => prev + '6️⃣ 檢查記錄數量...\n');
      
      const finalSubsResponse = await fetch('/api/subscriptions/simple', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const finalSubsResult = await finalSubsResponse.json();
      if (finalSubsResult.success) {
        const serviceSubscriptions = finalSubsResult.data.filter(sub => sub.service_id === testSubscription.service_id);
        setResult(prev => prev + `📊 服務 ${testSubscription.service_name} 的訂閱記錄數: ${serviceSubscriptions.length}\n`);
        
        if (serviceSubscriptions.length === 1) {
          setResult(prev => prev + `✅ 正確！只有一個記錄\n`);
        } else {
          setResult(prev => prev + `❌ 錯誤！有 ${serviceSubscriptions.length} 個記錄\n`);
          serviceSubscriptions.forEach(sub => {
            setResult(prev => prev + `   記錄 ${sub.id}: ${sub.status}\n`);
          });
        }
      }

      setResult(prev => prev + '\n🎯 測試完成！\n');

    } catch (error) {
      setResult(prev => prev + `❌ 測試失敗: ${error.message}\n`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResult = () => {
    setResult('');
  };

  return (
    <>
      <Head>
        <title>簡單重新訂閱測試 - 會員管理系統</title>
      </Head>

      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">🔄 簡單重新訂閱測試</h1>
              <p className="text-gray-600 mt-2">一鍵測試重新訂閱是否重用現有記錄</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 控制面板 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試控制</h2>
                
                <div className="space-y-4">
                  <button
                    onClick={testResubscribe}
                    disabled={isLoading}
                    className="w-full px-6 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 text-lg font-medium"
                  >
                    {isLoading ? '測試中...' : '🧪 開始測試'}
                  </button>

                  <button
                    onClick={clearResult}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    🗑️ 清除結果
                  </button>
                </div>

                {/* 說明 */}
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="font-medium text-blue-900 mb-2">📋 測試步驟</h3>
                  <div className="text-sm text-blue-800 space-y-1">
                    <div>1. 獲取第一個活躍訂閱</div>
                    <div>2. 取消該訂閱</div>
                    <div>3. 重新訂閱同一服務</div>
                    <div>4. 檢查是否重用記錄</div>
                    <div>5. 驗證記錄數量</div>
                  </div>
                </div>

                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h3 className="font-medium text-green-900 mb-2">✅ 預期結果</h3>
                  <div className="text-sm text-green-800 space-y-1">
                    <div>• 操作類型: reactivated</div>
                    <div>• 記錄ID: 與原記錄相同</div>
                    <div>• 記錄數量: 1 個</div>
                  </div>
                </div>

                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h3 className="font-medium text-red-900 mb-2">❌ 錯誤情況</h3>
                  <div className="text-sm text-red-800 space-y-1">
                    <div>• 操作類型: created</div>
                    <div>• 記錄ID: 與原記錄不同</div>
                    <div>• 記錄數量: 2 個或更多</div>
                  </div>
                </div>
              </div>

              {/* 測試結果 */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">測試結果</h2>
                
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                  {result ? (
                    <pre className="whitespace-pre-wrap">{result}</pre>
                  ) : (
                    <div className="text-gray-500">點擊「開始測試」按鈕...</div>
                  )}
                </div>
              </div>
            </div>

            {/* 問題說明 */}
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-3">🎯 問題說明</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p><strong>當前問題:</strong> 重新訂閱時可能創建新記錄而不是重用現有記錄</p>
                <p><strong>正確行為:</strong> 重新訂閱已取消的服務時，應該將現有記錄的狀態從 'cancelled' 改為 'active'</p>
                <p><strong>錯誤行為:</strong> 創建新的訂閱記錄，導致同一服務有多個記錄</p>
                <p><strong>影響:</strong> 數量計算錯誤，使用中訂閱和已取消訂閱會重複</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
