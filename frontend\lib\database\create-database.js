// 創建和初始化 SQLite 資料庫
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');

// 資料庫文件路徑
const dbPath = path.join(__dirname, 'database.sqlite');

console.log('開始創建資料庫:', dbPath);

// 如果資料庫文件已存在，先刪除
if (fs.existsSync(dbPath)) {
  fs.unlinkSync(dbPath);
  console.log('刪除舊的資料庫文件');
}

// 創建新的資料庫
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('創建資料庫失敗:', err);
    process.exit(1);
  }
  console.log('資料庫文件創建成功');
});

// 創建表格和初始化數據
db.serialize(async () => {
  console.log('開始創建表格...');

  // 創建 members 表
  db.run(`
    CREATE TABLE IF NOT EXISTS members (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      email TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      password_hash TEXT NOT NULL,
      role TEXT DEFAULT 'user',
      status TEXT DEFAULT 'active',
      phone TEXT,
      email_verified BOOLEAN DEFAULT 0,
      last_login_at DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('創建 members 表失敗:', err);
    } else {
      console.log('✅ members 表創建成功');
    }
  });

  // 創建 services 表
  db.run(`
    CREATE TABLE IF NOT EXISTS services (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      price DECIMAL(10,2) NOT NULL,
      billing_cycle TEXT DEFAULT 'monthly',
      status TEXT DEFAULT 'active',
      category TEXT,
      features TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('創建 services 表失敗:', err);
    } else {
      console.log('✅ services 表創建成功');
    }
  });

  // 創建 member_services 表 (訂閱關係)
  db.run(`
    CREATE TABLE IF NOT EXISTS member_services (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      member_id INTEGER NOT NULL,
      service_id INTEGER NOT NULL,
      status TEXT DEFAULT 'active',
      subscribed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      next_billing_date DATE,
      auto_renew BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (member_id) REFERENCES members(id),
      FOREIGN KEY (service_id) REFERENCES services(id)
    )
  `, (err) => {
    if (err) {
      console.error('創建 member_services 表失敗:', err);
    } else {
      console.log('✅ member_services 表創建成功');
    }
  });

  // 等待表格創建完成後插入初始數據
  setTimeout(async () => {
    console.log('開始插入初始數據...');

    try {
      // 生成密碼哈希
      const adminPasswordHash = await bcrypt.hash('1234', 10);
      const userPasswordHash = await bcrypt.hash('1234', 10);

      // 插入初始會員數據
      const members = [
        {
          email: '<EMAIL>',
          name: '系統管理員',
          password_hash: adminPasswordHash,
          role: 'admin',
          status: 'active',
          phone: '0912345678',
          email_verified: 1,
          last_login_at: '2025-01-26 10:00:00'
        },
        {
          email: '<EMAIL>',
          name: '一般用戶',
          password_hash: userPasswordHash,
          role: 'user',
          status: 'active',
          phone: '0987654321',
          email_verified: 1,
          last_login_at: '2025-01-26 09:30:00'
        },
        {
          email: '<EMAIL>',
          name: '測試用戶123',
          password_hash: userPasswordHash,
          role: 'user',
          status: 'active',
          phone: '0912345678',
          email_verified: 0,
          last_login_at: null
        },
        {
          email: '<EMAIL>',
          name: '測試用戶456',
          password_hash: userPasswordHash,
          role: 'user',
          status: 'active',
          phone: '0912345678',
          email_verified: 0,
          last_login_at: null
        }
      ];

      // 插入會員數據
      const insertMember = db.prepare(`
        INSERT INTO members (email, name, password_hash, role, status, phone, email_verified, last_login_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      members.forEach((member, index) => {
        insertMember.run([
          member.email,
          member.name,
          member.password_hash,
          member.role,
          member.status,
          member.phone,
          member.email_verified,
          member.last_login_at
        ], function(err) {
          if (err) {
            console.error(`插入會員 ${member.email} 失敗:`, err);
          } else {
            console.log(`✅ 插入會員: ${member.email} (ID: ${this.lastID})`);
          }
        });
      });

      insertMember.finalize();

      // 插入服務數據
      const services = [
        {
          name: 'AI 文字生成服務',
          description: '使用先進的 AI 技術生成高質量文字內容',
          price: 299.00,
          billing_cycle: 'monthly',
          status: 'active',
          category: 'AI',
          features: JSON.stringify(['無限文字生成', '多種寫作風格', '24/7 技術支援'])
        },
        {
          name: '圖片處理服務',
          description: '專業的圖片編輯和處理工具',
          price: 199.00,
          billing_cycle: 'monthly',
          status: 'active',
          category: '圖片處理',
          features: JSON.stringify(['批量處理', '多種濾鏡', '雲端存儲'])
        },
        {
          name: '數據分析服務',
          description: '強大的數據分析和視覺化工具',
          price: 499.00,
          billing_cycle: 'monthly',
          status: 'active',
          category: '數據分析',
          features: JSON.stringify(['即時分析', '自定義報表', '數據視覺化'])
        }
      ];

      const insertService = db.prepare(`
        INSERT INTO services (name, description, price, billing_cycle, status, category, features)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      services.forEach((service) => {
        insertService.run([
          service.name,
          service.description,
          service.price,
          service.billing_cycle,
          service.status,
          service.category,
          service.features
        ], function(err) {
          if (err) {
            console.error(`插入服務 ${service.name} 失敗:`, err);
          } else {
            console.log(`✅ 插入服務: ${service.name} (ID: ${this.lastID})`);
          }
        });
      });

      insertService.finalize();

      // 插入一些訂閱關係
      setTimeout(() => {
        const subscriptions = [
          { member_id: 1, service_id: 1, next_billing_date: '2025-02-26' },
          { member_id: 1, service_id: 2, next_billing_date: '2025-02-26' },
          { member_id: 2, service_id: 1, next_billing_date: '2025-02-26' }
        ];

        const insertSubscription = db.prepare(`
          INSERT INTO member_services (member_id, service_id, next_billing_date)
          VALUES (?, ?, ?)
        `);

        subscriptions.forEach((sub) => {
          insertSubscription.run([sub.member_id, sub.service_id, sub.next_billing_date], function(err) {
            if (err) {
              console.error('插入訂閱關係失敗:', err);
            } else {
              console.log(`✅ 插入訂閱: 會員${sub.member_id} -> 服務${sub.service_id}`);
            }
          });
        });

        insertSubscription.finalize();

        // 關閉資料庫連接
        setTimeout(() => {
          db.close((err) => {
            if (err) {
              console.error('關閉資料庫失敗:', err);
            } else {
              console.log('🎉 資料庫初始化完成！');
              console.log('資料庫文件位置:', dbPath);
              
              // 顯示文件信息
              const stats = fs.statSync(dbPath);
              console.log('文件大小:', Math.round(stats.size / 1024), 'KB');
              console.log('創建時間:', stats.birthtime);
            }
          });
        }, 1000);
      }, 1000);

    } catch (error) {
      console.error('插入初始數據失敗:', error);
    }
  }, 1000);
});
