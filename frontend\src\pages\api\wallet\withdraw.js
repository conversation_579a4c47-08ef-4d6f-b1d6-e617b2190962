// 錢包提現 API
const jwt = require('jsonwebtoken');
const Wallet = require('../../../../lib/database/models/Wallet');
const WalletTransaction = require('../../../../lib/database/models/WalletTransaction');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

export default async function handler(req, res) {
  try {
    console.log('錢包提現 API: 收到請求', req.method);

    // 設置 CORS 頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        error: '方法不被允許'
      });
    }

    // 驗證認證
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '請先登入'
      });
    }

    const token = authHeader.substring(7);
    let decoded;

    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      console.error('錢包提現 API: JWT 驗證失敗:', jwtError);
      return res.status(401).json({
        success: false,
        error: '認證失效，請重新登入'
      });
    }

    const userId = decoded.userId;
    console.log('錢包提現 API: 用戶 ID:', userId);

    // 獲取請求數據
    const { amount, bank_account_id, description } = req.body;

    // 驗證提現金額
    if (!amount || isNaN(amount) || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: '請輸入有效的提現金額'
      });
    }

    const amountNum = parseFloat(amount);

    // 檢查最小提現金額
    if (amountNum < 100) {
      return res.status(400).json({
        success: false,
        error: '最小提現金額為 NT$ 100'
      });
    }

    // 檢查最大提現金額
    if (amountNum > 50000) {
      return res.status(400).json({
        success: false,
        error: '單次提現金額不能超過 NT$ 50,000'
      });
    }

    // 獲取當前餘額
    const currentBalance = await Wallet.getBalance(userId);
    
    if (amountNum > currentBalance) {
      return res.status(400).json({
        success: false,
        error: '餘額不足'
      });
    }

    // 計算手續費 (提現手續費 1%)
    const feeRate = 0.01;
    const fee = amountNum * feeRate;
    const totalDeduction = amountNum + fee;

    if (totalDeduction > currentBalance) {
      return res.status(400).json({
        success: false,
        error: `餘額不足，需要 NT$ ${totalDeduction.toFixed(2)} (包含手續費 NT$ ${fee.toFixed(2)})`
      });
    }

    // 生成提現參考號
    const withdrawReference = `WD_${Date.now()}_${userId}`;

    // 創建提現交易記錄
    const transaction = await WalletTransaction.create({
      member_id: userId,
      transaction_type: 'withdraw',
      amount: amountNum,
      balance_before: currentBalance,
      balance_after: currentBalance - totalDeduction,
      description: description || '錢包提現',
      status: 'completed',
      payment_method: '銀行轉帳',
      payment_reference: withdrawReference
    });

    // 更新錢包餘額
    await Wallet.updateBalance(userId, currentBalance - totalDeduction);

    // 如果有手續費，創建手續費記錄
    if (fee > 0) {
      await WalletTransaction.create({
        member_id: userId,
        transaction_type: 'penalty',
        amount: fee,
        balance_before: currentBalance - amountNum,
        balance_after: currentBalance - totalDeduction,
        description: '提現手續費',
        status: 'completed',
        payment_method: '系統扣除',
        payment_reference: withdrawReference + '_FEE'
      });
    }

    // 獲取更新後的餘額
    const newBalanceAmount = await Wallet.getBalance(userId);
    const newBalance = {
      balance: newBalanceAmount,
      formatted_balance: `$${newBalanceAmount.toFixed(2)}`
    };

    console.log('錢包提現 API: 提現成功', {
      id: transaction.id,
      amount: amountNum,
      fee: fee,
      total: totalDeduction,
      newBalance: newBalanceAmount
    });

    return res.status(200).json({
      success: true,
      data: {
        transaction: transaction.toJSON(),
        new_balance: newBalance,
        withdraw_details: {
          amount: amountNum,
          fee: fee,
          total: totalDeduction,
          bank_account_id: bank_account_id,
          withdraw_reference: withdrawReference,
          estimated_arrival: '1-3 個工作天'
        }
      },
      message: '提現申請成功'
    });

  } catch (error) {
    console.error('錢包提現 API: 處理失敗:', error);
    return res.status(500).json({
      success: false,
      error: '服務器內部錯誤: ' + error.message
    });
  }
}
