// API 安全防護中間件
class ApiSecurity {
  constructor() {
    // 限流配置
    this.rateLimitConfig = {
      windowMs: 15 * 60 * 1000, // 15分鐘窗口
      maxRequests: 100,          // 最大請求數
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    };

    // 請求記錄（實際應用中應使用 Redis）
    this.requestLog = new Map();
    
    // IP 黑名單
    this.ipBlacklist = new Set();
    
    // 可疑活動檢測
    this.suspiciousActivity = new Map();
  }

  // 限流中間件
  rateLimit(req, res, next) {
    const clientId = this.getClientId(req);
    const now = Date.now();
    const windowStart = now - this.rateLimitConfig.windowMs;

    // 清理過期記錄
    this.cleanupOldRequests(clientId, windowStart);

    // 獲取當前窗口內的請求
    const requests = this.requestLog.get(clientId) || [];
    const recentRequests = requests.filter(time => time > windowStart);

    // 檢查是否超過限制
    if (recentRequests.length >= this.rateLimitConfig.maxRequests) {
      // 記錄可疑活動
      this.recordSuspiciousActivity(clientId, 'rate_limit_exceeded');
      
      return res.status(429).json({
        success: false,
        error: 'Too Many Requests',
        message: '請求過於頻繁，請稍後再試',
        retryAfter: Math.ceil(this.rateLimitConfig.windowMs / 1000)
      });
    }

    // 記錄當前請求
    recentRequests.push(now);
    this.requestLog.set(clientId, recentRequests);

    // 設置響應標頭
    res.setHeader('X-RateLimit-Limit', this.rateLimitConfig.maxRequests);
    res.setHeader('X-RateLimit-Remaining', this.rateLimitConfig.maxRequests - recentRequests.length);
    res.setHeader('X-RateLimit-Reset', new Date(now + this.rateLimitConfig.windowMs));

    next();
  }

  // 輸入驗證中間件
  validateInput(req, res, next) {
    const errors = [];

    // 檢查 SQL 注入
    if (this.detectSqlInjection(req)) {
      errors.push('檢測到潛在的 SQL 注入攻擊');
      this.recordSuspiciousActivity(this.getClientId(req), 'sql_injection_attempt');
    }

    // 檢查 XSS
    if (this.detectXss(req)) {
      errors.push('檢測到潛在的 XSS 攻擊');
      this.recordSuspiciousActivity(this.getClientId(req), 'xss_attempt');
    }

    // 檢查路徑遍歷
    if (this.detectPathTraversal(req)) {
      errors.push('檢測到潛在的路徑遍歷攻擊');
      this.recordSuspiciousActivity(this.getClientId(req), 'path_traversal_attempt');
    }

    // 檢查過大的請求
    if (this.detectOversizedRequest(req)) {
      errors.push('請求大小超過限制');
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid Input',
        message: '輸入驗證失敗',
        details: errors
      });
    }

    next();
  }

  // IP 黑名單檢查
  checkIpBlacklist(req, res, next) {
    const clientIp = this.getClientIp(req);
    
    if (this.ipBlacklist.has(clientIp)) {
      return res.status(403).json({
        success: false,
        error: 'Forbidden',
        message: 'IP 地址已被封鎖'
      });
    }

    next();
  }

  // 安全標頭中間件
  securityHeaders(req, res, next) {
    // 防止點擊劫持
    res.setHeader('X-Frame-Options', 'DENY');
    
    // 防止 MIME 類型嗅探
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // XSS 保護
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // 內容安全策略
    res.setHeader('Content-Security-Policy', 
      "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
    
    // 嚴格傳輸安全
    if (req.secure) {
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    }
    
    // 推薦者策略
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // 權限策略
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

    next();
  }

  // 獲取客戶端 ID
  getClientId(req) {
    // 優先使用用戶 ID，其次使用 IP
    const userId = req.user?.userId;
    const ip = this.getClientIp(req);
    return userId ? `user:${userId}` : `ip:${ip}`;
  }

  // 獲取客戶端 IP
  getClientIp(req) {
    return req.headers['x-forwarded-for'] || 
           req.headers['x-real-ip'] || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           '127.0.0.1';
  }

  // 清理過期請求記錄
  cleanupOldRequests(clientId, windowStart) {
    const requests = this.requestLog.get(clientId);
    if (requests) {
      const recentRequests = requests.filter(time => time > windowStart);
      this.requestLog.set(clientId, recentRequests);
    }
  }

  // 檢測 SQL 注入
  detectSqlInjection(req) {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /(\'|\"|;|--|\*|\|)/,
      /(\bSCRIPT\b)/i
    ];

    const checkString = JSON.stringify(req.body) + JSON.stringify(req.query) + JSON.stringify(req.params);
    
    return sqlPatterns.some(pattern => pattern.test(checkString));
  }

  // 檢測 XSS
  detectXss(req) {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<img[^>]+src[^>]*>/gi
    ];

    const checkString = JSON.stringify(req.body) + JSON.stringify(req.query) + JSON.stringify(req.params);
    
    return xssPatterns.some(pattern => pattern.test(checkString));
  }

  // 檢測路徑遍歷
  detectPathTraversal(req) {
    const pathPatterns = [
      /\.\.\//g,
      /\.\.\\g,
      /%2e%2e%2f/gi,
      /%2e%2e%5c/gi
    ];

    const checkString = req.url + JSON.stringify(req.body) + JSON.stringify(req.query);
    
    return pathPatterns.some(pattern => pattern.test(checkString));
  }

  // 檢測過大請求
  detectOversizedRequest(req) {
    const maxSize = 1024 * 1024; // 1MB
    const bodySize = JSON.stringify(req.body).length;
    return bodySize > maxSize;
  }

  // 記錄可疑活動
  recordSuspiciousActivity(clientId, activityType) {
    const now = Date.now();
    const activities = this.suspiciousActivity.get(clientId) || [];
    
    activities.push({
      type: activityType,
      timestamp: now
    });

    // 只保留最近1小時的活動
    const oneHourAgo = now - (60 * 60 * 1000);
    const recentActivities = activities.filter(activity => activity.timestamp > oneHourAgo);
    
    this.suspiciousActivity.set(clientId, recentActivities);

    // 如果可疑活動過多，自動封鎖
    if (recentActivities.length >= 10) {
      const ip = clientId.startsWith('ip:') ? clientId.substring(3) : null;
      if (ip) {
        this.ipBlacklist.add(ip);
        console.log(`IP ${ip} 已被自動封鎖，原因：可疑活動過多`);
      }
    }
  }

  // 獲取安全統計
  getSecurityStats() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    
    let totalRequests = 0;
    let activeClients = 0;
    let suspiciousActivities = 0;

    // 統計請求
    for (const [clientId, requests] of this.requestLog.entries()) {
      const recentRequests = requests.filter(time => time > oneHourAgo);
      if (recentRequests.length > 0) {
        activeClients++;
        totalRequests += recentRequests.length;
      }
    }

    // 統計可疑活動
    for (const activities of this.suspiciousActivity.values()) {
      suspiciousActivities += activities.filter(activity => activity.timestamp > oneHourAgo).length;
    }

    return {
      totalRequests,
      activeClients,
      suspiciousActivities,
      blacklistedIps: this.ipBlacklist.size,
      rateLimitConfig: this.rateLimitConfig
    };
  }

  // 手動封鎖 IP
  blockIp(ip, reason = 'Manual block') {
    this.ipBlacklist.add(ip);
    console.log(`IP ${ip} 已被手動封鎖，原因：${reason}`);
    return { success: true, message: `IP ${ip} 已被封鎖` };
  }

  // 解除 IP 封鎖
  unblockIp(ip) {
    const wasBlocked = this.ipBlacklist.delete(ip);
    if (wasBlocked) {
      console.log(`IP ${ip} 已被解除封鎖`);
      return { success: true, message: `IP ${ip} 已被解除封鎖` };
    } else {
      return { success: false, message: `IP ${ip} 未在黑名單中` };
    }
  }

  // 清理過期數據
  cleanup() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    // 清理請求記錄
    for (const [clientId, requests] of this.requestLog.entries()) {
      const recentRequests = requests.filter(time => time > oneHourAgo);
      if (recentRequests.length === 0) {
        this.requestLog.delete(clientId);
      } else {
        this.requestLog.set(clientId, recentRequests);
      }
    }

    // 清理可疑活動記錄
    for (const [clientId, activities] of this.suspiciousActivity.entries()) {
      const recentActivities = activities.filter(activity => activity.timestamp > oneHourAgo);
      if (recentActivities.length === 0) {
        this.suspiciousActivity.delete(clientId);
      } else {
        this.suspiciousActivity.set(clientId, recentActivities);
      }
    }
  }
}

// 創建全局實例
const apiSecurity = new ApiSecurity();

// 定期清理（每30分鐘）
setInterval(() => {
  apiSecurity.cleanup();
}, 30 * 60 * 1000);

// 導出
module.exports = { ApiSecurity, apiSecurity };
